<?php

declare(strict_types=1);

use Hyperf\Database\Seeders\Seeder;
use App\Model\Permission\Menu;
use App\Model\Permission\Meta;
use Hyperf\DbConnection\Db;

class MenuUdpateArticle extends Seeder
{
    public const BASE_DATA = [
        'name' => '',
        'path' => '',
        'component' => '',
        'redirect' => '',
        'created_by' => 0,
        'updated_by' => 0,
        'remark' => '',
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (env('DB_DRIVER') === 'odbc-sql-server') {
            Db::unprepared('SET IDENTITY_INSERT [' . Menu::getModel()->getTable() . '] ON;');
        }
        $this->create($this->data());
        if (env('DB_DRIVER') === 'odbc-sql-server') {
            Db::unprepared('SET IDENTITY_INSERT [' . Menu::getModel()->getTable() . '] OFF;');
        }
    }

    public function data(): array
    {
        return [
            [
                'name' => 'articleManage',
                'path' => '/articleManage',
                'meta' => new Meta([
                    'title' => '资讯管理',
                    'i18n' => 'articleMenu.information',
                    'icon' => 'ri:database-line',
                    'type' => 'M',
                    'hidden' => 0,
                    'componentPath' => 'modules/',
                    'componentSuffix' => '.vue',
                    'breadcrumbEnable' => 1,
                    'copyright' => 1,
                    'cache' => 1,
                    'affix' => 0,
                ]),
                'children' => [
                    [
                        'name' => 'article:category',
                        'path' => '/article/category',
                        'component' => 'article/views/category/index',
                        'meta' => new Meta([
                            'title' => '分类管理',
                            'type' => 'M',
                            'hidden' => 0,
                            'icon' => 'ri:attachment-line',
                            'i18n' => 'article.Category',
                            'componentPath' => 'modules/',
                            'componentSuffix' => '.vue',
                            'breadcrumbEnable' => 1,
                            'copyright' => 1,
                            'cache' => 1,
                            'affix' => 0,
                        ]),
                        'children' => [
                            [
                                'name' => 'article:category:list',
                                'meta' => new Meta([
                                    'title' => '分类列表',
                                    'i18n' => 'article.category.list',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:category:create',
                                'meta' => new Meta([
                                    'title' => '分类新增',
                                    'i18n' => 'article.category.create',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:category:save',
                                'meta' => new Meta([
                                    'title' => '分类编辑',
                                    'i18n' => 'article.category.save',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:category:delete',
                                'meta' => new Meta([
                                    'title' => '分类删除',
                                    'i18n' => 'article.category.delete',
                                    'type' => 'B',
                                ]),
                            ],
                        ],
                    ],
                    [
                        'name' => 'article',
                        'path' => '/article/article',
                        'component' => 'article/views/article/index',
                        'meta' => new Meta([
                            'title' => '文章列表',
                            'type' => 'M',
                            'hidden' => 0,
                            'icon' => 'ant-design:align-left-outlined',
                            'i18n' => 'article.article.title',
                            'componentPath' => 'modules/',
                            'componentSuffix' => '.vue',
                            'breadcrumbEnable' => 1,
                            'copyright' => 1,
                            'cache' => 1,
                            'affix' => 0,
                        ]),
                        'children' => [
                            [
                                'name' => 'article:article:list',
                                'meta' => new Meta([
                                    'title' => '文章列表',
                                    'i18n' => 'article.article.list',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:article:create',
                                'meta' => new Meta([
                                    'title' => '文章新增',
                                    'i18n' => 'article.article.create',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:article:save',
                                'meta' => new Meta([
                                    'title' => '文章编辑',
                                    'i18n' => 'article.article.save',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:article:delete',
                                'meta' => new Meta([
                                    'title' => '文章删除',
                                    'i18n' => 'article.article.delete',
                                    'type' => 'B',
                                ]),
                            ],
                        ],
                    ],
                    [
                        'name' => 'notice',
                        'path' => '/article/notice',
                        'component' => 'article/views/notice/index',
                        'meta' => new Meta([
                            'title' => '公告列表',
                            'type' => 'M',
                            'hidden' => 0,
                            'icon' => 'ant-design:align-left-outlined',
                            'i18n' => 'article.Notice',
                            'componentPath' => 'modules/',
                            'componentSuffix' => '.vue',
                            'breadcrumbEnable' => 1,
                            'copyright' => 1,
                            'cache' => 1,
                            'affix' => 0,
                        ]),
                        'children' => [
                            [
                                'name' => 'article:notice:list',
                                'meta' => new Meta([
                                    'title' => '公告列表',
                                    'i18n' => 'article.notice.list',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:notice:create',
                                'meta' => new Meta([
                                    'title' => '公告新增',
                                    'i18n' => 'article.notice.create',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:notice:save',
                                'meta' => new Meta([
                                    'title' => '公告编辑',
                                    'i18n' => 'article.notice.save',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:notice:delete',
                                'meta' => new Meta([
                                    'title' => '公告删除',
                                    'i18n' => 'article.notice.delete',
                                    'type' => 'B',
                                ]),
                            ],
                        ],
                    ],
                    [
                        'name' => 'dynamics',
                        'path' => '/article/dynamics',
                        'component' => 'article/views/dynamics/index',
                        'meta' => new Meta([
                            'title' => '动态列表',
                            'type' => 'M',
                            'hidden' => 0,
                            'icon' => 'ant-design:audit-outlined',
                            'i18n' => 'article.Dynamics',
                            'componentPath' => 'modules/',
                            'componentSuffix' => '.vue',
                            'breadcrumbEnable' => 1,
                            'copyright' => 1,
                            'cache' => 1,
                            'affix' => 0,
                        ]),
                        'children' => [
                            [
                                'name' => 'article:dynamics:list',
                                'meta' => new Meta([
                                    'title' => '动态列表',
                                    'i18n' => 'article.dynamics.list',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:dynamics:create',
                                'meta' => new Meta([
                                    'title' => '动态新增',
                                    'i18n' => 'article.dynamics.create',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:dynamics:save',
                                'meta' => new Meta([
                                    'title' => '动态编辑',
                                    'i18n' => 'article.dynamics.save',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:dynamics:delete',
                                'meta' => new Meta([
                                    'title' => '动态删除',
                                    'i18n' => 'article.dynamics.delete',
                                    'type' => 'B',
                                ]),
                            ],
                        ],
                    ],
                    [
                        'name' => 'tipoffs',
                        'path' => '/article/tipoffs',
                        'component' => 'article/views/tipoffs/index',
                        'meta' => new Meta([
                            'title' => '举报列表',
                            'type' => 'M',
                            'hidden' => 0,
                            'icon' => 'ant-design:appstore-add-outlined',
                            'i18n' => 'article.Tipoffs',
                            'componentPath' => 'modules/',
                            'componentSuffix' => '.vue',
                            'breadcrumbEnable' => 1,
                            'copyright' => 1,
                            'cache' => 1,
                            'affix' => 0,
                        ]),
                        'children' => [
                            [
                                'name' => 'article:tipoffs:list',
                                'meta' => new Meta([
                                    'title' => '举报列表',
                                    'i18n' => 'article.tipoffs.list',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:tipoffs:create',
                                'meta' => new Meta([
                                    'title' => '举报新增',
                                    'i18n' => 'article.tipoffs.create',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:tipoffs:save',
                                'meta' => new Meta([
                                    'title' => '举报编辑',
                                    'i18n' => 'article.tipoffs.save',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:tipoffs:delete',
                                'meta' => new Meta([
                                    'title' => '举报删除',
                                    'i18n' => 'article.tipoffs.delete',
                                    'type' => 'B',
                                ]),
                            ],
                        ],
                    ],
                    [
                        'name' => 'dynamicsCurrency',
                        'path' => '/article/dynamicsCurrency',
                        'component' => 'article/views/dynamicsCurrency/index',
                        'meta' => new Meta([
                            'title' => '币种管理',
                            'type' => 'M',
                            'hidden' => 0,
                            'icon' => 'ant-design:appstore-add-outlined',
                            'i18n' => 'article.DynamicsCurrency',
                            'componentPath' => 'modules/',
                            'componentSuffix' => '.vue',
                            'breadcrumbEnable' => 1,
                            'copyright' => 1,
                            'cache' => 1,
                            'affix' => 0,
                        ]),
                        'children' => [
                            [
                                'name' => 'article:dynamics_currency:list',
                                'meta' => new Meta([
                                    'title' => '币种列表',
                                    'i18n' => 'article.dynamics_currency.list',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:dynamics_currency:create',
                                'meta' => new Meta([
                                    'title' => '币种新增',
                                    'i18n' => 'article.dynamics_currency.create',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:dynamics_currency:save',
                                'meta' => new Meta([
                                    'title' => '币种编辑',
                                    'i18n' => 'article.dynamics_currency.save',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:dynamics_currency:delete',
                                'meta' => new Meta([
                                    'title' => '币种删除',
                                    'i18n' => 'article.dynamics_currency.delete',
                                    'type' => 'B',
                                ]),
                            ],
                        ],
                    ],
                    [
                        'name' => 'hotTopic',
                        'path' => '/article/hotTopic',
                        'component' => 'article/views/hotTopic/index',
                        'meta' => new Meta([
                            'title' => '热门话题',
                            'type' => 'M',
                            'hidden' => 0,
                            'icon' => 'ant-design:align-right-outlined',
                            'i18n' => 'article.HotTopic',
                            'componentPath' => 'modules/',
                            'componentSuffix' => '.vue',
                            'breadcrumbEnable' => 1,
                            'copyright' => 1,
                            'cache' => 1,
                            'affix' => 0,
                        ]),
                        'children' => [
                            [
                                'name' => 'article:hot_topic:list',
                                'meta' => new Meta([
                                    'title' => '热门话题列表',
                                    'i18n' => 'article.hot_topic.list',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:hot_topic:create',
                                'meta' => new Meta([
                                    'title' => '热门话题新增',
                                    'i18n' => 'article.hot_topic.create',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:hot_topic:save',
                                'meta' => new Meta([
                                    'title' => '热门话题编辑',
                                    'i18n' => 'article.hot_topic.save',
                                    'type' => 'B',
                                ]),
                            ],
                            [
                                'name' => 'article:hot_topic:delete',
                                'meta' => new Meta([
                                    'title' => '热门话题删除',
                                    'i18n' => 'article.hot_topic.delete',
                                    'type' => 'B',
                                ]),
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    public function create(array $data, int $parent_id = 0): void
    {
        foreach ($data as $v) {
            $_v = $v;
            if (isset($v['children'])) {
                unset($_v['children']);
            }
            $_v['parent_id'] = $parent_id;
            $menu = Menu::create(array_merge(self::BASE_DATA, $_v));
            if (isset($v['children']) && count($v['children'])) {
                $this->create($v['children'], $menu->id);
            }
        }
    }
}
