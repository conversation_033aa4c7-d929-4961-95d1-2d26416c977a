<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddFrozenUsedAmountToTradeMarginOrder extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trade_margin_order', function (Blueprint $table) {
            $table->decimal('frozen_amount', 30, 18)->default(0)->comment('冻结资金')->after('reduce_only');
            $table->decimal('used_amount', 30, 18)->default(0)->comment('使用资金')->after('frozen_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trade_margin_order', function (Blueprint $table) {
            $table->dropColumn(['frozen_amount', 'used_amount']);
        });
    }
} 