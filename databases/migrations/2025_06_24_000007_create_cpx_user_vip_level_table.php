<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user_vip_level', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('用户ID');
            $table->bigInteger('vip_level_id')->comment('VIP等级ID');
            $table->decimal('current_spot_trading_volume', 20, 8)->default(0)->comment('当前现货交易量');
            $table->decimal('current_futures_trading_volume', 20, 8)->default(0)->comment('当前合约交易量');
            $table->decimal('current_total_asset', 20, 8)->default(0)->comment('当前总资产');
            $table->decimal('current_specific_asset_amount', 20, 8)->default(0)->comment('当前特定币种资产');
            $table->timestamp('level_achieved_at')->comment('达到等级时间');
            $table->timestamp('level_expires_at')->nullable()->comment('等级过期时间');
            $table->tinyInteger('is_active')->default(1)->comment('是否当前等级:0=否,1=是');
            $table->tinyInteger('gift_received')->default(0)->comment('是否领取礼包:0=否,1=是');
            $table->timestamps();

            // 索引
            $table->unique(['user_id']);
            $table->index(['vip_level_id']);
            $table->index(['is_active']);
            $table->index(['level_achieved_at']);
            $table->index(['level_expires_at']);

            $table->comment('用户VIP等级关联表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user_vip_level');
    }
};
