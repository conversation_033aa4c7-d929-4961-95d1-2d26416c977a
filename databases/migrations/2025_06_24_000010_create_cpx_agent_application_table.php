<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_agent_application', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('申请用户ID');
            $table->string('company_name', 200)->nullable()->comment('公司名称');
            $table->string('contact_person', 100)->comment('联系人');
            $table->string('contact_phone', 20)->comment('联系电话');
            $table->string('contact_email', 100)->comment('联系邮箱');
            $table->string('business_license', 255)->nullable()->comment('营业执照');
            $table->text('business_description')->nullable()->comment('业务描述');
            $table->text('marketing_plan')->nullable()->comment('营销计划');
            $table->decimal('expected_monthly_volume', 20, 8)->default(0)->comment('预期月交易量');
            $table->integer('expected_user_count')->default(0)->comment('预期用户数量');
            $table->string('contact_method', 200)->comment('联系方法 Telegram、WhatsApp、KaKaoTalk、LINE、Wechat');
            $table->string('contact_account', 200)->comment('联系账号');
            $table->text('additional_info')->nullable()->comment('附加信息');
            $table->tinyInteger('status')->default(0)->comment('申请状态:0=待审核,1=已通过,2=已拒绝');
            $table->text('rejection_reason')->nullable()->comment('拒绝原因');
            $table->timestamp('submitted_at')->comment('提交时间');
            $table->timestamp('reviewed_at')->nullable()->comment('审核时间');
            $table->bigInteger('reviewer_id')->nullable()->comment('审核员ID');
            $table->timestamps();

            // 索引
            $table->unique(['user_id']);
            $table->index(['status']);
            $table->index(['submitted_at']);
            $table->index(['reviewed_at']);
            $table->index(['reviewer_id']);

            $table->comment('代理商申请表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_agent_application');
    }
};
