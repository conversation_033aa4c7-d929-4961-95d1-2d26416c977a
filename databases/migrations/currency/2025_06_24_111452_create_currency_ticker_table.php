<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currency_ticker', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('currency_id')->comment('交易标的');
            $table->tinyInteger('market_type')->comment('交易市场');
            $table->decimal('price_change', 20, 8)->comment('价格变化');
            $table->decimal('price_changeP', 10, 2)->comment('价格变化百分比');
            $table->decimal('pre_close_price', 20, 8)->comment('上周期收盘价');
            $table->decimal('last_price', 20, 8)->comment('最新价格	');
            $table->decimal('last_qty', 20, 2)->comment('最新交易量');
            $table->decimal('open_price', 20, 8)->comment('开盘价格');
            $table->decimal('high_price', 20, 8)->comment('最高价格');
            $table->decimal('low_price', 20, 8)->comment('最低价格');
            $table->decimal('volume', 20, 8)->nullable()->comment('交易日成交量');
            $table->unique(['currency_id', 'market_type'], 'idx_union');
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
            $table->comment('交易对24小时价格变化数据');
            $table->engine = 'InnoDB';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency_ticker');
    }
};
