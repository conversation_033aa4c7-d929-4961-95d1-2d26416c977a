<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currency', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('symbol', 32)->comment('交易标的');
            $table->string('base_asset', 32)->comment('基础标的');
            $table->tinyInteger('base_assets_precision')->comment('基础标的资产精度');
            $table->string('quote_asset', 32)->comment('交易资产');
            $table->tinyInteger('quote_precision')->comment('交易资产计算精度');
            $table->tinyInteger('is_spotTrade')->default(0)->comment('是否支持现货交易');
            $table->tinyInteger('is_marginTrade')->default(0)->comment('是否支持合约交易');
            $table->tinyInteger('market_type')->comment('标的交易市场类型 : 1加密数字货币 2美股 3外汇 4期货');
            $table->char('trading_start', 12)->nullable()->comment('交易时段开始时间：h:i | 0为不限制');
            $table->char('trading_end', 12)->nullable()->comment('交易时段结束时间：h:i | 0为不限制');
            $table->char('trading_timezone', 12)->nullable()->comment('交易时间时区:UTC');
            $table->tinyInteger('status')->default(0)->comment('是否启用标的');
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
            $table->comment('平台所有交易标的数据');
            $table->engine = 'InnoDB';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency');
    }
};
