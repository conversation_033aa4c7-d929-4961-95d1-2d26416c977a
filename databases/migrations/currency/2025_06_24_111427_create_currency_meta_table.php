<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currency_mate', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('currency_id')->comment('关联 currency 表的id');
            $table->string('name', 32)->nullable()->comment('交易标的名称简介');
            $table->text('description')->nullable()->comment('标的介绍');
            $table->string('logo', 255)->nullable();
            $table->json('tags')->nullable()->comment('展示标签');
            $table->json('urls')->nullable()->comment('包含一些关于标的的数据');
            $table->string('twitter_username', 32)->nullable();
            $table->string('contract_address', 255)->nullable()->comment('合约地址');
            $table->tinyInteger('infinite_supply')->default(0)->comment('是否无限增发(仅对币种有效)');
            $table->json('address')->nullable()->comment('所在地址（仅对股票有效）');
            $table->string('cik', 255)->nullable()->comment('股票代码');
            $table->integer('phone_number')->nullable()->comment('（仅对股票有效）');
            $table->string('category', 32)->nullable()->comment('交易标的行情分类');
            $table->integer('rank')->nullable()->default(0)->comment('加密数字货币排名');
            $table->bigInteger('max_supply')->nullable()->comment('最大供应量(加密数字货币有效)');
            $table->bigInteger('total_supply')->nullable()->comment('流通供应量(加密数字货币有效)');
            $table->dateTime('date_added')->nullable()->comment('发行时间(加密数字货币有效)');
            $table->decimal('market_cap_dominance', 10, 2)->nullable()->comment('市场占有率(加密数字货币有效)');
            $table->timestamp('creatred_at')->nullable()->useCurrent();
            $table->timestamp('updated_at')->nullable();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
            $table->comment('交易标的介绍元数据');
            $table->engine = 'InnoDB';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency_mate');
    }
};
