<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('用户ID，主键');
            $table->bigInteger('parent_id')->nullable()->comment('上级ID');
            $table->string('account', 20)->unique()->comment('用户account');
            $table->string('username', 50)->unique()->comment('用户名');
            $table->string('display_name', 100)->unique()->comment('展示名称，默认为用户名');
            $table->string('email', 100)->nullable()->unique()->comment('邮箱');
            $table->string('phone', 20)->nullable()->unique()->comment('手机号');
            $table->string('phone_country_code', 10)->nullable()->comment('手机国家代码');
            $table->string('password', 255)->comment('登录密码');
            $table->string('fund_password', 255)->nullable()->comment('资金密码');
            $table->string('avatar', 255)->nullable()->comment('头像URL');
            $table->string('invite_code', 20)->unique()->comment('邀请码');
            $table->tinyInteger('register_type')->default(1)->comment('注册类型:1=邮箱,2=手机,3=苹果,4=谷歌');
            $table->tinyInteger('status')->default(1)->comment('状态:1=正常,2=禁用,3=注销');
            $table->timestamp('last_login_at')->nullable()->comment('最后登录时间');
            $table->ipAddress('last_login_ip')->nullable()->comment('最后登录IP');
            $table->string('last_login_device', 100)->nullable()->comment('最后登录设备');
            $table->string('google2fa_secret', 512)->nullable()->comment('谷歌验证码密钥，加密保存');
            $table->json('third_party_auth')->nullable()->comment('第三方登录认证信息(Apple/Google等)');
            $table->json('social_bindings')->nullable()->comment('社交媒体绑定信息(Telegram/微信/Twitter等)');
            // 代理id
            $table->bigInteger('agent_id')->nullable()->comment('代理商ID（为代理商时写入代理商表ID，方便查询）');
            // 代理商直客id
            $table->bigInteger('agent_client_id')->nullable()->comment('代理商直客ID（为代理商直客时写入代理商直客表ID，方便查询）');
            $table->text('concern_uids')->comment('关注的user_ids');
            $table->string('language', 50)->nullable()->comment('语言偏好');
            $table->string('introduction',255)->comment('个人简介');
            $table->timestamps();
            $table->softDeletes();

            // 索引
            $table->index(['account']);
            $table->index(['invite_code']);
            $table->index(['parent_id']);
            $table->index(['status']);

            $table->comment('交易所用户表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user');
    }
};
