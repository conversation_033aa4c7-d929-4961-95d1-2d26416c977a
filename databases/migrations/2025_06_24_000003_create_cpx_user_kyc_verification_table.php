<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user_kyc_verification', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('用户ID');
            $table->string('first_name', 50)->comment('名');
            $table->string('middle_name', 50)->nullable()->comment('中间名');
            $table->string('last_name', 50)->comment('姓');
            $table->tinyInteger('status')->default(0)->comment('认证状态:0=未提交,1=审核中,2=已通过,3=已拒绝');
            $table->string('document_type', 50)->nullable()->comment('证件类型:passport=护照,id_card=身份证,driver_license=驾驶证');
            $table->string('document_number', 100)->nullable()->comment('证件号码');
            $table->string('document_country', 50)->nullable()->comment('证件签发国家');
            $table->date('document_expiry_date')->nullable()->comment('证件到期日期');
            $table->string('document_front_image', 255)->nullable()->comment('证件正面照片');
            $table->string('document_back_image', 255)->nullable()->comment('证件背面照片');
            $table->string('selfie_image', 255)->nullable()->comment('手持证件自拍照');
            $table->date('birthday')->nullable()->comment('生日');
            $table->text('rejection_reason')->nullable()->comment('拒绝原因');
            $table->timestamp('submitted_at')->nullable()->comment('提交时间');
            $table->timestamp('reviewed_at')->nullable()->comment('审核时间');
            $table->bigInteger('reviewer_id')->nullable()->comment('审核员ID');
            $table->timestamps();

            // 索引
            $table->unique(['user_id']);
            $table->index(['status']);
            $table->index(['document_type']);
            $table->index(['submitted_at']);
            $table->index(['reviewed_at']);

            $table->comment('用户KYC认证表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user_kyc_verification');
    }
};
