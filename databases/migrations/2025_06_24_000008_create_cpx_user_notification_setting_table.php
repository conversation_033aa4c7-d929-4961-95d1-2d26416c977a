<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user_notification_setting', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('用户ID');

            // 站内信设置
            $table->tinyInteger('system_message_enabled')->default(1)->comment('系统消息:0=关闭,1=开启');
            $table->tinyInteger('trading_message_enabled')->default(1)->comment('交易消息:0=关闭,1=开启');
            $table->tinyInteger('security_message_enabled')->default(1)->comment('安全消息:0=关闭,1=开启');
            $table->tinyInteger('promotion_message_enabled')->default(1)->comment('推广消息:0=关闭,1=开启');

            // 邮件通知设置
            $table->tinyInteger('email_login_enabled')->default(1)->comment('登录邮件通知:0=关闭,1=开启');
            $table->tinyInteger('email_trading_enabled')->default(1)->comment('交易邮件通知:0=关闭,1=开启');
            $table->tinyInteger('email_withdrawal_enabled')->default(1)->comment('提现邮件通知:0=关闭,1=开启');
            $table->tinyInteger('email_security_enabled')->default(1)->comment('安全邮件通知:0=关闭,1=开启');
            $table->tinyInteger('email_promotion_enabled')->default(0)->comment('推广邮件通知:0=关闭,1=开启');
            $table->tinyInteger('email_news_enabled')->default(0)->comment('新闻邮件通知:0=关闭,1=开启');

            // APP推送设置
            $table->tinyInteger('push_login_enabled')->default(1)->comment('登录推送通知:0=关闭,1=开启');
            $table->tinyInteger('push_trading_enabled')->default(1)->comment('交易推送通知:0=关闭,1=开启');
            $table->tinyInteger('push_price_alert_enabled')->default(1)->comment('价格提醒推送:0=关闭,1=开启');
            $table->tinyInteger('push_security_enabled')->default(1)->comment('安全推送通知:0=关闭,1=开启');
            $table->tinyInteger('push_promotion_enabled')->default(0)->comment('推广推送通知:0=关闭,1=开启');
            $table->tinyInteger('push_news_enabled')->default(0)->comment('新闻推送通知:0=关闭,1=开启');

            // 短信通知设置
            $table->tinyInteger('sms_login_enabled')->default(0)->comment('登录短信通知:0=关闭,1=开启');
            $table->tinyInteger('sms_trading_enabled')->default(0)->comment('交易短信通知:0=关闭,1=开启');
            $table->tinyInteger('sms_withdrawal_enabled')->default(1)->comment('提现短信通知:0=关闭,1=开启');
            $table->tinyInteger('sms_security_enabled')->default(1)->comment('安全短信通知:0=关闭,1=开启');

            $table->timestamps();

            // 索引
            $table->unique(['user_id']);

            $table->comment('用户通知设置表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user_notification_setting');
    }
};
