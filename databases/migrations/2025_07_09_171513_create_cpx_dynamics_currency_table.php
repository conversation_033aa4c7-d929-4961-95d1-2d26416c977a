<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_dynamics_currency', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('symbol_id')->comment('币种ID');
            $table->string('symbol',100)->comment('币种标的');
            $table->text('link')->comment('链接');
            $table->datetimes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_dynamics_currency');
    }
};
