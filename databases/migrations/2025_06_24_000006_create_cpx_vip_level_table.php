<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_vip_level', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->unique()->comment('等级名称 VIP0,VIP1');
            $table->integer('level')->unique()->comment('等级数值');
            $table->string('icon', 255)->nullable()->comment('等级图标');
            $table->string('color', 20)->nullable()->comment('等级颜色');
            $table->decimal('spot_trading_volume_requirement', 20, 8)->default(0)->comment('现货交易量要求');
            $table->decimal('futures_trading_volume_requirement', 20, 8)->default(0)->comment('合约交易量要求');
            $table->decimal('total_asset_requirement', 20, 8)->default(0)->comment('总资产要求');
            $table->string('specific_asset_symbol', 20)->nullable()->comment('特定币种资产要求符号');
            $table->decimal('specific_asset_amount', 20, 8)->default(0)->comment('特定币种资产要求数量');
            $table->decimal('spot_maker_fee_rate', 8, 6)->default(0.001)->comment('现货挂单手续费率');
            $table->decimal('spot_taker_fee_rate', 8, 6)->default(0.001)->comment('现货吃单手续费率');
            $table->decimal('futures_maker_fee_rate', 8, 6)->default(0.0002)->comment('合约挂单手续费率');
            $table->decimal('futures_taker_fee_rate', 8, 6)->default(0.0004)->comment('合约吃单手续费率');
            $table->decimal('daily_withdrawal_limit', 20, 8)->default(0)->comment('日提现限额');
            $table->json('vip_gift')->nullable()->comment('VIP升级礼包（分trading（交易）,assets（资产）两种类型）');
            $table->json('vip_privileges')->nullable()->comment('VIP权限列表');
            $table->text('description')->nullable()->comment('等级描述');
            $table->tinyInteger('status')->default(1)->comment('状态:1=启用,2=禁用');
            $table->integer('sort')->default(0)->comment('排序');
            $table->timestamps();

            // 索引
            $table->index(['level']);
            $table->index(['status']);
            $table->index(['sort']);

            $table->comment('会员等级表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_vip_level');
    }
};
