<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_agent_client', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('agent_id')->comment('代理商ID');
            $table->bigInteger('invite_code_id')->comment('邀请码ID');
            $table->bigInteger('user_id')->comment('客户用户ID');
            $table->decimal('spot_commission_rate', 8, 6)->default(0)->comment('现货返佣比例');
            $table->decimal('contract_commission_rate', 8, 6)->default(0)->comment('合约返佣比例');
            $table->decimal('trader_spot_commission_rate', 8, 6)->default(0)->comment('交易员现货返佣比例');
            $table->decimal('trader_contract_commission_rate', 8, 6)->default(0)->comment('交易员合约返佣比例');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamps();

            // 索引
            $table->index(['agent_id']);
            $table->index(['invite_code_id']);
            $table->index(['user_id']);

            $table->comment('代理商直推用户(直客)表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_agent_client');
    }
};
