<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_agent_invite_code', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('用户ID');
            $table->bigInteger('agent_id')->comment('代理商ID');
            $table->string('invite_code', 32)->comment('邀请码');
            $table->decimal('spot_commission_rate', 8, 6)->default(0)->comment('直客现货返佣比例');
            $table->decimal('contract_commission_rate', 8, 6)->default(0)->comment('直客合约返佣比例');
            $table->decimal('trader_spot_commission_rate', 8, 6)->default(0)->comment('直客交易员现货返佣比例');
            $table->decimal('trader_contract_commission_rate', 8, 6)->default(0)->comment('直客交易员合约返佣比例');
            $table->tinyInteger('is_default')->default(0)->comment('是否默认:0=否,1=是');
            $table->integer('click_count')->default(0)->comment('点击量');
            $table->timestamps();

            // 索引
            $table->unique(['invite_code']);
            $table->index(['agent_id']);
            $table->index(['user_id']);

            $table->comment('代理商邀请码表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_agent_invite_code');
    }
};
