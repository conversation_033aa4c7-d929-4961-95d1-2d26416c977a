<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_hot_topic', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->text('title')->comment('标题');
            $table->text('content')->comment('内容');
            $table->bigInteger('look_num')->comment('查看数量');
            $table->text('currency')->comment('关联币种IDs');
            $table->datetimes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_hot_topic');
    }
};
