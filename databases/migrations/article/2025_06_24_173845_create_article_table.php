<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_article', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键');
            $table->bigInteger('category_id')->default(0)->comment('分类ID');
            $table->text('title')->default('')->comment('标题');
            $table->text('content')->comment('内容');
            $table->text('summary')->default('')->comment('摘要');
            $table->text('remark')->default('')->comment('备注');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
            $table->comment('文章表');
            $table->engine = 'InnoDB';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('article');
    }
};
