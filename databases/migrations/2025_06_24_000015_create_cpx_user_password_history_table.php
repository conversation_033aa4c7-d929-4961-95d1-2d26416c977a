<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user_password_history', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('用户ID');
            $table->tinyInteger('password_type')->comment('密码类型:1=登录密码,2=交易密码');
            $table->string('password_hash', 255)->comment('密码哈希');
            $table->ipAddress('change_ip')->comment('修改IP');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'password_type']);
            $table->index(['created_at']);

            $table->comment('用户密码历史表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user_password_history');
    }
};
