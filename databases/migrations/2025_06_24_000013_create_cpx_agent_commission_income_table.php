<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_agent_commission_income', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('agent_id')->comment('代理商ID');
            $table->bigInteger('sub_agent_id')->nullable()->comment('下级代理商ID（如果开单用户为下级代理）');
            $table->bigInteger('agent_client_id')->nullable()->comment('代理商直客ID（如果开单用户为直客）');
            $table->bigInteger('trade_user_id')->comment('开单用户ID');
            $table->bigInteger('order_id')->comment('订单ID');
            $table->tinyInteger('trade_type')->comment('交易类型:1=现货,2=合约,3=现货杠杆');
            $table->decimal('trade_amount', 20, 8)->comment('交易金额');
            $table->decimal('fee_amount', 20, 8)->comment('手续费金额');
            $table->decimal('commission_income_rate', 8, 6)->comment('返佣收益比例 = 代理商返佣比例 - 下级返佣比例');
            $table->decimal('commission_income_amount', 20, 8)->comment('返佣收益金额');
            $table->tinyInteger('status')->default(0)->comment('状态:0=待结算,1=已结算,2=已取消');
            $table->timestamp('trade_time')->comment('交易时间');
            $table->timestamp('settled_at')->nullable()->comment('结算时间');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamps();

            // 索引
            $table->index(['agent_id', 'status']);
            $table->index(['trade_user_id']);
            $table->index(['order_id']);
            $table->index(['trade_type']);
            $table->index(['trade_time']);
            $table->index(['settled_at']);

            $table->comment('代理商佣金收益记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_agent_commission_income');
    }
};
