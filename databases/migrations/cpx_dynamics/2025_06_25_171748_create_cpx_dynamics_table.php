<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_dynamics', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键');
            $table->bigInteger('pid_id')->default(0)->comment('上级ID');
            $table->bigInteger('user_id')->default(0)->comment('会员ID');
            $table->bigInteger('liked')->default(0)->comment('点赞数量');
            $table->text('liked_uids')->comment('点赞用户ID');
            $table->text('title')->comment('标题');
            $table->text('content')->comment('内容');
            $table->text('forward_uids')->comment('转发人 user_id');
            $table->text('collect_uids')->comment('收藏人 user_ids');
            $table->bigInteger('look_num')->comment('浏览数量');
            $table->string('dynamics_currency',200)->comment('关联币种ID集合');
            $table->bigInteger('hot_topic_id')->comment('管理话题ID');
            $table->tinyInteger('look_auth')->default(1)->comment('查看权限\n1、公开，所有人将看到该动态\n2、关注我的，关注您的用户将看到该动态\n3、私密，将保存到您的笔记，仅本人可见');
            $table->tinyInteger('type')->default(1)->comment('动态类型，1观点，2文章');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
            $table->comment('动态表');
            $table->engine = 'InnoDB';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_dynamics');
    }
};
