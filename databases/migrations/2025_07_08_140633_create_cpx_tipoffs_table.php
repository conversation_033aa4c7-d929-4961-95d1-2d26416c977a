<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_tipoffs', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键');
            $table->bigInteger('dynamics_id')->default(0)->comment('动态ID');
            $table->bigInteger('user_id')->default(0)->comment('会员ID');
            $table->bigInteger('tipoffs_user_id')->default(0)->comment('被举报会员ID');
            $table->text('title')->comment('标题');
            $table->text('content')->comment('内容');
            $table->text('voucher')->comment('举报内容');
            $table->text('reason')->comment('举报理由');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->charset = 'utf8mb4';
            $table->collation = 'utf8mb4_unicode_ci';
            $table->comment('举报表');
            $table->engine = 'InnoDB';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_tipoffs');
    }
};
