<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateTradeMarginOrderTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trade_margin_order', function (Blueprint $table) {
            $table->id()->comment('自增主键');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->unsignedBigInteger('currency_id')->comment('交易对ID');
            $table->unsignedBigInteger('match_order_id')->comment('撮合订单ID');
            $table->tinyInteger('margin_type')->comment('保证金类型：1-逐仓，2-全仓');
            $table->decimal('leverage', 6, 2)->comment('杠杆倍数');
            $table->tinyInteger('position_side')->comment('仓位方向：1-多头，2-空头');
            $table->decimal('margin_amount', 30, 18)->comment('保证金数量');
            $table->tinyInteger('reduce_only')->default(0)->comment('减仓标识：0-否，1-是');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'currency_id'], 'idx_user_currency');
            $table->index('match_order_id', 'idx_match_order');
            $table->index(['margin_type', 'position_side'], 'idx_margin_position');
            $table->index('created_at', 'idx_created_at');

            $table->comment('杠杆订单表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trade_margin_order');
    }
} 