<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_agent', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('代理商用户ID');
            $table->bigInteger('parent_agent_id')->nullable()->comment('上级代理商ID（如果在用户表中上级不是代理则这里为null）');
            $table->decimal('spot_commission_rate', 8, 6)->default(0)->comment('现货返佣比例');
            $table->decimal('contract_commission_rate', 8, 6)->default(0)->comment('合约返佣比例');
            $table->decimal('trader_spot_commission_rate', 8, 6)->default(0)->comment('交易员现货返佣比例');
            $table->decimal('trader_contract_commission_rate', 8, 6)->default(0)->comment('交易员合约返佣比例');
            $table->tinyInteger('status')->default(1)->comment('状态:0=禁用,1=正常');
            $table->timestamps();

            // 索引
            $table->unique(['user_id']);
            $table->index(['parent_agent_id']);
            $table->index(['status']);

            $table->comment('代理商表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_agent');
    }
};
