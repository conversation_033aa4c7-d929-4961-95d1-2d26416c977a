<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户 Passkey 表迁移文件
 */

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user_passkey', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('user_id')->comment('用户ID');
            $table->string('credential_id', 512)->unique()->comment('凭证ID');
            $table->text('public_key')->comment('公钥');
            $table->unsignedInteger('counter')->default(0)->comment('计数器');
            $table->string('attestation_type', 32)->comment('认证类型');
            $table->string('device_info')->nullable()->comment('设备信息');
            $table->timestamps();

            // 索引
            $table->index(['user_id']);

            $table->comment('用户通行密钥 Passkey 表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user_passkey');
    }
};
