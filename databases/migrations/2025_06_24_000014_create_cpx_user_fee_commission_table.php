<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user_fee_commission', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('parent_id')->nullable()->comment('上级ID（如果存在上级，则存在此ID）冗余字段');
            $table->bigInteger('user_id')->comment('客户用户ID');
            $table->bigInteger('agent_id')->nullable()->comment('代理商ID（如果是代理商则存在此ID）冗余字段');
            $table->bigInteger('agent_client_id')->nullable()->comment('代理商直客ID（如果是代理商直客则存在此ID）冗余字段');
            $table->bigInteger('order_id')->comment('订单ID');
            $table->tinyInteger('trade_type')->comment('交易类型:1=现货,2=合约，3=现货杠杆');
            $table->decimal('trade_amount', 20, 8)->comment('交易金额');
            $table->decimal('fee_amount', 20, 8)->comment('手续费金额');
            $table->decimal('commission_rate', 8, 6)->comment('返佣比例');
            $table->decimal('commission_amount', 20, 8)->comment('返佣金额');
            $table->tinyInteger('status')->default(0)->comment('状态:0=待结算,1=已结算,2=已取消');
            $table->timestamp('trade_time')->comment('交易时间');
            $table->timestamp('settled_at')->nullable()->comment('结算时间');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamps();

            // 索引
            $table->index(['agent_id', 'status']);
            $table->index(['user_id']);
            $table->index(['order_id']);
            $table->index(['trade_type']);
            $table->index(['trade_time']);
            $table->index(['settled_at']);

            $table->comment('用户手续费返佣记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user_commission');
    }
};
