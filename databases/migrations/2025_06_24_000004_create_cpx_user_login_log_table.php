<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user_login_log', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('用户ID');
            $table->ipAddress('ip_address')->comment('登录IP地址');
            $table->string('user_agent', 500)->nullable()->comment('用户代理 请求头中的User-Agent');
            $table->string('device_type', 50)->nullable()->comment('设备类型:web=网页,ios=苹果,android=安卓,api=API');
            $table->string('device_name', 100)->nullable()->comment('设备名称');
            $table->string('device_id', 100)->nullable()->comment('设备唯一标识');
            $table->string('browser', 100)->nullable()->comment('浏览器');
            $table->string('os', 100)->nullable()->comment('操作系统');
            $table->string('location_country', 100)->nullable()->comment('登录国家');
            $table->string('location_province', 100)->nullable()->comment('登录省份');
            $table->string('location_city', 100)->nullable()->comment('登录城市');
            $table->tinyInteger('login_result')->default(1)->comment('登录结果:1=成功,2=失败');
            $table->string('failure_reason', 255)->nullable()->comment('失败原因');
            $table->timestamp('login_time')->comment('登录时间');
            $table->timestamps();

            // 索引
            $table->index(['user_id']);
            $table->index(['ip_address']);
            $table->index(['device_type']);
            $table->index(['login_result']);

            $table->comment('用户登录日志表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user_login_log');
    }
};
