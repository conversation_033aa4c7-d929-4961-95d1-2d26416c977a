<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateTradeMarginPositionTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trade_margin_position', function (Blueprint $table) {
            $table->id()->comment('自增主键');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->unsignedBigInteger('currency_id')->comment('交易对ID');
            $table->tinyInteger('side')->comment('仓位方向：1-多头，2-空头');
            $table->tinyInteger('margin_type')->comment('保证金类型：1-逐仓，2-全仓');
            $table->decimal('leverage', 6, 2)->comment('杠杆倍数');
            $table->decimal('quantity', 30, 18)->comment('仓位数量');
            $table->decimal('available_quantity', 30, 18)->comment('可用数量');
            $table->decimal('frozen_quantity', 30, 18)->comment('冻结数量');
            $table->decimal('entry_price', 30, 18)->comment('开仓均价');
            $table->decimal('margin_amount', 30, 18)->comment('保证金数量');
            $table->decimal('initial_margin', 30, 18)->comment('初始保证金');
            $table->decimal('maintenance_margin', 30, 18)->comment('维持保证金');
            $table->decimal('realized_pnl', 30, 18)->default(0)->comment('已实现盈亏');
            $table->tinyInteger('status')->default(1)->comment('仓位状态：1-正常，2-强平中，3-已平仓');
            $table->timestamps();

            // 索引
            $table->unique(['user_id', 'currency_id', 'side', 'margin_type'], 'uk_user_currency_side_type');
            $table->index(['user_id', 'margin_type'], 'idx_user_margin_type');
            $table->index('status', 'idx_status');
            $table->index('created_at', 'idx_created_at');

            $table->comment('杠杆仓位表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trade_margin_position');
    }
} 