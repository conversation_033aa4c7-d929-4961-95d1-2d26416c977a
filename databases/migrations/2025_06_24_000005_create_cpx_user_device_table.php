<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user_device', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('用户ID');
            $table->string('device_id', 100)->comment('设备唯一标识');
            $table->string('device_name', 100)->comment('设备名称');
            $table->string('device_type', 50)->comment('设备类型:web=网页,ios=苹果,android=安卓');
            $table->string('browser', 100)->nullable()->comment('浏览器 User-Agent');
            $table->string('os', 100)->nullable()->comment('操作系统');
            $table->string('app_version', 50)->nullable()->comment('应用版本');
            $table->ipAddress('first_login_ip')->comment('首次登录IP');
            $table->ipAddress('last_login_ip')->comment('最后登录IP');
            $table->timestamp('first_login_at')->comment('首次登录时间');
            $table->timestamp('last_login_at')->comment('最后登录时间');
            $table->string('token')->comment('设备登录令牌');
            $table->tinyInteger('status')->default(1)->comment('设备状态:1=正常,2=禁用');
            $table->tinyInteger('is_frequently_used')->default(0)->comment('是否经常使用:0=否,1=是');
            $table->string('push_token', 255)->nullable()->comment('推送通知令牌');
            $table->json('device_info')->nullable()->comment('设备详细信息');
            $table->timestamps();

            // 索引
            $table->unique(['user_id', 'device_id']);
            $table->index(['device_type']);
            $table->index(['status']);
            $table->index(['is_frequently_used']);

            $table->comment('用户设备管理表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user_device');
    }
};
