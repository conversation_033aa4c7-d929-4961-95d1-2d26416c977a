<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user_withdrawal_setting', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('用户ID');
            $table->tinyInteger('small_withdrawal_enabled')->default(0)->comment('是否启用小额免密提现:0=否,1=是');
            $table->decimal('small_withdrawal_limit', 20, 8)->nullable()->comment('小额免密提现限额');
            $table->tinyInteger('withdrawal_whitelist_enabled')->default(0)->comment('是否启用提现白名单:0=否,1=是');
            $table->json('withdrawal_whitelist')->nullable()->comment('提现白名单地址（网络类型、提现地址、备注）');
            $table->tinyInteger('withdrawal_revoke_enabled')->default(0)->comment('是否开启撤销提现（开启后1分钟内可撤销提现）:0=否,1=是');
            $table->json('preferred_networks')->nullable()->comment('偏好网络列表');
            $table->timestamps();

            // 索引
            $table->unique(['user_id']);

            $table->comment('用户提现设置表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user_withdrawal_setting');
    }
};
