<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user_relation', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('用户ID');
            $table->bigInteger('parent_id')->nullable()->comment('直接上级ID');
            $table->json('link')->nullable()->comment('完整上级链路，格式：[4,3,2,1]（从直接上级到顶级）');
            $table->timestamps();

            // 索引
            $table->unique(['user_id']);

            $table->comment('用户关系表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user_relation');
    }
};
