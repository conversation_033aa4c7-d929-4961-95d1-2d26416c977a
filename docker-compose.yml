version: '3'
services:
  hyperf:
    build:
      dockerfile: Dockerfile
      context: .
    restart: always
    environment:
      - "TIMEZONE=Asia/Shanghai"
      - "APP_NAME=algoquant"
    working_dir: "/opt/www"
    volumes:
      - ./:/opt/www
    entrypoint: ["php", "watch", "-c"]
  mysql:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: algoquant
  redis:
    image: redis:alpine
    restart: always
  frontend:
    build:
      context: ./web
      args:
        MINE_NODE_ENV: production
    restart: always