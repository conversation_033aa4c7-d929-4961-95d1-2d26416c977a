<?php

declare(strict_types=1);

namespace App\Database\Commands\Ast;

use Hyperf\Database\Commands\ModelData;
use Hyperf\Database\Commands\ModelOption;
use <PERSON>p<PERSON>arser\Comment\Doc;
use <PERSON>p<PERSON>ars<PERSON>\Node;
use <PERSON>pParser\Node\Stmt\Class_;
use <PERSON>p<PERSON><PERSON><PERSON>\Node\Stmt\ClassConst;
use Php<PERSON>arser\NodeVisitorAbstract;

class AddFieldConstantsVisitor extends NodeVisitorAbstract
{
    /**
     * @var string 字段常量前缀
     */
    protected string $fieldPrefix = 'FIELD_';

    protected string $className;

    public function __construct(protected ModelOption $option, protected ModelData $data)
    {
        $this->className = $data->getClass();
    }

    /**
     * @param Node $node
     * @return Node|Class_|null
     */
    public function leaveNode(Node $node): Node|Node\Stmt\Class_|null
    {
        if ($node instanceof Node\Stmt\Class_) {
            $columns = $this->getColumnsWithComment();
            $columns = array_reverse($columns);

            $existingConstants = [];
            foreach ($node->stmts as $stmt) {
                if ($stmt instanceof ClassConst) {
                    foreach ($stmt->consts as $const) {
                        $existingConstants[] = $const->name->toString();
                    }
                }
            }

            foreach ($columns as $column) {
                $constNodes = [];
                $constantName = $this->fieldPrefix . strtoupper($column['column_name']);

                if (in_array($constantName, $existingConstants)) {
                    continue;
                }

                $constNodes[] = new Node\Const_(
                    new Node\Name($constantName),
                    new Node\Scalar\String_($column['column_name']),
                    ['comments' => [new Doc("/**\n * {$column['column_comment']}\n */")]]
                );
                switch ($column['column_name']) {
                    case 'updated_at':
                        $column['column_comment'] = "更新时间";
                        break;
                    case 'created_at':
                        $column['column_comment'] = '创建时间';
                        break;
                }
                if (!empty($constNodes)) {

                    $docComment = "/**\n * {$column['column_comment']}";
                    if (!empty($comment)) {
                        $docComment .= "\n * {$comment}";
                    }
                    $docComment .= "\n */";

                    $constStmt = new ClassConst(
                        $constNodes,
                        Node\Stmt\Class_::MODIFIER_PUBLIC,
                        ['comments' => [new Doc($docComment)]]
                    );

                    array_unshift($node->stmts, $constStmt);
                }
            }
        }

        return $node;
    }

    protected function getColumnsWithComment(): array
    {
        // data->getColumns() 已包含列信息，包括注释
        return $this->data->getColumns();
    }
}