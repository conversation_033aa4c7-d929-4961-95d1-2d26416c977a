<?php

declare(strict_types=1);
/**
 * 说明：备份数据库
 *
 * @return
 * @param   type
 * <AUTHOR> (<EMAIL>)
 */

namespace App\Listener;

use Carbon\Carbon;
use Hyperf\Framework\Event\OnStart;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;

#[Listener]
class BootDatabaseBackupListener implements ListenerInterface
{
    public function listen(): array
    {
        return [
            OnStart::class,
        ];
    }

    /**
     * @param OnStart $event
     */
    public function process(object $event): void
    {
        try {
            $this->backupDatabase();
        } catch (\Throwable $e) {
            dump("【数据库备份】Database backup failed: " . $e->getMessage());
        }
    }

    /**
     * 备份数据库
     */
    protected function backupDatabase(): void
    {
        if (!in_array(env('APP_ENV'), ['dev', 'local'])) {
            return;
        }

        $config = config('databases-backup');

        // 检查是否启用备份
        if (!$config['enabled']) {
            return;
        }

        // 创建备份目录
        if (!is_dir($config['backup_path'])) {
            mkdir($config['backup_path'], 0755, true);
        }

        // 检查上次备份时间
        $lastBackupFile = $this->getLastBackupFile($config['backup_path']);
        if ($lastBackupFile) {
            $lastBackupTime = Carbon::createFromTimestamp(filemtime($lastBackupFile));
            $hoursSinceLastBackup = $lastBackupTime->diffInHours(Carbon::now());

            if ($hoursSinceLastBackup < $config['interval']) {
                dump("【数据库备份】Skipping backup, last backup was " . $hoursSinceLastBackup . " hours ago");
                return;
            }
        }

        // 获取数据库配置
        $dbConfig = config('databases.default');

        // 构建 mysqldump 命令
        $filename = $config['backup_path'] . '/backup_' . date('Y-m-d_H-i-s') . '.sql';
        $command = sprintf(
            'mysqldump -h%s -P%s -u%s -p%s %s',
            $dbConfig['host'],
            $dbConfig['port'],
            $dbConfig['username'],
            $dbConfig['password'],
            $dbConfig['database']
        );

        // 添加排除表
        if (!empty($config['exclude_tables'])) {
            foreach ($config['exclude_tables'] as $table) {
                $command .= " --ignore-table={$dbConfig['database']}.{$table}";
            }
        }

        // 执行备份
        $command .= " > {$filename}";
        $output = shell_exec($command);

        if ($output === null) {
            dump("【数据库备份】Database backup created: " . $filename);

            // 清理旧备份文件
            $this->cleanOldBackups($config['backup_path'], $config['max_files']);
        } else {
            throw new \RuntimeException('Database backup failed');
        }
    }

    /**
     * 获取最后一次备份文件
     */
    protected function getLastBackupFile(string $backupPath): ?string
    {
        $files = glob($backupPath . '/backup_*.sql');
        if (empty($files)) {
            return null;
        }

        rsort($files);
        return $files[0];
    }

    /**
     * 清理旧的备份文件
     */
    protected function cleanOldBackups(string $backupPath, int $maxFiles): void
    {
        $files = glob($backupPath . '/backup_*.sql');
        if (count($files) <= $maxFiles) {
            return;
        }

        rsort($files);
        $filesToDelete = array_slice($files, $maxFiles);

        foreach ($filesToDelete as $file) {
            unlink($file);
            dump("【数据库备份】Deleted old backup file: " . $file);
        }
    }
}
