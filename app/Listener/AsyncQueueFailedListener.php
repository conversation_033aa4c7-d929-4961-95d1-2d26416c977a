<?php

/**
 * AsyncQueueFailedListener.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/27
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Listener;

use Hyperf\AsyncQueue\Event\FailedHandle;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;

#[Listener]
class AsyncQueueFailedListener implements ListenerInterface
{
    public function listen(): array
    {
        return [
            FailedHandle::class
        ];
    }

    public function process(object $event): void
    {
    }
}