<?php

/**
 * AbstractMessage.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/1
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Model\WebsocketData;

use MessagePack\MessagePack;

/**
 * websocket消息模型抽象类
 */
abstract class AbstractMessage
{
    /**
     * 消息数据
     * @var array
     */
    public array $data;
    /**
     * 填充消息模型
     * @param array $message
     * @return void
     */
    abstract public function fill(array $message):void;

    /**
     * 获取消息内容
     * @return array
     */
    public function toArray(): array
    {
        return $this->data;
    }

    /**
     * 获取json字符串消息内容
     * @return string
     */
    public function toJsonString(): string
    {
        return json_encode($this->data);
    }

    /**
     * 获取二进制协议内容
     * @return string
     */
    public function toBinary(): string
    {
        return MessagePack::pack($this->data);
    }
}