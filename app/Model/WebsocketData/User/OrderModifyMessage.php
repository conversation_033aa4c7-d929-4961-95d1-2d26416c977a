<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 订单修改消息格式
 */

namespace App\Model\WebsocketData\User;

use App\Model\WebsocketData\AbstractMessage;

class OrderModifyMessage extends AbstractMessage
{
    public array $data = [
        'type' => 'user.order.modify',
        'market_type' => 0,
        'data' => [],
        'timestamp' => 0
    ];

    public function __construct(array $data)
    {
        $this->fill($data);
    }

    public function fill(array $message): void
    {
        $this->data['data'] = $message;
        $this->data['timestamp'] = time();
    }

    public function setMarkeType(int $value): void
    {
        $this->data['market_type'] = $value;
    }
} 