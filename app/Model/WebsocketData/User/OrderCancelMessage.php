<?php

/**
 * OrderCancelMessage.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/5
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Model\WebsocketData\User;

use App\Model\WebsocketData\AbstractMessage;

class OrderCancelMessage extends AbstractMessage
{
    public array $data = [
        'type' => 'user.order.cancel',
        'market_type' => 0,
        'data' => [],
        'timestamp' => 0
    ];

    public function __construct(array $data)
    {
        $this->fill($data);
    }

    public function fill(array $message): void
    {
        $this->data['data'] = $message;
        $this->data['timestamp'] = time();
    }

    public function setMarkeType(int $value)
    {
        $this->data['market_type'] = $value;
    }
}