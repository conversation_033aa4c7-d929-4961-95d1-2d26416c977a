<?php

/**
 * AccountChangedMessageFormat.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/4
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Model\WebsocketData\User;

use App\Model\WebsocketData\AbstractMessage;
use Carbon\Carbon;

/**
 * 用户账户余额变动
 * data = ['currency_id' => '','available' => '','frozen' => '','locked' => ''];
 */
class AccountChangedMessageFormat extends AbstractMessage
{
    public array $data = [
        'type' => 'user.balance.changed',
        'data' => [],
        'timestamp' => 0
    ];

    public function __construct(array $data)
    {
        $this->fill($data);
    }

    public function fill(array $message): void
    {
        $this->data['data'] = $message;
        $this->data['timestamp'] = Carbon::now()->getTimestamp();
    }
}