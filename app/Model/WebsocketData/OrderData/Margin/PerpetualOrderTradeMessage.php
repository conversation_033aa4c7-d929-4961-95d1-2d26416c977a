<?php

/**
 * PerpetualOrderTradeMessage.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/10
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Model\WebsocketData\OrderData\Margin;

use App\Model\WebsocketData\AbstractMessage;

/**
 * 永续合约订单成交消息格式化类
 */
class PerpetualOrderTradeMessage extends AbstractMessage
{
    public array $data = [
        'type' => 'user.order.trade',
        'market_type' => 5, // MarketType::MARGIN
        'data' => [],
        'timestamp' => 0
    ];

    public function __construct(array $data = [])
    {
        if (!empty($data)) {
            $this->fill($data);
        }
    }

    public function fill(array $message): void
    {
        $this->data['data'] = $this->formatTradeData($message);
        $this->data['timestamp'] = time();
    }

    /**
     * 格式化永续合约成交数据
     */
    private function formatTradeData(array $message): array
    {
        return [
            // 成交基础信息
            'trade_id' => (string)($message['trade_id'] ?? ''),
            'order_id' => (string)($message['order_id'] ?? ''),
            'match_order_id' => (string)($message['match_order_id'] ?? ''),
            'position_id' => (string)($message['position_id'] ?? ''),

            // 成交数据
            'filled_quantity' => (float)($message['filled_quantity'] ?? 0),
            'filled_price' => (float)($message['filled_price'] ?? 0),
            'trade_time' => (int)($message['trade_time'] ?? time()),

            // 关联仓位信息
            'position_quantity' => (float)($message['position_quantity'] ?? 0),
            'position_entry_price' => (float)($message['position_entry_price'] ?? 0),
            'position_margin' => (float)($message['position_margin'] ?? 0),
        ];
    }



    /**
     * 设置市场类型
     */
    public function setMarketType(int $value): void
    {
        $this->data['market_type'] = $value;
    }
}
