<?php

/**
 * PerpetualOrderCancelMessage.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/10
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Model\WebsocketData\OrderData\Margin;

use App\Model\WebsocketData\AbstractMessage;

/**
 * 永续合约订单取消消息格式化类
 */
class PerpetualOrderCancelMessage extends AbstractMessage
{
    public array $data = [
        'type' => 'user.order.cancel',
        'market_type' => 5, // MarketType::MARGIN
        'data' => [],
        'timestamp' => 0
    ];

    public function __construct(array $data = [])
    {
        if (!empty($data)) {
            $this->fill($data);
        }
    }

    public function fill(array $message): void
    {
        $this->data['data'] = $this->formatCancelData($message);
        $this->data['timestamp'] = time();
    }

    /**
     * 格式化永续合约订单取消数据
     */
    private function formatCancelData(array $message): array
    {
        return [
            // 基础订单信息
            'order_id' => (string)($message['order_id'] ?? ''),
            'match_order_id' => (string)($message['match_order_id'] ?? ''),

            // 订单基础数据
            'order_type' => (int)($message['order_type'] ?? 1),
            'price' => (float)($message['price'] ?? 0),
            'quantity' => (float)($message['quantity'] ?? 0),
            'filled_quantity' => (float)($message['filled_quantity'] ?? 0),
            'remaining_quantity' => (float)($message['remaining_quantity'] ?? 0),

            // 取消时间
            'cancel_time' => (int)($message['cancel_time'] ?? time()),
        ];
    }



    /**
     * 设置市场类型
     */
    public function setMarketType(int $value): void
    {
        $this->data['market_type'] = $value;
    }
}
