<?php

/**
 * PerpetualOrderFilledMessage.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/10
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Model\WebsocketData\OrderData\Margin;

use App\Model\WebsocketData\AbstractMessage;

/**
 * 永续合约订单完全成交消息格式化类
 */
class PerpetualOrderFilledMessage extends AbstractMessage
{
    public array $data = [
        'type' => 'user.order.filled',
        'market_type' => 5, // MarketType::MARGIN
        'data' => [],
        'timestamp' => 0
    ];

    public function __construct(array $data = [])
    {
        if (!empty($data)) {
            $this->fill($data);
        }
    }

    public function fill(array $message): void
    {
        $this->data['data'] = $this->formatFilledData($message);
        $this->data['timestamp'] = time();
    }

    /**
     * 格式化永续合约订单完全成交数据
     */
    private function formatFilledData(array $message): array
    {
        return [
            // 基础订单信息
            'order_id' => (string)($message['order_id'] ?? ''),
            'match_order_id' => (string)($message['match_order_id'] ?? ''),
            'position_id' => (string)($message['position_id'] ?? ''),

            // 订单完成信息
            'quantity' => (float)($message['quantity'] ?? 0),
            'filled_quantity' => (float)($message['filled_quantity'] ?? 0),
            'avg_price' => (float)($message['avg_price'] ?? 0),

            // 仓位信息（完成后的状态，特别是平仓后仓位为0的情况）
            'position_quantity' => (float)($message['position_quantity'] ?? 0),
            'position_entry_price' => (float)($message['position_entry_price'] ?? 0),
            'position_margin' => (float)($message['position_margin'] ?? 0),

            // 完成时间
            'filled_time' => (int)($message['filled_time'] ?? time()),
        ];
    }



    /**
     * 设置市场类型
     */
    public function setMarketType(int $value): void
    {
        $this->data['market_type'] = $value;
    }
}
