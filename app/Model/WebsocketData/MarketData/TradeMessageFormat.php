<?php

/**
 * TickerMessageFormat.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/1
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Model\WebsocketData\MarketData;

use App\Model\WebsocketData\AbstractMessage;

class TradeMessageFormat extends AbstractMessage
{
    public array $data = [
        'type' => 'trade',
        'makery_type' => 0,
        'data' => [],
        'count' => 0,
        'timestamp' => 0
    ];

    public function __construct(array $message = [])
    {
        $this->fill($message);
    }

    public function fill(array $message): void
    {
        $this->data = $message;
    }
}