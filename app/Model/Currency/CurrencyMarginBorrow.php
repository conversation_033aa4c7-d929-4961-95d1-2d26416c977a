<?php

declare(strict_types=1);

namespace App\Model\Currency;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $currency_id 
 * @property int $is_cross 1 全仓 2逐仓
 * @property array $base_level 基础币币种对应的档位数据{vip,day_rate,borrowlimit}
 * @property array $quote_level 计价币的借贷档位配置
 * @property int $is_base_borrow 基础币是否可借
 * @property int $is_quote_borrow 计价币是否可借
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class CurrencyMarginBorrow extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 1 全仓 2逐仓
     */
    public const FIELD_IS_CROSS = 'is_cross';
    /**
     * 基础币币种对应的档位数据{vip,day_rate,borrowlimit}
     */
    public const FIELD_BASE_LEVEL = 'base_level';
    /**
     * 计价币的借贷档位配置
     */
    public const FIELD_QUOTE_LEVEL = 'quote_level';
    /**
     * 基础币是否可借
     */
    public const FIELD_IS_BASE_BORROW = 'is_base_borrow';
    /**
     * 计价币是否可借
     */
    public const FIELD_IS_QUOTE_BORROW = 'is_quote_borrow';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'currency_margin_borrow';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'currency_id', 'is_cross', 'base_level', 'quote_level', 'is_base_borrow', 'is_quote_borrow', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'currency_id' => 'integer',
        'is_cross' => 'integer',
        'is_base_borrow' => 'integer',
        'is_quote_borrow' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'base_level' => 'array',
        'quote_level' => 'array'
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getIsCross() : int
    {
        return $this->is_cross;
    }
    public function setIsCross($value) : object
    {
        $this->is_cross = $value;
        return $this;
    }
    public function getBaseLevel() : array
    {
        return $this->base_level;
    }
    public function setBaseLevel($value) : object
    {
        $this->base_level = $value;
        return $this;
    }
    public function getQuoteLevel() : array
    {
        return $this->quote_level;
    }
    public function setQuoteLevel($value) : object
    {
        $this->quote_level = $value;
        return $this;
    }
    public function getIsBaseBorrow() : int
    {
        return $this->is_base_borrow;
    }
    public function setIsBaseBorrow($value) : object
    {
        $this->is_base_borrow = $value;
        return $this;
    }
    public function getIsQuoteBorrow() : int
    {
        return $this->is_quote_borrow;
    }
    public function setIsQuoteBorrow($value) : object
    {
        $this->is_quote_borrow = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 关联币种表
     */
    public function currency(): \Hyperf\Database\Model\Relations\BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }

    /**
     * 关联币种详情表
     */
    public function currencyMate(): \Hyperf\Database\Model\Relations\HasOneThrough
    {
        return $this->hasOneThrough(CurrencyMate::class, Currency::class, 'id', 'currency_id', 'currency_id', 'id');
    }
}
