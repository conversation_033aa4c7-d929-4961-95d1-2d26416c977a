<?php

declare(strict_types=1);

namespace App\Model\Currency;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $currency_id 交易标的id
 * @property array $mate_info 交易标的原数据
 * @property array $description 简介 多语言
 * @property string $logo 币种图标
 * @property array $cateIds 币种标签
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class CurrencyMate extends Model
{
    /**
     * 币种标签
     */
    public const FIELD_CATEIDS = 'cateIds';
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 交易标的id
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 交易标的原数据
     */
    public const FIELD_MATE_INFO = 'mate_info';
    /**
     * 简介 多语言
     */
    public const FIELD_DESCRIPTION = 'description';
    /**
     * 币种图标
     */
    public const FIELD_LOGO = 'logo';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'currency_mate';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'currency_id', 'mate_info', 'description', 'logo', 'cateIds', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'mate_info' => 'array',
        'description' => 'array',
        'cateIds' => 'array',
        'currency_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getMateInfo() : array
    {
        return $this->mate_info;
    }
    public function setMateInfo($value) : object
    {
        $this->mate_info = $value;
        return $this;
    }
    public function getDescription() : array
    {
        return $this->description;
    }
    public function setDescription($value) : object
    {
        $this->description = $value;
        return $this;
    }
    public function getLogo() : string
    {
        return $this->logo;
    }
    public function setLogo($value) : object
    {
        $this->logo = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
    public function getCateIds() : array
    {
        return $this->cateIds;
    }
    public function setCateIds($value) : object
    {
        $this->cateIds = $value;
        return $this;
    }
}
