<?php

declare(strict_types=1);

namespace App\Model\Currency;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property array $cate_name 栏目名称
 * @property array $cate_desc 栏目简介
 * @property int $sort 排序
 * @property int $status 是否启用
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class CurrencyCategory extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 栏目名称
     */
    public const FIELD_CATE_NAME = 'cate_name';
    /**
     * 栏目简介
     */
    public const FIELD_CATE_DESC = 'cate_desc';
    /**
     * 排序
     */
    public const FIELD_SORT = 'sort';
    /**
     * 是否启用
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'currency_category';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'cate_name', 'cate_desc', 'sort', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'cate_name' => 'array',
        'cate_desc' => 'array',
        'sort' => 'integer',
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCateName() : array
    {
        return $this->cate_name;
    }
    public function setCateName($value) : object
    {
        $this->cate_name = $value;
        return $this;
    }
    public function getCateDesc() : array
    {
        return $this->cate_desc;
    }
    public function setCateDesc($value) : object
    {
        $this->cate_desc = $value;
        return $this;
    }
    public function getSort() : int
    {
        return $this->sort;
    }
    public function setSort($value) : object
    {
        $this->sort = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
