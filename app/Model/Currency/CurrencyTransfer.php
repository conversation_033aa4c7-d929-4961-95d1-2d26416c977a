<?php

declare(strict_types=1);

namespace App\Model\Currency;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $currency_id 
 * @property int $transfer 是否可以划转
 * @property array $chains 支持的链列表
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class CurrencyTransfer extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 是否可以划转
     */
    public const FIELD_TRANSFER = 'transfer';
    /**
     * 支持的链列表	
     */
    public const FIELD_CHAINS = 'chains';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    public bool $incrementing = false;
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'currency_transfer';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'currency_id', 'transfer', 'chains', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'currency_id' => 'integer',
        'transfer' => 'integer',
        'chains' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getTransfer() : int
    {
        return $this->transfer;
    }
    public function setTransfer($value) : object
    {
        $this->transfer = $value;
        return $this;
    }
    public function getChains() : array
    {
        return $this->chains;
    }
    public function setChains($value) : object
    {
        $this->chains = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
