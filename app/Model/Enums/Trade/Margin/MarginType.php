<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 保证金类型枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum MarginType: int
{
    use EnumConstantsTrait;

    /**
     * 全仓
     */
    #[Message('trade.margin.margin_type.1')]
    case CROSS = 1;

    /**
     * 逐仓
     */
    #[Message('trade.margin.margin_type.2')]
    case ISOLATED = 2;
} 