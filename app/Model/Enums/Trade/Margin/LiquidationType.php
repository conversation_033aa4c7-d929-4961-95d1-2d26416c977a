<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 强平类型枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum LiquidationType: int
{
    use EnumConstantsTrait;

    /**
     * 保证金不足
     */
    #[Message('trade.margin.liquidation_type.1')]
    case MARGIN_INSUFFICIENT = 1;

    /**
     * ADL自动减仓
     */
    #[Message('trade.margin.liquidation_type.2')]
    case ADL_REDUCTION = 2;

    /**
     * 到期强平
     */
    #[Message('trade.margin.liquidation_type.3')]
    case EXPIRY_LIQUIDATION = 3;
} 