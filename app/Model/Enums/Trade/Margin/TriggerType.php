<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆委托订单触发类型枚举
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum TriggerType: int
{
    use EnumConstantsTrait;

    /**
     * 最新价触发
     */
    #[Message('trade.margin.trigger_type.1')]
    case LAST_PRICE = 1;

    /**
     * 标记价触发
     */
    #[Message('trade.margin.trigger_type.2')]
    case MARK_PRICE = 2;
} 