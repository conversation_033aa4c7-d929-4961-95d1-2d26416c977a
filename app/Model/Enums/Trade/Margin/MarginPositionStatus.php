<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆仓位状态枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum MarginPositionStatus: int
{
    use EnumConstantsTrait;

    /**
     * 持仓中
     */
    #[Message('trade.margin.margin_position_status.1')]
    case HOLDING = 1;

    /**
     * 已平仓
     */
    #[Message('trade.margin.margin_position_status.2')]
    case CLOSED = 2;

    /**
     * 强制平仓
     */
    #[Message('trade.margin.margin_position_status.3')]
    case LIQUIDATED = 3;
} 