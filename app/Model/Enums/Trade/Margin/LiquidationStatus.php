<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 强平记录状态枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum LiquidationStatus: int
{
    use EnumConstantsTrait;

    /**
     * 待执行
     */
    #[Message('trade.margin.liquidation_status.1')]
    case PENDING = 1;

    /**
     * 执行中
     */
    #[Message('trade.margin.liquidation_status.2')]
    case EXECUTING = 2;

    /**
     * 已完成
     */
    #[Message('trade.margin.liquidation_status.3')]
    case COMPLETED = 3;

    /**
     * 执行失败
     */
    #[Message('trade.margin.liquidation_status.4')]
    case FAILED = 4;

    /**
     * 已取消
     */
    #[Message('trade.margin.liquidation_status.5')]
    case CANCELLED = 5;
} 