<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆委托订单类型枚举
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ConditionalOrderType: int
{
    use EnumConstantsTrait;

    /**
     * 止盈单
     */
    #[Message('trade.margin.conditional_order_type.1')]
    case TAKE_PROFIT = 1;

    /**
     * 止损单
     */
    #[Message('trade.margin.conditional_order_type.2')]
    case STOP_LOSS = 2;
} 