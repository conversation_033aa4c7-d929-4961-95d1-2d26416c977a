<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆订单状态枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum MarginOrderStatus: int
{
    use EnumConstantsTrait;

    /**
     * 已取消
     */
    #[Message('trade.margin.margin_order_status.0')]
    case CANCELLED = 0;

    /**
     * 待成交
     */
    #[Message('trade.margin.margin_order_status.1')]
    case PENDING = 1;

    /**
     * 部分成交
     */
    #[Message('trade.margin.margin_order_status.2')]
    case PARTIAL_FILLED = 2;

    /**
     * 完全成交
     */
    #[Message('trade.margin.margin_order_status.3')]
    case FILLED = 3;
} 