<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 仓位方向枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum PositionSide: int
{
    use EnumConstantsTrait;

    /**
     * 做多
     */
    #[Message('trade.margin.position_side.1')]
    case LONG = 1;

    /**
     * 做空
     */
    #[Message('trade.margin.position_side.2')]
    case SHORT = 2;
} 