<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 利息记录状态枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum MarginInterestStatus: int
{
    use EnumConstantsTrait;

    /**
     * 待扣除
     */
    #[Message('trade.margin.margin_interest_status.1')]
    case PENDING = 1;

    /**
     * 已扣除
     */
    #[Message('trade.margin.margin_interest_status.2')]
    case DEDUCTED = 2;

    /**
     * 扣除失败
     */
    #[Message('trade.margin.margin_interest_status.3')]
    case FAILED = 3;

    /**
     * 已豁免
     */
    #[Message('trade.margin.margin_interest_status.4')]
    case WAIVED = 4;
} 