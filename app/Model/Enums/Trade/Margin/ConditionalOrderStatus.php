<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆委托订单状态枚举
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ConditionalOrderStatus: int
{
    use EnumConstantsTrait;

    /**
     * 等待触发
     */
    #[Message('trade.margin.conditional_order_status.1')]
    case WAITING = 1;

    /**
     * 已触发
     */
    #[Message('trade.margin.conditional_order_status.2')]
    case TRIGGERED = 2;

    /**
     * 已撤销
     */
    #[Message('trade.margin.conditional_order_status.3')]
    case CANCELLED = 3;

    /**
     * 触发失败
     */
    #[Message('trade.margin.conditional_order_status.4')]
    case FAILED = 4;
} 