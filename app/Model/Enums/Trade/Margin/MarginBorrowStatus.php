<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 借贷记录状态枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum MarginBorrowStatus: int
{
    use EnumConstantsTrait;

    /**
     * 借贷中
     */
    #[Message('trade.margin.margin_borrow_status.1')]
    case ACTIVE = 1;

    /**
     * 已还清
     */
    #[Message('trade.margin.margin_borrow_status.2')]
    case REPAID = 2;

    /**
     * 逾期
     */
    #[Message('trade.margin.margin_borrow_status.3')]
    case OVERDUE = 3;
} 