<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 触发来源枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum TriggerSource: int
{
    use EnumConstantsTrait;

    /**
     * 系统自动
     */
    #[Message('trade.margin.trigger_source.1')]
    case SYSTEM = 1;

    /**
     * 人工干预
     */
    #[Message('trade.margin.trigger_source.2')]
    case MANUAL = 2;
} 