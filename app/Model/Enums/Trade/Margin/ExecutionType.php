<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆委托订单执行类型枚举
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ExecutionType: int
{
    use EnumConstantsTrait;

    /**
     * 市价执行
     */
    #[Message('trade.margin.execution_type.1')]
    case MARKET = 1;

    /**
     * 限价执行
     */
    #[Message('trade.margin.execution_type.2')]
    case LIMIT = 2;
} 