<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 减仓标识枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Model\Enums\Trade\Margin;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ReduceOnly: int
{
    use EnumConstantsTrait;

    /**
     * 否
     */
    #[Message('trade.margin.reduce_only.0')]
    case FALSE = 0;

    /**
     * 是
     */
    #[Message('trade.margin.reduce_only.1')]
    case TRUE = 1;
} 