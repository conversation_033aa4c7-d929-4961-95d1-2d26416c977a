<?php

declare(strict_types=1);
/**
 * 委托单状态枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ConditionalOrderStatus: int
{
    use EnumConstantsTrait;

    #[Message("等待触发")]
    case WAITING = 1;

    #[Message("已触发")]
    case TRIGGERED = 2;

    #[Message("已执行")]
    case EXECUTED = 3;

    #[Message("已撤销")]
    case CANCELLED = 4;

    #[Message("已过期")]
    case EXPIRED = 5;

    #[Message("执行失败")]
    case FAILED = 6;

    public function getName(): string
    {
        return match($this) {
            self::WAITING => '等待触发',
            self::TRIGGERED => '已触发',
            self::EXECUTED => '已执行',
            self::CANCELLED => '已撤销',
            self::EXPIRED => '已过期',
            self::FAILED => '执行失败',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::WAITING => 'waiting',
            self::TRIGGERED => 'triggered',
            self::EXECUTED => 'executed',
            self::CANCELLED => 'cancelled',
            self::EXPIRED => 'expired',
            self::FAILED => 'failed',
        };
    }

    public function isActive(): bool
    {
        return $this === self::WAITING;
    }

    public function isFinished(): bool
    {
        return in_array($this, [self::EXECUTED, self::CANCELLED, self::EXPIRED, self::FAILED]);
    }
}
