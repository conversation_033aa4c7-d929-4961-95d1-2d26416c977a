<?php

declare(strict_types=1);
/**
 * 触发类型枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum TriggerType: int
{
    use EnumConstantsTrait;

    #[Message("标记价格")]
    case MARK_PRICE = 1;

    #[Message("最新价格")]
    case LAST_PRICE = 2;

    #[Message("指数价格")]
    case INDEX_PRICE = 3;

    public function getName(): string
    {
        return match($this) {
            self::MARK_PRICE => '标记价格',
            self::LAST_PRICE => '最新价格',
            self::INDEX_PRICE => '指数价格',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::MARK_PRICE => 'MARK_PRICE',
            self::LAST_PRICE => 'LAST_PRICE',
            self::INDEX_PRICE => 'INDEX_PRICE',
        };
    }
}