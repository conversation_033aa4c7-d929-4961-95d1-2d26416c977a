<?php

declare(strict_types=1);
/**
 * 仓位状态枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum PositionStatus: int
{
    use EnumConstantsTrait;

    #[Message("持仓中")]
    case HOLDING = 1;

    #[Message("已平仓")]
    case CLOSED = 2;

    #[Message("强制平仓")]
    case LIQUIDATED = 3;

    public function getName(): string
    {
        return match($this) {
            self::HOLDING => '持仓中',
            self::CLOSED => '已平仓',
            self::LIQUIDATED => '强制平仓',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::HOLDING => 'HOLDING',
            self::CLOSED => 'CLOSED',
            self::LIQUIDATED => 'LIQUIDATED',
        };
    }
}