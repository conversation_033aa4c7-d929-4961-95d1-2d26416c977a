<?php

declare(strict_types=1);
/**
 * 强平状态枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum LiquidationStatus: int
{
    use EnumConstantsTrait;

    #[Message("待执行")]
    case PENDING = 1;

    #[Message("执行中")]
    case EXECUTING = 2;

    #[Message("已完成")]
    case COMPLETED = 3;

    #[Message("执行失败")]
    case FAILED = 4;

    public function getName(): string
    {
        return match($this) {
            self::PENDING => '待执行',
            self::EXECUTING => '执行中',
            self::COMPLETED => '已完成',
            self::FAILED => '执行失败',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::PENDING => 'PENDING',
            self::EXECUTING => 'EXECUTING',
            self::COMPLETED => 'COMPLETED',
            self::FAILED => 'FAILED',
        };
    }
}