<?php

declare(strict_types=1);
/**
 * 合约订单类型枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ContractOrderType: int
{
    use EnumConstantsTrait;

    #[Message("限价单")]
    case LIMIT = 1;

    #[Message("市价单")]
    case MARKET = 2;

    #[Message("止盈止损单")]
    case STOP = 3;

    #[Message("止损市价单")]
    case STOP_MARKET = 4;

    #[Message("跟踪止损单")]
    case TRAILING_STOP_MARKET = 5;

    public function getName(): string
    {
        return match($this) {
            self::LIMIT => '限价单',
            self::MARKET => '市价单',
            self::STOP => '止盈止损单',
            self::STOP_MARKET => '止损市价单',
            self::TRAILING_STOP_MARKET => '跟踪止损单',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::LIMIT => 'LIMIT',
            self::MARKET => 'MARKET',
            self::STOP => 'STOP',
            self::STOP_MARKET => 'STOP_MARKET',
            self::TRAILING_STOP_MARKET => 'TRAILING_STOP_MARKET',
        };
    }
}