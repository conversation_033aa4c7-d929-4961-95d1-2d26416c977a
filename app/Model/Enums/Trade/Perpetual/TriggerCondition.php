<?php

declare(strict_types=1);
/**
 * 触发条件枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum TriggerCondition: int
{
    use EnumConstantsTrait;

    #[Message("大于等于")]
    case GREATER_THAN_OR_EQUAL = 1;

    #[Message("小于等于")]
    case LESS_THAN_OR_EQUAL = 2;

    public function getName(): string
    {
        return match($this) {
            self::GREATER_THAN_OR_EQUAL => '大于等于',
            self::LESS_THAN_OR_EQUAL => '小于等于',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::GREATER_THAN_OR_EQUAL => 'gte',
            self::LESS_THAN_OR_EQUAL => 'lte',
        };
    }

    public function getSymbol(): string
    {
        return match($this) {
            self::GREATER_THAN_OR_EQUAL => '>=',
            self::LESS_THAN_OR_EQUAL => '<=',
        };
    }

    public function checkCondition(string $currentPrice, string $triggerPrice): bool
    {
        return match($this) {
            self::GREATER_THAN_OR_EQUAL => bccomp($currentPrice, $triggerPrice, 8) >= 0,
            self::LESS_THAN_OR_EQUAL => bccomp($currentPrice, $triggerPrice, 8) <= 0,
        };
    }
}
