<?php

declare(strict_types=1);
/**
 * 强平类型枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum LiquidationType: int
{
    use EnumConstantsTrait;

    #[Message("保证金不足")]
    case INSUFFICIENT_MARGIN = 1;

    #[Message("ADL自动减仓")]
    case ADL = 2;

    #[Message("手动强平")]
    case MANUAL = 3;

    public function getName(): string
    {
        return match($this) {
            self::INSUFFICIENT_MARGIN => '保证金不足',
            self::ADL => 'ADL自动减仓',
            self::MANUAL => '手动强平',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::INSUFFICIENT_MARGIN => 'INSUFFICIENT_MARGIN',
            self::ADL => 'ADL',
            self::MANUAL => 'MANUAL',
        };
    }
}