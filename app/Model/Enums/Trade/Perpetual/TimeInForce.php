<?php

declare(strict_types=1);
/**
 * 订单有效期枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum TimeInForce: int
{
    use EnumConstantsTrait;

    #[Message("撤单前有效")]
    case GTC = 1;

    #[Message("立即成交或撤销")]
    case IOC = 2;

    #[Message("全部成交或撤销")]
    case FOK = 3;

    #[Message("当日有效")]
    case GTD = 4;

    public function getName(): string
    {
        return match($this) {
            self::GTC => '撤单前有效',
            self::IOC => '立即成交或撤销',
            self::FOK => '全部成交或撤销',
            self::GTD => '当日有效',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::GTC => 'GTC',
            self::IOC => 'IOC',
            self::FOK => 'FOK',
            self::GTD => 'GTD',
        };
    }
}