<?php

declare(strict_types=1);
/**
 * 合约方向枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ContractSide: int
{
    use EnumConstantsTrait;

    #[Message("买入开多")]
    case BUY_OPEN = 1;

    #[Message("卖出开空")]
    case SELL_OPEN = 2;

    #[Message("买入平空")]
    case BUY_CLOSE = 3;

    #[Message("卖出平多")]
    case SELL_CLOSE = 4;

    public function getName(): string
    {
        return match($this) {
            self::BUY_OPEN => '买入开多',
            self::SELL_OPEN => '卖出开空',
            self::BUY_CLOSE => '买入平空',
            self::SELL_CLOSE => '卖出平多',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::BUY_OPEN => 'BUY_OPEN',
            self::SELL_OPEN => 'SELL_OPEN',
            self::BUY_CLOSE => 'BUY_CLOSE',
            self::SELL_CLOSE => 'SELL_CLOSE',
        };
    }

    public function isLong(): bool
    {
        return in_array($this, [self::BUY_OPEN, self::BUY_CLOSE]);
    }

    public function isOpen(): bool
    {
        return in_array($this, [self::BUY_OPEN, self::SELL_OPEN]);
    }

    public function getPositionSide(): string
    {
        return match($this) {
            self::BUY_OPEN, self::SELL_CLOSE => 'LONG',
            self::SELL_OPEN, self::BUY_CLOSE => 'SHORT',
        };
    }
}