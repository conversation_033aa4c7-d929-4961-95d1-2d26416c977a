<?php

declare(strict_types=1);
/**
 * 保证金模式枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum MarginMode: int
{
    use EnumConstantsTrait;

    #[Message("全仓")]
    case CROSS = 1;

    #[Message("逐仓")]
    case ISOLATED = 2;

    public function getName(): string
    {
        return match($this) {
            self::CROSS => '全仓',
            self::ISOLATED => '逐仓',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::CROSS => 'cross',
            self::ISOLATED => 'isolated',
        };
    }
}