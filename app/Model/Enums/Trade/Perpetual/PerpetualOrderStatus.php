<?php

declare(strict_types=1);
/**
 * 永续合约订单状态枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum PerpetualOrderStatus: int
{
    use EnumConstantsTrait;

    #[Message("新建")]
    case NEW = 1;

    #[Message("部分成交")]
    case PARTIAL_FILLED = 2;

    #[Message("完全成交")]
    case FILLED = 3;

    #[Message("已撤销")]
    case CANCELLED = 4;

    #[Message("已拒绝")]
    case REJECTED = 5;

    #[Message("已过期")]
    case EXPIRED = 6;

    public function getName(): string
    {
        return match($this) {
            self::NEW => '新建',
            self::PARTIAL_FILLED => '部分成交',
            self::FILLED => '完全成交',
            self::CANCELLED => '已撤销',
            self::REJECTED => '已拒绝',
            self::EXPIRED => '已过期',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::NEW => 'NEW',
            self::PARTIAL_FILLED => 'PARTIAL_FILLED',
            self::FILLED => 'FILLED',
            self::CANCELLED => 'CANCELLED',
            self::REJECTED => 'REJECTED',
            self::EXPIRED => 'EXPIRED',
        };
    }

    /**
     * 是否为活跃状态（可以撤销）
     */
    public function isActive(): bool
    {
        return in_array($this, [self::NEW, self::PARTIAL_FILLED]);
    }

    /**
     * 是否为终结状态
     */
    public function isFinal(): bool
    {
        return in_array($this, [self::FILLED, self::CANCELLED, self::REJECTED, self::EXPIRED]);
    }
}
