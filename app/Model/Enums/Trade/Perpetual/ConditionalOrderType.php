<?php

declare(strict_types=1);
/**
 * 委托单类型枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ConditionalOrderType: int
{
    use EnumConstantsTrait;

    #[Message("止盈委托")]
    case TAKE_PROFIT = 1;

    #[Message("止损委托")]
    case STOP_LOSS = 2;

    #[Message("计划委托")]
    case SCHEDULED = 3;

    #[Message("追踪委托")]
    case TRAILING = 4;

    public function getName(): string
    {
        return match($this) {
            self::TAKE_PROFIT => '止盈委托',
            self::STOP_LOSS => '止损委托',
            self::SCHEDULED => '计划委托',
            self::TRAILING => '追踪委托',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::TAKE_PROFIT => 'take_profit',
            self::STOP_LOSS => 'stop_loss',
            self::SCHEDULED => 'scheduled',
            self::TRAILING => 'trailing',
        };
    }

    public function isStopOrder(): bool
    {
        return in_array($this, [self::TAKE_PROFIT, self::STOP_LOSS]);
    }
}
