<?php

declare(strict_types=1);
/**
 * 执行模式枚举
 */

namespace App\Model\Enums\Trade\Perpetual;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ExecutionMode: int
{
    use EnumConstantsTrait;

    #[Message("固定价格")]
    case FIXED_PRICE = 1;

    #[Message("回调浮动")]
    case CALLBACK_FLOAT = 2;

    public function getName(): string
    {
        return match($this) {
            self::FIXED_PRICE => '固定价格',
            self::CALLBACK_FLOAT => '回调浮动',
        };
    }

    public function getEnglishName(): string
    {
        return match($this) {
            self::FIXED_PRICE => 'fixed_price',
            self::CALLBACK_FLOAT => 'callback_float',
        };
    }

    public function isFixedPrice(): bool
    {
        return $this === self::FIXED_PRICE;
    }

    public function isCallbackFloat(): bool
    {
        return $this === self::CALLBACK_FLOAT;
    }
}
