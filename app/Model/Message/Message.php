<?php

declare(strict_types=1);

namespace App\Model\Message;

use App\Model\Article\Category;
use Hyperf\DbConnection\Model\Model as MineModel;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;



/**
 * 消息表模型
 *
 * @property int $category_id 分类ID
 * @property array $user_ids 发送用户ID
 * @property array $title 标题
 * @property array $content 内容
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class Message extends MineModel
{
    /**
     * 数据表名称
     *
     * @var string
     */
    protected ?string $table = 'cpx_message';

    /**
     * 允许批量赋值的属性
     *
     * @var array
     */
    protected array $fillable = [
        'category_id',
        'user_ids',
        'title',
        'content',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置
     *
     * @var array
     */
    protected array $casts = [
        'category_id' => 'integer',
        'user_ids' => 'array',
        'title' => 'array',
        'content' => 'array',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected array $hidden = [
    ];


    /**
     * ID
     */
    public const FIELD_ID = 'id';

    /**
     * 分类ID
     */
    public const FIELD_CATEGORY_ID = 'category_id';

    /**
     * 标题
     */
    public const FIELD_TITLE = 'title';

    /**
     * 内容
     */
    public const FIELD_CONTENT = 'content';

    /**
     * 用户ID
     */
    public const FIELD_USER_IDS = 'user_ids';



    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }



}