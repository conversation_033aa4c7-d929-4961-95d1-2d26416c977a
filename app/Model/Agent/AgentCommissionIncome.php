<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 代理商佣金收益记录模型
 */

namespace App\Model\Agent;

use App\Model\Agent\Enums\CommissionStatus;
use App\Model\Agent\Enums\TradeType;
use App\Model\User\User;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Builder;

/**
 * @property int $id 自增主键
 * @property int $agent_id 代理商ID
 * @property int|null $sub_agent_id 下级代理商ID（如果开单用户为下级代理）
 * @property int|null $agent_client_id 代理商直客ID（如果开单用户为直客）
 * @property int $trade_user_id 开单用户ID
 * @property int $order_id 订单ID
 * @property TradeType $trade_type 交易类型:1=现货,2=合约,3=现货杠杆
 * @property string $trade_amount 交易金额
 * @property string $fee_amount 手续费金额
 * @property string $commission_income_rate 返佣收益比例 = 代理商返佣比例 - 下级返佣比例
 * @property string $commission_income_amount 返佣收益金额
 * @property CommissionStatus $status 状态:0=待结算,1=已结算,2=已取消
 * @property Carbon $trade_time 交易时间
 * @property Carbon|null $settled_at 结算时间
 * @property string|null $remark 备注
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
final class AgentCommissionIncome extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 代理商ID
     */
    public const FIELD_AGENT_ID = 'agent_id';
    /**
     * 下级代理商ID（如果开单用户为下级代理）
     */
    public const FIELD_SUB_AGENT_ID = 'sub_agent_id';
    /**
     * 代理商直客ID（如果开单用户为直客）
     */
    public const FIELD_AGENT_CLIENT_ID = 'agent_client_id';
    /**
     * 开单用户ID
     */
    public const FIELD_TRADE_USER_ID = 'trade_user_id';
    /**
     * 订单ID
     */
    public const FIELD_ORDER_ID = 'order_id';
    /**
     * 交易类型:1=现货,2=合约,3=现货杠杆
     */
    public const FIELD_TRADE_TYPE = 'trade_type';
    /**
     * 交易金额
     */
    public const FIELD_TRADE_AMOUNT = 'trade_amount';
    /**
     * 手续费金额
     */
    public const FIELD_FEE_AMOUNT = 'fee_amount';
    /**
     * 返佣收益比例 = 代理商返佣比例 - 下级返佣比例
     */
    public const FIELD_COMMISSION_INCOME_RATE = 'commission_income_rate';
    /**
     * 返佣收益金额
     */
    public const FIELD_COMMISSION_INCOME_AMOUNT = 'commission_income_amount';
    /**
     * 状态:0=待结算,1=已结算,2=已取消
     */
    public const FIELD_STATUS = 'status';
    /**
     * 交易时间
     */
    public const FIELD_TRADE_TIME = 'trade_time';
    /**
     * 结算时间
     */
    public const FIELD_SETTLED_AT = 'settled_at';
    /**
     * 备注
     */
    public const FIELD_REMARK = 'remark';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_agent_commission_income';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'agent_id', // 代理商ID
        'sub_agent_id', // 下级代理商ID（如果开单用户为下级代理）
        'agent_client_id', // 代理商直客ID（如果开单用户为直客）
        'trade_user_id', // 开单用户ID
        'order_id', // 订单ID
        'trade_type', // 交易类型:1=现货,2=合约,3=现货杠杆
        'trade_amount', // 交易金额
        'fee_amount', // 手续费金额
        'commission_income_rate', // 返佣收益比例 = 代理商返佣比例 - 下级返佣比例
        'commission_income_amount', // 返佣收益金额
        'status', // 状态:0=待结算,1=已结算,2=已取消
        'trade_time', // 交易时间
        'settled_at', // 结算时间
        'remark', // 备注
        'created_at', // 创建时间
        'updated_at' // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'agent_id' => 'integer', // 代理商ID
        'sub_agent_id' => 'integer', // 下级代理商ID（如果开单用户为下级代理）
        'agent_client_id' => 'integer', // 代理商直客ID（如果开单用户为直客）
        'trade_user_id' => 'integer', // 开单用户ID
        'order_id' => 'integer', // 订单ID
        'trade_type' => TradeType::class, // 交易类型:1=现货,2=合约,3=现货杠杆
        'trade_amount' => 'string', // 交易金额
        'fee_amount' => 'string', // 手续费金额
        'commission_income_rate' => 'string', // 返佣收益比例 = 代理商返佣比例 - 下级返佣比例
        'commission_income_amount' => 'string', // 返佣收益金额
        'status' => CommissionStatus::class, // 状态:0=待结算,1=已结算,2=已取消
        'trade_time' => 'datetime', // 交易时间
        'settled_at' => 'datetime', // 结算时间
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime' // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取代理商ID
     */
    public function getAgentId(): int
    {
        return $this->agent_id;
    }

    /**
     * 设置代理商ID
     *
     * @param int $value 代理商ID值
     */
    public function setAgentId($value): static
    {
        $this->agent_id = $value;
        return $this;
    }

    /**
     * 获取下级代理商ID
     */
    public function getSubAgentId(): ?int
    {
        return $this->sub_agent_id;
    }

    /**
     * 设置下级代理商ID
     *
     * @param int|null $value 下级代理商ID值
     */
    public function setSubAgentId($value): static
    {
        $this->sub_agent_id = $value;
        return $this;
    }

    /**
     * 获取代理商直客ID
     */
    public function getAgentClientId(): ?int
    {
        return $this->agent_client_id;
    }

    /**
     * 设置代理商直客ID
     *
     * @param int|null $value 代理商直客ID值
     */
    public function setAgentClientId($value): static
    {
        $this->agent_client_id = $value;
        return $this;
    }

    /**
     * 获取开单用户ID
     */
    public function getTradeUserId(): int
    {
        return $this->trade_user_id;
    }

    /**
     * 设置开单用户ID
     *
     * @param int $value 开单用户ID值
     */
    public function setTradeUserId($value): static
    {
        $this->trade_user_id = $value;
        return $this;
    }

    /**
     * 获取订单ID
     */
    public function getOrderId(): int
    {
        return $this->order_id;
    }

    /**
     * 设置订单ID
     *
     * @param int $value 订单ID值
     */
    public function setOrderId($value): static
    {
        $this->order_id = $value;
        return $this;
    }

    /**
     * 获取交易类型
     */
    public function getTradeType(): TradeType
    {
        return $this->trade_type;
    }

    /**
     * 设置交易类型
     *
     * @param TradeType|int $value 交易类型值
     */
    public function setTradeType($value): static
    {
        $this->trade_type = $value;
        return $this;
    }

    /**
     * 获取交易金额
     */
    public function getTradeAmount(): string
    {
        return $this->trade_amount;
    }

    /**
     * 设置交易金额
     *
     * @param string $value 交易金额值
     */
    public function setTradeAmount($value): static
    {
        $this->trade_amount = $value;
        return $this;
    }

    /**
     * 获取手续费金额
     */
    public function getFeeAmount(): string
    {
        return $this->fee_amount;
    }

    /**
     * 设置手续费金额
     *
     * @param string $value 手续费金额值
     */
    public function setFeeAmount($value): static
    {
        $this->fee_amount = $value;
        return $this;
    }

    /**
     * 获取返佣收益比例
     */
    public function getCommissionIncomeRate(): string
    {
        return $this->commission_income_rate;
    }

    /**
     * 设置返佣收益比例
     *
     * @param string $value 返佣收益比例值
     */
    public function setCommissionIncomeRate($value): static
    {
        $this->commission_income_rate = $value;
        return $this;
    }

    /**
     * 获取返佣收益金额
     */
    public function getCommissionIncomeAmount(): string
    {
        return $this->commission_income_amount;
    }

    /**
     * 设置返佣收益金额
     *
     * @param string $value 返佣收益金额值
     */
    public function setCommissionIncomeAmount($value): static
    {
        $this->commission_income_amount = $value;
        return $this;
    }

    /**
     * 获取状态
     */
    public function getStatus(): CommissionStatus
    {
        return $this->status;
    }

    /**
     * 设置状态
     *
     * @param CommissionStatus|int $value 状态值
     */
    public function setStatus($value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取交易时间
     */
    public function getTradeTime(): Carbon
    {
        return $this->trade_time;
    }

    /**
     * 设置交易时间
     *
     * @param Carbon|string $value 交易时间值
     */
    public function setTradeTime($value): static
    {
        $this->trade_time = $value;
        return $this;
    }

    /**
     * 获取结算时间
     */
    public function getSettledAt(): ?Carbon
    {
        return $this->settled_at;
    }

    /**
     * 设置结算时间
     *
     * @param Carbon|string|null $value 结算时间值
     */
    public function setSettledAt($value): static
    {
        $this->settled_at = $value;
        return $this;
    }

    /**
     * 获取备注
     */
    public function getRemark(): ?string
    {
        return $this->remark;
    }

    /**
     * 设置备注
     *
     * @param string|null $value 备注内容
     */
    public function setRemark($value): static
    {
        $this->remark = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param Carbon|string|null $value 创建时间值
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param Carbon|string|null $value 更新时间值
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 关联代理商
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class, 'agent_id', 'id');
    }

    /**
     * 关联下级代理商
     */
    public function subAgent()
    {
        return $this->belongsTo(Agent::class, 'sub_agent_id', 'id');
    }

    /**
     * 关联代理商直客
     */
    public function agentClient()
    {
        return $this->belongsTo(AgentClient::class, 'agent_client_id', 'id');
    }

    /**
     * 关联交易用户
     */
    public function tradeUser()
    {
        return $this->belongsTo(User::class, 'trade_user_id', 'id');
    }

    /**
     * 通过 sub_agent_id agent_client_id 筛选交易用户是下级代理商或直客 作用域
     * @param Builder $query
     * @param int $type 1:直客 2:子代理
     */
    public function scopeTradeUserType(Builder $query, $type)
    {
        if ($type == 1) {
            return $query->whereNotNull(self::FIELD_AGENT_CLIENT_ID)->whereNull(self::FIELD_SUB_AGENT_ID);
        } else if ($type == 2) {
            return $query->whereNotNull(self::FIELD_SUB_AGENT_ID);
        }
        return $query;
    }

    public function scopeTradeTimeBetween(Builder $query, $start, $end): Builder
    {
        return $query->whereBetween(self::FIELD_TRADE_TIME, [$start, $end]);
    }
}
