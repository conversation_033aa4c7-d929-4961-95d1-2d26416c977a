<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 代理商佣金收益状态枚举
 */

namespace App\Model\Agent\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum CommissionStatus: int
{
    use EnumConstantsTrait;

    /**
     * 待结算
     */
    #[Message('agent.enums.commission_status.0')]
    case PENDING = 0;

    /**
     * 已结算
     */
    #[Message('agent.enums.commission_status.1')]
    case SETTLED = 1;

    /**
     * 已取消
     */
    #[Message('agent.enums.commission_status.2')]
    case CANCELLED = 2;
}
