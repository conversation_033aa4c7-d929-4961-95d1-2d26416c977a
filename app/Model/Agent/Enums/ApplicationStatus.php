<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 代理商申请状态枚举
 */

namespace App\Model\Agent\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ApplicationStatus: int
{
    use EnumConstantsTrait;

    /**
     * 待审核
     */
    #[Message('agent.enums.application_status.0')]
    case PENDING = 0;

    /**
     * 已通过
     */
    #[Message('agent.enums.application_status.1')]
    case APPROVED = 1;

    /**
     * 已拒绝
     */
    #[Message('agent.enums.application_status.2')]
    case REJECTED = 2;
}
