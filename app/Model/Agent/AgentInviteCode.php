<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 代理商邀请码模型
 */

namespace App\Model\Agent;

use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int $user_id 用户ID
 * @property int $agent_id 代理商ID
 * @property string $invite_code 邀请码
 * @property float $spot_commission_rate 直客现货返佣比例
 * @property float $contract_commission_rate 直客合约返佣比例
 * @property float $trader_spot_commission_rate 直客交易员现货返佣比例
 * @property float $trader_contract_commission_rate 直客交易员合约返佣比例
 * @property int $is_default 是否默认:0=否,1=是
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class AgentInviteCode extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 代理商ID
     */
    public const FIELD_AGENT_ID = 'agent_id';
    /**
     * 邀请码
     */
    public const FIELD_INVITE_CODE = 'invite_code';
    /**
     * 直客现货返佣比例
     */
    public const FIELD_SPOT_COMMISSION_RATE = 'spot_commission_rate';
    /**
     * 直客合约返佣比例
     */
    public const FIELD_CONTRACT_COMMISSION_RATE = 'contract_commission_rate';
    /**
     * 直客交易员现货返佣比例
     */
    public const FIELD_TRADER_SPOT_COMMISSION_RATE = 'trader_spot_commission_rate';
    /**
     * 直客交易员合约返佣比例
     */
    public const FIELD_TRADER_CONTRACT_COMMISSION_RATE = 'trader_contract_commission_rate';
    /**
     * 是否默认:0=否,1=是
     */
    public const FIELD_IS_DEFAULT = 'is_default';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_agent_invite_code';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'user_id', // 用户ID
        'agent_id', // 代理商ID
        'invite_code', // 邀请码
        'spot_commission_rate', // 直客现货返佣比例
        'contract_commission_rate', // 直客合约返佣比例
        'trader_spot_commission_rate', // 直客交易员现货返佣比例
        'trader_contract_commission_rate', // 直客交易员合约返佣比例
        'is_default', // 是否默认:0=否,1=是
        'created_at', // 创建时间
        'updated_at' // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'user_id' => 'integer', // 用户ID
        'agent_id' => 'integer', // 代理商ID
        'spot_commission_rate' => 'float', // 直客现货返佣比例
        'contract_commission_rate' => 'float', // 直客合约返佣比例
        'trader_spot_commission_rate' => 'float', // 直客交易员现货返佣比例
        'trader_contract_commission_rate' => 'float', // 直客交易员合约返佣比例
        'is_default' => 'integer', // 是否默认:0=否,1=是
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime' // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取代理商ID
     */
    public function getAgentId(): int
    {
        return $this->agent_id;
    }

    /**
     * 设置代理商ID
     *
     * @param int $value 代理商ID值
     */
    public function setAgentId($value): static
    {
        $this->agent_id = $value;
        return $this;
    }

    /**
     * 获取邀请码
     */
    public function getInviteCode(): string
    {
        return $this->invite_code;
    }

    /**
     * 设置邀请码
     *
     * @param string $value 邀请码值
     */
    public function setInviteCode($value): static
    {
        $this->invite_code = $value;
        return $this;
    }

    /**
     * 获取直客现货返佣比例
     */
    public function getSpotCommissionRate(): float
    {
        return $this->spot_commission_rate;
    }

    /**
     * 设置直客现货返佣比例
     *
     * @param float $value 返佣比例值
     */
    public function setSpotCommissionRate($value): static
    {
        $this->spot_commission_rate = $value;
        return $this;
    }

    /**
     * 获取直客合约返佣比例
     */
    public function getContractCommissionRate(): float
    {
        return $this->contract_commission_rate;
    }

    /**
     * 设置直客合约返佣比例
     *
     * @param float $value 返佣比例值
     */
    public function setContractCommissionRate($value): static
    {
        $this->contract_commission_rate = $value;
        return $this;
    }

    /**
     * 获取直客交易员现货返佣比例
     */
    public function getTraderSpotCommissionRate(): float
    {
        return $this->trader_spot_commission_rate;
    }

    /**
     * 设置直客交易员现货返佣比例
     *
     * @param float $value 返佣比例值
     */
    public function setTraderSpotCommissionRate($value): static
    {
        $this->trader_spot_commission_rate = $value;
        return $this;
    }

    /**
     * 获取直客交易员合约返佣比例
     */
    public function getTraderContractCommissionRate(): float
    {
        return $this->trader_contract_commission_rate;
    }

    /**
     * 设置直客交易员合约返佣比例
     *
     * @param float $value 返佣比例值
     */
    public function setTraderContractCommissionRate($value): static
    {
        $this->trader_contract_commission_rate = $value;
        return $this;
    }

    /**
     * 获取是否默认
     */
    public function getIsDefault(): int
    {
        return $this->is_default;
    }

    /**
     * 设置是否默认
     *
     * @param int $value 是否默认值
     */
    public function setIsDefault($value): static
    {
        $this->is_default = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
