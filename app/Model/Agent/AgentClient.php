<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 代理商直客模型
 */

namespace App\Model\Agent;

use App\Model\User\User;
use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int $agent_id 代理商ID
 * @property int $user_id 客户用户ID
 * @property float $spot_commission_rate 现货返佣比例
 * @property float $contract_commission_rate 合约返佣比例
 * @property float $trader_spot_commission_rate 交易员现货返佣比例
 * @property float $trader_contract_commission_rate 交易员合约返佣比例
 * @property string|null $remark 备注
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class AgentClient extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 代理商ID
     */
    public const FIELD_AGENT_ID = 'agent_id';
    /**
     * 客户用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 现货返佣比例
     */
    public const FIELD_SPOT_COMMISSION_RATE = 'spot_commission_rate';
    /**
     * 合约返佣比例
     */
    public const FIELD_CONTRACT_COMMISSION_RATE = 'contract_commission_rate';
    /**
     * 交易员现货返佣比例
     */
    public const FIELD_TRADER_SPOT_COMMISSION_RATE = 'trader_spot_commission_rate';
    /**
     * 交易员合约返佣比例
     */
    public const FIELD_TRADER_CONTRACT_COMMISSION_RATE = 'trader_contract_commission_rate';
    /**
     * 备注
     */
    public const FIELD_REMARK = 'remark';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_agent_client';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'agent_id', // 代理商ID
        'user_id', // 客户用户ID
        'spot_commission_rate', // 现货返佣比例
        'contract_commission_rate', // 合约返佣比例
        'trader_spot_commission_rate', // 交易员现货返佣比例
        'trader_contract_commission_rate', // 交易员合约返佣比例
        'remark', // 备注
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'agent_id' => 'integer', // 代理商ID
        'user_id' => 'integer', // 客户用户ID
        'spot_commission_rate' => 'decimal:6', // 现货返佣比例
        'contract_commission_rate' => 'decimal:6', // 合约返佣比例
        'trader_spot_commission_rate' => 'decimal:6', // 交易员现货返佣比例
        'trader_contract_commission_rate' => 'decimal:6', // 交易员合约返佣比例
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取代理商ID
     */
    public function getAgentId(): int
    {
        return $this->agent_id;
    }

    /**
     * 设置代理商ID
     *
     * @param int $value 代理商ID值
     */
    public function setAgentId($value): static
    {
        $this->agent_id = $value;
        return $this;
    }

    /**
     * 获取客户用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置客户用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取现货返佣比例
     */
    public function getSpotCommissionRate(): float
    {
        return $this->spot_commission_rate;
    }

    /**
     * 设置现货返佣比例
     *
     * @param float $value 现货返佣比例值
     */
    public function setSpotCommissionRate($value): static
    {
        $this->spot_commission_rate = $value;
        return $this;
    }

    /**
     * 获取合约返佣比例
     */
    public function getContractCommissionRate(): float
    {
        return $this->contract_commission_rate;
    }

    /**
     * 设置合约返佣比例
     *
     * @param float $value 合约返佣比例值
     */
    public function setContractCommissionRate($value): static
    {
        $this->contract_commission_rate = $value;
        return $this;
    }

    /**
     * 获取交易员现货返佣比例
     */
    public function getTraderSpotCommissionRate(): float
    {
        return $this->trader_spot_commission_rate;
    }

    /**
     * 设置交易员现货返佣比例
     *
     * @param float $value 交易员现货返佣比例值
     */
    public function setTraderSpotCommissionRate($value): static
    {
        $this->trader_spot_commission_rate = $value;
        return $this;
    }

    /**
     * 获取交易员合约返佣比例
     */
    public function getTraderContractCommissionRate(): float
    {
        return $this->trader_contract_commission_rate;
    }

    /**
     * 设置交易员合约返佣比例
     *
     * @param float $value 交易员合约返佣比例值
     */
    public function setTraderContractCommissionRate($value): static
    {
        $this->trader_contract_commission_rate = $value;
        return $this;
    }

    /**
     * 获取备注
     */
    public function getRemark(): ?string
    {
        return $this->remark;
    }

    /**
     * 设置备注
     *
     * @param string|null $value 备注内容
     */
    public function setRemark($value): static
    {
        $this->remark = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 获取关联的代理商模型
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class, self::FIELD_AGENT_ID, Agent::FIELD_ID);
    }

    /**
     * 获取关联的用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
