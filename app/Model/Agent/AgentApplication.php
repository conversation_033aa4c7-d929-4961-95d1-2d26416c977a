<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 代理商申请模型
 */

namespace App\Model\Agent;

use App\Model\Agent\Enums\ApplicationStatus;
use App\Model\User\User;
use App\QueryBuilder\Model;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Filesystem\FilesystemFactory;

/**
 * @property int $id 自增主键
 * @property int $user_id 申请用户ID
 * @property string|null $company_name 公司名称
 * @property string $contact_person 联系人
 * @property string $contact_phone 联系电话
 * @property string $contact_email 联系邮箱
 * @property string|null $business_license 营业执照
 * @property string|null $business_description 业务描述
 * @property string|null $marketing_plan 营销计划
 * @property float $expected_monthly_volume 预期月交易量
 * @property int $expected_user_count 预期用户数量
 * @property string $contact_method 联系方法 Telegram、WhatsApp、KaKaoTalk、LINE、Wechat
 * @property string $contact_account 联系账号
 * @property string|null $additional_info 附加信息
 * @property ApplicationStatus $status 申请状态:0=待审核,1=已通过,2=已拒绝
 * @property string|null $rejection_reason 拒绝原因
 * @property \Carbon\Carbon $submitted_at 提交时间
 * @property \Carbon\Carbon|null $reviewed_at 审核时间
 * @property int|null $reviewer_id 审核员ID
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class AgentApplication extends Model
{
    #[Inject]
    protected FilesystemFactory $filesystemFactory;

    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 申请用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 公司名称
     */
    public const FIELD_COMPANY_NAME = 'company_name';
    /**
     * 联系人
     */
    public const FIELD_CONTACT_PERSON = 'contact_person';
    /**
     * 联系电话
     */
    public const FIELD_CONTACT_PHONE = 'contact_phone';
    /**
     * 联系邮箱
     */
    public const FIELD_CONTACT_EMAIL = 'contact_email';
    /**
     * 营业执照
     */
    public const FIELD_BUSINESS_LICENSE = 'business_license';
    /**
     * 业务描述
     */
    public const FIELD_BUSINESS_DESCRIPTION = 'business_description';
    /**
     * 营销计划
     */
    public const FIELD_MARKETING_PLAN = 'marketing_plan';
    /**
     * 预期月交易量
     */
    public const FIELD_EXPECTED_MONTHLY_VOLUME = 'expected_monthly_volume';
    /**
     * 预期用户数量
     */
    public const FIELD_EXPECTED_USER_COUNT = 'expected_user_count';
    /**
     * 联系方法
     */
    public const FIELD_CONTACT_METHOD = 'contact_method';
    /**
     * 联系账号
     */
    public const FIELD_CONTACT_ACCOUNT = 'contact_account';
    /**
     * 附加信息
     */
    public const FIELD_ADDITIONAL_INFO = 'additional_info';
    /**
     * 申请状态
     */
    public const FIELD_STATUS = 'status';
    /**
     * 拒绝原因
     */
    public const FIELD_REJECTION_REASON = 'rejection_reason';
    /**
     * 提交时间
     */
    public const FIELD_SUBMITTED_AT = 'submitted_at';
    /**
     * 审核时间
     */
    public const FIELD_REVIEWED_AT = 'reviewed_at';
    /**
     * 审核员ID
     */
    public const FIELD_REVIEWER_ID = 'reviewer_id';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_agent_application';

    protected array $appends = [
        'business_license_url',
    ];

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'user_id', // 申请用户ID
        'company_name', // 公司名称
        'contact_person', // 联系人
        'contact_phone', // 联系电话
        'contact_email', // 联系邮箱
        'business_license', // 营业执照
        'business_description', // 业务描述
        'marketing_plan', // 营销计划
        'expected_monthly_volume', // 预期月交易量
        'expected_user_count', // 预期用户数量
        'contact_method', // 联系方法
        'contact_account', // 联系账号
        'additional_info', // 附加信息
        'status', // 申请状态
        'rejection_reason', // 拒绝原因
        'submitted_at', // 提交时间
        'reviewed_at', // 审核时间
        'reviewer_id', // 审核员ID
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'user_id' => 'integer', // 申请用户ID
        'expected_monthly_volume' => 'decimal:8', // 预期月交易量
        'expected_user_count' => 'integer', // 预期用户数量
        'status' => ApplicationStatus::class, // 申请状态
        'submitted_at' => 'datetime', // 提交时间
        'reviewed_at' => 'datetime', // 审核时间
        'reviewer_id' => 'integer', // 审核员ID
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 处理 business_license url
     */
    public function getBusinessLicenseUrlAttribute(): string
    {
        if (empty($this->business_license) || str_starts_with($this->business_license, 'http')) {
            return $this->business_license ?? '';
        }
        $filesystem = $this->filesystemFactory->get(config('file.default', 'local'));
        return $filesystem->publicUrl($this->business_license);
    }

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取申请用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置申请用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取公司名称
     */
    public function getCompanyName(): ?string
    {
        return $this->company_name;
    }

    /**
     * 设置公司名称
     *
     * @param string|null $value 公司名称
     */
    public function setCompanyName($value): static
    {
        $this->company_name = $value;
        return $this;
    }

    /**
     * 获取联系人
     */
    public function getContactPerson(): string
    {
        return $this->contact_person;
    }

    /**
     * 设置联系人
     *
     * @param string $value 联系人姓名
     */
    public function setContactPerson($value): static
    {
        $this->contact_person = $value;
        return $this;
    }

    /**
     * 获取联系电话
     */
    public function getContactPhone(): string
    {
        return $this->contact_phone;
    }

    /**
     * 设置联系电话
     *
     * @param string $value 联系电话号码
     */
    public function setContactPhone($value): static
    {
        $this->contact_phone = $value;
        return $this;
    }

    /**
     * 获取联系邮箱
     */
    public function getContactEmail(): string
    {
        return $this->contact_email;
    }

    /**
     * 设置联系邮箱
     *
     * @param string $value 联系邮箱地址
     */
    public function setContactEmail($value): static
    {
        $this->contact_email = $value;
        return $this;
    }

    /**
     * 获取营业执照
     */
    public function getBusinessLicense(): ?string
    {
        return $this->business_license;
    }

    /**
     * 设置营业执照
     *
     * @param string|null $value 营业执照文件路径
     */
    public function setBusinessLicense($value): static
    {
        $this->business_license = $value;
        return $this;
    }

    /**
     * 获取业务描述
     */
    public function getBusinessDescription(): ?string
    {
        return $this->business_description;
    }

    /**
     * 设置业务描述
     *
     * @param string|null $value 业务描述内容
     */
    public function setBusinessDescription($value): static
    {
        $this->business_description = $value;
        return $this;
    }

    /**
     * 获取营销计划
     */
    public function getMarketingPlan(): ?string
    {
        return $this->marketing_plan;
    }

    /**
     * 设置营销计划
     *
     * @param string|null $value 营销计划内容
     */
    public function setMarketingPlan($value): static
    {
        $this->marketing_plan = $value;
        return $this;
    }

    /**
     * 获取预期月交易量
     */
    public function getExpectedMonthlyVolume(): float
    {
        return $this->expected_monthly_volume;
    }

    /**
     * 设置预期月交易量
     *
     * @param float $value 预期月交易量
     */
    public function setExpectedMonthlyVolume($value): static
    {
        $this->expected_monthly_volume = $value;
        return $this;
    }

    /**
     * 获取预期用户数量
     */
    public function getExpectedUserCount(): int
    {
        return $this->expected_user_count;
    }

    /**
     * 设置预期用户数量
     *
     * @param int $value 预期用户数量
     */
    public function setExpectedUserCount($value): static
    {
        $this->expected_user_count = $value;
        return $this;
    }

    /**
     * 获取联系方法
     */
    public function getContactMethod(): string
    {
        return $this->contact_method;
    }

    /**
     * 设置联系方法
     *
     * @param string $value 联系方法
     */
    public function setContactMethod($value): static
    {
        $this->contact_method = $value;
        return $this;
    }

    /**
     * 获取联系账号
     */
    public function getContactAccount(): string
    {
        return $this->contact_account;
    }

    /**
     * 设置联系账号
     *
     * @param string $value 联系账号
     */
    public function setContactAccount($value): static
    {
        $this->contact_account = $value;
        return $this;
    }

    /**
     * 获取附加信息
     */
    public function getAdditionalInfo(): ?string
    {
        return $this->additional_info;
    }

    /**
     * 设置附加信息
     *
     * @param string|null $value 附加信息内容
     */
    public function setAdditionalInfo($value): static
    {
        $this->additional_info = $value;
        return $this;
    }

    /**
     * 获取申请状态
     */
    public function getStatus(): ApplicationStatus
    {
        return $this->status;
    }

    /**
     * 设置申请状态
     *
     * @param ApplicationStatus $value 申请状态值
     */
    public function setStatus(ApplicationStatus $value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取拒绝原因
     */
    public function getRejectionReason(): ?string
    {
        return $this->rejection_reason;
    }

    /**
     * 设置拒绝原因
     *
     * @param string|null $value 拒绝原因内容
     */
    public function setRejectionReason($value): static
    {
        $this->rejection_reason = $value;
        return $this;
    }

    /**
     * 获取提交时间
     */
    public function getSubmittedAt(): \Carbon\Carbon
    {
        return $this->submitted_at;
    }

    /**
     * 设置提交时间
     *
     * @param \Carbon\Carbon|string $value 提交时间
     */
    public function setSubmittedAt($value): static
    {
        $this->submitted_at = $value;
        return $this;
    }

    /**
     * 获取审核时间
     */
    public function getReviewedAt(): ?\Carbon\Carbon
    {
        return $this->reviewed_at;
    }

    /**
     * 设置审核时间
     *
     * @param \Carbon\Carbon|string|null $value 审核时间
     */
    public function setReviewedAt($value): static
    {
        $this->reviewed_at = $value;
        return $this;
    }

    /**
     * 获取审核员ID
     */
    public function getReviewerId(): ?int
    {
        return $this->reviewer_id;
    }

    /**
     * 设置审核员ID
     *
     * @param int|null $value 审核员ID值
     */
    public function setReviewerId($value): static
    {
        $this->reviewer_id = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 获取关联的申请用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * 获取关联的审核员用户模型
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, self::FIELD_REVIEWER_ID, User::FIELD_ID);
    }
}
