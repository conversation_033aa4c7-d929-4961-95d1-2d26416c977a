<?php

declare(strict_types=1);

namespace App\Model\Match;

use App\Model\Currency\Currency;
use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model;

/**
* @property int $id 
* @property int $user_id 用户id
* @property int $currency_id 交易标的
* @property int $order_id 由系统生成
* @property int $market_type 市场类型
* @property int $side 1买-1卖
* @property float $quantity 交易数量
* @property float $fill_quantity 成交数量
* @property float $price 挂单价格
* @property float $avg_price 平均成交价格
* @property int $order_type 1 市价 2 限价
* @property int $trade_type -1：撤单 0 待成交 1:完全成交 2 部分成交 
* @property string $order_force gtc等待全部成交
ioc立即成交，剩余部分取消
fok全部成交或者全部取消
* @property int $fill_time 成交时间 毫秒
* @property int $status -1 待加入撮合引擎
1 pending 等待成交
2 partial_filler 部分成交
3 filled 全部成交
0 订单取消
* @property string $reason 撤单原因
* @property int $has_trade 是否处理过成交
* @property Carbon $created_at 
* @property Carbon $updated_at 
* @property-read null|Currency $currency 
*/
class MatchOrder extends Model
{
    /**
     * 是否处理过成交
     */
    public const FIELD_HAS_TRADE = 'has_trade';
    /**
     * 撤单原因
     */
    public const FIELD_REASON = 'reason';
    /**
     * 1买-1卖
     */
    public const FIELD_SIDE = 'side';
    /**
    * 1 pending 等待成交
    2 partial_filler 部分成交
    3 filled 全部成交
    0 订单取消
    */
    public const FIELD_STATUS = 'status';
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 用户id
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 交易标的
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 订单id ，订单id 由撮合引擎生成
     */
    public const FIELD_ORDER_ID = 'order_id';
    /**
     * 市场类型
     */
    public const FIELD_MARKET_TYPE = 'market_type';
    /**
     * 交易数量
     */
    public const FIELD_QUANTITY = 'quantity';
    /**
     * 成交数量
     */
    public const FIELD_FILL_QUANTITY = 'fill_quantity';
    /**
     * 挂单价格
     */
    public const FIELD_PRICE = 'price';
    /**
     * 平均成交价格
     */
    public const FIELD_AVG_PRICE = 'avg_price';
    /**
     * 1 市价 2 限价
     */
    public const FIELD_ORDER_TYPE = 'order_type';
    /**
     * -1：撤单 0 待成交 1:完全成交 2 部分成交 
     */
    public const FIELD_TRADE_TYPE = 'trade_type';
    /**
     * 
     */
    public const FIELD_ORDER_FORCE = 'order_force';
    /**
     * 成交时间
     */
    public const FIELD_FILL_TIME = 'fill_time';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'match_orders';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'order_id', 'market_type', 'side', 'quantity', 'fill_quantity', 'price', 'avg_price', 'order_type', 'trade_type', 'order_force', 'fill_time', 'status', 'reason', 'has_trade', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'user_id' => 'integer', 'currency_id' => 'integer', 'order_id' => 'integer', 'market_type' => 'integer', 'side' => 'integer', 'order_type' => 'integer', 'trade_type' => 'integer', 'fill_time' => 'integer', 'status' => 'integer', 'has_trade' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];


    // 定义模型关联获取币种的信息
    public function currency(): \Hyperf\Database\Model\Relations\HasOne
    {
        return $this->hasOne(Currency::class, 'id', 'currency_id');
    }

    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getOrderId() : int
    {
        return $this->order_id;
    }
    public function setOrderId($value) : object
    {
        $this->order_id = $value;
        return $this;
    }
    public function getMarketType() : int
    {
        return $this->market_type;
    }
    public function setMarketType($value) : object
    {
        $this->market_type = $value;
        return $this;
    }
    public function getQuantity() : float
    {
        return (float)$this->quantity;
    }
    public function setQuantity($value) : object
    {
        $this->quantity = $value;
        return $this;
    }
    public function getFillQuantity() : float
    {
        return $this->fill_quantity;
    }
    public function setFillQuantity($value) : object
    {
        $this->fill_quantity = $value;
        return $this;
    }
    public function getPrice() : float
    {
        return (float)$this->price;
    }
    public function setPrice($value) : object
    {
        $this->price = $value;
        return $this;
    }
    public function getAvgPrice() : float
    {
        return $this->avg_price;
    }
    public function setAvgPrice($value) : object
    {
        $this->avg_price = $value;
        return $this;
    }
    public function getOrderType() : int
    {
        return $this->order_type;
    }
    public function setOrderType($value) : object
    {
        $this->order_type = $value;
        return $this;
    }
    public function getTradeType() : int
    {
        return $this->trade_type;
    }
    public function setTradeType($value) : object
    {
        $this->trade_type = $value;
        return $this;
    }
    public function getOrderForce() : string
    {
        return $this->order_force;
    }
    public function setOrderForce($value) : object
    {
        $this->order_force = $value;
        return $this;
    }
    public function getFillTime() : mixed
    {
        return $this->fill_time;
    }
    public function setFillTime($value) : object
    {
        $this->fill_time = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
    public function getSide() : int
    {
        return $this->side;
    }
    public function setSide($value) : object
    {
        $this->side = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getReason() : string
    {
        return $this->reason;
    }
    public function setReason($value) : object
    {
        $this->reason = $value;
        return $this;
    }
    public function getHasTrade() : int
    {
        return $this->has_trade;
    }
    public function setHasTrade($value) : object
    {
        $this->has_trade = $value;
        return $this;
    }
}
