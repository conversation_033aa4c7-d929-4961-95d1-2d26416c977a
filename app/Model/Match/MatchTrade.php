<?php

declare(strict_types=1);

namespace App\Model\Match;

use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model;
use App\Model\Match\MatchOrder;
use App\Model\Trade\TradeSpotOrder;

/**
 * @property int $id 
 * @property int $currency_id 交易标的id
 * @property int $buy_order_id 买单交易id
 * @property int $sell_order_id 卖单交易id
 * @property int $buy_user_id 买单用户
 * @property int $sell_user_id 卖单用户
 * @property int $market_type 交易市场类型
 * @property int $trade_id 撮合成交id
 * @property float $price 成交价格
 * @property float $quantity 成交数量
 * @property int $is_bot 是否机器人
 * @property int $match_time 成交时间 毫秒
 * @property Carbon $created_at 
 * @property Carbon $updated_at 
 */
class MatchTrade extends Model
{
    /**
     * 买单交易id
     */
    public const FIELD_BUY_ORDER_ID = 'buy_order_id';
    /**
     * 卖单交易id
     */
    public const FIELD_SELL_ORDER_ID = 'sell_order_id';
    /**
     * 买单用户
     */
    public const FIELD_BUY_USER_ID = 'buy_user_id';
    /**
     * 卖单用户
     */
    public const FIELD_SELL_USER_ID = 'sell_user_id';
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 交易标的id
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 交易市场类型
     */
    public const FIELD_MARKET_TYPE = 'market_type';
    /**
     * 撮合成交id
     */
    public const FIELD_TRADE_ID = 'trade_id';
    /**
     * 交易方向 1 买 -1 卖
     */
    public const FIELD_SIDE = 'side';
    /**
     * 卖单用户
     */
    public const FIELD_MAKER_USER_ID = 'maker_user_id';
    /**
     * 买单用户
     */
    public const FIELD_TAKER_USER_ID = 'taker_user_id';
    /**
     * 卖单交易id
     */
    public const FIELD_MAKER_ORDER_ID = 'maker_order_id';
    /**
     * 买单交易id
     */
    public const FIELD_TAKER_ORDER_ID = 'taker_order_id';
    /**
     * 成交价格
     */
    public const FIELD_PRICE = 'price';
    /**
     * 成交数量
     */
    public const FIELD_QUANTITY = 'quantity';
    /**
     * 是否机器人
     */
    public const FIELD_IS_BOT = 'is_bot';
    /**
     * 成交时间
     */
    public const FIELD_MATCH_TIME = 'match_time';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'match_trades';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'currency_id', 'buy_order_id', 'sell_order_id', 'buy_user_id', 'sell_user_id', 'market_type', 'trade_id', 'price', 'quantity', 'is_bot', 'match_time', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'currency_id' => 'integer', 'buy_order_id' => 'integer', 'sell_order_id' => 'integer', 'buy_user_id' => 'integer', 'sell_user_id' => 'integer', 'market_type' => 'integer', 'trade_id' => 'integer', 'is_bot' => 'integer', 'match_time' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * 买单关联到撮合引擎订单
     */
    public function buyMatchOrder(): \Hyperf\Database\Model\Relations\HasOne
    {
        return $this->hasOne(MatchOrder::class, 'order_id', 'buy_order_id');
    }

    /**
     * 卖单关联到撮合引擎订单
     */
    public function sellMatchOrder(): \Hyperf\Database\Model\Relations\HasOne
    {
        return $this->hasOne(MatchOrder::class, 'order_id', 'sell_order_id');
    }

    /**
     * 买单关联到现货订单（通过撮合引擎订单）
     */
    public function buySpotOrder(): \Hyperf\Database\Model\Relations\HasOneThrough
    {
        return $this->hasOneThrough(
            TradeSpotOrder::class,
            MatchOrder::class,
            'order_id',    // match_orders表的外键
            'match_order', // trade_spot_orders表的外键
            'buy_order_id', // match_trades表的本地键
            'id'           // match_orders表的本地键
        )->select('trade_spot_order.*');
    }

    /**
     * 卖单关联到现货订单（通过撮合引擎订单）
     */
    public function sellSpotOrder(): \Hyperf\Database\Model\Relations\HasOneThrough
    {
        return $this->hasOneThrough(
            TradeSpotOrder::class,
            MatchOrder::class,
            'order_id',     // match_orders表的外键
            'match_order',  // trade_spot_orders表的外键
            'sell_order_id', // match_trades表的本地键
            'id'            // match_orders表的本地键
        )->select('trade_spot_order.*');
    }
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getMarketType() : int
    {
        return $this->market_type;
    }
    public function setMarketType($value) : object
    {
        $this->market_type = $value;
        return $this;
    }
    public function getTradeId() : int
    {
        return $this->trade_id;
    }
    public function setTradeId($value) : object
    {
        $this->trade_id = $value;
        return $this;
    }
    public function getSide() : int
    {
        return $this->side;
    }
    public function setSide($value) : object
    {
        $this->side = $value;
        return $this;
    }
    public function getMakerUserId() : int
    {
        return $this->maker_user_id;
    }
    public function setMakerUserId($value) : object
    {
        $this->maker_user_id = $value;
        return $this;
    }
    public function getTakerUserId() : int
    {
        return $this->taker_user_id;
    }
    public function setTakerUserId($value) : object
    {
        $this->taker_user_id = $value;
        return $this;
    }
    public function getMakerOrderId() : int
    {
        return $this->maker_order_id;
    }
    public function setMakerOrderId($value) : object
    {
        $this->maker_order_id = $value;
        return $this;
    }
    public function getTakerOrderId() : int
    {
        return $this->taker_order_id;
    }
    public function setTakerOrderId($value) : object
    {
        $this->taker_order_id = $value;
        return $this;
    }
    public function getPrice() : float
    {
        return $this->price;
    }
    public function setPrice($value) : object
    {
        $this->price = $value;
        return $this;
    }
    public function getQuantity() : float
    {
        return $this->quantity;
    }
    public function setQuantity($value) : object
    {
        $this->quantity = $value;
        return $this;
    }
    public function getIsBot() : int
    {
        return $this->is_bot;
    }
    public function setIsBot($value) : object
    {
        $this->is_bot = $value;
        return $this;
    }
    public function getMatchTime() : mixed
    {
        return $this->match_time;
    }
    public function setMatchTime($value) : object
    {
        $this->match_time = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
    public function getBuyOrderId() : int
    {
        return $this->buy_order_id;
    }
    public function setBuyOrderId($value) : object
    {
        $this->buy_order_id = $value;
        return $this;
    }
    public function getSellOrderId() : int
    {
        return $this->sell_order_id;
    }
    public function setSellOrderId($value) : object
    {
        $this->sell_order_id = $value;
        return $this;
    }
    public function getBuyUserId() : int
    {
        return $this->buy_user_id;
    }
    public function setBuyUserId($value) : object
    {
        $this->buy_user_id = $value;
        return $this;
    }
    public function getSellUserId() : int
    {
        return $this->sell_user_id;
    }
    public function setSellUserId($value) : object
    {
        $this->sell_user_id = $value;
        return $this;
    }
}
