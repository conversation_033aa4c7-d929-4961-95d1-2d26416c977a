<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Article;

use App\Model\User\User;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * 举报表模型.
 *
 * @property int $dynamics_id 动态ID
 * @property int $user_id 会员ID
 * @property int $tipoffs_user_id 被举报人 会员ID
 * @property string $content 举报内容
 * @property string $voucher 凭证图片
 * @property string $reason 举报理由
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class Tipoffs extends MineModel
{
    /**
     * 数据表名称.
     */
    protected ?string $table = 'cpx_tipoffs';

    /**
     * 允许批量赋值的属性.
     */
    protected array $fillable = [
        'dynamics_id',
        'user_id',
        'tipoffs_user_id',
        'content',
        'voucher',
        'reason',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置.
     */
    protected array $casts = [
        'dynamics_id' => 'integer',
        'user_id' => 'integer',
        'tipoffs_user_id'=> 'integer',
        'content' => 'string',
        'voucher' => 'array',
        'reason' => 'string',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性.
     */
    protected array $hidden = [
    ];


    /**
     * ID
     */
    public const FIELD_ID = 'id';

    /**
     * 举报内容
     * Summary of FIELD_CONTENT
     * @var string
     */
    public const FIELD_CONTENT = 'content';

    /**
     * 动态ID
     * Summary of FIELD_DYNAMICS_ID
     * @var string
     */
    public const FIELD_DYNAMICS_ID = 'dynamics_id';

    /**
     * 用户ID
     * Summary of FIELD_USER_ID
     * @var string
     */
    public const FIELD_USER_ID = 'user_id';

    /**
     * 被举报人用户ID
     * Summary of FIELD_TIPOFFS_USER_ID
     * @var string
     */
    public const FIELD_TIPOFFS_USER_ID = 'tipoffs_user_id';

    /**
     * 凭证
     * Summary of FIELD_VOUCHER
     * @var string
     */
    public const FIELD_VOUCHER = 'voucher';

    /**
     * 举报原因
     * Summary of FIELD_REASON
     * @var string
     */
    public const FIELD_REASON = 'reason';

    public const FIELD_CREATED_AT = 'created_at';

    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * 关联 User 模型.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    
    /**
     * 关联 被举报人User 模型.
     */
    public function tipOffsUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tipoffs_user_id', 'id');
    }

    /**
     * 举报文章.
     */
    public function dynamics(): BelongsTo
    {
        return $this->belongsTo(Dynamics::class, 'dynamics_id', 'id');
    }

}