<?php

declare(strict_types=1);

namespace App\Model\Article;

use App\Model\User\User;
use Hyperf\DbConnection\Model\Model as MineModel;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;



/**
 * 动态表模型
 *
 * @property int $pid_id 上级ID
 * @property int $member_id 会员ID
 * @property string $content 内容
 * @property array $images 图片
 * @property array $forward_uids 转发人 user_id
 * @property array $collect_uids 收藏人 user_ids
 * @property string $look_auth 查看权限
 * @property string $look_num 查看数量
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class Dynamics extends MineModel
{
    /**
     * 数据表名称
     *
     * @var string
     */
    protected ?string $table = 'cpx_dynamics';

    /**
     * 允许批量赋值的属性
     *
     * @var array
     */
    protected array $fillable = [
        'pid_id',
        'user_id',
        'liked',
        'liked_uids',
        'title',
        'content',
        'images',
        'forward_uids',
        'collect_uids',
        'look_auth',
        'look_num',
        'dynamics_currency',
        'hot_topic_id',
        'type',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置
     *
     * @var array
     */
    protected array $casts = [
        'pid_id' => 'integer',
        'user_id' => 'integer',
        'liked' => 'integer',
        'liked_uids' => 'array',
        'content' => 'string',
        'title' => 'string',
        'images'=> 'array',
        'forward_uids'=>'array',
        'collect_uids'=>'array',
        'look_auth'=>'integer',
        'look_num'=>'integer',
        'hot_topic_id'=>'integer',
        'dynamics_currency'=> 'array',
        'type'=>'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected array $hidden = [
    ];


    /**
     * ID
     */
    public const FIELD_ID = 'id';


    /**
     * ID
     */
    public const FIELD_PID = 'pid';

    /**
     * 分类ID
     */
    public const FIELD_USER_ID = 'user_id';

    /**
     * 标题
     */
    public const FIELD_TITLE = 'title';

    /**
     * 内容
     */
    public const FIELD_CONTENT = 'content';

    /**
     * 图片
     */
    public const FIELD_IMAGES = 'images';


    /**
     * Summary of 转发人 user_id
     * @var array
     */
    public const FIELD_FORWARD_UIDS = 'forward_uids';

    /**
     * Summary of 收藏人 user_id
     * @var array
     */
    public const FIELD_COLLECT_UIDS = 'collect_uids';

    /**
     * Summary of 查看权限
     * @var integer
     */
    public const FIELD_LOOK_AUTH = 'look_auth';

    /**
     * Summary of 查看数量
     * @var string
     */
    public const FIELD_LOOK_NUM = 'look_num';


    /**
     * Summary of 关联币种ID集合
     * @var array
     */
    public const FIELD_DYNAMICS_CURRENCY = 'dynamics_currency';

    /**
     * Summary of 管理话题ID
     * @var string
     */
    public const FIELD_HOT_TOPIC_ID = 'hot_topic_id';

    /**
     * Summary of 动态类型，1观点，2文章
     * @var int
     */
    public const FIELD_TYPE = 'type';

    /**
     * Summary of 点赞数量
     * @var int
     */
    public const FIELD_LIKED = 'liked';

    /**
     * Summary of 点赞用户ID
     * @var array
     */
    public const FIELD_LIKED_UIDS = 'liked_uids';
    

    /**
     * 关联用户
     * Summary of user
     * @return BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联热门话题表
     * Summary of hottopic
     * @return BelongsTo
     */
    public function hottopic(): BelongsTo{
        return $this->belongsTo(HotTopic::class, 'hot_topic_id', 'id');
    }

}