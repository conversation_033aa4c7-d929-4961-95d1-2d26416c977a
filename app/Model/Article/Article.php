<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 
 */

namespace App\Model\Article;

use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * @property int $id 主键
 * @property string $title 标题
 * @property string $content 内容
 * @property string $summary 简介
 * @property string $remark 备注
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @property Carbon $deleted_at 删除时间
 */
final class Article extends MineModel
{
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_article';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'category_id', 'title', 'content', 'summary', 'remark'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'category_id' => 'integer',
        'title' => 'array',
        'content' => 'array',
        'summary' => 'array',
        'remark' => 'array',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'];

    /**
     * ID
     */
    public const FIELD_ID = 'id';

    /**
     * 分类ID
     */
    public const FIELD_CATEGORY_ID = 'category_id';

    /**
     * 标题
     */
    public const FIELD_TITLE = 'title';

    /**
     * 内容
     */
    public const FIELD_CONTENT = 'content';

    /**
     * 简介
     */
    public const FIELD_SUMMARY = 'summary';

    /**
     * 备注
     */
    public const FIELD_REMARK = 'remark';

    /**
     * 分类
     */
    public function category()
    {
        return $this->hasOne(Category::class, 'id', 'category_id');
    }

}
