<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 动态权限枚举
 */

namespace App\Model\Article\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum DynamicsAuth: int
{
    use EnumConstantsTrait;

    /**
     * 公开
     */
    #[Message('dynamics.enums.auth.1')]
    case PUBLIC = 1;

    /**
     * 关注我的
     */
    #[Message('dynamics.enums.auth.2')]
    case LIKED = 2;

    /**
     * 私密
     */
    #[Message('dynamics.enums.auth.3')]
    case PRIVATE = 3;
}
