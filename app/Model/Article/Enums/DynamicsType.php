<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 动态类型枚举
 */

namespace App\Model\Article\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum DynamicsType: int
{
    use EnumConstantsTrait;

    /**
     * 观点
     */
    #[Message('dynamics.enums.type.1')]
    case POINT = 1;

    /**
     * 文章
     */
    #[Message('dynamics.enums.type.2')]
    case ARTICLE = 2;
}
