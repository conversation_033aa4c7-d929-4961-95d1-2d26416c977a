<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Article;

use App\Model\Currency\Currency;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * 动态币种表模型.
 *
 * @property int $symbol_id 币种ID
 * @property int $symbol 币种标的
 * @property string $url 链接
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class DynamicsCurrency extends MineModel
{
    /**
     * 数据表名称.
     */
    protected ?string $table = 'cpx_dynamics_currency';

    /**
     * 允许批量赋值的属性.
     */
    protected array $fillable = [
        'symbol_id',
        'symbol',
        'url',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置.
     */
    protected array $casts = [
        'symbol_id' => 'integer',
        'symbol' => 'string',
        'url' => 'string',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性.
     */
    protected array $hidden = [
    ];



    /**
     * ID
     */
    public const FIELD_ID = 'id';

    /**
     * 币种ID
     */
    public const FIELD_SYMBOL_ID = 'symbol_id';

    /**
     * 币种标的
     */
    public const FIELD_SYMBOL = 'symbol';

    /**
     * 链接
     */
    public const FIELD_URL = 'url';

    /**
     * 创建者
     */
    public const FIELD_CREATED_BY = 'created_by';

    /**
     * 更新者
     */
    public const FIELD_UPDATED_BY = 'updated_by';

    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';

    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';



    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'symbol_id', 'id');
    }

}