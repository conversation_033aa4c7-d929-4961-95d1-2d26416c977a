<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Article;

use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * 热门话题表模型.
 *
 * @property string $currency 关联币种ID（cpx_dynamics_currency表）
 * @property string $title 标题
 * @property string $content 内容
 * @property int $look_num 查看数量
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class HotTopic extends MineModel
{
    /**
     * 数据表名称.
     */
    protected ?string $table = 'cpx_hot_topic';

    /**
     * 允许批量赋值的属性.
     */
    protected array $fillable = [
        'currency',
        'title',
        'content',
        'look_num',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置.
     */
    protected array $casts = [
        'currency' => 'array',
        'title' => 'string',
        'content' => 'string',
        'look_num' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性.
     */
    protected array $hidden = [
    ];

    /**
     * 关联币种ID（cpx_dynamics_currency表）
     */
    public const FIELD_CURRENCY = 'currency';

    /**
     * 标题
     */
    public const FIELD_TITLE = 'title';

    /**
     * 内容
     */
    public const FIELD_CONTENT = 'content';

    /**
     * 查看数量
     */
    public const FIELD_LOOK_NUM = 'look_num';

    /**
     * 创建者
     */
    public const FIELD_CREATED_BY = 'created_by';

    /**
     * 更新者
     */
    public const FIELD_UPDATED_BY = 'updated_by';

    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';

    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';


}