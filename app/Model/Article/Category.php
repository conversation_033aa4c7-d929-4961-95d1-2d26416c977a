<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 
 */

namespace App\Model\Article;

use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * @property int $id 主键
 * @property string $name 分类名称
 * @property string $key 分类标识
 * @property int $sort 排序
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @property Carbon $deleted_at 删除时间
 */
final class Category extends MineModel
{
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_category';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id',
        'name',
        'key',
        'sort'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', 
        'name' => 'array',
        'key' => 'string',
        'sort' => 'integer',
        'created_by' => 'integer', 
        'updated_by' => 'integer', 
        'created_at' => 'datetime', 
        'updated_at' => 'datetime', 
        'deleted_at' => 'datetime'];

    /**
     * ID
     */
    public const FIELD_ID = 'id';

    /**
     * 分类名称
     */
    public const FIELD_NAME = 'name';

    /**
     * 分类标识
     */
    public const FIELD_KEY = 'key';

    /**
     * 排序
     */
    public const FIELD_SORT = 'sort';


}
