<?php

declare(strict_types=1);

namespace App\Model\Article;

use Hyperf\DbConnection\Model\Model as MineModel;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;



/**
 * 公告表模型
 *
 * @property int $category_id 分类ID
 * @property string $title 标题
 * @property string $content 内容
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
final class Notice extends MineModel
{
    /**
     * 数据表名称
     *
     * @var string
     */
    protected ?string $table = 'cpx_notice';

    /**
     * 允许批量赋值的属性
     *
     * @var array
     */
    protected array $fillable = [
        'category_id',
        'title',
        'content',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    /**
     * 数据转换设置
     *
     * @var array
     */
    protected array $casts = [
        'category_id' => 'integer',
        'title' => 'array',
        'content' => 'array',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


    /**
     * ID
     */
    public const FIELD_ID = 'id';

    /**
     * 分类ID
     */
    public const FIELD_CATEGORY_ID = 'category_id';

    /**
     * 标题
     */
    public const FIELD_TITLE = 'title';

    /**
     * 内容
     */
    public const FIELD_CONTENT = 'content';



    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected array $hidden = [
    ];

    public function category()
    {
        return $this->hasOne(Category::class, 'id', 'category_id');
    }


}