<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $currency_id 
 * @property int $level 档位等级
 * @property float $hold_start 持仓范围起点
 * @property float $hold_end 持仓范围结束
 * @property float $max_lever 最大杠杆
 * @property float $keep_rate 维持保证金率
 * @property float $init_rate 初始保证金率
 * @property int $is_cross 1全仓 2逐仓
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class TradeMarginBracket extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 档位等级
     */
    public const FIELD_LEVEL = 'level';
    /**
     * 持仓范围起点
     */
    public const FIELD_HOLD_START = 'hold_start';
    /**
     * 持仓范围结束
     */
    public const FIELD_HOLD_END = 'hold_end';
    /**
     * 最大杠杆
     */
    public const FIELD_MAX_LEVER = 'max_lever';
    /**
     * 维持保证金率
     */
    public const FIELD_KEEP_RATE = 'keep_rate';
    /**
     * 初始保证金率
     */
    public const FIELD_INIT_RATE = 'init_rate';
    /**
     * 1全仓 2逐仓
     */
    public const FIELD_IS_CROSS = 'is_cross';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_margin_brackets';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'currency_id', 'level', 'hold_start', 'hold_end', 'max_lever', 'keep_rate', 'init_rate', 'is_cross', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'currency_id' => 'integer', 'level' => 'integer', 'is_cross' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getLevel() : int
    {
        return $this->level;
    }
    public function setLevel($value) : object
    {
        $this->level = $value;
        return $this;
    }
    public function getHoldStart() : float
    {
        return (float)$this->hold_start;
    }
    public function setHoldStart($value) : object
    {
        $this->hold_start = $value;
        return $this;
    }
    public function getHoldEnd() : float
    {
        return (float)$this->hold_end;
    }
    public function setHoldEnd($value) : object
    {
        $this->hold_end = $value;
        return $this;
    }
    public function getMaxLever() : float
    {
        return (float)$this->max_lever;
    }
    public function setMaxLever($value) : object
    {
        $this->max_lever = $value;
        return $this;
    }
    public function getKeepRate() : float
    {
        return (float)$this->keep_rate;
    }
    public function setKeepRate($value) : object
    {
        $this->keep_rate = $value;
        return $this;
    }
    public function getInitRate() : float
    {
        return (float)$this->init_rate;
    }
    public function setInitRate($value) : object
    {
        $this->init_rate = $value;
        return $this;
    }
    public function getIsCross() : int
    {
        return $this->is_cross;
    }
    public function setIsCross($value) : object
    {
        $this->is_cross = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
