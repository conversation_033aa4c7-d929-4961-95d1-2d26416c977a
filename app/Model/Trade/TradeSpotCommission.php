<?php

declare(strict_types=1);

namespace App\Model\Trade;

use App\Model\Currency\Currency;
use App\Model\Trade\Enums\CommissionOrderType;
use App\Model\Trade\Enums\CommissionStatus;
use App\Model\Trade\Enums\TriggerCondition;
use App\Model\Trade\Enums\TriggerType;
use App\Model\User\User;
use App\QueryBuilder\Model;

/**
 * @property int $id 
 * @property int $user_id 
 * @property int $currency_id 
 * @property float $trigger_price 触发价格
 * @property float $amount 委托数量
 * @property TriggerType $trigger_type 触发后的下单方式（限价/市价）
 * @property float|null $place_price 触发后的下单价格（限价单必填）
 * @property CommissionOrderType $order_type 委托订单类型
 * @property int $side 订单方向：1买，-1卖
 * @property TriggerCondition $trigger_condition 触发条件
 * @property int|null $order_id 关联的现货订单id，触发后生成更新
 * @property CommissionStatus $status 委托订单状态
 * @property \Carbon\Carbon|null $triggered_at 触发时间
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 * @property string|null $trigger_reason 触发原因或失败原因
 */
class TradeSpotCommission extends Model
{
    /**
     * 订单方向
     */
    public const FIELD_SIDE = 'side';
    /**
     * 1 大于等于 -1 小于等于
     */
    public const FIELD_TRIGGER_CONDITION = 'trigger_condition';
    /**
     * 触发时间
     */
    public const FIELD_TRIGGERED_AT = 'triggered_at';
    /**
     * 失败原因
     */
    public const FIELD_TRIGGER_REASON = 'trigger_reason';
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 触发价格
     */
    public const FIELD_TRIGGER_PRICE = 'trigger_price';
    /**
     * 委托数量
     */
    public const FIELD_AMOUNT = 'amount';
    /**
     * 触发后的下单方式 限价或者市价
     */
    public const FIELD_TRIGGER_TYPE = 'trigger_type';
    /**
     * 触发后的下单价格
     */
    public const FIELD_PLACE_PRICE = 'place_price';
    /**
     * 1止盈止损，2计划委托 3追踪委托
     */
    public const FIELD_ORDER_TYPE = 'order_type';
    /**
     * 关联的现货订单id，触发后生成更新
     */
    public const FIELD_ORDER_ID = 'order_id';
    /**
     * 1完成 -1取消 0等待执行
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_spot_commission';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'trigger_price', 'amount', 'trigger_type', 'place_price', 'order_type', 'side', 'trigger_condition', 'order_id', 'status', 'triggered_at', 'created_at', 'updated_at', 'trigger_reason'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'currency_id' => 'integer',
        'trigger_price' => 'decimal:8',
        'amount' => 'decimal:8',
        'trigger_type' => TriggerType::class,
        'place_price' => 'decimal:8',
        'order_type' => CommissionOrderType::class,
        'side' => 'integer',
        'trigger_condition' => TriggerCondition::class,
        'order_id' => 'integer',
        'status' => CommissionStatus::class,
        'triggered_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getTriggerPrice() : float
    {
        return $this->trigger_price;
    }
    public function setTriggerPrice($value) : object
    {
        $this->trigger_price = $value;
        return $this;
    }
    public function getAmount() : float
    {
        return $this->amount;
    }
    public function setAmount($value) : object
    {
        $this->amount = $value;
        return $this;
    }
    public function getTriggerType() : TriggerType
    {
        return $this->trigger_type;
    }
    public function setTriggerType($value) : object
    {
        $this->trigger_type = $value;
        return $this;
    }
    public function getPlacePrice() : float
    {
        return $this->place_price;
    }
    public function setPlacePrice($value) : object
    {
        $this->place_price = $value;
        return $this;
    }
    public function getOrderType() : CommissionOrderType
    {
        return $this->order_type;
    }
    public function setOrderType($value) : object
    {
        $this->order_type = $value;
        return $this;
    }
    public function getOrderId() : int
    {
        return $this->order_id;
    }
    public function setOrderId($value) : object
    {
        $this->order_id = $value;
        return $this;
    }
    public function getStatus() : CommissionStatus
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
    public function getSide() : int
    {
        return $this->side;
    }
    public function setSide($value) : object
    {
        $this->side = $value;
        return $this;
    }
    public function getTriggerCondition() : TriggerCondition
    {
        return $this->trigger_condition;
    }
    public function setTriggerCondition($value) : object
    {
        $this->trigger_condition = $value;
        return $this;
    }
    public function getTriggeredAt() : mixed
    {
        return $this->triggered_at;
    }
    public function setTriggeredAt($value) : object
    {
        $this->triggered_at = $value;
        return $this;
    }
    public function getTriggerReason() : string
    {
        return $this->trigger_reason;
    }
    public function setTriggerReason($value) : object
    {
        $this->trigger_reason = $value;
        return $this;
    }

    /**
     * 关联用户
     */
    public function user(): \Hyperf\Database\Model\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联币种
     */
    public function currency(): \Hyperf\Database\Model\Relations\BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }

    /**
     * 关联现货订单（触发后生成的订单）
     */
    public function spotOrder(): \Hyperf\Database\Model\Relations\BelongsTo
    {
        return $this->belongsTo(TradeSpotOrder::class, 'order_id', 'id');
    }
}
