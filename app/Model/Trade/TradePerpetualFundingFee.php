<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 记录ID
 * @property int $user_id 用户ID
 * @property int $contract_id 合约ID
 * @property int $position_id 仓位ID
 * @property int $margin_mode 保证金模式：1-全仓，2-逐仓
 * @property int $side 仓位方向：1-多头，2-空头
 * @property float $funding_rate 资金费率
 * @property float $position_size 仓位大小（币本位）
 * @property float $position_value 仓位价值
 * @property float $funding_fee 资金费用（正数为支付，负数为收取）
 * @property string $funding_time 计费时间
 * @property int $status 状态：1-待收取，2-已收取，3-收取失败
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class TradePerpetualFundingFee extends Model
{
    /**
     * 记录ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 合约ID
     */
    public const FIELD_CONTRACT_ID = 'contract_id';
    /**
     * 仓位ID
     */
    public const FIELD_POSITION_ID = 'position_id';
    /**
     * 保证金模式：1-全仓，2-逐仓
     */
    public const FIELD_MARGIN_MODE = 'margin_mode';
    /**
     * 仓位方向：1-多头，2-空头
     */
    public const FIELD_SIDE = 'side';
    /**
     * 资金费率
     */
    public const FIELD_FUNDING_RATE = 'funding_rate';
    /**
     * 仓位大小（币本位）
     */
    public const FIELD_POSITION_SIZE = 'position_size';
    /**
     * 仓位价值
     */
    public const FIELD_POSITION_VALUE = 'position_value';
    /**
     * 资金费用（正数为支付，负数为收取）
     */
    public const FIELD_FUNDING_FEE = 'funding_fee';
    /**
     * 计费时间
     */
    public const FIELD_FUNDING_TIME = 'funding_time';
    /**
     * 状态：1-待收取，2-已收取，3-收取失败
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_perpetual_funding_fee';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'contract_id', 'position_id', 'margin_mode', 'side', 'funding_rate', 'position_size', 'position_value', 'funding_fee', 'funding_time', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        // 记录ID
        'id' => 'integer',
        // 用户ID
        'user_id' => 'integer',
        // 合约ID
        'contract_id' => 'integer',
        // 仓位ID
        'position_id' => 'integer',
        // 保证金模式：1-全仓，2-逐仓
        'margin_mode' => 'integer',
        // 仓位方向：1-多头，2-空头
        'side' => 'integer',
        // 资金费率
        'funding_rate' => 'float',
        // 仓位大小（币本位）
        'position_size' => 'float',
        // 仓位价值
        'position_value' => 'float',
        // 资金费用（正数为支付，负数为收取）
        'funding_fee' => 'float',
        // 状态：1-待收取，2-已收取，3-收取失败
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getContractId() : int
    {
        return $this->contract_id;
    }
    public function setContractId($value) : object
    {
        $this->contract_id = $value;
        return $this;
    }
    public function getPositionId() : int
    {
        return $this->position_id;
    }
    public function setPositionId($value) : object
    {
        $this->position_id = $value;
        return $this;
    }
    public function getMarginMode() : int
    {
        return $this->margin_mode;
    }
    public function setMarginMode($value) : object
    {
        $this->margin_mode = $value;
        return $this;
    }
    public function getSide() : int
    {
        return $this->side;
    }
    public function setSide($value) : object
    {
        $this->side = $value;
        return $this;
    }
    public function getFundingRate() : float
    {
        return $this->funding_rate;
    }
    public function setFundingRate($value) : object
    {
        $this->funding_rate = $value;
        return $this;
    }
    public function getPositionSize() : float
    {
        return $this->position_size;
    }
    public function setPositionSize($value) : object
    {
        $this->position_size = $value;
        return $this;
    }
    public function getPositionValue() : float
    {
        return $this->position_value;
    }
    public function setPositionValue($value) : object
    {
        $this->position_value = $value;
        return $this;
    }
    public function getFundingFee() : float
    {
        return $this->funding_fee;
    }
    public function setFundingFee($value) : object
    {
        $this->funding_fee = $value;
        return $this;
    }
    public function getFundingTime() : mixed
    {
        return $this->funding_time;
    }
    public function setFundingTime($value) : object
    {
        $this->funding_time = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
