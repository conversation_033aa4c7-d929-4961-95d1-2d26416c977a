<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆交易仓位模型
 */

namespace App\Model\Trade;

use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\Trade\Margin\PositionSide;
use App\Model\Enums\Trade\Margin\MarginPositionStatus;
use App\Model\Currency\Currency;
use App\Model\User\User;
use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int $user_id 用户ID
 * @property int $currency_id 交易币种ID
 * @property PositionSide $side 仓位方向：1-做多，2-做空
 * @property MarginType $margin_type 保证金类型：1-逐仓，2-全仓
 * @property float $leverage 杠杆倍数
 * @property float $quantity 持仓数量
 * @property float $available_quantity 可用数量（可平仓数量）
 * @property float $frozen_quantity 冻结数量（挂单占用）
 * @property float $entry_price 开仓均价
 * @property float $margin_amount 保证金金额
 * @property float $initial_margin 初始保证金
 * @property float $maintenance_margin 维持保证金
 * @property float $realized_pnl 已实现盈亏
 * @property MarginPositionStatus $status 状态：1-持仓中，2-已平仓，3-强制平仓
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 */
final class TradeMarginPosition extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 交易币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 仓位方向：1-做多，2-做空
     */
    public const FIELD_SIDE = 'side';
    /**
     * 保证金类型：1-逐仓，2-全仓
     */
    public const FIELD_MARGIN_TYPE = 'margin_type';
    /**
     * 杠杆倍数
     */
    public const FIELD_LEVERAGE = 'leverage';
    /**
     * 持仓数量
     */
    public const FIELD_QUANTITY = 'quantity';
    /**
     * 可用数量（可平仓数量）
     */
    public const FIELD_AVAILABLE_QUANTITY = 'available_quantity';
    /**
     * 冻结数量（挂单占用）
     */
    public const FIELD_FROZEN_QUANTITY = 'frozen_quantity';
    /**
     * 开仓均价
     */
    public const FIELD_ENTRY_PRICE = 'entry_price';
    /**
     * 保证金金额
     */
    public const FIELD_MARGIN_AMOUNT = 'margin_amount';
    /**
     * 初始保证金
     */
    public const FIELD_INITIAL_MARGIN = 'initial_margin';
    /**
     * 维持保证金
     */
    public const FIELD_MAINTENANCE_MARGIN = 'maintenance_margin';
    /**
     * 已实现盈亏
     */
    public const FIELD_REALIZED_PNL = 'realized_pnl';
    /**
     * 状态：1-持仓中，2-已平仓，3-强制平仓
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_margin_position';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'user_id', // 用户ID
        'currency_id', // 交易币种ID
        'side', // 仓位方向：1-做多，2-做空
        'margin_type', // 保证金类型：1-逐仓，2-全仓
        'leverage', // 杠杆倍数
        'quantity', // 持仓数量
        'available_quantity', // 可用数量（可平仓数量）
        'frozen_quantity', // 冻结数量（挂单占用）
        'entry_price', // 开仓均价
        'margin_amount', // 保证金金额
        'initial_margin', // 初始保证金
        'maintenance_margin', // 维持保证金
        'realized_pnl', // 已实现盈亏
        'status', // 状态：1-持仓中，2-已平仓，3-强制平仓
        'created_at', // 创建时间
        'updated_at' // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'user_id' => 'integer', // 用户ID
        'currency_id' => 'integer', // 交易币种ID
        'side' => PositionSide::class, // 仓位方向
        'margin_type' => MarginType::class, // 保证金类型
        'leverage' => 'float', // 杠杆倍数
        'quantity' => 'float', // 持仓数量
        'available_quantity' => 'float', // 可用数量
        'frozen_quantity' => 'float', // 冻结数量
        'entry_price' => 'float', // 开仓均价
        'margin_amount' => 'float', // 保证金金额
        'initial_margin' => 'float', // 初始保证金
        'maintenance_margin' => 'float', // 维持保证金
        'realized_pnl' => 'float', // 已实现盈亏
        'status' => MarginPositionStatus::class, // 状态
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime' // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取交易币种ID
     */
    public function getCurrencyId(): int
    {
        return $this->currency_id;
    }

    /**
     * 设置交易币种ID
     *
     * @param int $value 交易币种ID值
     */
    public function setCurrencyId($value): static
    {
        $this->currency_id = $value;
        return $this;
    }

    /**
     * 获取仓位方向
     */
    public function getSide(): PositionSide
    {
        return $this->side;
    }

    /**
     * 设置仓位方向
     *
     * @param PositionSide|int $value 仓位方向值
     */
    public function setSide($value): static
    {
        $this->side = $value;
        return $this;
    }

    /**
     * 获取保证金类型
     */
    public function getMarginType(): MarginType
    {
        return $this->margin_type;
    }

    /**
     * 设置保证金类型
     *
     * @param MarginType|int $value 保证金类型值
     */
    public function setMarginType($value): static
    {
        $this->margin_type = $value;
        return $this;
    }

    /**
     * 获取杠杆倍数
     */
    public function getLeverage(): float
    {
        return $this->leverage;
    }

    /**
     * 设置杠杆倍数
     *
     * @param float $value 杠杆倍数值
     */
    public function setLeverage($value): static
    {
        $this->leverage = $value;
        return $this;
    }

    /**
     * 获取持仓数量
     */
    public function getQuantity(): float
    {
        return (float)$this->quantity;
    }

    /**
     * 设置持仓数量
     *
     * @param float $value 持仓数量值
     */
    public function setQuantity($value): static
    {
        $this->quantity = $value;
        return $this;
    }

    /**
     * 获取可用数量
     */
    public function getAvailableQuantity(): float
    {
        return $this->available_quantity;
    }

    /**
     * 设置可用数量
     *
     * @param float $value 可用数量值
     */
    public function setAvailableQuantity($value): static
    {
        $this->available_quantity = $value;
        return $this;
    }

    /**
     * 获取冻结数量
     */
    public function getFrozenQuantity(): float
    {
        return $this->frozen_quantity;
    }

    /**
     * 设置冻结数量
     *
     * @param float $value 冻结数量值
     */
    public function setFrozenQuantity($value): static
    {
        $this->frozen_quantity = $value;
        return $this;
    }

    /**
     * 获取开仓均价
     */
    public function getEntryPrice(): float
    {
        return $this->entry_price;
    }

    /**
     * 设置开仓均价
     *
     * @param float $value 开仓均价值
     */
    public function setEntryPrice($value): static
    {
        $this->entry_price = $value;
        return $this;
    }

    /**
     * 获取保证金金额
     */
    public function getMarginAmount(): float
    {
        return $this->margin_amount;
    }

    /**
     * 设置保证金金额
     *
     * @param float $value 保证金金额值
     */
    public function setMarginAmount($value): static
    {
        $this->margin_amount = $value;
        return $this;
    }

    /**
     * 获取初始保证金
     */
    public function getInitialMargin(): float
    {
        return $this->initial_margin;
    }

    /**
     * 设置初始保证金
     *
     * @param float $value 初始保证金值
     */
    public function setInitialMargin($value): static
    {
        $this->initial_margin = $value;
        return $this;
    }

    /**
     * 获取维持保证金
     */
    public function getMaintenanceMargin(): float
    {
        return $this->maintenance_margin;
    }

    /**
     * 设置维持保证金
     *
     * @param float $value 维持保证金值
     */
    public function setMaintenanceMargin($value): static
    {
        $this->maintenance_margin = $value;
        return $this;
    }

    /**
     * 获取已实现盈亏
     */
    public function getRealizedPnl(): float
    {
        return $this->realized_pnl;
    }

    /**
     * 设置已实现盈亏
     *
     * @param float $value 已实现盈亏值
     */
    public function setRealizedPnl($value): static
    {
        $this->realized_pnl = $value;
        return $this;
    }

    /**
     * 获取状态
     */
    public function getStatus(): MarginPositionStatus
    {
        return $this->status;
    }

    /**
     * 设置状态
     *
     * @param MarginPositionStatus|int $value 状态值
     */
    public function setStatus($value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联交易币种
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }
}
