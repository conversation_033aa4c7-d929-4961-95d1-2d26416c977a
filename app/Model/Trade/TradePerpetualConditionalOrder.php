<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;
use App\Model\Currency\Currency;

/**
 * @property int $id 委托单ID
 * @property int $user_id 用户ID
 * @property int $currency_id 币种ID
 * @property int $conditional_type 委托类型：1-止盈，2-止损，3-计划委托，4-追踪委托
 * @property int $margin_mode 保证金模式：1-全仓，2-逐仓
 * @property int $side 方向：1-买入开多，2-卖出开空，3-买入平空，4-卖出平多
 * @property float $quantity 委托数量（币本位）
 * @property float $leverage 杠杆倍数
 * @property int $reduce_only 只减仓：0-否，1-是
 * @property int $time_in_force 有效期：1-GTC，2-IOC，3-FOK
 * @property float $trigger_price 触发价格
 * @property int $trigger_condition 触发条件：1-大于等于，2-小于等于
 * @property int $execution_type 执行方式：1-限价，2-市价
 * @property float $execution_price 执行价格（固定价格模式使用）
 * @property float $callback_rate 回调幅度（百分比）：止盈止损回调浮动模式、追踪委托回调幅度
 * @property int $execution_mode 执行模式：1-固定价格，2-回调浮动（止盈止损使用）
 * @property int $status 状态：1-等待触发，2-已触发，3-已执行，4-已撤销，5-已过期，6-执行失败
 * @property \Carbon\Carbon $triggered_at 触发时间
 * @property \Carbon\Carbon $executed_at 执行时间
 * @property int $executed_order_id 执行后的订单ID
 * @property string $failure_reason 失败原因
 * @property \Carbon\Carbon $expires_at 过期时间
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class TradePerpetualConditionalOrder extends Model
{
    /**
     * 委托单ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 委托类型：1-止盈，2-止损，3-计划委托，4-追踪委托
     */
    public const FIELD_CONDITIONAL_TYPE = 'conditional_type';
    /**
     * 保证金模式：1-全仓，2-逐仓
     */
    public const FIELD_MARGIN_MODE = 'margin_mode';
    /**
     * 方向：1-买入开多，2-卖出开空，3-买入平空，4-卖出平多
     */
    public const FIELD_SIDE = 'side';
    /**
     * 委托数量（币本位）
     */
    public const FIELD_QUANTITY = 'quantity';
    /**
     * 杠杆倍数
     */
    public const FIELD_LEVERAGE = 'leverage';
    /**
     * 只减仓：0-否，1-是
     */
    public const FIELD_REDUCE_ONLY = 'reduce_only';
    /**
     * 有效期：1-GTC，2-IOC，3-FOK
     */
    public const FIELD_TIME_IN_FORCE = 'time_in_force';
    /**
     * 触发价格
     */
    public const FIELD_TRIGGER_PRICE = 'trigger_price';
    /**
     * 触发条件：1-大于等于，2-小于等于
     */
    public const FIELD_TRIGGER_CONDITION = 'trigger_condition';
    /**
     * 执行方式：1-限价，2-市价
     */
    public const FIELD_EXECUTION_TYPE = 'execution_type';
    /**
     * 执行价格（固定价格模式使用）
     */
    public const FIELD_EXECUTION_PRICE = 'execution_price';
    /**
     * 回调幅度（百分比）：止盈止损回调浮动模式、追踪委托回调幅度
     */
    public const FIELD_CALLBACK_RATE = 'callback_rate';
    /**
     * 执行模式：1-固定价格，2-回调浮动（止盈止损使用）
     */
    public const FIELD_EXECUTION_MODE = 'execution_mode';
    /**
     * 状态：1-等待触发，2-已触发，3-已执行，4-已撤销，5-已过期，6-执行失败
     */
    public const FIELD_STATUS = 'status';
    /**
     * 触发时间
     */
    public const FIELD_TRIGGERED_AT = 'triggered_at';
    /**
     * 执行时间
     */
    public const FIELD_EXECUTED_AT = 'executed_at';
    /**
     * 执行后的订单ID
     */
    public const FIELD_EXECUTED_ORDER_ID = 'executed_order_id';
    /**
     * 失败原因
     */
    public const FIELD_FAILURE_REASON = 'failure_reason';
    /**
     * 过期时间
     */
    public const FIELD_EXPIRES_AT = 'expires_at';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_perpetual_conditional_order';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'conditional_type', 'margin_mode', 'side', 'quantity', 'leverage', 'reduce_only', 'time_in_force', 'trigger_price', 'trigger_condition', 'execution_type', 'execution_price', 'callback_rate', 'execution_mode', 'status', 'triggered_at', 'executed_at', 'executed_order_id', 'failure_reason', 'expires_at', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        // 委托单ID
        'id' => 'integer',
        // 用户ID
        'user_id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 委托类型：1-止盈，2-止损，3-计划委托，4-追踪委托
        'conditional_type' => 'integer',
        // 保证金模式：1-全仓，2-逐仓
        'margin_mode' => 'integer',
        // 方向：1-买入开多，2-卖出开空，3-买入平空，4-卖出平多
        'side' => 'integer',
        // 委托数量（币本位）
        'quantity' => 'float',
        // 杠杆倍数
        'leverage' => 'float',
        // 只减仓：0-否，1-是
        'reduce_only' => 'integer',
        // 有效期：1-GTC，2-IOC，3-FOK
        'time_in_force' => 'integer',
        // 触发价格
        'trigger_price' => 'float',
        // 触发条件：1-大于等于，2-小于等于
        'trigger_condition' => 'integer',
        // 执行方式：1-限价，2-市价
        'execution_type' => 'integer',
        // 执行价格（固定价格模式使用）
        'execution_price' => 'float',
        // 回调幅度（百分比）：止盈止损回调浮动模式、追踪委托回调幅度
        'callback_rate' => 'float',
        // 执行模式：1-固定价格，2-回调浮动（止盈止损使用）
        'execution_mode' => 'integer',
        // 状态：1-等待触发，2-已触发，3-已执行，4-已撤销，5-已过期，6-执行失败
        'status' => 'integer',
        // 执行后的订单ID
        'executed_order_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'expires_at' => 'datetime',
        'triggered_at' => 'datetime',
        'executed_at' => 'datetime'
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getConditionalType() : int
    {
        return $this->conditional_type;
    }
    public function setConditionalType($value) : object
    {
        $this->conditional_type = $value;
        return $this;
    }
    public function getMarginMode() : int
    {
        return $this->margin_mode;
    }
    public function setMarginMode($value) : object
    {
        $this->margin_mode = $value;
        return $this;
    }
    public function getSide() : int
    {
        return $this->side;
    }
    public function setSide($value) : object
    {
        $this->side = $value;
        return $this;
    }
    public function getQuantity() : float
    {
        return $this->quantity;
    }
    public function setQuantity($value) : object
    {
        $this->quantity = $value;
        return $this;
    }
    public function getLeverage() : float
    {
        return $this->leverage;
    }
    public function setLeverage($value) : object
    {
        $this->leverage = $value;
        return $this;
    }
    public function getReduceOnly() : int
    {
        return $this->reduce_only;
    }
    public function setReduceOnly($value) : object
    {
        $this->reduce_only = $value;
        return $this;
    }
    public function getTimeInForce() : int
    {
        return $this->time_in_force;
    }
    public function setTimeInForce($value) : object
    {
        $this->time_in_force = $value;
        return $this;
    }
    public function getTriggerPrice() : float
    {
        return $this->trigger_price;
    }
    public function setTriggerPrice($value) : object
    {
        $this->trigger_price = $value;
        return $this;
    }
    public function getTriggerCondition() : int
    {
        return $this->trigger_condition;
    }
    public function setTriggerCondition($value) : object
    {
        $this->trigger_condition = $value;
        return $this;
    }
    public function getExecutionType() : int
    {
        return $this->execution_type;
    }
    public function setExecutionType($value) : object
    {
        $this->execution_type = $value;
        return $this;
    }
    public function getExecutionPrice() : float
    {
        return $this->execution_price;
    }
    public function setExecutionPrice($value) : object
    {
        $this->execution_price = $value;
        return $this;
    }
    public function getCallbackRate() : float
    {
        return $this->callback_rate;
    }
    public function setCallbackRate($value) : object
    {
        $this->callback_rate = $value;
        return $this;
    }
    public function getExecutionMode() : int
    {
        return $this->execution_mode;
    }
    public function setExecutionMode($value) : object
    {
        $this->execution_mode = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getTriggeredAt() : mixed
    {
        return $this->triggered_at;
    }
    public function setTriggeredAt($value) : object
    {
        $this->triggered_at = $value;
        return $this;
    }
    public function getExecutedAt() : mixed
    {
        return $this->executed_at;
    }
    public function setExecutedAt($value) : object
    {
        $this->executed_at = $value;
        return $this;
    }
    public function getExecutedOrderId() : int
    {
        return $this->executed_order_id;
    }
    public function setExecutedOrderId($value) : object
    {
        $this->executed_order_id = $value;
        return $this;
    }
    public function getFailureReason() : string
    {
        return $this->failure_reason;
    }
    public function setFailureReason($value) : object
    {
        $this->failure_reason = $value;
        return $this;
    }
    public function getExpiresAt() : mixed
    {
        return $this->expires_at;
    }
    public function setExpiresAt($value) : object
    {
        $this->expires_at = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 币种关联
     */
    public function currency(): \Hyperf\Database\Model\Relations\BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }
}
