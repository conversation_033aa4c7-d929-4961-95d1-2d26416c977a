<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 记录ID
 * @property int $user_id 用户ID
 * @property int $position_id 仓位ID
 * @property int $currency_id 币种ID
 * @property int $margin_mode 保证金模式：1-全仓，2-逐仓
 * @property int $liquidation_type 强平类型：1-保证金不足，2-ADL自动减仓
 * @property int $trigger_source 触发来源：1-系统自动，2-人工干预
 * @property int $liquidation_order_id 强平执行订单ID
 * @property float $original_quantity 原始持仓数量（币本位）
 * @property float $liquidated_quantity 强平数量（币本位）
 * @property float $liquidation_price 强平价格
 * @property float $mark_price 触发时标记价格
 * @property float $margin_ratio 强平时保证金率
 * @property float $liquidation_fee 强平手续费
 * @property float $insurance_fund 保险基金使用
 * @property float $bankruptcy_amount 穿仓金额
 * @property string $trigger_time 强平触发时间
 * @property string $completed_time 强平完成时间
 * @property int $status 状态：1-待执行，2-执行中，3-已完成，4-执行失败
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class TradePerpetualLiquidation extends Model
{
    /**
     * 记录ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 仓位ID
     */
    public const FIELD_POSITION_ID = 'position_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 保证金模式：1-全仓，2-逐仓
     */
    public const FIELD_MARGIN_MODE = 'margin_mode';
    /**
     * 强平类型：1-保证金不足，2-ADL自动减仓
     */
    public const FIELD_LIQUIDATION_TYPE = 'liquidation_type';
    /**
     * 触发来源：1-系统自动，2-人工干预
     */
    public const FIELD_TRIGGER_SOURCE = 'trigger_source';
    /**
     * 强平执行订单ID
     */
    public const FIELD_LIQUIDATION_ORDER_ID = 'liquidation_order_id';
    /**
     * 原始持仓数量（币本位）
     */
    public const FIELD_ORIGINAL_QUANTITY = 'original_quantity';
    /**
     * 强平数量（币本位）
     */
    public const FIELD_LIQUIDATED_QUANTITY = 'liquidated_quantity';
    /**
     * 强平价格
     */
    public const FIELD_LIQUIDATION_PRICE = 'liquidation_price';
    /**
     * 触发时标记价格
     */
    public const FIELD_MARK_PRICE = 'mark_price';
    /**
     * 强平时保证金率
     */
    public const FIELD_MARGIN_RATIO = 'margin_ratio';
    /**
     * 强平手续费
     */
    public const FIELD_LIQUIDATION_FEE = 'liquidation_fee';
    /**
     * 保险基金使用
     */
    public const FIELD_INSURANCE_FUND = 'insurance_fund';
    /**
     * 穿仓金额
     */
    public const FIELD_BANKRUPTCY_AMOUNT = 'bankruptcy_amount';
    /**
     * 强平触发时间
     */
    public const FIELD_TRIGGER_TIME = 'trigger_time';
    /**
     * 强平完成时间
     */
    public const FIELD_COMPLETED_TIME = 'completed_time';
    /**
     * 状态：1-待执行，2-执行中，3-已完成，4-执行失败
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_perpetual_liquidation';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'position_id', 'currency_id', 'margin_mode', 'liquidation_type', 'trigger_source', 'liquidation_order_id', 'original_quantity', 'liquidated_quantity', 'liquidation_price', 'mark_price', 'margin_ratio', 'liquidation_fee', 'insurance_fund', 'bankruptcy_amount', 'trigger_time', 'completed_time', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        // 记录ID
        'id' => 'integer',
        // 用户ID
        'user_id' => 'integer',
        // 仓位ID
        'position_id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 保证金模式：1-全仓，2-逐仓
        'margin_mode' => 'integer',
        // 强平类型：1-保证金不足，2-ADL自动减仓
        'liquidation_type' => 'integer',
        // 触发来源：1-系统自动，2-人工干预
        'trigger_source' => 'integer',
        // 强平执行订单ID
        'liquidation_order_id' => 'integer',
        // 原始持仓数量（币本位）
        'original_quantity' => 'float',
        // 强平数量（币本位）
        'liquidated_quantity' => 'float',
        // 强平价格
        'liquidation_price' => 'float',
        // 触发时标记价格
        'mark_price' => 'float',
        // 强平时保证金率
        'margin_ratio' => 'float',
        // 强平手续费
        'liquidation_fee' => 'float',
        // 保险基金使用
        'insurance_fund' => 'float',
        // 穿仓金额
        'bankruptcy_amount' => 'float',
        // 状态：1-待执行，2-执行中，3-已完成，4-执行失败
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getPositionId() : int
    {
        return $this->position_id;
    }
    public function setPositionId($value) : object
    {
        $this->position_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getMarginMode() : int
    {
        return $this->margin_mode;
    }
    public function setMarginMode($value) : object
    {
        $this->margin_mode = $value;
        return $this;
    }
    public function getLiquidationType() : int
    {
        return $this->liquidation_type;
    }
    public function setLiquidationType($value) : object
    {
        $this->liquidation_type = $value;
        return $this;
    }
    public function getTriggerSource() : int
    {
        return $this->trigger_source;
    }
    public function setTriggerSource($value) : object
    {
        $this->trigger_source = $value;
        return $this;
    }
    public function getLiquidationOrderId() : int
    {
        return $this->liquidation_order_id;
    }
    public function setLiquidationOrderId($value) : object
    {
        $this->liquidation_order_id = $value;
        return $this;
    }
    public function getOriginalQuantity() : float
    {
        return $this->original_quantity;
    }
    public function setOriginalQuantity($value) : object
    {
        $this->original_quantity = $value;
        return $this;
    }
    public function getLiquidatedQuantity() : float
    {
        return $this->liquidated_quantity;
    }
    public function setLiquidatedQuantity($value) : object
    {
        $this->liquidated_quantity = $value;
        return $this;
    }
    public function getLiquidationPrice() : float
    {
        return $this->liquidation_price;
    }
    public function setLiquidationPrice($value) : object
    {
        $this->liquidation_price = $value;
        return $this;
    }
    public function getMarkPrice() : float
    {
        return $this->mark_price;
    }
    public function setMarkPrice($value) : object
    {
        $this->mark_price = $value;
        return $this;
    }
    public function getMarginRatio() : float
    {
        return $this->margin_ratio;
    }
    public function setMarginRatio($value) : object
    {
        $this->margin_ratio = $value;
        return $this;
    }
    public function getLiquidationFee() : float
    {
        return $this->liquidation_fee;
    }
    public function setLiquidationFee($value) : object
    {
        $this->liquidation_fee = $value;
        return $this;
    }
    public function getInsuranceFund() : float
    {
        return $this->insurance_fund;
    }
    public function setInsuranceFund($value) : object
    {
        $this->insurance_fund = $value;
        return $this;
    }
    public function getBankruptcyAmount() : float
    {
        return $this->bankruptcy_amount;
    }
    public function setBankruptcyAmount($value) : object
    {
        $this->bankruptcy_amount = $value;
        return $this;
    }
    public function getTriggerTime() : mixed
    {
        return $this->trigger_time;
    }
    public function setTriggerTime($value) : object
    {
        $this->trigger_time = $value;
        return $this;
    }
    public function getCompletedTime() : mixed
    {
        return $this->completed_time;
    }
    public function setCompletedTime($value) : object
    {
        $this->completed_time = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
