<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆交易订单模型
 */

namespace App\Model\Trade;

use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\Trade\Margin\PositionSide;
use App\Model\Enums\Trade\Margin\ReduceOnly;
use App\Model\Match\MatchOrder;
use App\Model\Currency\Currency;
use App\Model\User\User;
use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int $user_id 用户ID
 * @property int $currency_id 交易币种ID
 * @property int $match_order_id 撮合引擎订单ID
 * @property int $margin_type 保证金类型：1-全仓，2-逐仓
 * @property string $leverage 杠杆倍数
 * @property int $position_side 仓位方向：1-做多，2-做空
 * @property string $margin_amount 保证金金额
 * @property int $reduce_only 只减仓：0-否，1-是
 * @property float $frozen_amount 冻结资金
 * @property float $used_amount 使用资金
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * @property-read null|User $user 
 * @property-read null|Currency $currency 
 * @property-read null|MatchOrder $matchOrder 
 */
final class TradeMarginOrder extends Model
{
    /**
     * 冻结资金
     */
    public const FIELD_FROZEN_AMOUNT = 'frozen_amount';
    /**
     * 使用资金
     */
    public const FIELD_USED_AMOUNT = 'used_amount';
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 交易币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 撮合引擎订单ID
     */
    public const FIELD_MATCH_ORDER_ID = 'match_order_id';
    /**
     * 保证金类型：1-逐仓，2-全仓
     */
    public const FIELD_MARGIN_TYPE = 'margin_type';
    /**
     * 杠杆倍数
     */
    public const FIELD_LEVERAGE = 'leverage';
    /**
     * 仓位方向：1-做多，2-做空
     */
    public const FIELD_POSITION_SIDE = 'position_side';
    /**
     * 保证金金额
     */
    public const FIELD_MARGIN_AMOUNT = 'margin_amount';
    /**
     * 只减仓：0-否，1-是
     */
    public const FIELD_REDUCE_ONLY = 'reduce_only';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_margin_order';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'match_order_id', 'margin_type', 'leverage', 'position_side', 'margin_amount', 'reduce_only', 'frozen_amount', 'used_amount', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer', 
        'currency_id' => 'integer',
        'match_order_id' => 'integer',
        'margin_type' => MarginType::class,
        'leverage' => 'float',
        'position_side' => PositionSide::class,
        'margin_amount' => 'float',
        'reduce_only' => ReduceOnly::class,
        'frozen_amount' => 'float',
        'used_amount' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取交易币种ID
     */
    public function getCurrencyId(): int
    {
        return $this->currency_id;
    }

    /**
     * 设置交易币种ID
     *
     * @param int $value 交易币种ID值
     */
    public function setCurrencyId($value): static
    {
        $this->currency_id = $value;
        return $this;
    }

    /**
     * 获取撮合引擎订单ID
     */
    public function getMatchOrderId(): int
    {
        return $this->match_order_id;
    }

    /**
     * 设置撮合引擎订单ID
     *
     * @param int $value 撮合引擎订单ID值
     */
    public function setMatchOrderId($value): static
    {
        $this->match_order_id = $value;
        return $this;
    }

    /**
     * 获取保证金类型
     */
    public function getMarginType(): MarginType
    {
        return $this->margin_type;
    }

    /**
     * 设置保证金类型
     *
     * @param MarginType|int $value 保证金类型值
     */
    public function setMarginType($value): static
    {
        $this->margin_type = $value;
        return $this;
    }

    /**
     * 获取杠杆倍数
     */
    public function getLeverage(): float
    {
        return $this->leverage;
    }

    /**
     * 设置杠杆倍数
     *
     * @param float $value 杠杆倍数值
     */
    public function setLeverage($value): static
    {
        $this->leverage = $value;
        return $this;
    }

    /**
     * 获取仓位方向
     */
    public function getPositionSide(): PositionSide
    {
        return $this->position_side;
    }

    /**
     * 设置仓位方向
     *
     * @param PositionSide|int $value 仓位方向值
     */
    public function setPositionSide($value): static
    {
        $this->position_side = $value;
        return $this;
    }

    /**
     * 获取保证金金额
     */
    public function getMarginAmount(): float
    {
        return $this->margin_amount;
    }

    /**
     * 设置保证金金额
     *
     * @param float $value 保证金金额值
     */
    public function setMarginAmount($value): static
    {
        $this->margin_amount = $value;
        return $this;
    }

    /**
     * 获取只减仓标识
     */
    public function getReduceOnly(): ReduceOnly
    {
        return $this->reduce_only;
    }

    /**
     * 设置只减仓标识
     *
     * @param ReduceOnly|int $value 只减仓标识值
     */
    public function setReduceOnly($value): static
    {
        $this->reduce_only = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联交易币种
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }

    /**
     * 关联撮合引擎订单
     */
    public function matchOrder()
    {
        return $this->belongsTo(MatchOrder::class, 'match_order_id', 'id');
    }

    /**
     * 获取冻结资金
     */
    public function getFrozenAmount(): float
    {
        return $this->frozen_amount;
    }

    /**
     * 设置冻结资金
     *
     * @param float $value 冻结资金值
     */
    public function setFrozenAmount($value): static
    {
        $this->frozen_amount = $value;
        return $this;
    }

    /**
     * 获取使用资金
     */
    public function getUsedAmount(): float
    {
        return $this->used_amount;
    }

    /**
     * 设置使用资金
     *
     * @param float $value 使用资金值
     */
    public function setUsedAmount($value): static
    {
        $this->used_amount = $value;
        return $this;
    }
}
