<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 记录ID
 * @property int $contract_id 合约ID
 * @property float $funding_rate 资金费率
 * @property string $funding_time 资金费率生效时间
 * @property string $next_funding_time 下次资金费率时间
 * @property float $premium_rate 溢价率
 * @property float $interest_rate 利率（日化0.01%）
 * @property float $mark_price 标记价格
 * @property float $index_price 指数价格
 * @property \Carbon\Carbon $created_at 
 */
class TradePerpetualFundingRate extends Model
{
    /**
     * 记录ID
     */
    public const FIELD_ID = 'id';
    /**
     * 合约ID
     */
    public const FIELD_CONTRACT_ID = 'contract_id';
    /**
     * 资金费率
     */
    public const FIELD_FUNDING_RATE = 'funding_rate';
    /**
     * 资金费率生效时间
     */
    public const FIELD_FUNDING_TIME = 'funding_time';
    /**
     * 下次资金费率时间
     */
    public const FIELD_NEXT_FUNDING_TIME = 'next_funding_time';
    /**
     * 溢价率
     */
    public const FIELD_PREMIUM_RATE = 'premium_rate';
    /**
     * 利率（日化0.01%）
     */
    public const FIELD_INTEREST_RATE = 'interest_rate';
    /**
     * 标记价格
     */
    public const FIELD_MARK_PRICE = 'mark_price';
    /**
     * 指数价格
     */
    public const FIELD_INDEX_PRICE = 'index_price';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    public bool $timestamps = false;
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_perpetual_funding_rate';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'contract_id', 'funding_rate', 'funding_time', 'next_funding_time', 'premium_rate', 'interest_rate', 'mark_price', 'index_price', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        // 记录ID
        'id' => 'integer',
        // 合约ID
        'contract_id' => 'integer',
        // 资金费率
        'funding_rate' => 'float',
        // 溢价率
        'premium_rate' => 'float',
        // 利率（日化0.01%）
        'interest_rate' => 'float',
        // 标记价格
        'mark_price' => 'float',
        // 指数价格
        'index_price' => 'float',
        'created_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getContractId() : int
    {
        return $this->contract_id;
    }
    public function setContractId($value) : object
    {
        $this->contract_id = $value;
        return $this;
    }
    public function getFundingRate() : float
    {
        return $this->funding_rate;
    }
    public function setFundingRate($value) : object
    {
        $this->funding_rate = $value;
        return $this;
    }
    public function getFundingTime() : mixed
    {
        return $this->funding_time;
    }
    public function setFundingTime($value) : object
    {
        $this->funding_time = $value;
        return $this;
    }
    public function getNextFundingTime() : mixed
    {
        return $this->next_funding_time;
    }
    public function setNextFundingTime($value) : object
    {
        $this->next_funding_time = $value;
        return $this;
    }
    public function getPremiumRate() : float
    {
        return $this->premium_rate;
    }
    public function setPremiumRate($value) : object
    {
        $this->premium_rate = $value;
        return $this;
    }
    public function getInterestRate() : float
    {
        return $this->interest_rate;
    }
    public function setInterestRate($value) : object
    {
        $this->interest_rate = $value;
        return $this;
    }
    public function getMarkPrice() : float
    {
        return $this->mark_price;
    }
    public function setMarkPrice($value) : object
    {
        $this->mark_price = $value;
        return $this;
    }
    public function getIndexPrice() : float
    {
        return $this->index_price;
    }
    public function setIndexPrice($value) : object
    {
        $this->index_price = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
}
