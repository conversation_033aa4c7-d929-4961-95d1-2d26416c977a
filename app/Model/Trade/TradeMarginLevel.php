<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $currency_id 交易对id
 * @property float $margin_max 持仓名义价值区间
 * @property float $margin_min 持仓名义价值区间
 * @property int $leverage_min 最低杠杆
 * @property int $leverage_max 最高杠杆
 * @property float $margin_rate 维持保证金比率
 * @property float $cum_amount 维持保证金速算额
 * @property int $level 层级
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class TradeMarginLevel extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 交易对id
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 最大开仓保证金
     */
    public const FIELD_MARGIN_MAX = 'margin_max';
    /**
     * 最小开仓保证金
     */
    public const FIELD_MARGIN_MIN = 'margin_min';
    /**
     * 最小开仓杠杆
     */
    public const FIELD_LEVERAGE_MIN = 'leverage_min';
    /**
     * 最大杠杆
     */
    public const FIELD_LEVERAGE_MAX = 'leverage_max';
    /**
     * 维持保证金比率
     */
    public const FIELD_MARGIN_RATE = 'margin_rate';
    /**
     * 维持保证金
     */
    public const FIELD_CUM_AMOUNT = 'cum_amount';
    /**
     * 层级
     */
    public const FIELD_LEVEL = 'level';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_margin_level';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'currency_id', 'margin_max', 'margin_min', 'leverage_min', 'leverage_max', 'margin_rate', 'cum_amount', 'level', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        // 交易对id
        'currency_id' => 'integer',
        // 持仓名义价值区间
        'margin_max' => 'float',
        // 持仓名义价值区间
        'margin_min' => 'float',
        // 最低杠杆
        'leverage_min' => 'integer',
        // 最高杠杆
        'leverage_max' => 'integer',
        // 维持保证金比率
        'margin_rate' => 'float',
        // 维持保证金速算额
        'cum_amount' => 'float',
        // 层级
        'level' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getMarginMax() : float
    {
        return $this->margin_max;
    }
    public function setMarginMax($value) : object
    {
        $this->margin_max = $value;
        return $this;
    }
    public function getMarginMin() : float
    {
        return $this->margin_min;
    }
    public function setMarginMin($value) : object
    {
        $this->margin_min = $value;
        return $this;
    }
    public function getLeverageMin() : int
    {
        return $this->leverage_min;
    }
    public function setLeverageMin($value) : object
    {
        $this->leverage_min = $value;
        return $this;
    }
    public function getLeverageMax() : int
    {
        return $this->leverage_max;
    }
    public function setLeverageMax($value) : object
    {
        $this->leverage_max = $value;
        return $this;
    }
    public function getMarginRate() : float
    {
        return $this->margin_rate;
    }
    public function setMarginRate($value) : object
    {
        $this->margin_rate = $value;
        return $this;
    }
    public function getCumAmount() : float
    {
        return $this->cum_amount;
    }
    public function setCumAmount($value) : object
    {
        $this->cum_amount = $value;
        return $this;
    }
    public function getLevel() : float
    {
        return $this->level;
    }
    public function setLevel($value) : object
    {
        $this->level = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
