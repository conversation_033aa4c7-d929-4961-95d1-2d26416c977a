<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 委托订单触发条件枚举
 */

namespace App\Model\Trade\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum TriggerCondition: int
{
    use EnumConstantsTrait;

    /**
     * 大于等于
     */
    #[Message('trade.enums.trigger_condition.1')]
    case GREATER_THAN_OR_EQUAL = 1;

    /**
     * 小于等于
     */
    #[Message('trade.enums.trigger_condition.2')]
    case LESS_THAN_OR_EQUAL = 2;
} 