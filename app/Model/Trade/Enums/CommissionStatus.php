<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 委托订单状态枚举
 */

namespace App\Model\Trade\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum CommissionStatus: int
{
    use EnumConstantsTrait;

    /**
     * 等待执行
     */
    #[Message('trade.enums.commission_status.0')]
    case PENDING = 0;

    /**
     * 已触发
     */
    #[Message('trade.enums.commission_status.1')]
    case TRIGGERED = 1;

    /**
     * 已完成
     */
    #[Message('trade.enums.commission_status.2')]
    case COMPLETED = 2;

    /**
     * 已取消
     */
    #[Message('trade.enums.commission_status.-1')]
    case CANCELLED = -1;

    /**
     * 触发失败
     */
    #[Message('trade.enums.commission_status.-2')]
    case TRIGGER_FAILED = -2;
} 