<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 委托订单类型枚举
 */

namespace App\Model\Trade\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum CommissionOrderType: int
{
    use EnumConstantsTrait;

    /**
     * 止盈止损
     */
    #[Message('trade.enums.commission_order_type.1')]
    case STOP_PROFIT_LOSS = 1;

    /**
     * 计划委托
     */
    #[Message('trade.enums.commission_order_type.2')]
    case PLAN_ORDER = 2;

    /**
     * 追踪委托
     */
    #[Message('trade.enums.commission_order_type.3')]
    case TRAILING_ORDER = 3;
} 