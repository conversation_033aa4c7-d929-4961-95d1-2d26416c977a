<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 委托订单触发类型枚举
 */

namespace App\Model\Trade\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum TriggerType: int
{
    use EnumConstantsTrait;

    /**
     * 限价单
     */
    #[Message('trade.enums.trigger_type.1')]
    case LIMIT = 1;

    /**
     * 市价单
     */
    #[Message('trade.enums.trigger_type.2')]
    case MARKET = 2;
} 