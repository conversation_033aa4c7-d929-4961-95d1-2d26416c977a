<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 记录ID
 * @property int $currency_id 币种ID
 * @property int $trigger_user_id 触发用户ID
 * @property int $target_user_id 目标用户ID
 * @property int $target_position_id 目标仓位ID
 * @property int $margin_mode 保证金模式：1-全仓，2-逐仓
 * @property float $adl_quantity 减仓数量（币本位）
 * @property float $adl_price 减仓价格
 * @property float $compensation_amount 补偿金额
 * @property int $status 状态：1-执行中，2-已完成，3-失败
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class TradePerpetualAdl extends Model
{
    /**
     * 记录ID
     */
    public const FIELD_ID = 'id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 触发用户ID
     */
    public const FIELD_TRIGGER_USER_ID = 'trigger_user_id';
    /**
     * 目标用户ID
     */
    public const FIELD_TARGET_USER_ID = 'target_user_id';
    /**
     * 目标仓位ID
     */
    public const FIELD_TARGET_POSITION_ID = 'target_position_id';
    /**
     * 保证金模式：1-全仓，2-逐仓
     */
    public const FIELD_MARGIN_MODE = 'margin_mode';
    /**
     * 减仓数量（币本位）
     */
    public const FIELD_ADL_QUANTITY = 'adl_quantity';
    /**
     * 减仓价格
     */
    public const FIELD_ADL_PRICE = 'adl_price';
    /**
     * 补偿金额
     */
    public const FIELD_COMPENSATION_AMOUNT = 'compensation_amount';
    /**
     * 状态：1-执行中，2-已完成，3-失败
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_perpetual_adl';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'currency_id', 'trigger_user_id', 'target_user_id', 'target_position_id', 'margin_mode', 'adl_quantity', 'adl_price', 'compensation_amount', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        // 记录ID
        'id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 触发用户ID
        'trigger_user_id' => 'integer',
        // 目标用户ID
        'target_user_id' => 'integer',
        // 目标仓位ID
        'target_position_id' => 'integer',
        // 保证金模式：1-全仓，2-逐仓
        'margin_mode' => 'integer',
        // 减仓数量（币本位）
        'adl_quantity' => 'float',
        // 减仓价格
        'adl_price' => 'float',
        // 补偿金额
        'compensation_amount' => 'float',
        // 状态：1-执行中，2-已完成，3-失败
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getTriggerUserId() : int
    {
        return $this->trigger_user_id;
    }
    public function setTriggerUserId($value) : object
    {
        $this->trigger_user_id = $value;
        return $this;
    }
    public function getTargetUserId() : int
    {
        return $this->target_user_id;
    }
    public function setTargetUserId($value) : object
    {
        $this->target_user_id = $value;
        return $this;
    }
    public function getTargetPositionId() : int
    {
        return $this->target_position_id;
    }
    public function setTargetPositionId($value) : object
    {
        $this->target_position_id = $value;
        return $this;
    }
    public function getMarginMode() : int
    {
        return $this->margin_mode;
    }
    public function setMarginMode($value) : object
    {
        $this->margin_mode = $value;
        return $this;
    }
    public function getAdlQuantity() : float
    {
        return $this->adl_quantity;
    }
    public function setAdlQuantity($value) : object
    {
        $this->adl_quantity = $value;
        return $this;
    }
    public function getAdlPrice() : float
    {
        return $this->adl_price;
    }
    public function setAdlPrice($value) : object
    {
        $this->adl_price = $value;
        return $this;
    }
    public function getCompensationAmount() : float
    {
        return $this->compensation_amount;
    }
    public function setCompensationAmount($value) : object
    {
        $this->compensation_amount = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
