<?php

declare(strict_types=1);

namespace App\Model\Trade;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 主键
 * @property int $user_id 用户ID
 * @property int $currency_id 币种ID
 * @property int $margin_type 保证金类型:1=全仓,2=逐仓
 * @property int $position_side 仓位方向:1=做多,2=做空
 * @property int $order_type 委托类型:1=止盈单,2=止损单
 * @property float $trigger_price 触发价格
 * @property float $close_quantity 平仓数量
 * @property int $trigger_type 触发条件:1=最新价触发,2=标记价触发
 * @property int $execution_type 执行类型:1=市价,2=限价
 * @property float|null $execution_price 执行价格(限价时必填)
 * @property int $status 状态:1=等待触发,2=已触发,3=已撤销,4=触发失败
 * @property \Carbon\Carbon $trigger_time 触发时间
 * @property int $triggered_order_id 触发后生成的杠杆订单ID
 * @property string $failure_reason 触发失败原因
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 */
class TradeMarginConditionalOrder extends Model
{
    /**
     * 主键
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 保证金类型:1=全仓,2=逐仓
     */
    public const FIELD_MARGIN_TYPE = 'margin_type';
    /**
     * 仓位方向:1=做多,2=做空
     */
    public const FIELD_POSITION_SIDE = 'position_side';
    /**
     * 委托类型:1=止盈单,2=止损单
     */
    public const FIELD_ORDER_TYPE = 'order_type';
    /**
     * 触发价格
     */
    public const FIELD_TRIGGER_PRICE = 'trigger_price';
    /**
     * 平仓数量
     */
    public const FIELD_CLOSE_QUANTITY = 'close_quantity';
    /**
     * 触发条件:1=最新价触发,2=标记价触发
     */
    public const FIELD_TRIGGER_TYPE = 'trigger_type';
    /**
     * 执行类型:1=市价,2=限价
     */
    public const FIELD_EXECUTION_TYPE = 'execution_type';
    /**
     * 执行价格(限价时必填)
     */
    public const FIELD_EXECUTION_PRICE = 'execution_price';
    /**
     * 状态:1=等待触发,2=已触发,3=已撤销,4=触发失败
     */
    public const FIELD_STATUS = 'status';
    /**
     * 触发时间
     */
    public const FIELD_TRIGGER_TIME = 'trigger_time';
    /**
     * 触发后生成的杠杆订单ID
     */
    public const FIELD_TRIGGERED_ORDER_ID = 'triggered_order_id';
    /**
     * 触发失败原因
     */
    public const FIELD_FAILURE_REASON = 'failure_reason';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'trade_margin_conditional_orders';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'margin_type', 'position_side', 'order_type', 'trigger_price', 'close_quantity', 'trigger_type', 'execution_type', 'execution_price', 'status', 'trigger_time', 'triggered_order_id', 'failure_reason', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        // 主键
        'id' => 'integer',
        // 用户ID
        'user_id' => 'integer',
        // 币种ID
        'currency_id' => 'integer',
        // 保证金类型:1=全仓,2=逐仓
        'margin_type' => 'integer',
        // 仓位方向:1=做多,2=做空
        'position_side' => 'integer',
        // 委托类型:1=止盈单,2=止损单
        'order_type' => 'integer',
        // 触发价格
        'trigger_price' => 'float',
        // 平仓数量
        'close_quantity' => 'float',
        // 触发条件:1=最新价触发,2=标记价触发
        'trigger_type' => 'integer',
        // 执行类型:1=市价,2=限价
        'execution_type' => 'integer',
        // 执行价格(限价时必填)
        'execution_price' => 'float',
        // 状态:1=等待触发,2=已触发,3=已撤销,4=触发失败
        'status' => 'integer',
        // 触发后生成的杠杆订单ID
        'triggered_order_id' => 'integer',
        // 创建时间
        'trigger_time' => 'datetime',
        'created_at' => 'datetime',
        // 更新时间
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getMarginType() : int
    {
        return $this->margin_type;
    }
    public function setMarginType($value) : object
    {
        $this->margin_type = $value;
        return $this;
    }
    public function getPositionSide() : int
    {
        return $this->position_side;
    }
    public function setPositionSide($value) : object
    {
        $this->position_side = $value;
        return $this;
    }
    public function getOrderType() : int
    {
        return $this->order_type;
    }
    public function setOrderType($value) : object
    {
        $this->order_type = $value;
        return $this;
    }
    public function getTriggerPrice() : float
    {
        return $this->trigger_price;
    }
    public function setTriggerPrice($value) : object
    {
        $this->trigger_price = $value;
        return $this;
    }
    public function getCloseQuantity() : float
    {
        return $this->close_quantity;
    }
    public function setCloseQuantity($value) : object
    {
        $this->close_quantity = $value;
        return $this;
    }
    public function getTriggerType() : int
    {
        return $this->trigger_type;
    }
    public function setTriggerType($value) : object
    {
        $this->trigger_type = $value;
        return $this;
    }
    public function getExecutionType() : int
    {
        return $this->execution_type;
    }
    public function setExecutionType($value) : object
    {
        $this->execution_type = $value;
        return $this;
    }
    public function getExecutionPrice() : ?float
    {
        return $this->execution_price;
    }
    public function setExecutionPrice($value) : object
    {
        $this->execution_price = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getTriggerTime() : mixed
    {
        return $this->trigger_time;
    }
    public function setTriggerTime($value) : object
    {
        $this->trigger_time = $value;
        return $this;
    }
    public function getTriggeredOrderId() : int
    {
        return $this->triggered_order_id;
    }
    public function setTriggeredOrderId($value) : object
    {
        $this->triggered_order_id = $value;
        return $this;
    }
    public function getFailureReason() : string
    {
        return $this->failure_reason;
    }
    public function setFailureReason($value) : object
    {
        $this->failure_reason = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
