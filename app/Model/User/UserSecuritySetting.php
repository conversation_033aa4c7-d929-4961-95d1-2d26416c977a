<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户安全设置模型
 */

namespace App\Model\User;

use App\QueryBuilder\Model;
use Carbon\Carbon;

/**
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property string|null $google_secret 谷歌验证器密钥
 * @property int $google_enabled 是否启用谷歌验证器:0=否,1=是
 * @property int $email_verification_enabled 是否启用邮箱验证:0=否,1=是
 * @property int $phone_verification_enabled 是否启用手机验证:0=否,1=是
 * @property int $biometric_enabled 是否启用生物识别:0=否,1=是
 * @property array|null $biometric_settings 生物识别设置
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
final class UserSecuritySetting extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 谷歌验证器密钥
     */
    public const FIELD_GOOGLE_SECRET = 'google_secret';
    /**
     * 是否启用谷歌验证器:0=否,1=是
     */
    public const FIELD_GOOGLE_ENABLED = 'google_enabled';
    /**
     * 是否启用邮箱验证:0=否,1=是
     */
    public const FIELD_EMAIL_VERIFICATION_ENABLED = 'email_verification_enabled';
    /**
     * 是否启用手机验证:0=否,1=是
     */
    public const FIELD_PHONE_VERIFICATION_ENABLED = 'phone_verification_enabled';
    /**
     * 是否启用生物识别:0=否,1=是
     */
    public const FIELD_BIOMETRIC_ENABLED = 'biometric_enabled';
    /**
     * 生物识别设置
     */
    public const FIELD_BIOMETRIC_SETTINGS = 'biometric_settings';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_security_setting';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 主键ID
        'user_id', // 用户ID
        'google_secret', // 谷歌验证器密钥
        'google_enabled', // 是否启用谷歌验证器
        'email_verification_enabled', // 是否启用邮箱验证
        'phone_verification_enabled', // 是否启用手机验证
        'biometric_enabled', // 是否启用生物识别
        'biometric_settings', // 生物识别设置
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'user_id' => 'integer', // 用户ID
        'google_enabled' => 'integer', // 是否启用谷歌验证器
        'email_verification_enabled' => 'integer', // 是否启用邮箱验证
        'phone_verification_enabled' => 'integer', // 是否启用手机验证
        'biometric_enabled' => 'integer', // 是否启用生物识别
        'biometric_settings' => 'array', // 生物识别设置
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     */
    public function setUserId(int $value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取谷歌验证器密钥
     */
    public function getGoogleSecret(): ?string
    {
        return $this->google_secret;
    }

    /**
     * 设置谷歌验证器密钥
     */
    public function setGoogleSecret(?string $value): static
    {
        $this->google_secret = $value;
        return $this;
    }

    /**
     * 获取是否启用谷歌验证器
     */
    public function getGoogleEnabled(): int
    {
        return $this->google_enabled;
    }

    /**
     * 设置是否启用谷歌验证器
     */
    public function setGoogleEnabled(int $value): static
    {
        $this->google_enabled = $value;
        return $this;
    }

    /**
     * 获取是否启用邮箱验证
     */
    public function getEmailVerificationEnabled(): int
    {
        return $this->email_verification_enabled;
    }

    /**
     * 设置是否启用邮箱验证
     */
    public function setEmailVerificationEnabled(int $value): static
    {
        $this->email_verification_enabled = $value;
        return $this;
    }

    /**
     * 获取是否启用手机验证
     */
    public function getPhoneVerificationEnabled(): int
    {
        return $this->phone_verification_enabled;
    }

    /**
     * 设置是否启用手机验证
     */
    public function setPhoneVerificationEnabled(int $value): static
    {
        $this->phone_verification_enabled = $value;
        return $this;
    }

    /**
     * 获取是否启用生物识别
     */
    public function getBiometricEnabled(): int
    {
        return $this->biometric_enabled;
    }

    /**
     * 设置是否启用生物识别
     */
    public function setBiometricEnabled(int $value): static
    {
        $this->biometric_enabled = $value;
        return $this;
    }

    /**
     * 获取生物识别设置
     */
    public function getBiometricSettings(): ?array
    {
        return $this->biometric_settings;
    }

    /**
     * 设置生物识别设置
     */
    public function setBiometricSettings(?array $value): static
    {
        $this->biometric_settings = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
