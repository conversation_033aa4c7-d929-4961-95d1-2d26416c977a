<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户KYC认证模型
 */

namespace App\Model\User;

use App\Model\User\Enums\KycStatus;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Filesystem\FilesystemFactory;

/**
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property string $first_name 名
 * @property string|null $middle_name 中间名
 * @property string $last_name 姓
 * @property KycStatus $status 认证状态:0=未提交,1=审核中,2=已通过,3=已拒绝
 * @property string|null $document_type 证件类型:passport=护照,id_card=身份证,driver_license=驾驶证
 * @property string|null $document_number 证件号码
 * @property string|null $document_country 证件签发国家
 * @property string|null $document_expiry_date 证件到期日期
 * @property string|null $document_front_image 证件正面照片
 * @property string|null $document_back_image 证件背面照片
 * @property string|null $selfie_image 手持证件自拍照
 * @property string|null $birthday 生日
 * @property string|null $rejection_reason 拒绝原因
 * @property Carbon|null $submitted_at 提交时间
 * @property Carbon|null $reviewed_at 审核时间
 * @property int|null $reviewer_id 审核员ID
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
final class UserKycVerification extends Model
{
    #[Inject]
    protected FilesystemFactory $filesystemFactory;

    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 名
     */
    public const FIELD_FIRST_NAME = 'first_name';
    /**
     * 中间名
     */
    public const FIELD_MIDDLE_NAME = 'middle_name';
    /**
     * 姓
     */
    public const FIELD_LAST_NAME = 'last_name';
    /**
     * 认证状态
     */
    public const FIELD_STATUS = 'status';
    /**
     * 证件类型
     */
    public const FIELD_DOCUMENT_TYPE = 'document_type';
    /**
     * 证件号码
     */
    public const FIELD_DOCUMENT_NUMBER = 'document_number';
    /**
     * 证件签发国家
     */
    public const FIELD_DOCUMENT_COUNTRY = 'document_country';
    /**
     * 证件到期日期
     */
    public const FIELD_DOCUMENT_EXPIRY_DATE = 'document_expiry_date';
    /**
     * 证件正面照片
     */
    public const FIELD_DOCUMENT_FRONT_IMAGE = 'document_front_image';
    /**
     * 证件背面照片
     */
    public const FIELD_DOCUMENT_BACK_IMAGE = 'document_back_image';
    /**
     * 手持证件自拍照
     */
    public const FIELD_SELFIE_IMAGE = 'selfie_image';
    /**
     * 生日
     */
    public const FIELD_BIRTHDAY = 'birthday';
    /**
     * 拒绝原因
     */
    public const FIELD_REJECTION_REASON = 'rejection_reason';
    /**
     * 提交时间
     */
    public const FIELD_SUBMITTED_AT = 'submitted_at';
    /**
     * 审核时间
     */
    public const FIELD_REVIEWED_AT = 'reviewed_at';
    /**
     * 审核员ID
     */
    public const FIELD_REVIEWER_ID = 'reviewer_id';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * 证件类型：护照
     */
    public const DOCUMENT_TYPE_PASSPORT = 'passport';
    /**
     * 证件类型：身份证
     */
    public const DOCUMENT_TYPE_ID_CARD = 'id_card';
    /**
     * 证件类型：驾驶证
     */
    public const DOCUMENT_TYPE_DRIVER_LICENSE = 'driver_license';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_kyc_verification';

    protected array $appends = [
        'document_front_image_url',
        'document_back_image_url',
        'selfie_image_url',
    ];

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 主键ID
        'user_id', // 用户ID
        'first_name', // 名
        'middle_name', // 中间名
        'last_name', // 姓
        'status', // 认证状态
        'document_type', // 证件类型
        'document_number', // 证件号码
        'document_country', // 证件签发国家
        'document_expiry_date', // 证件到期日期
        'document_front_image', // 证件正面照片
        'document_back_image', // 证件背面照片
        'selfie_image', // 手持证件自拍照
        'birthday', // 生日
        'rejection_reason', // 拒绝原因
        'submitted_at', // 提交时间
        'reviewed_at', // 审核时间
        'reviewer_id', // 审核员ID
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'user_id' => 'integer', // 用户ID
        'status' => KycStatus::class, // 认证状态
        'document_expiry_date' => 'date', // 证件到期日期
        'birthday' => 'date', // 生日
        'submitted_at' => 'datetime', // 提交时间
        'reviewed_at' => 'datetime', // 审核时间
        'reviewer_id' => 'integer', // 审核员ID
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 处理 document_front_image url
     */
    public function getDocumentFrontImageUrlAttribute(): string
    {
        if (empty($this->document_front_image)) {
            return '';
        }
        $filesystem = $this->filesystemFactory->get(config('file.default', 'local'));
        return $filesystem->publicUrl($this->document_front_image);
    }

    /**
     * 处理 document_back_image url
     */
    public function getDocumentBackImageUrlAttribute(): string
    {
        if (empty($this->document_back_image)) {
            return '';
        }
        $filesystem = $this->filesystemFactory->get(config('file.default', 'local'));
        return $filesystem->publicUrl($this->document_back_image);
    }

    /**
     * 处理 selfie_image url
     */
    public function getSelfieImageUrlAttribute(): string
    {
        if (empty($this->selfie_image)) {
            return '';
        }
        $filesystem = $this->filesystemFactory->get(config('file.default', 'local'));
        return $filesystem->publicUrl($this->selfie_image);
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     */
    public function setUserId(int $value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取名
     */
    public function getFirstName(): string
    {
        return $this->first_name;
    }

    /**
     * 设置名
     */
    public function setFirstName(string $value): static
    {
        $this->first_name = $value;
        return $this;
    }

    /**
     * 获取中间名
     */
    public function getMiddleName(): ?string
    {
        return $this->middle_name;
    }

    /**
     * 设置中间名
     */
    public function setMiddleName(?string $value): static
    {
        $this->middle_name = $value;
        return $this;
    }

    /**
     * 获取姓
     */
    public function getLastName(): string
    {
        return $this->last_name;
    }

    /**
     * 设置姓
     */
    public function setLastName(string $value): static
    {
        $this->last_name = $value;
        return $this;
    }

    /**
     * 获取认证状态
     */
    public function getStatus(): KycStatus
    {
        return $this->status;
    }

    /**
     * 设置认证状态
     */
    public function setStatus(KycStatus $value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取证件类型
     */
    public function getDocumentType(): ?string
    {
        return $this->document_type;
    }

    /**
     * 设置证件类型
     */
    public function setDocumentType(?string $value): static
    {
        $this->document_type = $value;
        return $this;
    }

    /**
     * 获取证件号码
     */
    public function getDocumentNumber(): ?string
    {
        return $this->document_number;
    }

    /**
     * 设置证件号码
     */
    public function setDocumentNumber(?string $value): static
    {
        $this->document_number = $value;
        return $this;
    }

    /**
     * 获取证件签发国家
     */
    public function getDocumentCountry(): ?string
    {
        return $this->document_country;
    }

    /**
     * 设置证件签发国家
     */
    public function setDocumentCountry(?string $value): static
    {
        $this->document_country = $value;
        return $this;
    }

    /**
     * 获取证件到期日期
     */
    public function getDocumentExpiryDate(): ?string
    {
        return $this->document_expiry_date;
    }

    /**
     * 设置证件到期日期
     */
    public function setDocumentExpiryDate(?string $value): static
    {
        $this->document_expiry_date = $value;
        return $this;
    }

    /**
     * 获取证件正面照片
     */
    public function getDocumentFrontImage(): ?string
    {
        return $this->document_front_image;
    }

    /**
     * 设置证件正面照片
     */
    public function setDocumentFrontImage(?string $value): static
    {
        $this->document_front_image = $value;
        return $this;
    }

    /**
     * 获取证件背面照片
     */
    public function getDocumentBackImage(): ?string
    {
        return $this->document_back_image;
    }

    /**
     * 设置证件背面照片
     */
    public function setDocumentBackImage(?string $value): static
    {
        $this->document_back_image = $value;
        return $this;
    }

    /**
     * 获取手持证件自拍照
     */
    public function getSelfieImage(): ?string
    {
        return $this->selfie_image;
    }

    /**
     * 设置手持证件自拍照
     */
    public function setSelfieImage(?string $value): static
    {
        $this->selfie_image = $value;
        return $this;
    }

    /**
     * 获取生日
     */
    public function getBirthday(): ?string
    {
        return $this->birthday;
    }

    /**
     * 设置生日
     */
    public function setBirthday(?string $value): static
    {
        $this->birthday = $value;
        return $this;
    }

    /**
     * 获取拒绝原因
     */
    public function getRejectionReason(): ?string
    {
        return $this->rejection_reason;
    }

    /**
     * 设置拒绝原因
     */
    public function setRejectionReason(?string $value): static
    {
        $this->rejection_reason = $value;
        return $this;
    }

    /**
     * 获取提交时间
     */
    public function getSubmittedAt(): ?Carbon
    {
        return $this->submitted_at;
    }

    /**
     * 设置提交时间
     */
    public function setSubmittedAt(?Carbon $value): static
    {
        $this->submitted_at = $value;
        return $this;
    }

    /**
     * 获取审核时间
     */
    public function getReviewedAt(): ?Carbon
    {
        return $this->reviewed_at;
    }

    /**
     * 设置审核时间
     */
    public function setReviewedAt(?Carbon $value): static
    {
        $this->reviewed_at = $value;
        return $this;
    }

    /**
     * 获取审核员ID
     */
    public function getReviewerId(): ?int
    {
        return $this->reviewer_id;
    }

    /**
     * 设置审核员ID
     */
    public function setReviewerId(?int $value): static
    {
        $this->reviewer_id = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
