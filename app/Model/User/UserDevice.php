<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户设备模型
 */

namespace App\Model\User;

use App\Model\User\Enums\DeviceStatus;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\HasOne;

/**
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property string $device_id 设备唯一标识
 * @property string $device_name 设备名称
 * @property string $device_type 设备类型:web=网页,ios=苹果,android=安卓
 * @property string|null $browser 浏览器 User-Agent
 * @property string|null $os 操作系统
 * @property string|null $app_version 应用版本
 * @property string $first_login_ip 首次登录IP
 * @property string $last_login_ip 最后登录IP
 * @property Carbon $first_login_at 首次登录时间
 * @property Carbon $last_login_at 最后登录时间
 * @property string $token 设备登录令牌
 * @property DeviceStatus $status 设备状态:1=正常,2=禁用
 * @property int $is_frequently_used 是否经常使用:0=否,1=是
 * @property string|null $push_token 推送通知令牌
 * @property array|null $device_info 设备详细信息
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
final class UserDevice extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 设备唯一标识
     */
    public const FIELD_DEVICE_ID = 'device_id';
    /**
     * 设备名称
     */
    public const FIELD_DEVICE_NAME = 'device_name';
    /**
     * 设备类型
     */
    public const FIELD_DEVICE_TYPE = 'device_type';
    /**
     * 浏览器
     */
    public const FIELD_BROWSER = 'browser';
    /**
     * 操作系统
     */
    public const FIELD_OS = 'os';
    /**
     * 应用版本
     */
    public const FIELD_APP_VERSION = 'app_version';
    /**
     * 首次登录IP
     */
    public const FIELD_FIRST_LOGIN_IP = 'first_login_ip';
    /**
     * 最后登录IP
     */
    public const FIELD_LAST_LOGIN_IP = 'last_login_ip';
    /**
     * 首次登录时间
     */
    public const FIELD_FIRST_LOGIN_AT = 'first_login_at';
    /**
     * 最后登录时间
     */
    public const FIELD_LAST_LOGIN_AT = 'last_login_at';
    /**
     * 设备登录令牌
     */
    public const FIELD_TOKEN = 'token';
    /**
     * 设备状态
     */
    public const FIELD_STATUS = 'status';
    /**
     * 是否经常使用
     */
    public const FIELD_IS_FREQUENTLY_USED = 'is_frequently_used';
    /**
     * 推送通知令牌
     */
    public const FIELD_PUSH_TOKEN = 'push_token';
    /**
     * 设备详细信息
     */
    public const FIELD_DEVICE_INFO = 'device_info';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * 设备类型：网页
     */
    public const DEVICE_TYPE_WEB = 'web';
    /**
     * 设备类型：苹果
     */
    public const DEVICE_TYPE_IOS = 'ios';
    /**
     * 设备类型：安卓
     */
    public const DEVICE_TYPE_ANDROID = 'android';

    /**
     * 是否经常使用：否
     */
    public const FREQUENTLY_USED_NO = 0;
    /**
     * 是否经常使用：是
     */
    public const FREQUENTLY_USED_YES = 1;

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_device';

    protected array $hidden = [
        'token',
        'push_token'
    ];

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 主键ID
        'user_id', // 用户ID
        'device_id', // 设备唯一标识
        'device_name', // 设备名称
        'device_type', // 设备类型
        'browser', // 浏览器
        'os', // 操作系统
        'app_version', // 应用版本
        'first_login_ip', // 首次登录IP
        'last_login_ip', // 最后登录IP
        'first_login_at', // 首次登录时间
        'last_login_at', // 最后登录时间
        'token', // 设备登录令牌
        'status', // 设备状态
        'is_frequently_used', // 是否经常使用
        'push_token', // 推送通知令牌
        'device_info', // 设备详细信息
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'user_id' => 'integer', // 用户ID
        'status' => DeviceStatus::class, // 设备状态
        'is_frequently_used' => 'integer', // 是否经常使用
        'device_info' => 'array', // 设备详细信息
        'first_login_at' => 'datetime', // 首次登录时间
        'last_login_at' => 'datetime', // 最后登录时间
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 通过 device_id 关联 cpx_user_login_log 表，获取最后登录记录
     */
    public function lastLoginLog(): HasOne
    {
        return $this->hasOne(UserLoginLog::class, UserLoginLog::FIELD_DEVICE_ID, UserDevice::FIELD_DEVICE_ID)->orderBy(UserLoginLog::FIELD_CREATED_AT, 'desc');
    }

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     */
    public function setUserId(int $value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取设备唯一标识
     */
    public function getDeviceId(): string
    {
        return $this->device_id;
    }

    /**
     * 设置设备唯一标识
     */
    public function setDeviceId(string $value): static
    {
        $this->device_id = $value;
        return $this;
    }

    /**
     * 获取设备名称
     */
    public function getDeviceName(): string
    {
        return $this->device_name;
    }

    /**
     * 设置设备名称
     */
    public function setDeviceName(string $value): static
    {
        $this->device_name = $value;
        return $this;
    }

    /**
     * 获取设备类型
     */
    public function getDeviceType(): string
    {
        return $this->device_type;
    }

    /**
     * 设置设备类型
     */
    public function setDeviceType(string $value): static
    {
        $this->device_type = $value;
        return $this;
    }

    /**
     * 获取浏览器
     */
    public function getBrowser(): ?string
    {
        return $this->browser;
    }

    /**
     * 设置浏览器
     */
    public function setBrowser(?string $value): static
    {
        $this->browser = $value;
        return $this;
    }

    /**
     * 获取操作系统
     */
    public function getOs(): ?string
    {
        return $this->os;
    }

    /**
     * 设置操作系统
     */
    public function setOs(?string $value): static
    {
        $this->os = $value;
        return $this;
    }

    /**
     * 获取应用版本
     */
    public function getAppVersion(): ?string
    {
        return $this->app_version;
    }

    /**
     * 设置应用版本
     */
    public function setAppVersion(?string $value): static
    {
        $this->app_version = $value;
        return $this;
    }

    /**
     * 获取首次登录IP
     */
    public function getFirstLoginIp(): string
    {
        return $this->first_login_ip;
    }

    /**
     * 设置首次登录IP
     */
    public function setFirstLoginIp(string $value): static
    {
        $this->first_login_ip = $value;
        return $this;
    }

    /**
     * 获取最后登录IP
     */
    public function getLastLoginIp(): string
    {
        return $this->last_login_ip;
    }

    /**
     * 设置最后登录IP
     */
    public function setLastLoginIp(string $value): static
    {
        $this->last_login_ip = $value;
        return $this;
    }

    /**
     * 获取首次登录时间
     */
    public function getFirstLoginAt(): Carbon
    {
        return $this->first_login_at;
    }

    /**
     * 设置首次登录时间
     */
    public function setFirstLoginAt(Carbon $value): static
    {
        $this->first_login_at = $value;
        return $this;
    }

    /**
     * 获取最后登录时间
     */
    public function getLastLoginAt(): Carbon
    {
        return $this->last_login_at;
    }

    /**
     * 设置最后登录时间
     */
    public function setLastLoginAt(Carbon $value): static
    {
        $this->last_login_at = $value;
        return $this;
    }

    /**
     * 获取设备登录令牌
     */
    public function getToken(): string
    {
        return $this->token;
    }

    /**
     * 设置设备登录令牌
     */
    public function setToken(string $value): static
    {
        $this->token = $value;
        return $this;
    }

    /**
     * 获取设备状态
     */
    public function getStatus(): DeviceStatus
    {
        return $this->status;
    }

    /**
     * 设置设备状态
     */
    public function setStatus(DeviceStatus $value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取是否经常使用
     */
    public function getIsFrequentlyUsed(): int
    {
        return $this->is_frequently_used;
    }

    /**
     * 设置是否经常使用
     */
    public function setIsFrequentlyUsed(int $value): static
    {
        $this->is_frequently_used = $value;
        return $this;
    }

    /**
     * 获取推送通知令牌
     */
    public function getPushToken(): ?string
    {
        return $this->push_token;
    }

    /**
     * 设置推送通知令牌
     */
    public function setPushToken(?string $value): static
    {
        $this->push_token = $value;
        return $this;
    }

    /**
     * 获取设备详细信息
     */
    public function getDeviceInfo(): ?array
    {
        return $this->device_info;
    }

    /**
     * 设置设备详细信息
     */
    public function setDeviceInfo(?array $value): static
    {
        $this->device_info = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
