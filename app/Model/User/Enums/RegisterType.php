<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户注册类型枚举
 */

namespace App\Model\User\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum RegisterType: int
{
    use EnumConstantsTrait;

    /**
     * 邮箱注册
     */
    #[Message('cpx_user.enums.register_type.1')]
    case EMAIL = 1;

    /**
     * 手机注册
     */
    #[Message('cpx_user.enums.register_type.2')]
    case PHONE = 2;

    /**
     * 苹果登录注册
     */
    #[Message('cpx_user.enums.register_type.3')]
    case APPLE = 3;

    /**
     * 谷歌登录注册
     */
    #[Message('cpx_user.enums.register_type.4')]
    case GOOGLE = 4;
}
