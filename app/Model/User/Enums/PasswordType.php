<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 密码类型枚举
 */

namespace App\Model\User\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum PasswordType: int
{
    use EnumConstantsTrait;

    /**
     * 登录密码
     */
    #[Message('cpx_user.enums.password_type.1')]
    case LOGIN = 1;

    /**
     * 资金密码
     */
    #[Message('cpx_user.enums.password_type.2')]
    case FUND = 2;
}
