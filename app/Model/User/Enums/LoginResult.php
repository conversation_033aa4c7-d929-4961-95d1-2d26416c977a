<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 登录结果枚举
 */

namespace App\Model\User\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum LoginResult: int
{
    use EnumConstantsTrait;

    /**
     * 登录成功
     */
    #[Message('cpx_user.enums.login_result.1')]
    case SUCCESS = 1;

    /**
     * 登录失败
     */
    #[Message('cpx_user.enums.login_result.2')]
    case FAILED = 2;
}
