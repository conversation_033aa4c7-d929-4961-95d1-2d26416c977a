<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 返佣状态枚举
 */

namespace App\Model\User\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum CommissionStatus: int
{
    use EnumConstantsTrait;

    /**
     * 待结算
     */
    #[Message('cpx_user.enums.commission_status.0')]
    case PENDING = 0;

    /**
     * 已结算
     */
    #[Message('cpx_user.enums.commission_status.1')]
    case SETTLED = 1;

    /**
     * 已取消
     */
    #[Message('cpx_user.enums.commission_status.2')]
    case CANCELLED = 2;
}
