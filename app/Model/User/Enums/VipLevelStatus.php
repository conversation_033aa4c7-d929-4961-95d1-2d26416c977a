<?php

declare(strict_types=1);
/**
 * 策略平台API
 * VIP等级状态枚举
 */

namespace App\Model\User\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum VipLevelStatus: int
{
    use EnumConstantsTrait;

    /**
     * 启用
     */
    #[Message('cpx_user.enums.vip_level_status.1')]
    case ENABLED = 1;

    /**
     * 禁用
     */
    #[Message('cpx_user.enums.vip_level_status.2')]
    case DISABLED = 2;
}
