<?php

declare(strict_types=1);
/**
 * 策略平台API
 * KYC认证状态枚举
 */

namespace App\Model\User\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum KycStatus: int
{
    use EnumConstantsTrait;

    /**
     * 未提交
     */
    #[Message('cpx_user.enums.kyc_status.0')]
    case UNSUBMITTED = 0;

    /**
     * 审核中
     */
    #[Message('cpx_user.enums.kyc_status.1')]
    case PENDING = 1;

    /**
     * 已通过
     */
    #[Message('cpx_user.enums.kyc_status.2')]
    case APPROVED = 2;

    /**
     * 已拒绝
     */
    #[Message('cpx_user.enums.kyc_status.3')]
    case REJECTED = 3;
}
