<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 设备状态枚举
 */

namespace App\Model\User\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum DeviceStatus: int
{
    use EnumConstantsTrait;

    /**
     * 正常
     */
    #[Message('cpx_user.enums.device_status.1')]
    case NORMAL = 1;

    /**
     * 禁用
     */
    #[Message('cpx_user.enums.device_status.2')]
    case DISABLED = 2;
}
