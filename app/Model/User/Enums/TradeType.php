<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 交易类型枚举
 */

namespace App\Model\User\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum TradeType: int
{
    use EnumConstantsTrait;

    /**
     * 现货
     */
    #[Message('cpx_user.enums.trade_type.1')]
    case SPOT = 1;

    /**
     * 合约
     */
    #[Message('cpx_user.enums.trade_type.2')]
    case CONTRACT = 2;

    /**
     * 现货杠杆
     */
    #[Message('cpx_user.enums.trade_type.3')]
    case SPOT_LEVERAGE = 3;
}
