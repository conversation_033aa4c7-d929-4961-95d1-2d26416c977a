<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户杠杆利息记录模型
 */

namespace App\Model\User;

use App\Model\Enums\Trade\Margin\MarginInterestStatus;
use App\Model\Currency\Currency;
use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int $user_id 用户ID
 * @property int $borrow_id 借贷记录ID
 * @property int $currency_id 币种ID
 * @property int $account_type 账户类型：2-全仓杠杆，3-逐仓杠杆
 * @property float $interest_amount 利息金额
 * @property float $interest_rate 利率
 * @property int $calculation_period 计息周期（小时数）
 * @property string $start_time 计息开始时间
 * @property string $end_time 计息结束时间
 * @property string $deduct_time 实际扣除时间
 * @property MarginInterestStatus $status 状态：1-待扣除，2-已扣除，3-扣除失败，4-已豁免
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 */
final class UserMarginInterest extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 借贷记录ID
     */
    public const FIELD_BORROW_ID = 'borrow_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 账户类型：2-全仓杠杆，3-逐仓杠杆
     */
    public const FIELD_ACCOUNT_TYPE = 'account_type';
    /**
     * 利息金额
     */
    public const FIELD_INTEREST_AMOUNT = 'interest_amount';
    /**
     * 利率
     */
    public const FIELD_INTEREST_RATE = 'interest_rate';
    /**
     * 计息周期（小时数）
     */
    public const FIELD_CALCULATION_PERIOD = 'calculation_period';
    /**
     * 计息开始时间
     */
    public const FIELD_START_TIME = 'start_time';
    /**
     * 计息结束时间
     */
    public const FIELD_END_TIME = 'end_time';
    /**
     * 实际扣除时间
     */
    public const FIELD_DEDUCT_TIME = 'deduct_time';
    /**
     * 状态：1-待扣除，2-已扣除，3-扣除失败，4-已豁免
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'user_margin_interest';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'user_id', // 用户ID
        'borrow_id', // 借贷记录ID
        'currency_id', // 币种ID
        'account_type', // 账户类型：2-全仓杠杆，3-逐仓杠杆
        'interest_amount', // 利息金额
        'interest_rate', // 利率
        'calculation_period', // 计息周期（小时数）
        'start_time', // 计息开始时间
        'end_time', // 计息结束时间
        'deduct_time', // 实际扣除时间
        'status', // 状态：1-待扣除，2-已扣除，3-扣除失败，4-已豁免
        'created_at', // 创建时间
        'updated_at' // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'user_id' => 'integer', // 用户ID
        'borrow_id' => 'integer', // 借贷记录ID
        'currency_id' => 'integer', // 币种ID
        'account_type' => 'integer', // 账户类型
        'interest_amount' => 'float', // 利息金额
        'interest_rate' => 'float', // 利率
        'calculation_period' => 'integer', // 计息周期
        'status' => MarginInterestStatus::class, // 状态
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime' // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取借贷记录ID
     */
    public function getBorrowId(): int
    {
        return $this->borrow_id;
    }

    /**
     * 设置借贷记录ID
     *
     * @param int $value 借贷记录ID值
     */
    public function setBorrowId($value): static
    {
        $this->borrow_id = $value;
        return $this;
    }

    /**
     * 获取币种ID
     */
    public function getCurrencyId(): int
    {
        return $this->currency_id;
    }

    /**
     * 设置币种ID
     *
     * @param int $value 币种ID值
     */
    public function setCurrencyId($value): static
    {
        $this->currency_id = $value;
        return $this;
    }

    /**
     * 获取账户类型
     */
    public function getAccountType(): int
    {
        return $this->account_type;
    }

    /**
     * 设置账户类型
     *
     * @param int $value 账户类型值
     */
    public function setAccountType($value): static
    {
        $this->account_type = $value;
        return $this;
    }

    /**
     * 获取利息金额
     */
    public function getInterestAmount(): float
    {
        return $this->interest_amount;
    }

    /**
     * 设置利息金额
     *
     * @param float $value 利息金额值
     */
    public function setInterestAmount($value): static
    {
        $this->interest_amount = $value;
        return $this;
    }

    /**
     * 获取利率
     */
    public function getInterestRate(): float
    {
        return $this->interest_rate;
    }

    /**
     * 设置利率
     *
     * @param float $value 利率值
     */
    public function setInterestRate($value): static
    {
        $this->interest_rate = $value;
        return $this;
    }

    /**
     * 获取计息周期
     */
    public function getCalculationPeriod(): int
    {
        return $this->calculation_period;
    }

    /**
     * 设置计息周期
     *
     * @param int $value 计息周期值
     */
    public function setCalculationPeriod($value): static
    {
        $this->calculation_period = $value;
        return $this;
    }

    /**
     * 获取计息开始时间
     */
    public function getStartTime(): string
    {
        return $this->start_time;
    }

    /**
     * 设置计息开始时间
     *
     * @param string $value 计息开始时间值
     */
    public function setStartTime($value): static
    {
        $this->start_time = $value;
        return $this;
    }

    /**
     * 获取计息结束时间
     */
    public function getEndTime(): string
    {
        return $this->end_time;
    }

    /**
     * 设置计息结束时间
     *
     * @param string $value 计息结束时间值
     */
    public function setEndTime($value): static
    {
        $this->end_time = $value;
        return $this;
    }

    /**
     * 获取实际扣除时间
     */
    public function getDeductTime(): string
    {
        return $this->deduct_time;
    }

    /**
     * 设置实际扣除时间
     *
     * @param string $value 实际扣除时间值
     */
    public function setDeductTime($value): static
    {
        $this->deduct_time = $value;
        return $this;
    }

    /**
     * 获取状态
     */
    public function getStatus(): MarginInterestStatus
    {
        return $this->status;
    }

    /**
     * 设置状态
     *
     * @param MarginInterestStatus|int $value 状态值
     */
    public function setStatus($value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联借贷记录
     */
    public function borrow()
    {
        return $this->belongsTo(UserMarginBorrow::class, 'borrow_id', 'id');
    }

    /**
     * 关联币种
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }
}
