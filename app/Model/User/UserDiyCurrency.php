<?php

declare(strict_types=1);

namespace App\Model\User;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $user_id 
 * @property string $title 自定义自选标题
 * @property array $currency 自选的币种[currency_id=>markettype]
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class UserDiyCurrency extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 自定义自选标题
     */
    public const FIELD_TITLE = 'title';
    /**
     * 自选的币种[currency_id=>markettype]
     */
    public const FIELD_CURRENCY = 'currency';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'user_diy_currency';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'title', 'currency', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'user_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime','currency' => 'array'];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getTitle() : string
    {
        return $this->title;
    }
    public function setTitle($value) : object
    {
        $this->title = $value;
        return $this;
    }
    public function getCurrency() : array
    {
        return $this->currency;
    }
    public function setCurrency($value) : object
    {
        $this->currency = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
