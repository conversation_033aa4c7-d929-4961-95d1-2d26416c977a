<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户提现设置模型
 */

namespace App\Model\User;

use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int $user_id 用户ID
 * @property int $small_withdrawal_enabled 是否启用小额免密提现:0=否,1=是
 * @property float $small_withdrawal_limit 小额免密提现限额
 * @property int $withdrawal_whitelist_enabled 是否启用提现白名单:0=否,1=是
 * @property array|null $withdrawal_whitelist 提现白名单地址（网络类型、提现地址、备注）
 * @property int $withdrawal_revoke_enabled 是否开启撤销提现（开启后1分钟内可撤销提现）:0=否,1=是
 * @property array|null $preferred_networks 偏好网络列表
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class UserWithdrawalSetting extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 是否启用小额免密提现:0=否,1=是
     */
    public const FIELD_SMALL_WITHDRAWAL_ENABLED = 'small_withdrawal_enabled';
    /**
     * 小额免密提现限额
     */
    public const FIELD_SMALL_WITHDRAWAL_LIMIT = 'small_withdrawal_limit';
    /**
     * 是否启用提现白名单:0=否,1=是
     */
    public const FIELD_WITHDRAWAL_WHITELIST_ENABLED = 'withdrawal_whitelist_enabled';
    /**
     * 提现白名单地址（网络类型、提现地址、备注）
     */
    public const FIELD_WITHDRAWAL_WHITELIST = 'withdrawal_whitelist';
    /**
     * 是否开启撤销提现（开启后1分钟内可撤销提现）:0=否,1=是
     */
    public const FIELD_WITHDRAWAL_REVOKE_ENABLED = 'withdrawal_revoke_enabled';
    /**
     * 偏好网络列表
     */
    public const FIELD_PREFERRED_NETWORKS = 'preferred_networks';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * 开关状态：否
     */
    public const STATUS_DISABLED = 0;
    /**
     * 开关状态：是
     */
    public const STATUS_ENABLED = 1;

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_withdrawal_setting';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'user_id', // 用户ID
        'small_withdrawal_enabled', // 是否启用小额免密提现:0=否,1=是
        'small_withdrawal_limit', // 小额免密提现限额
        'withdrawal_whitelist_enabled', // 是否启用提现白名单:0=否,1=是
        'withdrawal_whitelist', // 提现白名单地址（网络类型、提现地址、备注）
        'withdrawal_revoke_enabled', // 是否开启撤销提现（开启后1分钟内可撤销提现）:0=否,1=是
        'preferred_networks', // 偏好网络列表
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'user_id' => 'integer', // 用户ID
        'small_withdrawal_enabled' => 'integer', // 是否启用小额免密提现:0=否,1=是
        'small_withdrawal_limit' => 'decimal:8', // 小额免密提现限额
        'withdrawal_whitelist_enabled' => 'integer', // 是否启用提现白名单:0=否,1=是
        'withdrawal_whitelist' => 'array', // 提现白名单地址（网络类型、提现地址、备注）
        'withdrawal_revoke_enabled' => 'integer', // 是否开启撤销提现（开启后1分钟内可撤销提现）:0=否,1=是
        'preferred_networks' => 'array', // 偏好网络列表
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取是否启用小额免密提现
     */
    public function getSmallWithdrawalEnabled(): int
    {
        return $this->small_withdrawal_enabled;
    }

    /**
     * 设置是否启用小额免密提现
     *
     * @param int $value 开关状态值
     */
    public function setSmallWithdrawalEnabled($value): static
    {
        $this->small_withdrawal_enabled = $value;
        return $this;
    }

    /**
     * 获取小额免密提现限额
     */
    public function getSmallWithdrawalLimit(): float
    {
        return $this->small_withdrawal_limit;
    }

    /**
     * 设置小额免密提现限额
     *
     * @param float $value 限额值
     */
    public function setSmallWithdrawalLimit($value): static
    {
        $this->small_withdrawal_limit = $value;
        return $this;
    }

    /**
     * 获取是否启用提现白名单
     */
    public function getWithdrawalWhitelistEnabled(): int
    {
        return $this->withdrawal_whitelist_enabled;
    }

    /**
     * 设置是否启用提现白名单
     *
     * @param int $value 开关状态值
     */
    public function setWithdrawalWhitelistEnabled($value): static
    {
        $this->withdrawal_whitelist_enabled = $value;
        return $this;
    }

    /**
     * 获取提现白名单地址
     */
    public function getWithdrawalWhitelist(): ?array
    {
        return $this->withdrawal_whitelist;
    }

    /**
     * 设置提现白名单地址
     *
     * @param array|null $value 白名单地址数组
     */
    public function setWithdrawalWhitelist($value): static
    {
        $this->withdrawal_whitelist = $value;
        return $this;
    }

    /**
     * 获取是否开启撤销提现
     */
    public function getWithdrawalRevokeEnabled(): int
    {
        return $this->withdrawal_revoke_enabled;
    }

    /**
     * 设置是否开启撤销提现
     *
     * @param int $value 开关状态值
     */
    public function setWithdrawalRevokeEnabled($value): static
    {
        $this->withdrawal_revoke_enabled = $value;
        return $this;
    }

    /**
     * 获取偏好网络列表
     */
    public function getPreferredNetworks(): ?array
    {
        return $this->preferred_networks;
    }

    /**
     * 设置偏好网络列表
     *
     * @param array|null $value 网络列表数组
     */
    public function setPreferredNetworks($value): static
    {
        $this->preferred_networks = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 获取关联的用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
