<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户登录日志模型
 */

namespace App\Model\User;

use App\Model\User\Enums\LoginResult;
use App\QueryBuilder\Model;
use Carbon\Carbon;

/**
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property string $ip_address 登录IP地址
 * @property string|null $user_agent 用户代理 请求头中的User-Agent
 * @property string|null $device_type 设备类型:web=网页,ios=苹果,android=安卓,api=API
 * @property string|null $device_name 设备名称
 * @property string|null $device_id 设备唯一标识
 * @property string|null $browser 浏览器
 * @property string|null $os 操作系统
 * @property string|null $location_country 登录国家
 * @property string|null $location_province 登录省份
 * @property string|null $location_city 登录城市
 * @property LoginResult $login_result 登录结果:1=成功,2=失败
 * @property string|null $failure_reason 失败原因
 * @property Carbon $login_time 登录时间
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
final class UserLoginLog extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 登录IP地址
     */
    public const FIELD_IP_ADDRESS = 'ip_address';
    /**
     * 用户代理
     */
    public const FIELD_USER_AGENT = 'user_agent';
    /**
     * 设备类型
     */
    public const FIELD_DEVICE_TYPE = 'device_type';
    /**
     * 设备名称
     */
    public const FIELD_DEVICE_NAME = 'device_name';
    /**
     * 设备唯一标识
     */
    public const FIELD_DEVICE_ID = 'device_id';
    /**
     * 浏览器
     */
    public const FIELD_BROWSER = 'browser';
    /**
     * 操作系统
     */
    public const FIELD_OS = 'os';
    /**
     * 登录国家
     */
    public const FIELD_LOCATION_COUNTRY = 'location_country';
    /**
     * 登录省份
     */
    public const FIELD_LOCATION_PROVINCE = 'location_province';
    /**
     * 登录城市
     */
    public const FIELD_LOCATION_CITY = 'location_city';
    /**
     * 登录结果
     */
    public const FIELD_LOGIN_RESULT = 'login_result';
    /**
     * 失败原因
     */
    public const FIELD_FAILURE_REASON = 'failure_reason';
    /**
     * 登录时间
     */
    public const FIELD_LOGIN_TIME = 'login_time';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * 设备类型：网页
     */
    public const DEVICE_TYPE_WEB = 'web';
    /**
     * 设备类型：苹果
     */
    public const DEVICE_TYPE_IOS = 'ios';
    /**
     * 设备类型：安卓
     */
    public const DEVICE_TYPE_ANDROID = 'android';
    /**
     * 设备类型：API
     */
    public const DEVICE_TYPE_API = 'api';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_login_log';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 主键ID
        'user_id', // 用户ID
        'ip_address', // 登录IP地址
        'user_agent', // 用户代理
        'device_type', // 设备类型
        'device_name', // 设备名称
        'device_id', // 设备唯一标识
        'browser', // 浏览器
        'os', // 操作系统
        'location_country', // 登录国家
        'location_province', // 登录省份
        'location_city', // 登录城市
        'login_result', // 登录结果
        'failure_reason', // 失败原因
        'login_time', // 登录时间
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'user_id' => 'integer', // 用户ID
        'login_result' => LoginResult::class, // 登录结果
        'login_time' => 'datetime', // 登录时间
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     */
    public function setUserId(int $value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取登录IP地址
     */
    public function getIpAddress(): string
    {
        return $this->ip_address;
    }

    /**
     * 设置登录IP地址
     */
    public function setIpAddress(string $value): static
    {
        $this->ip_address = $value;
        return $this;
    }

    /**
     * 获取用户代理
     */
    public function getUserAgent(): ?string
    {
        return $this->user_agent;
    }

    /**
     * 设置用户代理
     */
    public function setUserAgent(?string $value): static
    {
        $this->user_agent = $value;
        return $this;
    }

    /**
     * 获取设备类型
     */
    public function getDeviceType(): ?string
    {
        return $this->device_type;
    }

    /**
     * 设置设备类型
     */
    public function setDeviceType(?string $value): static
    {
        $this->device_type = $value;
        return $this;
    }

    /**
     * 获取设备名称
     */
    public function getDeviceName(): ?string
    {
        return $this->device_name;
    }

    /**
     * 设置设备名称
     */
    public function setDeviceName(?string $value): static
    {
        $this->device_name = $value;
        return $this;
    }

    /**
     * 获取设备唯一标识
     */
    public function getDeviceId(): ?string
    {
        return $this->device_id;
    }

    /**
     * 设置设备唯一标识
     */
    public function setDeviceId(?string $value): static
    {
        $this->device_id = $value;
        return $this;
    }

    /**
     * 获取浏览器
     */
    public function getBrowser(): ?string
    {
        return $this->browser;
    }

    /**
     * 设置浏览器
     */
    public function setBrowser(?string $value): static
    {
        $this->browser = $value;
        return $this;
    }

    /**
     * 获取操作系统
     */
    public function getOs(): ?string
    {
        return $this->os;
    }

    /**
     * 设置操作系统
     */
    public function setOs(?string $value): static
    {
        $this->os = $value;
        return $this;
    }

    /**
     * 获取登录国家
     */
    public function getLocationCountry(): ?string
    {
        return $this->location_country;
    }

    /**
     * 设置登录国家
     */
    public function setLocationCountry(?string $value): static
    {
        $this->location_country = $value;
        return $this;
    }

    /**
     * 获取登录省份
     */
    public function getLocationProvince(): ?string
    {
        return $this->location_province;
    }

    /**
     * 设置登录省份
     */
    public function setLocationProvince(?string $value): static
    {
        $this->location_province = $value;
        return $this;
    }

    /**
     * 获取登录城市
     */
    public function getLocationCity(): ?string
    {
        return $this->location_city;
    }

    /**
     * 设置登录城市
     */
    public function setLocationCity(?string $value): static
    {
        $this->location_city = $value;
        return $this;
    }

    /**
     * 获取登录结果
     */
    public function getLoginResult(): LoginResult
    {
        return $this->login_result;
    }

    /**
     * 设置登录结果
     */
    public function setLoginResult(LoginResult $value): static
    {
        $this->login_result = $value;
        return $this;
    }

    /**
     * 获取失败原因
     */
    public function getFailureReason(): ?string
    {
        return $this->failure_reason;
    }

    /**
     * 设置失败原因
     */
    public function setFailureReason(?string $value): static
    {
        $this->failure_reason = $value;
        return $this;
    }

    /**
     * 获取登录时间
     */
    public function getLoginTime(): Carbon
    {
        return $this->login_time;
    }

    /**
     * 设置登录时间
     */
    public function setLoginTime(Carbon $value): static
    {
        $this->login_time = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }
}
