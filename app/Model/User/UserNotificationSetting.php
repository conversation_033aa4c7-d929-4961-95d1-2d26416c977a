<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户通知设置模型
 */

namespace App\Model\User;

use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int $user_id 用户ID
 * @property int $system_message_enabled 系统消息:0=关闭,1=开启
 * @property int $trading_message_enabled 交易消息:0=关闭,1=开启
 * @property int $security_message_enabled 安全消息:0=关闭,1=开启
 * @property int $promotion_message_enabled 推广消息:0=关闭,1=开启
 * @property int $email_login_enabled 登录邮件通知:0=关闭,1=开启
 * @property int $email_trading_enabled 交易邮件通知:0=关闭,1=开启
 * @property int $email_withdrawal_enabled 提现邮件通知:0=关闭,1=开启
 * @property int $email_security_enabled 安全邮件通知:0=关闭,1=开启
 * @property int $email_promotion_enabled 推广邮件通知:0=关闭,1=开启
 * @property int $email_news_enabled 新闻邮件通知:0=关闭,1=开启
 * @property int $push_login_enabled 登录推送通知:0=关闭,1=开启
 * @property int $push_trading_enabled 交易推送通知:0=关闭,1=开启
 * @property int $push_price_alert_enabled 价格提醒推送:0=关闭,1=开启
 * @property int $push_security_enabled 安全推送通知:0=关闭,1=开启
 * @property int $push_promotion_enabled 推广推送通知:0=关闭,1=开启
 * @property int $push_news_enabled 新闻推送通知:0=关闭,1=开启
 * @property int $sms_login_enabled 登录短信通知:0=关闭,1=开启
 * @property int $sms_trading_enabled 交易短信通知:0=关闭,1=开启
 * @property int $sms_withdrawal_enabled 提现短信通知:0=关闭,1=开启
 * @property int $sms_security_enabled 安全短信通知:0=关闭,1=开启
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class UserNotificationSetting extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 系统消息:0=关闭,1=开启
     */
    public const FIELD_SYSTEM_MESSAGE_ENABLED = 'system_message_enabled';
    /**
     * 交易消息:0=关闭,1=开启
     */
    public const FIELD_TRADING_MESSAGE_ENABLED = 'trading_message_enabled';
    /**
     * 安全消息:0=关闭,1=开启
     */
    public const FIELD_SECURITY_MESSAGE_ENABLED = 'security_message_enabled';
    /**
     * 推广消息:0=关闭,1=开启
     */
    public const FIELD_PROMOTION_MESSAGE_ENABLED = 'promotion_message_enabled';
    /**
     * 登录邮件通知:0=关闭,1=开启
     */
    public const FIELD_EMAIL_LOGIN_ENABLED = 'email_login_enabled';
    /**
     * 交易邮件通知:0=关闭,1=开启
     */
    public const FIELD_EMAIL_TRADING_ENABLED = 'email_trading_enabled';
    /**
     * 提现邮件通知:0=关闭,1=开启
     */
    public const FIELD_EMAIL_WITHDRAWAL_ENABLED = 'email_withdrawal_enabled';
    /**
     * 安全邮件通知:0=关闭,1=开启
     */
    public const FIELD_EMAIL_SECURITY_ENABLED = 'email_security_enabled';
    /**
     * 推广邮件通知:0=关闭,1=开启
     */
    public const FIELD_EMAIL_PROMOTION_ENABLED = 'email_promotion_enabled';
    /**
     * 新闻邮件通知:0=关闭,1=开启
     */
    public const FIELD_EMAIL_NEWS_ENABLED = 'email_news_enabled';
    /**
     * 登录推送通知:0=关闭,1=开启
     */
    public const FIELD_PUSH_LOGIN_ENABLED = 'push_login_enabled';
    /**
     * 交易推送通知:0=关闭,1=开启
     */
    public const FIELD_PUSH_TRADING_ENABLED = 'push_trading_enabled';
    /**
     * 价格提醒推送:0=关闭,1=开启
     */
    public const FIELD_PUSH_PRICE_ALERT_ENABLED = 'push_price_alert_enabled';
    /**
     * 安全推送通知:0=关闭,1=开启
     */
    public const FIELD_PUSH_SECURITY_ENABLED = 'push_security_enabled';
    /**
     * 推广推送通知:0=关闭,1=开启
     */
    public const FIELD_PUSH_PROMOTION_ENABLED = 'push_promotion_enabled';
    /**
     * 新闻推送通知:0=关闭,1=开启
     */
    public const FIELD_PUSH_NEWS_ENABLED = 'push_news_enabled';
    /**
     * 登录短信通知:0=关闭,1=开启
     */
    public const FIELD_SMS_LOGIN_ENABLED = 'sms_login_enabled';
    /**
     * 交易短信通知:0=关闭,1=开启
     */
    public const FIELD_SMS_TRADING_ENABLED = 'sms_trading_enabled';
    /**
     * 提现短信通知:0=关闭,1=开启
     */
    public const FIELD_SMS_WITHDRAWAL_ENABLED = 'sms_withdrawal_enabled';
    /**
     * 安全短信通知:0=关闭,1=开启
     */
    public const FIELD_SMS_SECURITY_ENABLED = 'sms_security_enabled';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * 通知开关状态：关闭
     */
    public const STATUS_DISABLED = 0;
    /**
     * 通知开关状态：开启
     */
    public const STATUS_ENABLED = 1;

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_notification_setting';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'user_id', // 用户ID
        'system_message_enabled', // 系统消息:0=关闭,1=开启
        'trading_message_enabled', // 交易消息:0=关闭,1=开启
        'security_message_enabled', // 安全消息:0=关闭,1=开启
        'promotion_message_enabled', // 推广消息:0=关闭,1=开启
        'email_login_enabled', // 登录邮件通知:0=关闭,1=开启
        'email_trading_enabled', // 交易邮件通知:0=关闭,1=开启
        'email_withdrawal_enabled', // 提现邮件通知:0=关闭,1=开启
        'email_security_enabled', // 安全邮件通知:0=关闭,1=开启
        'email_promotion_enabled', // 推广邮件通知:0=关闭,1=开启
        'email_news_enabled', // 新闻邮件通知:0=关闭,1=开启
        'push_login_enabled', // 登录推送通知:0=关闭,1=开启
        'push_trading_enabled', // 交易推送通知:0=关闭,1=开启
        'push_price_alert_enabled', // 价格提醒推送:0=关闭,1=开启
        'push_security_enabled', // 安全推送通知:0=关闭,1=开启
        'push_promotion_enabled', // 推广推送通知:0=关闭,1=开启
        'push_news_enabled', // 新闻推送通知:0=关闭,1=开启
        'sms_login_enabled', // 登录短信通知:0=关闭,1=开启
        'sms_trading_enabled', // 交易短信通知:0=关闭,1=开启
        'sms_withdrawal_enabled', // 提现短信通知:0=关闭,1=开启
        'sms_security_enabled', // 安全短信通知:0=关闭,1=开启
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'user_id' => 'integer', // 用户ID
        'system_message_enabled' => 'integer', // 系统消息:0=关闭,1=开启
        'trading_message_enabled' => 'integer', // 交易消息:0=关闭,1=开启
        'security_message_enabled' => 'integer', // 安全消息:0=关闭,1=开启
        'promotion_message_enabled' => 'integer', // 推广消息:0=关闭,1=开启
        'email_login_enabled' => 'integer', // 登录邮件通知:0=关闭,1=开启
        'email_trading_enabled' => 'integer', // 交易邮件通知:0=关闭,1=开启
        'email_withdrawal_enabled' => 'integer', // 提现邮件通知:0=关闭,1=开启
        'email_security_enabled' => 'integer', // 安全邮件通知:0=关闭,1=开启
        'email_promotion_enabled' => 'integer', // 推广邮件通知:0=关闭,1=开启
        'email_news_enabled' => 'integer', // 新闻邮件通知:0=关闭,1=开启
        'push_login_enabled' => 'integer', // 登录推送通知:0=关闭,1=开启
        'push_trading_enabled' => 'integer', // 交易推送通知:0=关闭,1=开启
        'push_price_alert_enabled' => 'integer', // 价格提醒推送:0=关闭,1=开启
        'push_security_enabled' => 'integer', // 安全推送通知:0=关闭,1=开启
        'push_promotion_enabled' => 'integer', // 推广推送通知:0=关闭,1=开启
        'push_news_enabled' => 'integer', // 新闻推送通知:0=关闭,1=开启
        'sms_login_enabled' => 'integer', // 登录短信通知:0=关闭,1=开启
        'sms_trading_enabled' => 'integer', // 交易短信通知:0=关闭,1=开启
        'sms_withdrawal_enabled' => 'integer', // 提现短信通知:0=关闭,1=开启
        'sms_security_enabled' => 'integer', // 安全短信通知:0=关闭,1=开启
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取系统消息开关状态
     */
    public function getSystemMessageEnabled(): int
    {
        return $this->system_message_enabled;
    }

    /**
     * 设置系统消息开关状态
     *
     * @param int $value 开关状态值
     */
    public function setSystemMessageEnabled($value): static
    {
        $this->system_message_enabled = $value;
        return $this;
    }

    /**
     * 获取交易消息开关状态
     */
    public function getTradingMessageEnabled(): int
    {
        return $this->trading_message_enabled;
    }

    /**
     * 设置交易消息开关状态
     *
     * @param int $value 开关状态值
     */
    public function setTradingMessageEnabled($value): static
    {
        $this->trading_message_enabled = $value;
        return $this;
    }

    /**
     * 获取安全消息开关状态
     */
    public function getSecurityMessageEnabled(): int
    {
        return $this->security_message_enabled;
    }

    /**
     * 设置安全消息开关状态
     *
     * @param int $value 开关状态值
     */
    public function setSecurityMessageEnabled($value): static
    {
        $this->security_message_enabled = $value;
        return $this;
    }

    /**
     * 获取推广消息开关状态
     */
    public function getPromotionMessageEnabled(): int
    {
        return $this->promotion_message_enabled;
    }

    /**
     * 设置推广消息开关状态
     *
     * @param int $value 开关状态值
     */
    public function setPromotionMessageEnabled($value): static
    {
        $this->promotion_message_enabled = $value;
        return $this;
    }

    /**
     * 获取登录邮件通知开关状态
     */
    public function getEmailLoginEnabled(): int
    {
        return $this->email_login_enabled;
    }

    /**
     * 设置登录邮件通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setEmailLoginEnabled($value): static
    {
        $this->email_login_enabled = $value;
        return $this;
    }

    /**
     * 获取交易邮件通知开关状态
     */
    public function getEmailTradingEnabled(): int
    {
        return $this->email_trading_enabled;
    }

    /**
     * 设置交易邮件通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setEmailTradingEnabled($value): static
    {
        $this->email_trading_enabled = $value;
        return $this;
    }

    /**
     * 获取提现邮件通知开关状态
     */
    public function getEmailWithdrawalEnabled(): int
    {
        return $this->email_withdrawal_enabled;
    }

    /**
     * 设置提现邮件通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setEmailWithdrawalEnabled($value): static
    {
        $this->email_withdrawal_enabled = $value;
        return $this;
    }

    /**
     * 获取安全邮件通知开关状态
     */
    public function getEmailSecurityEnabled(): int
    {
        return $this->email_security_enabled;
    }

    /**
     * 设置安全邮件通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setEmailSecurityEnabled($value): static
    {
        $this->email_security_enabled = $value;
        return $this;
    }

    /**
     * 获取推广邮件通知开关状态
     */
    public function getEmailPromotionEnabled(): int
    {
        return $this->email_promotion_enabled;
    }

    /**
     * 设置推广邮件通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setEmailPromotionEnabled($value): static
    {
        $this->email_promotion_enabled = $value;
        return $this;
    }

    /**
     * 获取新闻邮件通知开关状态
     */
    public function getEmailNewsEnabled(): int
    {
        return $this->email_news_enabled;
    }

    /**
     * 设置新闻邮件通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setEmailNewsEnabled($value): static
    {
        $this->email_news_enabled = $value;
        return $this;
    }

    /**
     * 获取登录推送通知开关状态
     */
    public function getPushLoginEnabled(): int
    {
        return $this->push_login_enabled;
    }

    /**
     * 设置登录推送通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setPushLoginEnabled($value): static
    {
        $this->push_login_enabled = $value;
        return $this;
    }

    /**
     * 获取交易推送通知开关状态
     */
    public function getPushTradingEnabled(): int
    {
        return $this->push_trading_enabled;
    }

    /**
     * 设置交易推送通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setPushTradingEnabled($value): static
    {
        $this->push_trading_enabled = $value;
        return $this;
    }

    /**
     * 获取价格提醒推送开关状态
     */
    public function getPushPriceAlertEnabled(): int
    {
        return $this->push_price_alert_enabled;
    }

    /**
     * 设置价格提醒推送开关状态
     *
     * @param int $value 开关状态值
     */
    public function setPushPriceAlertEnabled($value): static
    {
        $this->push_price_alert_enabled = $value;
        return $this;
    }

    /**
     * 获取安全推送通知开关状态
     */
    public function getPushSecurityEnabled(): int
    {
        return $this->push_security_enabled;
    }

    /**
     * 设置安全推送通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setPushSecurityEnabled($value): static
    {
        $this->push_security_enabled = $value;
        return $this;
    }

    /**
     * 获取推广推送通知开关状态
     */
    public function getPushPromotionEnabled(): int
    {
        return $this->push_promotion_enabled;
    }

    /**
     * 设置推广推送通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setPushPromotionEnabled($value): static
    {
        $this->push_promotion_enabled = $value;
        return $this;
    }

    /**
     * 获取新闻推送通知开关状态
     */
    public function getPushNewsEnabled(): int
    {
        return $this->push_news_enabled;
    }

    /**
     * 设置新闻推送通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setPushNewsEnabled($value): static
    {
        $this->push_news_enabled = $value;
        return $this;
    }

    /**
     * 获取登录短信通知开关状态
     */
    public function getSmsLoginEnabled(): int
    {
        return $this->sms_login_enabled;
    }

    /**
     * 设置登录短信通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setSmsLoginEnabled($value): static
    {
        $this->sms_login_enabled = $value;
        return $this;
    }

    /**
     * 获取交易短信通知开关状态
     */
    public function getSmsTradingEnabled(): int
    {
        return $this->sms_trading_enabled;
    }

    /**
     * 设置交易短信通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setSmsTradingEnabled($value): static
    {
        $this->sms_trading_enabled = $value;
        return $this;
    }

    /**
     * 获取提现短信通知开关状态
     */
    public function getSmsWithdrawalEnabled(): int
    {
        return $this->sms_withdrawal_enabled;
    }

    /**
     * 设置提现短信通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setSmsWithdrawalEnabled($value): static
    {
        $this->sms_withdrawal_enabled = $value;
        return $this;
    }

    /**
     * 获取安全短信通知开关状态
     */
    public function getSmsSecurityEnabled(): int
    {
        return $this->sms_security_enabled;
    }

    /**
     * 设置安全短信通知开关状态
     *
     * @param int $value 开关状态值
     */
    public function setSmsSecurityEnabled($value): static
    {
        $this->sms_security_enabled = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 获取关联的用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
