<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户杠杆借贷记录模型
 */

namespace App\Model\User;

use App\Model\Enums\Trade\Margin\MarginBorrowStatus;
use App\Model\Currency\Currency;
use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int $user_id 用户ID
 * @property int $currency_id 借贷币种ID
 * @property int $account_type 账户类型：2-全仓杠杆，3-逐仓杠杆
 * @property float $borrow_amount 借贷金额
 * @property float $repaid_amount 已还金额
 * @property float $daily_rate 日利率
 * @property int $borrow_source 借贷来源：1-自动借贷，2-手动借贷
 * @property \Carbon\Carbon|null $borrow_time 借贷时间
 * @property \Carbon\Carbon|null $last_interest_time 最后计息时间
 * @property \Carbon\Carbon|null $repay_time 还款时间
 * @property MarginBorrowStatus $status 状态：1-借贷中，2-已还清，3-逾期
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 */
final class UserMarginBorrow extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 借贷币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 账户类型：2-全仓杠杆，3-逐仓杠杆
     */
    public const FIELD_ACCOUNT_TYPE = 'account_type';
    /**
     * 借贷金额
     */
    public const FIELD_BORROW_AMOUNT = 'borrow_amount';
    /**
     * 已还金额
     */
    public const FIELD_REPAID_AMOUNT = 'repaid_amount';
    /**
     * 日利率
     */
    public const FIELD_DAILY_RATE = 'daily_rate';
    /**
     * 借贷来源：1-自动借贷，2-手动借贷
     */
    public const FIELD_BORROW_SOURCE = 'borrow_source';
    /**
     * 借贷时间
     */
    public const FIELD_BORROW_TIME = 'borrow_time';
    /**
     * 最后计息时间
     */
    public const FIELD_LAST_INTEREST_TIME = 'last_interest_time';
    /**
     * 还款时间
     */
    public const FIELD_REPAY_TIME = 'repay_time';
    /**
     * 状态：1-借贷中，2-已还清，3-逾期
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'user_margin_borrow';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'user_id', // 用户ID
        'currency_id', // 借贷币种ID
        'account_type', // 账户类型：2-全仓杠杆，3-逐仓杠杆
        'borrow_amount', // 借贷金额
        'repaid_amount', // 已还金额
        'daily_rate', // 日利率
        'borrow_source', // 借贷来源：1-自动借贷，2-手动借贷
        'borrow_time', // 借贷时间
        'last_interest_time', // 最后计息时间
        'repay_time', // 还款时间
        'status', // 状态：1-借贷中，2-已还清，3-逾期
        'created_at', // 创建时间
        'updated_at' // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'user_id' => 'integer', // 用户ID
        'currency_id' => 'integer', // 借贷币种ID
        'account_type' => 'integer', // 账户类型
        'borrow_amount' => 'float', // 借贷金额
        'repaid_amount' => 'float', // 已还金额
        'daily_rate' => 'float', // 日利率
        'borrow_source' => 'integer', // 借贷来源
        'borrow_time' => 'datetime', // 借贷时间
        'last_interest_time' => 'datetime', // 最后计息时间
        'repay_time' => 'datetime', // 还款时间
        'status' => MarginBorrowStatus::class, // 状态
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime' // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取借贷币种ID
     */
    public function getCurrencyId(): int
    {
        return $this->currency_id;
    }

    /**
     * 设置借贷币种ID
     *
     * @param int $value 借贷币种ID值
     */
    public function setCurrencyId($value): static
    {
        $this->currency_id = $value;
        return $this;
    }

    /**
     * 获取账户类型
     */
    public function getAccountType(): int
    {
        return $this->account_type;
    }

    /**
     * 设置账户类型
     *
     * @param int $value 账户类型值
     */
    public function setAccountType($value): static
    {
        $this->account_type = $value;
        return $this;
    }

    /**
     * 获取借贷金额
     */
    public function getBorrowAmount(): float
    {
        return $this->borrow_amount;
    }

    /**
     * 设置借贷金额
     *
     * @param float $value 借贷金额值
     */
    public function setBorrowAmount($value): static
    {
        $this->borrow_amount = $value;
        return $this;
    }

    /**
     * 获取已还金额
     */
    public function getRepaidAmount(): float
    {
        return $this->repaid_amount;
    }

    /**
     * 设置已还金额
     *
     * @param float $value 已还金额值
     */
    public function setRepaidAmount($value): static
    {
        $this->repaid_amount = $value;
        return $this;
    }

    /**
     * 获取日利率
     */
    public function getDailyRate(): float
    {
        return $this->daily_rate;
    }

    /**
     * 设置日利率
     *
     * @param float $value 日利率值
     */
    public function setDailyRate($value): static
    {
        $this->daily_rate = $value;
        return $this;
    }

    /**
     * 获取借贷来源
     */
    public function getBorrowSource(): int
    {
        return $this->borrow_source;
    }

    /**
     * 设置借贷来源
     *
     * @param int $value 借贷来源值
     */
    public function setBorrowSource($value): static
    {
        $this->borrow_source = $value;
        return $this;
    }

    /**
     * 获取借贷时间
     */
    public function getBorrowTime(): ?\Carbon\Carbon
    {
        return $this->borrow_time;
    }

    /**
     * 设置借贷时间
     *
     * @param \Carbon\Carbon|string $value 借贷时间值
     */
    public function setBorrowTime($value): static
    {
        $this->borrow_time = $value;
        return $this;
    }

    /**
     * 获取最后计息时间
     */
    public function getLastInterestTime(): ?\Carbon\Carbon
    {
        return $this->last_interest_time;
    }

    /**
     * 设置最后计息时间
     *
     * @param \Carbon\Carbon|string $value 最后计息时间值
     */
    public function setLastInterestTime($value): static
    {
        $this->last_interest_time = $value;
        return $this;
    }

    /**
     * 获取还款时间
     */
    public function getRepayTime(): ?\Carbon\Carbon
    {
        return $this->repay_time;
    }

    /**
     * 设置还款时间
     *
     * @param \Carbon\Carbon|string $value 还款时间值
     */
    public function setRepayTime($value): static
    {
        $this->repay_time = $value;
        return $this;
    }

    /**
     * 获取状态
     */
    public function getStatus(): MarginBorrowStatus
    {
        return $this->status;
    }

    /**
     * 设置状态
     *
     * @param MarginBorrowStatus|int $value 状态值
     */
    public function setStatus($value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联借贷币种
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }

    /**
     * 关联利息记录
     */
    public function interests()
    {
        return $this->hasMany(UserMarginInterest::class, 'borrow_id', 'id');
    }
}
