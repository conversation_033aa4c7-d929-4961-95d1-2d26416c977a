<?php

declare(strict_types=1);

namespace App\Model\User;

use App\QueryBuilder\Model;
use Carbon\Carbon;

/**
 * @property int $id 
 * @property int $user_id 
 * @property int $currency_id 
 * @property int $hold_units 1-数量，2-成本价值，3-名义价值
 * @property int $hold_mode 1-单向持仓，2-双向持仓
 * @property int $pnl_source 1-标记价格，2-最新价格
 * @property int $tp_sl_source 1-最新价格，2-标记价格
 * @property int $aassets_mode 1-单币种保证金，2-联合保证金
 * @property int $price_protect 0-关闭，1-开启
 * @property int $lever 杠杆倍数
 * @property int $margin_type 1-全仓，2-逐仓
 * @property Carbon|null $created_at 
 * @property Carbon|null $updated_at 
 */
final class UserPerprtualConfig extends Model
{
    /**
     * 配置ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 币种ID
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 持仓单位：1-数量，2-成本价值，3-名义价值
     */
    public const FIELD_HOLD_UNITS = 'hold_units';
    /**
     * 持仓模式：1-单向持仓，2-双向持仓
     */
    public const FIELD_HOLD_MODE = 'hold_mode';
    /**
     * 盈亏来源：1-标记价格，2-最新价格
     */
    public const FIELD_PNL_SOURCE = 'pnl_source';
    /**
     * 止盈止损来源：1-最新价格，2-标记价格
     */
    public const FIELD_TP_SL_SOURCE = 'tp_sl_source';
    /**
     * 资产模式：1-单币种保证金，2-联合保证金
     */
    public const FIELD_AASSETS_MODE = 'aassets_mode';
    /**
     * 价差保护：0-关闭，1-开启
     */
    public const FIELD_PRICE_PROTECT = 'price_protect';
    /**
     * 杠杆倍数
     */
    public const FIELD_LEVER = 'lever';
    /**
     * 默认保证金类型：1-全仓，2-逐仓
     */
    public const FIELD_MARGIN_TYPE = 'margin_type';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'user_perprtual_config';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'currency_id', 'hold_units', 'hold_mode', 'pnl_source', 'tp_sl_source', 'aassets_mode', 'price_protect', 'lever', 'margin_type', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'currency_id' => 'integer',
        'hold_units' => 'integer', // 持仓单位
        'hold_mode' => 'integer', // 持仓模式
        'pnl_source' => 'integer', // 盈亏来源
        'tp_sl_source' => 'integer', // 止盈止损来源
        'aassets_mode' => 'integer', // 资产模式
        'price_protect' => 'integer', // 价差保护
        'lever' => 'integer', // 杠杆倍数
        'margin_type' => 'integer', // 默认保证金类型
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 与用户模型的关联
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 与币种模型的关联
     */
    public function currency()
    {
        return $this->belongsTo(\App\Model\Currency\Currency::class, 'currency_id', 'id');
    }

    /**
     * 获取持仓单位名称
     */
    public function getHoldUnitsName(): string
    {
        return match($this->hold_units) {
            1 => '数量',
            2 => '成本价值',
            3 => '名义价值',
            default => '未知'
        };
    }

    /**
     * 获取持仓模式名称
     */
    public function getHoldModeName(): string
    {
        return match($this->hold_mode) {
            1 => '单向持仓',
            2 => '双向持仓',
            default => '未知'
        };
    }

    /**
     * 获取盈亏来源名称
     */
    public function getPnlSourceName(): string
    {
        return match($this->pnl_source) {
            1 => '标记价格',
            2 => '最新价格',
            default => '未知'
        };
    }

    /**
     * 获取止盈止损来源名称
     */
    public function getTpSlSourceName(): string
    {
        return match($this->tp_sl_source) {
            1 => '最新价格',
            2 => '标记价格',
            default => '未知'
        };
    }

    /**
     * 获取资产模式名称
     */
    public function getAssetModeName(): string
    {
        return match($this->aassets_mode) {
            1 => '单币种保证金',
            2 => '联合保证金',
            default => '未知'
        };
    }

    /**
     * 获取保证金类型名称
     */
    public function getMarginTypeName(): string
    {
        return match($this->margin_type) {
            1 => '全仓保证金',
            2 => '逐仓保证金',
            default => '未知'
        };
    }

    /**
     * 检查是否为双向持仓模式
     */
    public function isBidirectionalMode(): bool
    {
        return $this->hold_mode === 2;
    }

    /**
     * 检查是否开启价差保护
     */
    public function isPriceProtectionEnabled(): bool
    {
        return $this->price_protect === 1;
    }

    /**
     * 检查是否为全仓保证金模式
     */
    public function isCrossMarginMode(): bool
    {
        return $this->margin_type === 1;
    }
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getHoldUnits() : int
    {
        return $this->hold_units;
    }
    public function setHoldUnits($value) : object
    {
        $this->hold_units = $value;
        return $this;
    }
    public function getHoldMode() : int
    {
        return $this->hold_mode;
    }
    public function setHoldMode($value) : object
    {
        $this->hold_mode = $value;
        return $this;
    }
    public function getPnlSource() : int
    {
        return $this->pnl_source;
    }
    public function setPnlSource($value) : object
    {
        $this->pnl_source = $value;
        return $this;
    }
    public function getTpSlSource() : int
    {
        return $this->tp_sl_source;
    }
    public function setTpSlSource($value) : object
    {
        $this->tp_sl_source = $value;
        return $this;
    }
    public function getAassetsMode() : int
    {
        return $this->aassets_mode;
    }
    public function setAassetsMode($value) : object
    {
        $this->aassets_mode = $value;
        return $this;
    }
    public function getPriceProtect() : int
    {
        return $this->price_protect;
    }
    public function setPriceProtect($value) : object
    {
        $this->price_protect = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
    public function getLever() : int
    {
        return $this->lever;
    }
    public function setLever($value) : object
    {
        $this->lever = $value;
        return $this;
    }
    public function getMarginType() : int
    {
        return $this->margin_type;
    }
    public function setMarginType($value) : object
    {
        $this->margin_type = $value;
        return $this;
    }
}
