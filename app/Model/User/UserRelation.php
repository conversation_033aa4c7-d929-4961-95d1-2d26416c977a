<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户关系模型
 */

namespace App\Model\User;

use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int $user_id 用户ID
 * @property int|null $parent_id 直接上级ID
 * @property array|null $link 完整上级链路，格式：[4,3,2,1]（从直接上级到顶级）
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class UserRelation extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 直接上级ID
     */
    public const FIELD_PARENT_ID = 'parent_id';
    /**
     * 完整上级链路
     */
    public const FIELD_LINK = 'link';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_relation';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'user_id', // 用户ID
        'parent_id', // 直接上级ID
        'link', // 完整上级链路
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'user_id' => 'integer', // 用户ID
        'parent_id' => 'integer', // 直接上级ID
        'link' => 'array', // 完整上级链路
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取直接上级ID
     */
    public function getParentId(): ?int
    {
        return $this->parent_id;
    }

    /**
     * 设置直接上级ID
     *
     * @param int|null $value 直接上级ID值
     */
    public function setParentId($value): static
    {
        $this->parent_id = $value;
        return $this;
    }

    /**
     * 获取完整上级链路
     */
    public function getLink(): ?array
    {
        return $this->link;
    }

    /**
     * 设置完整上级链路
     *
     * @param array|null $value 完整上级链路数组
     */
    public function setLink($value): static
    {
        $this->link = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 获取关联的用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * 获取关联的上级用户模型
     */
    public function parent()
    {
        return $this->belongsTo(User::class, self::FIELD_PARENT_ID, User::FIELD_ID);
    }
}
