<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户通行密钥 Passkey 模型
 */

namespace App\Model\User;

use App\QueryBuilder\Model;
use Carbon\Carbon;

/**
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property string $credential_id 凭证ID
 * @property string $public_key 公钥
 * @property int $counter 计数器
 * @property string $attestation_type 认证类型
 * @property string|null $device_info 设备信息
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
final class UserPasskey extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 凭证ID
     */
    public const FIELD_CREDENTIAL_ID = 'credential_id';
    /**
     * 公钥
     */
    public const FIELD_PUBLIC_KEY = 'public_key';
    /**
     * 计数器
     */
    public const FIELD_COUNTER = 'counter';
    /**
     * 认证类型
     */
    public const FIELD_ATTESTATION_TYPE = 'attestation_type';
    /**
     * 设备信息
     */
    public const FIELD_DEVICE_INFO = 'device_info';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_passkey';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 主键ID
        'user_id', // 用户ID
        'credential_id', // 凭证ID
        'public_key', // 公钥
        'counter', // 计数器
        'attestation_type', // 认证类型
        'device_info', // 设备信息
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'user_id' => 'integer', // 用户ID
        'counter' => 'integer', // 计数器
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     */
    public function setUserId(int $value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取凭证ID
     */
    public function getCredentialId(): string
    {
        return $this->credential_id;
    }

    /**
     * 设置凭证ID
     */
    public function setCredentialId(string $value): static
    {
        $this->credential_id = $value;
        return $this;
    }

    /**
     * 获取公钥
     */
    public function getPublicKey(): string
    {
        return $this->public_key;
    }

    /**
     * 设置公钥
     */
    public function setPublicKey(string $value): static
    {
        $this->public_key = $value;
        return $this;
    }

    /**
     * 获取计数器
     */
    public function getCounter(): int
    {
        return $this->counter;
    }

    /**
     * 设置计数器
     */
    public function setCounter(int $value): static
    {
        $this->counter = $value;
        return $this;
    }

    /**
     * 获取认证类型
     */
    public function getAttestationType(): string
    {
        return $this->attestation_type;
    }

    /**
     * 设置认证类型
     */
    public function setAttestationType(string $value): static
    {
        $this->attestation_type = $value;
        return $this;
    }

    /**
     * 获取设备信息
     */
    public function getDeviceInfo(): ?string
    {
        return $this->device_info;
    }

    /**
     * 设置设备信息
     */
    public function setDeviceInfo(?string $value): static
    {
        $this->device_info = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
