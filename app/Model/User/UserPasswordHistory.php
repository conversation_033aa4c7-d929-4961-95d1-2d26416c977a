<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户密码历史模型
 */

namespace App\Model\User;

use App\Model\User\Enums\PasswordType;
use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int $user_id 用户ID
 * @property PasswordType $password_type 密码类型:1=登录密码,2=交易密码
 * @property string $password_hash 密码哈希
 * @property string $change_ip 修改IP
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class UserPasswordHistory extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 密码类型:1=登录密码,2=交易密码
     */
    public const FIELD_PASSWORD_TYPE = 'password_type';
    /**
     * 密码哈希
     */
    public const FIELD_PASSWORD_HASH = 'password_hash';
    /**
     * 修改IP
     */
    public const FIELD_CHANGE_IP = 'change_ip';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_password_history';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'user_id', // 用户ID
        'password_type', // 密码类型:1=登录密码,2=交易密码
        'password_hash', // 密码哈希
        'change_ip', // 修改IP
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'user_id' => 'integer', // 用户ID
        'password_type' => PasswordType::class, // 密码类型:1=登录密码,2=交易密码
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取密码类型
     */
    public function getPasswordType(): PasswordType
    {
        return $this->password_type;
    }

    /**
     * 设置密码类型
     *
     * @param PasswordType $value 密码类型值
     */
    public function setPasswordType(PasswordType $value): static
    {
        $this->password_type = $value;
        return $this;
    }

    /**
     * 获取密码哈希
     */
    public function getPasswordHash(): string
    {
        return $this->password_hash;
    }

    /**
     * 设置密码哈希
     *
     * @param string $value 密码哈希值
     */
    public function setPasswordHash($value): static
    {
        $this->password_hash = $value;
        return $this;
    }

    /**
     * 获取修改IP
     */
    public function getChangeIp(): string
    {
        return $this->change_ip;
    }

    /**
     * 设置修改IP
     *
     * @param string $value IP地址
     */
    public function setChangeIp($value): static
    {
        $this->change_ip = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 获取关联的用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }
}
