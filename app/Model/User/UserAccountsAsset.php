<?php

declare(strict_types=1);

namespace App\Model\User;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property int $user_id 
 * @property int $account_type 枚举值：AccountType
 * @property int $currency_id 对应币种
 * @property float $available 可用余额
 * @property float $frozen 冻结资金
 * @property float $locked 锁仓资金
 * @property float $margin_quote 逐仓杠杆交易账户资产
 * @property float $margin_borrow 杠杆计价货币借款金额
 * @property float $borrowed_amount 杠杆基础币借贷金额
 * @property float $interest_amount 杠杆利息金额
 * @property int $status 账户状态
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class UserAccountsAsset extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 枚举值：AccountType
     */
    public const FIELD_ACCOUNT_TYPE = 'account_type';
    /**
     * 对应币种
     */
    public const FIELD_CURRENCY_ID = 'currency_id';
    /**
     * 可用余额
     */
    public const FIELD_AVAILABLE = 'available';
    /**
     * 冻结资金
     */
    public const FIELD_FROZEN = 'frozen';
    /**
     * 锁仓资金
     */
    public const FIELD_LOCKED = 'locked';
    /**
     * 逐仓杠杆交易账户资产
     */
    public const FIELD_MARGIN_QUOTE = 'margin_quote';

    /**
     * 逐仓杠杆借贷资金
     */
    public const FIELD_MARGIN_BORROW = 'margin_borrow';

    /**
     * 杠杆借贷金额
     */
    public const FIELD_BORROWED_AMOUNT = 'borrowed_amount';
    /**
     * 杠杆利息金额
     */
    public const FIELD_INTEREST_AMOUNT = 'interest_amount';
    /**
     * 账户状态
     */
    public const FIELD_STATUS = 'status';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'user_accounts_assets';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'user_id', 'account_type', 'currency_id', 'available', 'frozen', 'locked', 'margin_quote', 'margin_borrow', 'borrowed_amount', 'interest_amount', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        // 枚举值：AccountType
        'account_type' => 'integer',
        // 对应币种
        'currency_id' => 'integer',
        // 可用余额
        'available' => 'float',
        // 冻结资金
        'frozen' => 'float',
        // 锁仓资金
        'locked' => 'float',
        // 逐仓杠杆交易账户资产
        'margin_quote' => 'float',
        // 杠杆计价货币借款金额
        'margin_borrow' => 'float',
        // 杠杆基础币借贷金额
        'borrowed_amount' => 'float',
        // 杠杆利息金额
        'interest_amount' => 'float',
        // 账户状态
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getUserId() : int
    {
        return $this->user_id;
    }
    public function setUserId($value) : object
    {
        $this->user_id = $value;
        return $this;
    }
    public function getAccountType() : int
    {
        return $this->account_type;
    }
    public function setAccountType($value) : object
    {
        $this->account_type = $value;
        return $this;
    }
    public function getCurrencyId() : int
    {
        return $this->currency_id;
    }
    public function setCurrencyId($value) : object
    {
        $this->currency_id = $value;
        return $this;
    }
    public function getAvailable() : float
    {
        return (float)$this->available;
    }
    public function setAvailable($value) : object
    {
        $this->available = $value;
        return $this;
    }
    public function getFrozen() : float
    {
        return (float)$this->frozen;
    }
    public function setFrozen($value) : object
    {
        $this->frozen = $value;
        return $this;
    }
    public function getLocked() : float
    {
        return (float)$this->locked;
    }
    public function setLocked($value) : object
    {
        $this->locked = $value;
        return $this;
    }
    public function getMarginQuote() : float
    {
        return (float)$this->margin_quote;
    }
    public function setMarginQuote($value) : object
    {
        $this->margin_quote = $value;
        return $this;
    }
    public function getBorrowedAmount() : float
    {
        return (float)$this->borrowed_amount;
    }
    public function setBorrowedAmount($value) : object
    {
        $this->borrowed_amount = $value;
        return $this;
    }

    public function getMarginBorrow(): float
    {
        return (float)$this->margin_borrow;
    }

    public function setMarginBorrow($value): static
    {
        $this->margin_borrow = $value;
        return $this;
    }

    public function getInterestAmount() : float
    {
        return (float)$this->interest_amount;
    }
    public function setInterestAmount($value) : object
    {
        $this->interest_amount = $value;
        return $this;
    }
    public function getStatus() : int
    {
        return $this->status;
    }
    public function setStatus($value) : object
    {
        $this->status = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
