<?php

declare(strict_types=1);
/**
 * 策略平台API
 * VIP等级模型
 */

namespace App\Model\User;

use App\Model\User\Enums\VipLevelStatus;
use App\QueryBuilder\Model;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\HasMany;

/**
 * @property int $id 主键ID
 * @property string $name 等级名称 VIP0,VIP1
 * @property int $level 等级数值
 * @property string|null $icon 等级图标
 * @property string|null $color 等级颜色
 * @property float $spot_trading_volume_requirement 现货交易量要求
 * @property float $futures_trading_volume_requirement 合约交易量要求
 * @property float $total_asset_requirement 总资产要求
 * @property string|null $specific_asset_symbol 特定币种资产要求符号
 * @property float $specific_asset_amount 特定币种资产要求数量
 * @property float $spot_maker_fee_rate 现货挂单手续费率
 * @property float $spot_taker_fee_rate 现货吃单手续费率
 * @property float $futures_maker_fee_rate 合约挂单手续费率
 * @property float $futures_taker_fee_rate 合约吃单手续费率
 * @property float $daily_withdrawal_limit 日提现限额
 * @property array|null $vip_gift VIP升级礼包（分trading（交易）,assets（资产）两种类型）
 * @property array|null $vip_privileges VIP权限列表
 * @property string|null $description 等级描述
 * @property VipLevelStatus $status 状态:1=启用,2=禁用
 * @property int $sort 排序
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
final class VipLevel extends Model
{
    /**
     * 主键ID
     */
    public const FIELD_ID = 'id';
    /**
     * 等级名称
     */
    public const FIELD_NAME = 'name';
    /**
     * 等级数值
     */
    public const FIELD_LEVEL = 'level';
    /**
     * 等级图标
     */
    public const FIELD_ICON = 'icon';
    /**
     * 等级颜色
     */
    public const FIELD_COLOR = 'color';
    /**
     * 现货交易量要求
     */
    public const FIELD_SPOT_TRADING_VOLUME_REQUIREMENT = 'spot_trading_volume_requirement';
    /**
     * 合约交易量要求
     */
    public const FIELD_FUTURES_TRADING_VOLUME_REQUIREMENT = 'futures_trading_volume_requirement';
    /**
     * 总资产要求
     */
    public const FIELD_TOTAL_ASSET_REQUIREMENT = 'total_asset_requirement';
    /**
     * 特定币种资产要求符号
     */
    public const FIELD_SPECIFIC_ASSET_SYMBOL = 'specific_asset_symbol';
    /**
     * 特定币种资产要求数量
     */
    public const FIELD_SPECIFIC_ASSET_AMOUNT = 'specific_asset_amount';
    /**
     * 现货挂单手续费率
     */
    public const FIELD_SPOT_MAKER_FEE_RATE = 'spot_maker_fee_rate';
    /**
     * 现货吃单手续费率
     */
    public const FIELD_SPOT_TAKER_FEE_RATE = 'spot_taker_fee_rate';
    /**
     * 合约挂单手续费率
     */
    public const FIELD_FUTURES_MAKER_FEE_RATE = 'futures_maker_fee_rate';
    /**
     * 合约吃单手续费率
     */
    public const FIELD_FUTURES_TAKER_FEE_RATE = 'futures_taker_fee_rate';
    /**
     * 日提现限额
     */
    public const FIELD_DAILY_WITHDRAWAL_LIMIT = 'daily_withdrawal_limit';
    /**
     * VIP升级礼包
     */
    public const FIELD_VIP_GIFT = 'vip_gift';
    /**
     * VIP权限列表
     */
    public const FIELD_VIP_PRIVILEGES = 'vip_privileges';
    /**
     * 等级描述
     */
    public const FIELD_DESCRIPTION = 'description';
    /**
     * 状态
     */
    public const FIELD_STATUS = 'status';
    /**
     * 排序
     */
    public const FIELD_SORT = 'sort';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_vip_level';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 主键ID
        'name', // 等级名称
        'level', // 等级数值
        'icon', // 等级图标
        'color', // 等级颜色
        'spot_trading_volume_requirement', // 现货交易量要求
        'futures_trading_volume_requirement', // 合约交易量要求
        'total_asset_requirement', // 总资产要求
        'specific_asset_symbol', // 特定币种资产要求符号
        'specific_asset_amount', // 特定币种资产要求数量
        'spot_maker_fee_rate', // 现货挂单手续费率
        'spot_taker_fee_rate', // 现货吃单手续费率
        'futures_maker_fee_rate', // 合约挂单手续费率
        'futures_taker_fee_rate', // 合约吃单手续费率
        'daily_withdrawal_limit', // 日提现限额
        'vip_gift', // VIP升级礼包
        'vip_privileges', // VIP权限列表
        'description', // 等级描述
        'status', // 状态
        'sort', // 排序
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'level' => 'integer', // 等级数值
        'spot_trading_volume_requirement' => 'decimal:8', // 现货交易量要求
        'futures_trading_volume_requirement' => 'decimal:8', // 合约交易量要求
        'total_asset_requirement' => 'decimal:8', // 总资产要求
        'specific_asset_amount' => 'decimal:8', // 特定币种资产要求数量
        'spot_maker_fee_rate' => 'decimal:6', // 现货挂单手续费率
        'spot_taker_fee_rate' => 'decimal:6', // 现货吃单手续费率
        'futures_maker_fee_rate' => 'decimal:6', // 合约挂单手续费率
        'futures_taker_fee_rate' => 'decimal:6', // 合约吃单手续费率
        'daily_withdrawal_limit' => 'decimal:8', // 日提现限额
        'vip_gift' => 'array', // VIP升级礼包
        'vip_privileges' => 'array', // VIP权限列表
        'status' => VipLevelStatus::class, // 状态
        'sort' => 'integer', // 排序
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置主键ID
     */
    public function setId(int $value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取等级名称
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * 设置等级名称
     */
    public function setName(string $value): static
    {
        $this->name = $value;
        return $this;
    }

    /**
     * 获取等级数值
     */
    public function getLevel(): int
    {
        return $this->level;
    }

    /**
     * 设置等级数值
     */
    public function setLevel(int $value): static
    {
        $this->level = $value;
        return $this;
    }

    /**
     * 获取等级图标
     */
    public function getIcon(): ?string
    {
        return $this->icon;
    }

    /**
     * 设置等级图标
     */
    public function setIcon(?string $value): static
    {
        $this->icon = $value;
        return $this;
    }

    /**
     * 获取等级颜色
     */
    public function getColor(): ?string
    {
        return $this->color;
    }

    /**
     * 设置等级颜色
     */
    public function setColor(?string $value): static
    {
        $this->color = $value;
        return $this;
    }

    /**
     * 获取现货交易量要求
     */
    public function getSpotTradingVolumeRequirement(): float
    {
        return $this->spot_trading_volume_requirement;
    }

    /**
     * 设置现货交易量要求
     */
    public function setSpotTradingVolumeRequirement(float $value): static
    {
        $this->spot_trading_volume_requirement = $value;
        return $this;
    }

    /**
     * 获取合约交易量要求
     */
    public function getFuturesTradingVolumeRequirement(): float
    {
        return $this->futures_trading_volume_requirement;
    }

    /**
     * 设置合约交易量要求
     */
    public function setFuturesTradingVolumeRequirement(float $value): static
    {
        $this->futures_trading_volume_requirement = $value;
        return $this;
    }

    /**
     * 获取总资产要求
     */
    public function getTotalAssetRequirement(): float
    {
        return $this->total_asset_requirement;
    }

    /**
     * 设置总资产要求
     */
    public function setTotalAssetRequirement(float $value): static
    {
        $this->total_asset_requirement = $value;
        return $this;
    }

    /**
     * 获取特定币种资产要求符号
     */
    public function getSpecificAssetSymbol(): ?string
    {
        return $this->specific_asset_symbol;
    }

    /**
     * 设置特定币种资产要求符号
     */
    public function setSpecificAssetSymbol(?string $value): static
    {
        $this->specific_asset_symbol = $value;
        return $this;
    }

    /**
     * 获取特定币种资产要求数量
     */
    public function getSpecificAssetAmount(): float
    {
        return $this->specific_asset_amount;
    }

    /**
     * 设置特定币种资产要求数量
     */
    public function setSpecificAssetAmount(float $value): static
    {
        $this->specific_asset_amount = $value;
        return $this;
    }

    /**
     * 获取现货挂单手续费率
     */
    public function getSpotMakerFeeRate(): float
    {
        return (float)$this->spot_maker_fee_rate;
    }

    /**
     * 设置现货挂单手续费率
     */
    public function setSpotMakerFeeRate(float $value): static
    {
        $this->spot_maker_fee_rate = $value;
        return $this;
    }

    /**
     * 获取现货吃单手续费率
     */
    public function getSpotTakerFeeRate(): float
    {
        return (float)$this->spot_taker_fee_rate;
    }

    /**
     * 设置现货吃单手续费率
     */
    public function setSpotTakerFeeRate(float $value): static
    {
        $this->spot_taker_fee_rate = $value;
        return $this;
    }

    /**
     * 获取合约挂单手续费率
     */
    public function getFuturesMakerFeeRate(): float
    {
        return (float)$this->futures_maker_fee_rate;
    }

    /**
     * 设置合约挂单手续费率
     */
    public function setFuturesMakerFeeRate(float $value): static
    {
        $this->futures_maker_fee_rate = $value;
        return $this;
    }

    /**
     * 获取合约吃单手续费率
     */
    public function getFuturesTakerFeeRate(): float
    {
        return (float)$this->futures_taker_fee_rate;
    }

    /**
     * 设置合约吃单手续费率
     */
    public function setFuturesTakerFeeRate(float $value): static
    {
        $this->futures_taker_fee_rate = $value;
        return $this;
    }

    /**
     * 获取日提现限额
     */
    public function getDailyWithdrawalLimit(): float
    {
        return $this->daily_withdrawal_limit;
    }

    /**
     * 设置日提现限额
     */
    public function setDailyWithdrawalLimit(float $value): static
    {
        $this->daily_withdrawal_limit = $value;
        return $this;
    }

    /**
     * 获取VIP升级礼包
     */
    public function getVipGift(): ?array
    {
        return $this->vip_gift;
    }

    /**
     * 设置VIP升级礼包
     */
    public function setVipGift(?array $value): static
    {
        $this->vip_gift = $value;
        return $this;
    }

    /**
     * 获取VIP权限列表
     */
    public function getVipPrivileges(): ?array
    {
        return $this->vip_privileges;
    }

    /**
     * 设置VIP权限列表
     */
    public function setVipPrivileges(?array $value): static
    {
        $this->vip_privileges = $value;
        return $this;
    }

    /**
     * 获取等级描述
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * 设置等级描述
     */
    public function setDescription(?string $value): static
    {
        $this->description = $value;
        return $this;
    }

    /**
     * 获取状态
     */
    public function getStatus(): VipLevelStatus
    {
        return $this->status;
    }

    /**
     * 设置状态
     */
    public function setStatus(VipLevelStatus $value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取排序
     */
    public function getSort(): int
    {
        return $this->sort;
    }

    /**
     * 设置排序
     */
    public function setSort(int $value): static
    {
        $this->sort = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 关联用户VIP等级记录
     */
    public function userVipLevels(): HasMany
    {
        return $this->hasMany(UserVipLevel::class, 'vip_level_id', 'id');
    }

    /**
     * 关联当前激活的用户VIP等级记录
     */
    public function activeUserVipLevels(): HasMany
    {
        return $this->hasMany(UserVipLevel::class, 'vip_level_id', 'id')
            ->where(UserVipLevel::FIELD_IS_ACTIVE, UserVipLevel::IS_ACTIVE_YES);
    }

    /**
     * 获取启用状态的VIP等级列表
     */
    public static function getActiveVipLevels(): \Hyperf\Database\Model\Collection
    {
        return static::query()
            ->where(self::FIELD_STATUS, VipLevelStatus::ENABLED)
            ->orderBy(self::FIELD_SORT)
            ->orderBy(self::FIELD_LEVEL)
            ->get();
    }

    /**
     * 根据等级数值获取VIP等级
     */
    public static function getByLevel(int $level): ?self
    {
        return static::query()
            ->where(self::FIELD_LEVEL, $level)
            ->where(self::FIELD_STATUS, VipLevelStatus::ENABLED)
            ->first();
    }

    /**
     * 判断是否为启用状态
     */
    public function isEnabled(): bool
    {
        return $this->status === VipLevelStatus::ENABLED;
    }

    /**
     * 获取杠杆交易手续费率（如果有的话）
     * 注意：这里假设杠杆交易使用与合约相同的费率，实际可能需要单独的字段
     */
    public function getMarginMakerFeeRate(): float
    {
        return $this->futures_maker_fee_rate;
    }

    /**
     * 获取杠杆交易吃单手续费率
     */
    public function getMarginTakerFeeRate(): float
    {
        return $this->futures_taker_fee_rate;
    }
}
