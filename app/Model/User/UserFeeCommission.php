<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 用户手续费返佣记录模型
 */

namespace App\Model\User;

use App\Model\User\Enums\CommissionStatus;
use App\Model\User\Enums\TradeType;
use App\QueryBuilder\Model;

/**
 * @property int $id 自增主键
 * @property int|null $parent_id 上级ID（如果存在上级，则存在此ID）冗余字段
 * @property int $user_id 客户用户ID
 * @property int|null $agent_id 代理商ID（如果是代理商则存在此ID）冗余字段
 * @property int|null $agent_client_id 代理商直客ID（如果是代理商直客则存在此ID）冗余字段
 * @property int $order_id 订单ID
 * @property TradeType $trade_type 交易类型:1=现货,2=合约,3=现货杠杆
 * @property float $trade_amount 交易金额
 * @property float $fee_amount 手续费金额
 * @property float $commission_rate 返佣比例
 * @property float $commission_amount 返佣金额
 * @property CommissionStatus $status 状态:0=待结算,1=已结算,2=已取消
 * @property \Carbon\Carbon $trade_time 交易时间
 * @property \Carbon\Carbon|null $settled_at 结算时间
 * @property string|null $remark 备注
 * @property \Carbon\Carbon|null $created_at 创建时间
 * @property \Carbon\Carbon|null $updated_at 更新时间
 */
final class UserFeeCommission extends Model
{
    /**
     * 自增主键
     */
    public const FIELD_ID = 'id';
    /**
     * 上级ID（如果存在上级，则存在此ID）冗余字段
     */
    public const FIELD_PARENT_ID = 'parent_id';
    /**
     * 客户用户ID
     */
    public const FIELD_USER_ID = 'user_id';
    /**
     * 代理商ID（如果是代理商则存在此ID）冗余字段
     */
    public const FIELD_AGENT_ID = 'agent_id';
    /**
     * 代理商直客ID（如果是代理商直客则存在此ID）冗余字段
     */
    public const FIELD_AGENT_CLIENT_ID = 'agent_client_id';
    /**
     * 订单ID
     */
    public const FIELD_ORDER_ID = 'order_id';
    /**
     * 交易类型:1=现货,2=合约,3=现货杠杆
     */
    public const FIELD_TRADE_TYPE = 'trade_type';
    /**
     * 交易金额
     */
    public const FIELD_TRADE_AMOUNT = 'trade_amount';
    /**
     * 手续费金额
     */
    public const FIELD_FEE_AMOUNT = 'fee_amount';
    /**
     * 返佣比例
     */
    public const FIELD_COMMISSION_RATE = 'commission_rate';
    /**
     * 返佣金额
     */
    public const FIELD_COMMISSION_AMOUNT = 'commission_amount';
    /**
     * 状态:0=待结算,1=已结算,2=已取消
     */
    public const FIELD_STATUS = 'status';
    /**
     * 交易时间
     */
    public const FIELD_TRADE_TIME = 'trade_time';
    /**
     * 结算时间
     */
    public const FIELD_SETTLED_AT = 'settled_at';
    /**
     * 备注
     */
    public const FIELD_REMARK = 'remark';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';

    /**
     * The table associated with the model.
     */
    protected ?string $table = 'cpx_user_fee_commission';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = [
        'id', // 自增主键
        'parent_id', // 上级ID（如果存在上级，则存在此ID）冗余字段
        'user_id', // 客户用户ID
        'agent_id', // 代理商ID（如果是代理商则存在此ID）冗余字段
        'agent_client_id', // 代理商直客ID（如果是代理商直客则存在此ID）冗余字段
        'order_id', // 订单ID
        'trade_type', // 交易类型:1=现货,2=合约,3=现货杠杆
        'trade_amount', // 交易金额
        'fee_amount', // 手续费金额
        'commission_rate', // 返佣比例
        'commission_amount', // 返佣金额
        'status', // 状态:0=待结算,1=已结算,2=已取消
        'trade_time', // 交易时间
        'settled_at', // 结算时间
        'remark', // 备注
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = [
        'id' => 'integer', // 自增主键
        'parent_id' => 'integer', // 上级ID（如果存在上级，则存在此ID）冗余字段
        'user_id' => 'integer', // 客户用户ID
        'agent_id' => 'integer', // 代理商ID（如果是代理商则存在此ID）冗余字段
        'agent_client_id' => 'integer', // 代理商直客ID（如果是代理商直客则存在此ID）冗余字段
        'order_id' => 'integer', // 订单ID
        'trade_type' => TradeType::class, // 交易类型:1=现货,2=合约,3=现货杠杆
        'trade_amount' => 'decimal:8', // 交易金额
        'fee_amount' => 'decimal:8', // 手续费金额
        'commission_rate' => 'decimal:6', // 返佣比例
        'commission_amount' => 'decimal:8', // 返佣金额
        'status' => CommissionStatus::class, // 状态:0=待结算,1=已结算,2=已取消
        'trade_time' => 'datetime', // 交易时间
        'settled_at' => 'datetime', // 结算时间
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];

    /**
     * 获取自增主键ID
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * 设置自增主键ID
     *
     * @param int $value 主键ID值
     */
    public function setId($value): static
    {
        $this->id = $value;
        return $this;
    }

    /**
     * 获取上级ID
     */
    public function getParentId(): ?int
    {
        return $this->parent_id;
    }

    /**
     * 设置上级ID
     *
     * @param int|null $value 上级ID值
     */
    public function setParentId($value): static
    {
        $this->parent_id = $value;
        return $this;
    }

    /**
     * 获取客户用户ID
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * 设置客户用户ID
     *
     * @param int $value 用户ID值
     */
    public function setUserId($value): static
    {
        $this->user_id = $value;
        return $this;
    }

    /**
     * 获取代理商ID
     */
    public function getAgentId(): ?int
    {
        return $this->agent_id;
    }

    /**
     * 设置代理商ID
     *
     * @param int|null $value 代理商ID值
     */
    public function setAgentId($value): static
    {
        $this->agent_id = $value;
        return $this;
    }

    /**
     * 获取代理商直客ID
     */
    public function getAgentClientId(): ?int
    {
        return $this->agent_client_id;
    }

    /**
     * 设置代理商直客ID
     *
     * @param int|null $value 代理商直客ID值
     */
    public function setAgentClientId($value): static
    {
        $this->agent_client_id = $value;
        return $this;
    }

    /**
     * 获取订单ID
     */
    public function getOrderId(): int
    {
        return $this->order_id;
    }

    /**
     * 设置订单ID
     *
     * @param int $value 订单ID值
     */
    public function setOrderId($value): static
    {
        $this->order_id = $value;
        return $this;
    }

    /**
     * 获取交易类型
     */
    public function getTradeType(): TradeType
    {
        return $this->trade_type;
    }

    /**
     * 设置交易类型
     *
     * @param TradeType $value 交易类型值
     */
    public function setTradeType(TradeType $value): static
    {
        $this->trade_type = $value;
        return $this;
    }

    /**
     * 获取交易金额
     */
    public function getTradeAmount(): float
    {
        return $this->trade_amount;
    }

    /**
     * 设置交易金额
     *
     * @param float $value 交易金额值
     */
    public function setTradeAmount($value): static
    {
        $this->trade_amount = $value;
        return $this;
    }

    /**
     * 获取手续费金额
     */
    public function getFeeAmount(): float
    {
        return $this->fee_amount;
    }

    /**
     * 设置手续费金额
     *
     * @param float $value 手续费金额值
     */
    public function setFeeAmount($value): static
    {
        $this->fee_amount = $value;
        return $this;
    }

    /**
     * 获取返佣比例
     */
    public function getCommissionRate(): float
    {
        return $this->commission_rate;
    }

    /**
     * 设置返佣比例
     *
     * @param float $value 返佣比例值
     */
    public function setCommissionRate($value): static
    {
        $this->commission_rate = $value;
        return $this;
    }

    /**
     * 获取返佣金额
     */
    public function getCommissionAmount(): float
    {
        return $this->commission_amount;
    }

    /**
     * 设置返佣金额
     *
     * @param float $value 返佣金额值
     */
    public function setCommissionAmount($value): static
    {
        $this->commission_amount = $value;
        return $this;
    }

    /**
     * 获取状态
     */
    public function getStatus(): CommissionStatus
    {
        return $this->status;
    }

    /**
     * 设置状态
     *
     * @param CommissionStatus $value 状态值
     */
    public function setStatus(CommissionStatus $value): static
    {
        $this->status = $value;
        return $this;
    }

    /**
     * 获取交易时间
     */
    public function getTradeTime(): \Carbon\Carbon
    {
        return $this->trade_time;
    }

    /**
     * 设置交易时间
     *
     * @param \Carbon\Carbon|string $value 交易时间
     */
    public function setTradeTime($value): static
    {
        $this->trade_time = $value;
        return $this;
    }

    /**
     * 获取结算时间
     */
    public function getSettledAt(): ?\Carbon\Carbon
    {
        return $this->settled_at;
    }

    /**
     * 设置结算时间
     *
     * @param \Carbon\Carbon|string|null $value 结算时间
     */
    public function setSettledAt($value): static
    {
        $this->settled_at = $value;
        return $this;
    }

    /**
     * 获取备注
     */
    public function getRemark(): ?string
    {
        return $this->remark;
    }

    /**
     * 设置备注
     *
     * @param string|null $value 备注内容
     */
    public function setRemark($value): static
    {
        $this->remark = $value;
        return $this;
    }

    /**
     * 获取创建时间
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->created_at;
    }

    /**
     * 设置创建时间
     *
     * @param \Carbon\Carbon|string $value 创建时间
     */
    public function setCreatedAt($value): static
    {
        $this->created_at = $value;
        return $this;
    }

    /**
     * 获取更新时间
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->updated_at;
    }

    /**
     * 设置更新时间
     *
     * @param \Carbon\Carbon|string $value 更新时间
     */
    public function setUpdatedAt($value): static
    {
        $this->updated_at = $value;
        return $this;
    }

    /**
     * 获取关联的用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * 获取关联的代理商用户模型
     */
    public function agent()
    {
        return $this->belongsTo(User::class, self::FIELD_AGENT_ID, User::FIELD_ID);
    }

    /**
     * 获取关联的代理商直客用户模型
     */
    public function agentClient()
    {
        return $this->belongsTo(User::class, self::FIELD_AGENT_CLIENT_ID, User::FIELD_ID);
    }
}
