<?php

declare(strict_types=1);

namespace App\Model\Chain;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property string $tokenId 币种token
 * @property string $chainId 关联链id
 * @property string $contractAddres 合约地址
 * @property string $currency_name 
 * @property string $symbol 
 * @property string $icon 
 * @property string $narketCap 币种市值
 * @property int $decimals 精度
 * @property int $hotTag 是否热门
 * @property int $canTransfer 是否支持转账
 * @property int $tradeDecimal 交易精度
 * @property string $alphaId 币安id
 * @property int $score 评分
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class ChainCurrency extends Model
{
    /**
     * 合约地址
     */
    public const FIELD_CONTRACTADDRES = 'contractAddres';
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 币种token
     */
    public const FIELD_TOKENID = 'tokenId';
    /**
     * 关联链id
     */
    public const FIELD_CHAINID = 'chainId';
    /**
     * 
     */
    public const FIELD_CURRENCY_NAME = 'currency_name';
    /**
     * 
     */
    public const FIELD_SYMBOL = 'symbol';
    /**
     * 
     */
    public const FIELD_ICON = 'icon';
    /**
     * 币种市值
     */
    public const FIELD_NARKETCAP = 'narketCap';
    /**
     * 精度
     */
    public const FIELD_DECIMALS = 'decimals';
    /**
     * 是否热门
     */
    public const FIELD_HOTTAG = 'hotTag';
    /**
     * 是否支持转账
     */
    public const FIELD_CANTRANSFER = 'canTransfer';
    /**
     * 交易精度
     */
    public const FIELD_TRADEDECIMAL = 'tradeDecimal';
    /**
     * 币安id
     */
    public const FIELD_ALPHAID = 'alphaId';
    /**
     * 评分
     */
    public const FIELD_SCORE = 'score';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'chain_currency';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'tokenId', 'chainId', 'contractAddres', 'currency_name', 'symbol', 'icon', 'narketCap', 'decimals', 'hotTag', 'canTransfer', 'tradeDecimal', 'alphaId', 'score', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'decimals' => 'integer', 'hotTag' => 'integer', 'canTransfer' => 'integer', 'tradeDecimal' => 'integer', 'score' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getTokenId() : string
    {
        return $this->tokenId;
    }
    public function setTokenId($value) : object
    {
        $this->tokenId = $value;
        return $this;
    }
    public function getChainId() : int
    {
        return $this->chainId;
    }
    public function setChainId($value) : object
    {
        $this->chainId = $value;
        return $this;
    }
    public function getContractAddres() : string
    {
        return $this->contractAddres;
    }
    public function setContractAddres($value) : object
    {
        $this->contractAddres = $value;
        return $this;
    }
    public function getCurrencyName() : string
    {
        return $this->currency_name;
    }
    public function setCurrencyName($value) : object
    {
        $this->currency_name = $value;
        return $this;
    }
    public function getSymbol() : string
    {
        return $this->symbol;
    }
    public function setSymbol($value) : object
    {
        $this->symbol = $value;
        return $this;
    }
    public function getIcon() : string
    {
        return $this->icon;
    }
    public function setIcon($value) : object
    {
        $this->icon = $value;
        return $this;
    }
    public function getNarketCap() : float
    {
        return $this->narketCap;
    }
    public function setNarketCap($value) : object
    {
        $this->narketCap = $value;
        return $this;
    }
    public function getDecimals() : int
    {
        return $this->decimals;
    }
    public function setDecimals($value) : object
    {
        $this->decimals = $value;
        return $this;
    }
    public function getHotTag() : int
    {
        return $this->hotTag;
    }
    public function setHotTag($value) : object
    {
        $this->hotTag = $value;
        return $this;
    }
    public function getCanTransfer() : int
    {
        return $this->canTransfer;
    }
    public function setCanTransfer($value) : object
    {
        $this->canTransfer = $value;
        return $this;
    }
    public function getTradeDecimal() : int
    {
        return $this->tradeDecimal;
    }
    public function setTradeDecimal($value) : object
    {
        $this->tradeDecimal = $value;
        return $this;
    }
    public function getAlphaId() : string
    {
        return $this->alphaId;
    }
    public function setAlphaId($value) : object
    {
        $this->alphaId = $value;
        return $this;
    }
    public function getScore() : int
    {
        return $this->score;
    }
    public function setScore($value) : object
    {
        $this->score = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
