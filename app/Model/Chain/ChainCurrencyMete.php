<?php

declare(strict_types=1);

namespace App\Model\Chain;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property string $tokenId 币种token
 * @property array $mate 币种详情
 * @property array $description 币种介绍，多语言
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class ChainCurrencyMete extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 币种token
     */
    public const FIELD_TOKENID = 'tokenId';
    /**
     * 币种详情
     */
    public const FIELD_MATE = 'mate';
    /**
     * 币种介绍，多语言
     */
    public const FIELD_DESCRIPTION = 'description';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'chain_currency_mete';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'tokenId', 'mate', 'description', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer','mate' => 'array','description' => 'array', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getTokenId() : string
    {
        return $this->tokenId;
    }
    public function setTokenId($value) : object
    {
        $this->tokenId = $value;
        return $this;
    }
    public function getMate() : array
    {
        return $this->mate;
    }
    public function setMate($value) : object
    {
        $this->mate = $value;
        return $this;
    }
    public function getDescription() : array
    {
        return $this->description;
    }
    public function setDescription($value) : object
    {
        $this->description = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
