<?php

declare(strict_types=1);

namespace App\Model\Chain;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id 
 * @property string $chainId 链id
 * @property string $chainIcon 链图标
 * @property string $chainName 链名称
 * @property array $transformAssets 交易币种
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class ChainCurrencyCate extends Model
{
    /**
     * 
     */
    public const FIELD_ID = 'id';
    /**
     * 链id
     */
    public const FIELD_CHAINID = 'chainId';
    /**
     * 链图标
     */
    public const FIELD_CHAINICON = 'chainIcon';
    /**
     * 链名称
     */
    public const FIELD_CHAINNAME = 'chainName';
    /**
     * 交易币种
     */
    public const FIELD_TRANSFORMASSETS = 'transformAssets';
    /**
     * 创建时间
     */
    public const FIELD_CREATED_AT = 'created_at';
    /**
     * 更新时间
     */
    public const FIELD_UPDATED_AT = 'updated_at';
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'chain_currency_cate';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['id', 'chainId', 'chainIcon', 'chainName', 'transformAssets', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime','transformAssets' => 'array'];
    public function getId() : int
    {
        return $this->id;
    }
    public function setId($value) : object
    {
        $this->id = $value;
        return $this;
    }
    public function getChainId() : int
    {
        return $this->chainId;
    }
    public function setChainId($value) : object
    {
        $this->chainId = $value;
        return $this;
    }
    public function getChainIcon() : string
    {
        return $this->chainIcon;
    }
    public function setChainIcon($value) : object
    {
        $this->chainIcon = $value;
        return $this;
    }
    public function getChainName() : string
    {
        return $this->chainName;
    }
    public function setChainName($value) : object
    {
        $this->chainName = $value;
        return $this;
    }
    public function getTransformAssets() : array
    {
        return $this->transformAssets;
    }
    public function setTransformAssets($value) : object
    {
        $this->transformAssets = $value;
        return $this;
    }
    public function getCreatedAt() : mixed
    {
        return $this->created_at;
    }
    public function setCreatedAt($value) : static
    {
        $this->created_at = $value;
        return $this;
    }
    public function getUpdatedAt() : mixed
    {
        return $this->updated_at;
    }
    public function setUpdatedAt($value) : static
    {
        $this->updated_at = $value;
        return $this;
    }
}
