# Hyperf CRUD 代码生成工具

本目录包含用于 Hyperf CRUD 代码生成的工具和模板，帮助您快速生成符合项目规范的代码。

## 文档索引

1. [标准模板](./StandardTemplate.md) - 包含所有 CRUD 相关文件的标准模板
2. [提示词模板](./PromptTemplate.md) - 用于向 AI 请求生成代码的标准提示词模板
3. [开发指南](./DevelopmentGuide.md) - 详细的开发流程和最佳实践

## 快速开始

要生成一个新的 CRUD 功能，请按照以下步骤操作：

1. 准备您的数据库表 DDL 语句
2. 复制[提示词模板](./PromptTemplate.md)内容
3. 将您的 DDL 和配置信息填入提示词模板中
4. 向 AI 提交请求，生成 CRUD 代码
5. 审查并应用生成的代码

## 主要特点

### 验证规则自动生成

系统会根据数据库表 DDL 自动生成大部分验证规则，包括：

- 从`NOT NULL`约束生成`required`验证
- 从字符串长度生成`max:{length}`验证
- 从数据类型生成相应的类型验证
- 从唯一索引生成`unique`验证
- 从枚举类型生成`in:{options}`验证

您只需要在配置文件中添加那些无法从 DDL 中推断的特殊验证规则，如：数组验证、邮箱验证、URL 验证等。

### 特殊字段自动处理

系统能智能处理多种特殊字段类型：

- **枚举字段**：对于 tinyint 类型字段，如果注释中包含多个可选值（如"1-正常,2-禁用"），系统会自动创建对应的枚举类
- **布尔字段**：对于 tinyint 类型字段，如果注释中表明只有 0 和 1 两个值，系统会处理为布尔类型
- **JSON 字段**：对于 JSON 类型字段，会自动添加 array 验证和类型转换

### 简化的配置结构

配置文件结构已经过简化，只需要关注：

- 实体基本信息
- 可过滤和可排序字段
- 特殊验证规则（仅补充 DDL 无法表达的规则）
- 枚举字段配置（可选）
- API 接口配置

## 常见问题

### 如何选择需要过滤的字段？

通常选择用户可能需要按条件查询的字段，如名称、状态、类型等。

### 如何选择可排序字段？

通常包括 ID、创建时间、更新时间以及一些可能需要排序的业务字段，如排序值、价格等。

### 哪些验证规则需要手动添加？

只需添加那些 DDL 无法表达的验证规则，例如：

- `array`验证（用于 JSON 字段）
- `email`验证（用于邮箱字段）
- `url`验证（用于 URL 字段）
- 正则表达式验证
- 自定义验证规则

### 如何处理枚举字段？

对于有多个固定值的字段（如状态字段），系统会：

1. 自动从 DDL 注释中提取可能的枚举值
2. 创建相应的枚举类（位于`app/Model/Enums/{Module}/`目录）
3. 在模型中添加类型转换
4. 在验证规则中添加`in:{可选值列表}`验证

您也可以在配置中的`enums`部分手动指定枚举配置。
