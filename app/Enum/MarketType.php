<?php

/**
 * MarketType.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/23
 * Website:algoquant.org
 */

namespace App\Enum;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum MarketType:int
{

    use EnumConstantsTrait;

    #[Message("现货")]
    case CRYPTO = 1; //

    #[Message("股票")]
    case STOCKS = 2; //股票

    #[Message("外汇")]
    case FOREX = 3; //外汇

    #[Message("期货")]
    case FUTURES = 4; //期货

    #[Message("合约")]
    case MARGIN = 5; //合约

    #[Message('杠杆')]
    case LEVERAGE = 6; //杠杆交易

    public static function getMarketString(int $value): string
    {
        return match ($value){
            self::CRYPTO->value => 'spot',
            self::STOCKS->value => 'stocks',
            self::FOREX->value => 'forex',
            self::FUTURES->value => 'futures',
            self::MARGIN->value => 'contract',
            self::LEVERAGE->value => 'leverage',
            default => ''
        };
    }

    /**
     * 获取市场类型
     * @return array
     */
    public static function getMarketType(): array
    {
        return [
            self::CRYPTO->value => self::CRYPTO->getMessage(),
            self::STOCKS->value => self::STOCKS->getMessage(),
            self::FOREX->value => self::FOREX->getMessage(),
            self::FUTURES->value => self::FUTURES->getMessage(),
            self::MARGIN->value => self::MARGIN->getMessage(),
            self::LEVERAGE->value => self::LEVERAGE->getMessage(),
        ];
    }
}