<?php

/**
 * TradeConfigKey.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/4
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum\Config;

enum TradeConfigKey:string
{
    case TRADE_CONFIG_KEY = "config:trade:{currency_id}-{market_type}";

    public static function getTradeConfigKey(int $currency_id,int $market_id): array|string
    {
        return str_replace(['{currency_id}','{market_type}'],[$currency_id,$market_id],self::TRADE_CONFIG_KEY->value);
    }
}