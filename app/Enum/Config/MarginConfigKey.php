<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆配置缓存Key枚举
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Enum\Config;

/**
 * 杠杆配置缓存Key枚举
 */
class MarginConfigKey
{
    /**
     * 杠杆借贷配置缓存Key前缀
     * 格式: config:margin:borrow:{currency_id}:{is_cross}
     * 示例: config:margin:borrow:123:1 (全仓)
     *      config:margin:borrow:456:2 (逐仓)
     */
    public const MARGIN_BORROW_PREFIX = 'config:margin:borrow';

    /**
     * 生成杠杆借贷配置缓存Key
     *
     * @param int $currencyId 币种ID
     * @param int $isCross 杠杆类型 1-全仓 2-逐仓
     * @return string
     */
    public static function getMarginBorrowKey(int $currencyId, int $isCross): string
    {
        return self::MARGIN_BORROW_PREFIX . ':' . $currencyId . ':' . $isCross;
    }

    /**
     * 解析杠杆借贷配置缓存Key
     *
     * @param string $key 缓存Key
     * @return array|null [currency_id, is_cross] 或 null
     */
    public static function parseMarginBorrowKey(string $key): ?array
    {
        $pattern = '/^' . preg_quote(self::MARGIN_BORROW_PREFIX, '/') . ':(\d+):([12])$/';
        if (preg_match($pattern, $key, $matches)) {
            return [
                'currency_id' => (int)$matches[1],
                'is_cross' => (int)$matches[2],
            ];
        }
        return null;
    }

    // ========== 新增：风险监控相关键 ==========

    /**
     * 币种仓位数据缓存前缀
     * 格式: margin:positions:{currency_id}
     * 结构: Hash - field: {user_id}:{side}:{margin_type}, value: position_data_json
     */
    public const MARGIN_POSITIONS_PREFIX = 'margin:positions';

    /**
     * 最后计算价格记录
     * 格式: margin:price:last_calculated
     * 结构: Hash - field: currency_id, value: last_price
     */
    public const PRICE_LAST_CALCULATED = 'margin:price:last_calculated';

    /**
     * 风险等级索引前缀
     * 格式: margin:risk:{level}:{currency_id}
     * 结构: Set - members: position_keys
     */
    public const RISK_LEVEL_PREFIX = 'margin:risk';

    /**
     * 币种监控状态
     * 格式: margin:currency:monitor
     * 结构: Hash - field: currency_id, value: monitor_info_json
     */
    public const CURRENCY_MONITOR = 'margin:currency:monitor';

    /**
     * 仓位同步锁前缀
     * 格式: margin:sync:lock:{currency_id}
     */
    public const SYNC_LOCK_PREFIX = 'margin:sync:lock';

    // ========== 新增方法 ==========

    /**
     * 获取币种仓位缓存键
     */
    public static function getCurrencyPositionsKey(int $currencyId): string
    {
        return self::MARGIN_POSITIONS_PREFIX . ':' . $currencyId;
    }

    /**
     * 获取风险等级索引键
     */
    public static function getRiskLevelKey(string $level, int $currencyId): string
    {
        return self::RISK_LEVEL_PREFIX . ':' . $level . ':' . $currencyId;
    }

    /**
     * 获取仓位同步锁键
     */
    public static function getSyncLockKey(int $currencyId): string
    {
        return self::SYNC_LOCK_PREFIX . ':' . $currencyId;
    }

    /**
     * 构建仓位键
     */
    public static function buildPositionKey(int $userId, int $side, int $marginType): string
    {
        return "{$userId}:{$side}:{$marginType}";
    }
}