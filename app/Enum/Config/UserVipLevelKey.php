<?php

/**
 * UserVipLevelKey.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/4
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum\Config;

enum UserVipLevelKey:string
{
    case COMMON_VIP_LEVEL = "config:common_vip_level:{level_id}"; //用户等级的公共配置缓存key

    public static function getConfigKey(int $levelId): array|string
    {
        return str_replace("{level_id}",$levelId,self::COMMON_VIP_LEVEL->value);
    }
}