<?php

/**
 * OrderType.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/30
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum;

enum OrderType:string
{
    case MARKET_STRING = "market"; //市价单

    case LIMIT_STRING = "limit"; //限价单

    case TP_SL_STRING = "tp_sl"; //止盈止损单

    case H_LIMIT_STRING = 'h_limit'; //高级限价单

    case MARKET_INT = "1";

    case LIMIT_INT  = "2";

    case TP_SL = '3';

    case H_LIMIT = '4';

    public static function getOrderType(string $type): int|string|null
    {
        return match ($type){
            self::MARKET_STRING->value => intval(self::MARKET_INT->value),
            self::LIMIT_STRING->value => intval(self::LIMIT_INT->value),
            self::MARKET_INT->value => self::MARKET_STRING->value,
            self::LIMIT_INT->value => self::LIMIT_STRING->value,
            default => null
        };
    }
}