<?php

/**
 * UserAssetsCacheKey.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/7
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum\User;

/**
 * 用户仓位在相关变化后会进行自动维护 但有几秒的延迟，实施性较高的用途应使用mysql 查询
 */
enum UserAssetsCacheKey:string
{
    case USER_ASSETS_CROSS_CACHE_KEY = 'user:assets:{user_id}-{account_type}'; //全仓杠杆账户资产缓存

    case USER_ASSETS_ISOLATED_CACHE_KEY = 'user:assets:{user_id}-{account_type}-{currency_id}'; //逐仓杠杆账户资产缓存

    /**
     * 获取全仓资产缓存key
     * @param int $user_id
     * @param int $account_type
     * @return array|string
     */
    public static function getCrosstAssetsKey(int $user_id,int $account_type): array|string
    {
        return str_replace(["{user_id}","{account_type}"],[$user_id,$account_type],self::USER_ASSETS_CROSS_CACHE_KEY->value);
    }

    /**
     * 获取逐仓资产缓存key
     * @param int $user_id
     * @param int $account_type
     * @param int $currency_id
     * @return array|string
     */
    public static function getIsolatedAssetsKey(int $user_id,int $account_type,int $currency_id): array|string
    {
        return str_replace(["{user_id}","{account_type}","{currency_id}"],[$user_id,$account_type,$currency_id],self::USER_ASSETS_ISOLATED_CACHE_KEY->value);
    }
}