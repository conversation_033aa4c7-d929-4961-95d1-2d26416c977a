<?php

/**
 * TradeSide.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/30
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum;

enum TradeSide: string
{
    case BUY_STRING = "buy";

    case SELL_STRING = "sell";

    case BUY_INT = "1";

    case SELL_INT = "-1";

    public static function getSideInt(string $side): int
    {
        return match ($side){
            self::BUY_STRING->value => intval(self::BUY_INT->value),
            self::SELL_STRING->value => intval(self::SELL_INT->value),
            default => false,
        };
    }

    public static function getSideString(int $side): int|string
    {
        return match ($side){
            intval(self::BUY_INT->value) => self::BUY_STRING->value,
            intval(self::SELL_INT->value) => self::SELL_STRING->value,
            default => false
        };
    }
}