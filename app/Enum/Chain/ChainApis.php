<?php

/**
 * ChainApis.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/2
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum\Chain;

enum ChainApis:string
{
    case CHAIN_CATE = "https://www.bitget.com/v1/mix/onchain/allChain";

    case HOT_PICKS = "https://www.bitget.com/v1/mix/onchain/hotPicks"; //精选链上币种

    case NEW_PICKS = "https://www.bitget.com/v1/mix/onchain/newPicks"; //新上

    case FDV_PICKS = "https://www.bitget.com/v1/mix/onchain/fdv"; //dfv币种

    case VOLUME_PICKS = "https://www.bitget.com/v1/mix/onchain/volume"; //成交额币种

    case HOLDER_PICKS = "https://www.bitget.com/v1/mix/onchain/holders"; //持有人数币种

    public static function getFetchUrl(string $filter): bool|string
    {
        return match ($filter){
            'hot' => self::HOT_PICKS->value,
            'new' => self::NEW_PICKS->value,
            'fdv' => self::FDV_PICKS->value,
            'volume' => self::VOLUME_PICKS->value,
            'holders' => self::HOLDER_PICKS->value,
            default => false
        };
    }
}