<?php

/**
 * TradeSubscribeKey.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/28
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum\MarketData;

enum TradeSubscribeKey: string
{
    case CRYPTO_TRADE_KEY = "crypto-trade-inner:{symbol}-{market_type}"; //币种-市场类型

        case CRYPTO_TRADE_OUT = "crypto-trade-out:{symbol}-{market_type}"; //外部成交数据频道

    case CRYPTO_TRADE_CHANNEL_KEY = "crypto-trade-channel-{market_type}"; //全部币种成交事件-市场类型

    /**
     * 获取指定市场类型的全部币种成交事件redis 订阅主键
     * @param int $marketType
     * @return string
     */
    public static function getCryptoTradeChannelKey(int $marketType): string
    {
        return str_replace('{market_type}', $marketType, self::CRYPTO_TRADE_CHANNEL_KEY->value);
    }

    /**
     * 获取加密数字货币redis 订阅主键
     * @param string $symbol 币种
     * @param int $market_type 市场类型 1 现货 5 合约
     * @return array|string
     */
    public static function getCryptoTradeKey(string $symbol, int $market_type): array|string
    {
        return str_replace(
            ['{symbol}', '{market_type}'],
            [$symbol, $market_type],
            self::CRYPTO_TRADE_KEY->value
        );
    }

    /**
     * 获取外部成交频道
     * @param string $symbol
     * @param int $market_type
     * @return array|string
     */
    public static function getOuterTradeKey(string $symbol, int $market_type): array|string
    {
        return str_replace(
            ['{symbol}', '{market_type}'],
            [$symbol, $market_type],
            self::CRYPTO_TRADE_OUT->value
        );
    }
}