<?php

namespace App\Enum\MarketData;

/**
 * K线ES索引命名枚举
 */
enum KlineIndex: string 
{
    case INDEX_PATTERN = 'kline-{symbol}-{market_type}';
    case TEMPLATE_NAME = 'kline-template';
    
    /**
     * 内部数据聚合K线索引模式
     */
    case INNER_INDEX_PATTERN = 'kline-inner-{symbol}-{market_type}';
    
    /**
     * 内部数据聚合K线缓存键模式
     */
    case INNER_CACHE_KEY_PATTERN = 'marketdata:aggregator-inner:{symbol}-{market_type}-{period}';
    
    /**
     * 获取外部数据索引名称
     */
    public static function getIndexName(string $symbol, int $marketType): string
    {
        return str_replace(['{symbol}', '{market_type}'], 
            [strtolower($symbol), $marketType], 
            self::INDEX_PATTERN->value);
    }
    
    /**
     * 获取内部数据聚合索引名称
     */
    public static function getInnerIndexName(string $symbol, int $marketType): string
    {
        return str_replace(['{symbol}', '{market_type}'], 
            [strtolower($symbol), $marketType], 
            self::INNER_INDEX_PATTERN->value);
    }
    
    /**
     * 获取内部数据聚合缓存键名
     */
    public static function getInnerCacheKey(string $symbol, int $marketType, string $period): string
    {
        return str_replace(['{symbol}', '{market_type}', '{period}'], 
            [strtolower($symbol), $marketType, $period], 
            self::INNER_CACHE_KEY_PATTERN->value);
    }
    
    /**
     * 生成K线文档ID
     */
    public static function generateKlineId(string $period, int $openTime): string
    {
        return $period . '_' . $openTime;
    }
    
    /**
     * 生成内部数据K线文档ID
     */
    public static function generateInnerKlineId(string $symbol, int $marketType, string $period, int $openTime): string
    {
        return "{$symbol}-{$marketType}-{$period}-{$openTime}";
    }
}
