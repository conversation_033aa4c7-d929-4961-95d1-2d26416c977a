<?php

/**
 * TickerSyncKey.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/24
 * Website:algoquant.org
 */

namespace App\Enum\MarketData;

/**
 * ticker 数据储存区分内部和外部的ticker，方便在控盘或者平台成交数据使用时候的合并
 */
enum TickerSyncKey:string
{
    /**
     * 外部数据ticker
     */
    case TICKER_SYNC_KEY_OUT = 'marketdata:ticker-out:{currency_id}-{market_type}';

    /**
     * 内部数据ticker
     */
    case TICKER_SYNC_KEY_INR = 'marketdata:ticker-in:{currency_id}-{market_type}';

    /**
     * 内部数据聚合ticker（撮合引擎产生）
     */
    case TICKER_SYNC_KEY_INNER = 'marketdata:ticker-inner:{currency_id}-{market_type}';

    /**
     * 外部实时成交数据
     */
    case TRADE_SYNC_KEY_OUT = 'marketdata:trade-out:{currency_id}-{market_type}';

    /**
     * 内部实时成交数据
     */
    case TRADE_SYNC_KEY_INR = 'marketdata:trade-in:{currency_id}-{market_type}';

    /**
     * 内部数据聚合实时成交数据（撮合引擎产生）
     */
    case TRADE_SYNC_KEY_INNER = 'marketdata:trade-inner:{currency_id}-{market_type}';

    /**
     * 获取外部ticker键名
     */
    public static function getOuterTickerKey(int $currencyId, int $marketType): string
    {
        return str_replace(['{currency_id}', '{market_type}'], 
            [$currencyId, $marketType], 
            self::TICKER_SYNC_KEY_OUT->value);
    }

    /**
     * 获取内部ticker键名
     */
    public static function getInnerTickerKey(int $currencyId, int $marketType): string
    {
        return str_replace(['{currency_id}', '{market_type}'], 
            [$currencyId, $marketType], 
            self::TICKER_SYNC_KEY_INR->value);
    }

    /**
     * 获取内部聚合ticker键名（撮合引擎产生）
     */
    public static function getInnerAggregatorTickerKey(int $currencyId, int $marketType): string
    {
        return str_replace(['{currency_id}', '{market_type}'], 
            [$currencyId, $marketType], 
            self::TICKER_SYNC_KEY_INNER->value);
    }

    /**
     * 获取外部成交数据键名
     */
    public static function getOuterTradeKey(int $currencyId, int $marketType): string
    {
        return str_replace(['{currency_id}', '{market_type}'], 
            [$currencyId, $marketType], 
            self::TRADE_SYNC_KEY_OUT->value);
    }

    /**
     * 获取内部成交数据键名
     */
    public static function getInnerTradeKey(int $currencyId, int $marketType): string
    {
        return str_replace(['{currency_id}', '{market_type}'], 
            [$currencyId, $marketType], 
            self::TRADE_SYNC_KEY_INR->value);
    }

    /**
     * 获取内部聚合成交数据键名（撮合引擎产生）
     */
    public static function getInnerAggregatorTradeKey(int $currencyId, int $marketType): string
    {
        return str_replace(['{currency_id}', '{market_type}'], 
            [$currencyId, $marketType], 
            self::TRADE_SYNC_KEY_INNER->value);
    }
}