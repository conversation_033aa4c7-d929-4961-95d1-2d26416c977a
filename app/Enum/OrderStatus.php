<?php

/**
 * OrderStatus.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/30
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum;

enum OrderStatus:int
{
    case PENDING = 1;

    case PARTIAL_FILLED = 2;

    case FILLED = 3;

    case CANCELED = 0;

    case CREATED = -1;

    public static function getOrderStatus(string $status):int
    {
        return  match ($status){
            'partial_filled' => self::PARTIAL_FILLED->value,
            'filled' => self::FILLED->value,
            'canceled' => self::CANCELED->value,
            'pending' => self::PENDING->value,
            default => self::CREATED->value
        };
    }

    public static function getOrderStatusString(int $value): string
    {
        return match ($value){
            self::PENDING->value => 'pending',
            self::PARTIAL_FILLED->value => 'partial_filled',
            self::FILLED->value => 'filled',
            self::CANCELED->value => 'canceled',
            default => 'unknown'
        };
    }
}