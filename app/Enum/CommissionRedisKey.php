<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 委托订单Redis键值枚举
 */

namespace App\Enum;

class CommissionRedisKey
{
    /**
     * 买单止盈委托集合 (价格上涨触发)
     * score: 触发价格, member: 订单ID
     */
    public static function getBuyStopProfitKey(int $currencyId): string
    {
        return "commission:buy:stop_profit:{$currencyId}";
    }

    /**
     * 买单止损委托集合 (价格下跌触发)
     * score: 触发价格, member: 订单ID
     */
    public static function getBuyStopLossKey(int $currencyId): string
    {
        return "commission:buy:stop_loss:{$currencyId}";
    }

    /**
     * 卖单止盈委托集合 (价格下跌触发)
     * score: 触发价格, member: 订单ID
     */
    public static function getSellStopProfitKey(int $currencyId): string
    {
        return "commission:sell:stop_profit:{$currencyId}";
    }

    /**
     * 卖单止损委托集合 (价格上涨触发)
     * score: 触发价格, member: 订单ID
     */
    public static function getSellStopLossKey(int $currencyId): string
    {
        return "commission:sell:stop_loss:{$currencyId}";
    }

    /**
     * 计划委托买入集合 (价格下跌到指定价格触发买入)
     * score: 触发价格, member: 订单ID
     */
    public static function getPlanBuyKey(int $currencyId): string
    {
        return "commission:plan:buy:{$currencyId}";
    }

    /**
     * 计划委托卖出集合 (价格上涨到指定价格触发卖出)
     * score: 触发价格, member: 订单ID
     */
    public static function getPlanSellKey(int $currencyId): string
    {
        return "commission:plan:sell:{$currencyId}";
    }

    /**
     * 追踪委托集合 (动态调整触发价格)
     * score: 当前触发价格, member: 订单ID
     */
    public static function getTrailingStopKey(int $currencyId): string
    {
        return "commission:trailing:{$currencyId}";
    }

    /**
     * 委托订单详情哈希 (存储订单详细信息)
     * key: commission:detail:{order_id}
     */
    public static function getCommissionDetailKey(int $orderId): string
    {
        return "commission:detail:{$orderId}";
    }

    /**
     * 根据委托订单类型和方向获取对应的Redis键
     */
    public static function getCommissionKey(int $currencyId, int $side, int $orderType, int $triggerCondition): string
    {
        // side: 1=买, -1=卖
        // orderType: 1=止盈止损, 2=计划委托, 3=追踪委托
        // triggerCondition: 1=止盈, 2=止损, 3=计划买入, 4=计划卖出

        return match ([$side, $orderType, $triggerCondition]) {
            [1, 1, 1] => self::getBuyStopProfitKey($currencyId),    // 买单止盈
            [1, 1, 2] => self::getBuyStopLossKey($currencyId),     // 买单止损
            [-1, 1, 1] => self::getSellStopProfitKey($currencyId), // 卖单止盈
            [-1, 1, 2] => self::getSellStopLossKey($currencyId),   // 卖单止损
            [1, 2, 3] => self::getPlanBuyKey($currencyId),         // 计划买入
            [-1, 2, 4] => self::getPlanSellKey($currencyId),       // 计划卖出
            default => self::getTrailingStopKey($currencyId)       // 追踪委托或其他
        };
    }

    /**
     * 获取所有币种的委托订单键模式
     */
    public static function getAllCommissionKeyPatterns(int $currencyId): array
    {
        return [
            self::getBuyStopProfitKey($currencyId),
            self::getBuyStopLossKey($currencyId),
            self::getSellStopProfitKey($currencyId),
            self::getSellStopLossKey($currencyId),
            self::getPlanBuyKey($currencyId),
            self::getPlanSellKey($currencyId),
            self::getTrailingStopKey($currencyId),
        ];
    }
} 