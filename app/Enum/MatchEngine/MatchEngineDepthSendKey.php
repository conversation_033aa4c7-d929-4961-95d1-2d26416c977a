<?php

/**
 * MatchEngineDepthSendKey.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/27
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum\MatchEngine;

enum MatchEngineDepthSendKey:string
{
    case CRYPTO_SPOT_DEPTH_SEND = 'marketdata:depth:spot:{symbol}'; //深度数据redis 发布key

    case CRYPTO_MARGIN_DEPTH_SEND = 'marketdata:depth:margin:{symbol}';

    //全量现货币种深度数据订阅频道
    case SPOT_PUBLIC_DEPTH = "match-engine:depth:spot";

    //全量合约币种深度数据订阅频道
    case MARGIN_PUBLIC_DEPTH = "match-engine:depth:margin";

    public static function getCryptoSpotDepthSendKey(string $symbol): string
    {
        return str_replace('{symbol}', $symbol, self::CRYPTO_SPOT_DEPTH_SEND->value);
    }

    public static function getCryptoMarginDepthSendKey(string $symbol): array|string
    {
        return str_replace('{symbol}', $symbol, self::CRYPTO_MARGIN_DEPTH_SEND->value);
    }
}