<?php

declare(strict_types=1);

namespace App\Enum\MatchEngine;

/**
 * 管理进程 Redis Stream 订阅 Key 枚举
 */
enum ManagerStreamKey: string
{
    // 现货市场流
    /**
     * 现货命令流 - 接收现货市场管理命令
     */
    case SPOT_COMMAND_STREAM = 'match_engine:spot:commands';

    /**
     * 现货订单流 - 接收现货交易订单和撤单请求
     */
    case SPOT_ORDER_STREAM = 'match_engine:spot:orders';

    /**
     * 现货系统控制流 - 接收现货系统级控制命令
     */
    case SPOT_SYSTEM_CONTROL_STREAM = 'match_engine:spot:system_control';

    /**
     * 现货配置更新流 - 接收现货配置更新命令
     */
    case SPOT_CONFIG_UPDATE_STREAM = 'match_engine:spot:config_update';

    // 合约市场流
    /**
     * 合约命令流 - 接收合约市场管理命令
     */
    case CONTRACT_COMMAND_STREAM = 'match_engine:contract:commands';

    /**
     * 合约订单流 - 接收合约交易订单和撤单请求
     */
    case CONTRACT_ORDER_STREAM = 'match_engine:contract:orders';

    /**
     * 合约系统控制流 - 接收合约系统级控制命令
     */
    case CONTRACT_SYSTEM_CONTROL_STREAM = 'match_engine:contract:system_control';

    /**
     * 合约配置更新流 - 接收合约配置更新命令
     */
    case CONTRACT_CONFIG_UPDATE_STREAM = 'match_engine:contract:config_update';

    // 期货市场流
    /**
     * 期货命令流 - 接收期货市场管理命令
     */
    case FUTURES_COMMAND_STREAM = 'match_engine:futures:commands';

    /**
     * 期货订单流 - 接收期货交易订单和撤单请求
     */
    case FUTURES_ORDER_STREAM = 'match_engine:futures:orders';

    /**
     * 期货系统控制流 - 接收期货系统级控制命令
     */
    case FUTURES_SYSTEM_CONTROL_STREAM = 'match_engine:futures:system_control';

    /**
     * 期货配置更新流 - 接收期货配置更新命令
     */
    case FUTURES_CONFIG_UPDATE_STREAM = 'match_engine:futures:config_update';

    /**
     * 获取市场类型
     */
    public function getMarketType(): string
    {
        return match ($this) {
            self::SPOT_COMMAND_STREAM, self::SPOT_ORDER_STREAM, 
            self::SPOT_SYSTEM_CONTROL_STREAM, self::SPOT_CONFIG_UPDATE_STREAM => 'spot',
            
            self::CONTRACT_COMMAND_STREAM, self::CONTRACT_ORDER_STREAM,
            self::CONTRACT_SYSTEM_CONTROL_STREAM, self::CONTRACT_CONFIG_UPDATE_STREAM => 'contract',
            
            self::FUTURES_COMMAND_STREAM, self::FUTURES_ORDER_STREAM,
            self::FUTURES_SYSTEM_CONTROL_STREAM, self::FUTURES_CONFIG_UPDATE_STREAM => 'futures',
        };
    }

    /**
     * 获取流类型
     */
    public function getStreamType(): string
    {
        return match ($this) {
            self::SPOT_COMMAND_STREAM, self::CONTRACT_COMMAND_STREAM, self::FUTURES_COMMAND_STREAM => 'commands',
            self::SPOT_ORDER_STREAM, self::CONTRACT_ORDER_STREAM, self::FUTURES_ORDER_STREAM => 'orders',
            self::SPOT_SYSTEM_CONTROL_STREAM, self::CONTRACT_SYSTEM_CONTROL_STREAM, self::FUTURES_SYSTEM_CONTROL_STREAM => 'system_control',
            self::SPOT_CONFIG_UPDATE_STREAM, self::CONTRACT_CONFIG_UPDATE_STREAM, self::FUTURES_CONFIG_UPDATE_STREAM => 'config_update',
        };
    }

    /**
     * 获取消费者组名称
     */
    public function getConsumerGroup(): string
    {
        $marketType = $this->getMarketType();
        $streamType = $this->getStreamType();
        return "match_engine_{$marketType}_{$streamType}_group";
    }

    /**
     * 获取消费者名称
     */
    public function getConsumerName(): string
    {
        $marketType = $this->getMarketType();
        $streamType = $this->getStreamType();
        return "manager_{$marketType}_{$streamType}_consumer";
    }

    /**
     * 获取所有流的 key
     */
    public static function getAllStreamKeys(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * 根据市场类型获取流
     */
    public static function getStreamsByMarketType(string $marketType): array
    {
        return array_filter(self::cases(), fn($case) => $case->getMarketType() === $marketType);
    }

    /**
     * 根据流类型获取流
     */
    public static function getStreamsByStreamType(string $streamType): array
    {
        return array_filter(self::cases(), fn($case) => $case->getStreamType() === $streamType);
    }

    /**
     * 获取指定市场类型的命令流
     */
    public static function getCommandStreamByMarket(string $marketType): ?self
    {
        return match ($marketType) {
            'spot' => self::SPOT_COMMAND_STREAM,
            'contract' => self::CONTRACT_COMMAND_STREAM,
            'futures' => self::FUTURES_COMMAND_STREAM,
            default => null,
        };
    }

    /**
     * 获取指定市场类型的订单流
     */
    public static function getOrderStreamByMarket(string $marketType): ?self
    {
        return match ($marketType) {
            'spot' => self::SPOT_ORDER_STREAM,
            'contract' => self::CONTRACT_ORDER_STREAM,
            'futures' => self::FUTURES_ORDER_STREAM,
            default => null,
        };
    }
} 