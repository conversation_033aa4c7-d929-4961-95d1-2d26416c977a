<?php

/**
 * MatchEngineDepthKey.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/26
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum\MatchEngine;

enum MatchEngineDepthKey:string
{
    /**
     * 现货交易对的深度数据保存主键
     */
    case SPOT_DEPTH = 'match-engine:depth:spot:{symbol}';

    case MARGIN_DEPTH = 'match-engine:depth:margin:{symbol}';


    public static function getSpotDepthKey(string $symbol): string
    {
        return str_replace('{symbol}', $symbol, self::SPOT_DEPTH->value);
    }

    public static function getMarginDepthKey(string $symbol): string
    {
        return str_replace('{symbol}', $symbol, self::MARGIN_DEPTH->value);
    }
}