<?php

/**
 * OrderEventMap.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/7
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum\MatchEngine;

use App\Enum\MarketType;
use App\Model\Enums\User\AccountType;

enum OrderEventMap:string
{
    case SPOT = 'Spot';

    case MARGIN = 'Margin';

    case PERPETUAL = "Perpetual";

    public static function getDirection(int $market_type): string
    {
        return match ($market_type){
            MarketType::CRYPTO->value => self::SPOT->value,
            MarketType::LEVERAGE->value => self::MARGIN->value,
            MarketType::MARGIN->value => self::PERPETUAL->value
        };
    }
}