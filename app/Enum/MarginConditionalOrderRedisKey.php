<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆委托订单Redis键值枚举
 */

namespace App\Enum;

class MarginConditionalOrderRedisKey
{
    /**
     * 大于等于价格触发的委托订单集合
     * score: 触发价格, member: 委托订单ID
     */
    public static function getGteKey(int $currencyId): string
    {
        return "margin:conditional:gte:{$currencyId}";
    }

    /**
     * 小于等于价格触发的委托订单集合
     * score: 触发价格, member: 委托订单ID
     */
    public static function getLteKey(int $currencyId): string
    {
        return "margin:conditional:lte:{$currencyId}";
    }

    /**
     * 委托订单详情哈希
     * key: margin:conditional:detail:{order_id}
     */
    public static function getDetailKey(int $orderId): string
    {
        return "margin:conditional:detail:{$orderId}";
    }

    /**
     * 根据触发条件获取对应的Redis键
     * 
     * @param int $currencyId 币种ID
     * @param string $condition 触发条件 'gte'(>=) 或 'lte'(<=)
     * @return string
     */
    public static function getConditionalKey(int $currencyId, string $condition): string
    {
        return match ($condition) {
            'gte' => self::getGteKey($currencyId),
            'lte' => self::getLteKey($currencyId),
            default => throw new \InvalidArgumentException("无效的触发条件: {$condition}")
        };
    }

    /**
     * 获取所有币种的委托订单键模式
     */
    public static function getAllConditionalKeyPatterns(int $currencyId): array
    {
        return [
            self::getGteKey($currencyId),
            self::getLteKey($currencyId),
        ];
    }

    /**
     * 监控进程锁键
     */
    public static function getMonitorLockKey(int $currencyId): string
    {
        return "margin:conditional:monitor:lock:{$currencyId}";
    }

    /**
     * 委托订单触发历史记录
     */
    public static function getTriggerHistoryKey(int $currencyId): string
    {
        return "margin:conditional:trigger:history:{$currencyId}";
    }
} 