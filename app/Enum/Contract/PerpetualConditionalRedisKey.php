<?php

declare(strict_types=1);
/**
 * 永续合约委托单Redis键管理
 */

namespace App\Enum\Contract;

class PerpetualConditionalRedisKey
{
    // 委托单有序集合缓存键
    const CONDITIONAL_ORDERS_ZSET = 'perpetual:conditional:orders:';
    
    // 执行锁键
    const EXECUTION_LOCK = 'perpetual:conditional:lock:';
    
    // 价格Channel键
    const PRICE_CHANNEL = 'market:price:';

    // 用户委托单统计键
    const USER_STATS = 'perpetual:conditional:user_stats:';
    
    // 缓存过期时间（秒）
    const EXECUTION_LOCK_TTL = 30;      // 执行锁30秒
    const USER_STATS_TTL = 3600;        // 用户统计缓存1小时

    /**
     * 获取委托单有序集合键
     * @param int $currencyId 币种ID
     * @param int $triggerCondition 触发条件 1-大于等于 2-小于等于
     * @return string
     */
    public static function getConditionalOrdersZsetKey(int $currencyId, int $triggerCondition): string
    {
        return self::CONDITIONAL_ORDERS_ZSET . $currencyId . ':' . $triggerCondition;
    }

    /**
     * 获取执行锁键
     * @param int $conditionalOrderId 委托单ID
     * @return string
     */
    public static function getExecutionLockKey(int $conditionalOrderId): string
    {
        return self::EXECUTION_LOCK . $conditionalOrderId;
    }

    /**
     * 获取价格Channel键
     * @param int $currencyId 币种ID
     * @return string
     */
    public static function getPriceChannelKey(int $currencyId): string
    {
        return self::PRICE_CHANNEL . $currencyId;
    }



    /**
     * 获取用户统计缓存键
     * @param int $userId 用户ID
     * @return string
     */
    public static function getUserStatsKey(int $userId): string
    {
        return self::USER_STATS . $userId;
    }

    /**
     * 价格转换为Score（用于有序集合）
     * 将decimal价格转换为整数，保留8位小数精度
     * @param string $price 价格
     * @return int
     */
    public static function priceToScore(string $price): int
    {
        return (int)bcmul($price, '100000000', 0);
    }

    /**
     * Score转换为价格
     * @param int $score Score值
     * @return string
     */
    public static function scoreToPrice(int $score): string
    {
        return bcdiv((string)$score, '100000000', 8);
    }

    /**
     * 获取监控锁键
     * @param int $currencyId 币种ID
     * @return string
     */
    public static function getMonitorLockKey(int $currencyId): string
    {
        return 'perpetual:conditional:monitor_lock:' . $currencyId;
    }


}
