<?php

/**
 * ProcessCmdKey.php
 * Author    chenmqq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/24
 * @link     algoquant.org
 * @document algoquant.org
 */

namespace App\Enum;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ProcessCmdKey:string
{
    use EnumConstantsTrait;

    #[Message("加密数字货币现货行情数据处理进程消息发送key")]
    case CRYPTO_SPOT_TICKER_CMD = 'crypto-spot-ticker';

    #[Message("加密数字货币合约行情数据处理进程消息发送key")]
    case CRYPTO_MARGIN_TICKER_CMD = "crypto-margin-ticker";

    #[Message("加密数字货币现货实时成交数据处理进程消息发送key")]
    case CRYPTO_SPOT_TRADE_CMD = "crypto-spot-trade";

    #[Message("加密数字货币h合约实时成交数据处理进程消息发送key")]
    case CRYPTO_MARGIN_TRADE_CMD = "crypto-margin-trade";

    #[Message("K线数据连续性检查和补全进程消息发送key")]
    case KLINE_CONTINUITY_CHECK_CMD = "kline-continuity-check";

    #[Message("现货交易撮合引擎进程消息发送key")]
    case SPOT_MATCH_ENGINE_CMD = "spot-match-engine";

    #[Message('链上交易websocket进程通信key')]
    case CHAIN_SOCKET_CMD = "chain-socket";

}