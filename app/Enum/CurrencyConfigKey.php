<?php

/**
 * CurrencyConfigKey.php
 * Author    chen<PERSON>q (<EMAIL>)
 * Version   1.0
 * Date      2025/7/2
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Enum;

enum CurrencyConfigKey:string
{
    case CURRENCY_CRYPTO_CONFIG = "currency:crypto";

    /**
     * 获取币种的配置key
     * @param int $currencyId
     * @return string
     */
    public static function getCurrencyKey(int $currencyId): string
    {
        return self::CURRENCY_CRYPTO_CONFIG->value.":{$currencyId}";
    }
}