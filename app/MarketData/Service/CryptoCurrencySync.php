<?php

/**
 * CryptoCurrencySync.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/24
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\MarketData\Service;

use App\Enum\CurrencyConfigKey;
use App\Enum\MarketType;
use App\Logger\LoggerFactory;
use App\Model\Currency\Currency;
use App\Model\Currency\CurrencyMate;
use App\Model\Currency\CurrencyTransfer;
use App\Model\Currency\CurrencyCategory;
use App\Service\TranslationService;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use App\Model\Trade\TradeMarginLevel;
use App\Model\Trade\TradeConfig;

/**
 * 同步币安交易所支持的所有交易对和交易对原数据
 */
class CryptoCurrencySync
{
    #[Inject]
    public LoggerFactory $loggerFactory;

    #[Inject]
    public TranslationService $translationService;

    #[Inject]
    public Redis $redis;

    public string $binance_api  = "https://api.binance.com";

    /**
     * 检查是否执行加密数字货币数据同步
     * @return bool
     */
    private function isSync(): bool
    {
        return Currency::query()->count() <= 0;
    }

    /**
     * 同步币安的加密数字货币交易对
     * @return void
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function sync(): void
    {
        if(!$this->isSync()){
            $this->loggerFactory->get(self::class)->info('加密数字货币数据已同步，跳过同步');
            return;
        }

        $result = $this->filterCurrency($this->getCurrencyData());

        if($result){
            //同步完成后推送消息到进程启动ticker 的订阅
        }

        $this->loggerFactory->get(self::class)->info($result ? '加密数字货币数据同步完成':'加密数字货币数据同步失败');
    }

    public function filterCurrency(array $currency): bool
    {
        try {

            $batch_data = [];
            $batch_index = 0;
            foreach ($currency['symbols'] as $value) {
                if(strtoupper($value['quoteAsset']) != "USDT"){
                    continue;
                }

                $currencyData = [
                    'symbol' => $value['symbol'],
                    'base_asset' => $value['baseAsset'],
                    'base_assets_precision' => $value['baseAssetPrecision'],
                    'quote_asset' => $value['quoteAsset'],
                    's_price_precision' => $value['quotePrecision'],
                    's_quantity_precision' => $value['quoteAssetPrecision'],
                    'is_spotTrade' => in_array("SPOT",$value['permissionSets'][0]) ? 1 : 0,
                    'is_marginTrade' => in_array("MARGIN",$value['permissionSets'][0]) ? 1 : 0,
                    'market_type' => MarketType::CRYPTO,
                    'trading_start' => '0',
                    'trading_end' => '0',
                    'trading_timezone' => 'UTC+8',
                    'status' => $batch_index < 50 ? 1 : 0,
                ];
                $batch_index++;

                $batch_data[] = $currencyData;

                if(count($batch_data) >= 20){
                    $result = Db::table(Currency::getTableName())->insert($batch_data);
                    if($result){
                        $symbols = array_column($batch_data,'base_asset');
                        $currencyIds = $this->getCurrencyIds($batch_data);
                        $this->processCurrencyMate($symbols, $currencyIds);
                    }
                    $batch_data = [];
                }
            }

            if (!empty($batch_data)) {
                $result = Db::table(table: Currency::getTableName())->insert($batch_data);
                if ($result) {
                    $symbols = array_column($batch_data, 'base_asset');
                    $currencyIds = $this->getCurrencyIds($batch_data);
                    $this->processCurrencyMate($symbols, $currencyIds);
                }
            }

            return true;
        }catch (\Throwable $t){
            $this->loggerFactory->get(self::class)->error("数据同步失败:{$t->getMessage()}");
            return false;
        }
    }

    private function getCurrencyIds(array $batch_data): array
    {
        $currencyIds = [];
        foreach ($batch_data as $currency) {
            $record = Currency::query()->where('symbol', $currency['symbol'])->first();
            if ($record) {
                $currencyIds[$currency['base_asset']] = $record->id;
            }
        }
        return $currencyIds;
    }



    private function processCurrencyMate(array $symbols, array $currencyIds): void
    {
        $logoData = $this->getBinanceLogos();
        $descriptionData = $this->getBinanceDescriptions();

        foreach ($symbols as $symbol) {
            if (!isset($currencyIds[$symbol])) {
                continue;
            }

            $tokenInfo = $this->getBinanceTokenInfo($symbol);
            if (!empty($tokenInfo)) {
                $this->saveBinanceMate($symbol, $tokenInfo, $logoData, $descriptionData, $currencyIds[$symbol]);
            }

            usleep(100000);
        }
    }

    private function getBinanceLogos(): array
    {
        try {
            $client = client();
            $response = $client->get("https://www.binance.com/bapi/asset/v1/public/asset/asset/get-asset-logo");
            $result = json_decode($response->getBody(), true);

            $logoMap = [];
            if (isset($result['data']) && is_array($result['data'])) {
                foreach ($result['data'] as $item) {
                    if (isset($item['asset']) && isset($item['pic'])) {
                        $logoMap[$item['asset']] = $item['pic'];
                    }
                }
            }

            return $logoMap;
        } catch (\Throwable $e) {
            return [];
        }
    }

    private function getBinanceDescriptions(): array
    {
        try {
            $client = client();
            $response = $client->get("https://bin.bnbstatic.com/api/i18n/-/web/cms/zh-CN/symbol-description");
            $result = json_decode($response->getBody(), true);

            $descMap = [];
            if (is_array($result)) {
                foreach ($result as $key => $value) {
                    if (strpos($key, 'symbol_desc_') === 0) {
                        $symbol = str_replace('symbol_desc_', '', $key);
                        $descMap[$symbol] = $value;
                    }
                }
            }

            return $descMap;
        } catch (\Throwable $e) {
            return [];
        }
    }

    private function getBinanceTokenInfo(string $symbol): array
    {
        try {
            $client = client();
            $response = $client->get("https://www.binance.com/bapi/apex/v1/friendly/apex/marketing/web/token-info", [
                'query' => ['symbol' => $symbol]
            ]);

            return json_decode($response->getBody(), true) ?? [];
        } catch (\Throwable $e) {
            return [];
        }
    }



    private function saveBinanceMate(string $symbol, array $tokenInfo, array $logoData, array $descriptionData, int $currencyId): void
    {
        $zhDescription = $descriptionData[$symbol] ?? '';
        $enDescription = '';
        if (!empty($zhDescription)) {
            $enDescription = $this->translationService->translate($zhDescription, 'zh', 'en');
        }

        $descriptionArray = [
            ['lang' => 'zh_cn', 'text' => $zhDescription],
            ['lang' => 'en', 'text' => $enDescription]
        ];

        $mateInfo = [];
        if (isset($tokenInfo['data'])) {
            $data = $tokenInfo['data'];
            $mateInfo = [
                'high_price_time' => isset($data['athd']) ? date('Y-m-d', $data['athd'] / 1000) : null,
                'high_price' => $data['athpu'] ?? null,
                'low_price_time' => isset($data['ald']) ? date('Y-m-d', $data['ald'] / 1000) : null,
                'low_price' => $data['atlpu'] ?? null,
                'market_cap_dominance' => $data['dmc'] ?? null,
                'rank' => $data['rk'] ?? null,
                'concentration_ratio' => $data['hhi'] ?? null,
                'market_cap' => $data['mc'] ?? null,
                'fully_diluted_market_cap' => $data['fdmc'] ?? null,
                'volume' => $data['v'] ?? null,
                'volume_market_ratio' => $data['vpm'] ?? null,
                'circulating_supply' => $data['cs'] ?? null,
                'max_supply' => $data['ms'] ?? null,
                'total_supply' => $data['ts'] ?? null,
                'website' => $data['ws'] ?? null,
                'whitepaper' => $data['wpu'] ?? null,
                'explorer' => $data['eu'] ?? null,
            ];
        }

        $saveData = [
            'currency_id' => $currencyId,
            'mate_info' => $mateInfo,
            'description' => $descriptionArray,
            'logo' => $logoData[$symbol] ?? '',
        ];

        CurrencyMate::query()->updateOrCreate(
            ['currency_id' => $currencyId],
            $saveData
        );
    }

    public function getCurrencyData()
    {
        try {
            $client = client(['base_uri' => $this->binance_api]);
            $response = $client->get("api/v3/exchangeInfo", [
                'query' => [
                    'permissions' => '["SPOT","MARGIN"]',
                    'symbolStatus' => 'TRADING'
                ]
            ]);
            return json_decode($response->getBody(), true);
        }catch (\Throwable $t){
            $this->loggerFactory->get('crypto-currency-sync')->error("币种数据同步失败，请检查网络:[{$t->getMessage()}]");
            return [];
        }
    }

    /**
     * 同步币安合约交易保证金档位数据
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function syncMarginLevels(): bool
    {
        try {
            // 获取币安风险档位数据
            $bracketsData = $this->getBinanceBrackets();
            if (empty($bracketsData) || !isset($bracketsData['data']['brackets'])) {
                $this->loggerFactory->get(self::class)->error('获取币安风险档位数据失败或数据为空');
                return false;
            }

            // 获取支持合约交易的货币列表
            $marginCurrencies = $this->getMarginCurrencies();
            if (empty($marginCurrencies)) {
                $this->loggerFactory->get(self::class)->info('没有找到支持合约交易的货币');
                return true;
            }

            // 批量保存保证金档位数据
            $result = $this->saveMarginLevels($bracketsData['data']['brackets'], $marginCurrencies);
            
            $this->loggerFactory->get(self::class)->info($result ? '保证金档位数据同步完成' : '保证金档位数据同步失败');
            return $result;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("保证金档位数据同步失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 获取币安风险档位数据
     * @return array
     */
    private function getBinanceBrackets(): array
    {
        try {
            $client = client();
            $response = $client->get('https://www.binance.com/bapi/futures/v1/friendly/future/common/brackets');
            return json_decode($response->getBody(), true) ?? [];
        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("请求币安风险档位API失败: {$e->getMessage()}");
            return [];
        }
    }

    /**
     * 获取支持合约交易的货币列表
     * @return array [symbol => currency_id]
     */
    private function getMarginCurrencies(): array
    {
        return Currency::query()
            ->where('is_marginTrade', 1)
            ->where('market_type', MarketType::CRYPTO->value)
            ->pluck('id', 'symbol')
            ->toArray();
    }

    /**
     * 保存保证金档位数据
     * @param array $brackets
     * @param array $marginCurrencies
     * @return bool
     */
    private function saveMarginLevels(array $brackets, array $marginCurrencies): bool
    {
        try {
            return Db::transaction(function () use ($brackets, $marginCurrencies) {
                $batchData = [];
                $processedCount = 0;

                foreach ($brackets as $bracketInfo) {
                    $symbol = $bracketInfo['symbol'] ?? '';
                    if (!isset($marginCurrencies[$symbol])) {
                        continue; // 跳过不支持合约交易的标的
                    }

                    $currencyId = $marginCurrencies[$symbol];
                    $riskBrackets = $bracketInfo['riskBrackets'] ?? [];

                    // 删除该货币的旧档位数据
                    TradeMarginLevel::query()->where('currency_id', $currencyId)->delete();

                    // 准备新的档位数据
                    foreach ($riskBrackets as $bracket) {
                        $batchData[] = [
                            'currency_id' => $currencyId,
                            'level' => $bracket['bracketSeq'] ?? 0,
                            'margin_min' => $bracket['bracketNotionalFloor'] ?? 0,
                            'margin_max' => $bracket['bracketNotionalCap'] ?? 0,
                            'leverage_min' => $bracket['minOpenPosLeverage'] ?? 1,
                            'leverage_max' => $bracket['maxOpenPosLeverage'] ?? 1,
                            'margin_rate' => $bracket['bracketMaintenanceMarginRate'] ?? 0,
                            'cum_amount' => $bracket['cumFastMaintenanceAmount'] ?? 0
                        ];

                        // 批量插入，每100条执行一次
                        if (count($batchData) >= 100) {
                            TradeMarginLevel::query()->insert($batchData);
                            $processedCount += count($batchData);
                            $batchData = [];
                        }
                    }
                }

                // 插入剩余数据
                if (!empty($batchData)) {
                    TradeMarginLevel::query()->insert($batchData);
                    $processedCount += count($batchData);
                }

                $this->loggerFactory->get(self::class)->info("成功处理 {$processedCount} 条保证金档位数据");
                return true;
            });

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("保存保证金档位数据失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 解析交易过滤器数据（通用版本，支持现货和合约）
     * @param array $filters
     * @param int $triggerProtect
     * @return array
     */
    private function parseTradeFilters(array $filters, float $triggerProtect = 0.0,float $liquidationFee=0.0): array
    {
        $tradeConfig = [
            'min_trade_num' => 0.0,
            'max_trade_num' => 0.0, 
            'min_trade_price' => 0.0,
            'max_trade_price' => 0.0,
            'limit_price_rate' => 0,
            'maker_limit' => 0.0,
            'limit_limit' => 0.0,
            'order_limit' => 0,
            'trigger_protect' => $triggerProtect,
            'liquidation_fee' => $liquidationFee,
            'tick_size' => 0.01
        ];

        foreach ($filters as $filter) {
            $filterType = $filter['filterType'] ?? '';
            
            switch ($filterType) {
                case 'LOT_SIZE':
                    $tradeConfig['min_trade_num'] = (float)($filter['minQty'] ?? 0);
                    $tradeConfig['max_trade_num'] = (float)($filter['maxQty'] ?? 0);
                    $tradeConfig['limit_limit'] = (float)($filter['maxQty'] ?? 0);
                    break;
                    
                case 'PRICE_FILTER':
                    $tradeConfig['min_trade_price'] = (float)($filter['minPrice'] ?? 0);
                    $tradeConfig['max_trade_price'] = (float)($filter['maxPrice'] ?? 0);
                    $tradeConfig['tick_size'] = (float)($filter['tickSize'] ?? 0.01);
                    break;
                    
                case 'PERCENT_PRICE_BY_SIDE':
                    // 现货使用这个类型
                    $tradeConfig['limit_price_rate'] = (int)($filter['avgPriceMins'] ?? 0);
                    break;
                    
                case 'PERCENT_PRICE':
                    // 合约使用这个类型，特殊处理：计算multiplierUp - multiplierDown
                    $multiplierUp = (float)($filter['multiplierUp'] ?? 0);
                    $multiplierDown = (float)($filter['multiplierDown'] ?? 0);
                    $difference = $multiplierUp - $multiplierDown;
                    $tradeConfig['limit_price_rate'] = (int)round($difference * 100);
                    break;
                    
                case 'MARKET_LOT_SIZE':
                    $tradeConfig['maker_limit'] = (float)($filter['maxQty'] ?? 0);
                    break;
                    
                case 'MAX_NUM_ORDERS':
                    $tradeConfig['order_limit'] = (int)($filter['maxNumOrders'] ?? $filter['limit'] ?? 0);
                    break;
                    
                case 'MIN_NOTIONAL':
                    // 可根据需要处理最小名义价值
                    break;
            }
        }

        return $tradeConfig;
    }

    /**
     * 批量保存交易配置数据（通用方法）
     * @param array $filtersData [symbol => [currency_id, filters, trigger_protect?]]
     * @param int $marketType
     * @return void
     */
    private function batchSaveTradeConfigs(array $filtersData, int $marketType): void
    {
        try {
            $batchConfigData = [];
            foreach ($filtersData as $symbol => $data) {
                $currencyId = $data['currency_id'];
                $filters = $data['filters'];
                $triggerProtect = (float)$data['trigger_protect'] ?? 0.0;
                $liquidationFee = (float)$data['liquidationFee'] ?? 0.0;
                $tradeConfig = $this->parseTradeFilters($filters, $triggerProtect,$liquidationFee);
                
                $batchConfigData[] = array_merge($tradeConfig, [
                    'currency_id' => $currencyId,
                    'market_type' => $marketType,
                ]);

                
                // 批量处理，每50条执行一次
                if (count($batchConfigData) >= 50) {
                    $this->insertTradeConfigs($batchConfigData);
                    $batchConfigData = [];
                }
            }
            
            // 处理剩余数据
            if (!empty($batchConfigData)) {
                $this->insertTradeConfigs($batchConfigData);
            }
            
        } catch (\Throwable $e) {
            var_dump($e->getMessage());
            $this->loggerFactory->get(self::class)->error("批量保存交易配置失败: {$e->getMessage()}");
        }
    }

    /**
     * 插入交易配置数据（通用方法）
     * @param array $batchConfigData
     * @return void
     */
    private function insertTradeConfigs(array $batchConfigData): void
    {
        foreach ($batchConfigData as $configData) {
            // 确保数据类型正确
            $configData['min_trade_num'] = (float)$configData['min_trade_num'];
            $configData['max_trade_num'] = (float)$configData['max_trade_num'];
            $configData['min_trade_price'] = (float)$configData['min_trade_price'];
            $configData['max_trade_price'] = (float)$configData['max_trade_price'];
            $configData['maker_limit'] = (float)$configData['maker_limit'];
            $configData['limit_limit'] = (float)$configData['limit_limit'];
            $configData['limit_price_rate'] = (int)$configData['limit_price_rate'];
            $configData['order_limit'] = (int)$configData['order_limit'];
            $configData['trigger_protect'] = (float)$configData['trigger_protect'];
            
            TradeConfig::query()->updateOrCreate(
                [
                    'currency_id' => $configData['currency_id'],
                    'market_type' => $configData['market_type'],
                ],
                $configData
            );
        }
    }

    /**
     * 同步币安合约交易配置数据
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function syncContractCurrencyConfig(): bool
    {
        try {
            // 获取币安合约交易信息
            $contractData = $this->getBinanceContractExchangeInfo();
            if (empty($contractData) || !isset($contractData['symbols'])) {
                $this->loggerFactory->get(self::class)->error('获取币安合约交易信息失败或数据为空');
                return false;
            }

            // 获取支持合约交易的货币列表
            $marginCurrencies = $this->getMarginCurrencies();
            if (empty($marginCurrencies)) {
                $this->loggerFactory->get(self::class)->info('没有找到支持合约交易的货币');
                return true;
            }

            // 处理合约数据
            $result = $this->processContractData($contractData['symbols'], $marginCurrencies);
            
            $this->loggerFactory->get(self::class)->info($result ? '合约交易配置数据同步完成' : '合约交易配置数据同步失败');
            return $result;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("合约交易配置数据同步失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 获取币安合约交易信息
     * @return array
     */
    private function getBinanceContractExchangeInfo(): array
    {
        try {
            $client = client();
            $response = $client->get('https://fapi.binance.com/fapi/v1/exchangeInfo');
            return json_decode($response->getBody(), true) ?? [];
        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("请求币安合约交易API失败: {$e->getMessage()}");
            return [];
        }
    }

    /**
     * 处理合约数据
     * @param array $symbols
     * @param array $marginCurrencies [symbol => currency_id]
     * @return bool
     */
    private function processContractData(array $symbols, array $marginCurrencies): bool
    {
        try {
            return Db::transaction(function () use ($symbols, $marginCurrencies) {
                $precisionUpdates = [];
                $contractFilters = [];
                $processedCount = 0;

                foreach ($symbols as $symbolData) {
                    $symbol = $symbolData['symbol'] ?? '';
                    
                    if (!isset($marginCurrencies[$symbol])) {
                        continue; // 跳过不支持合约交易的标的
                    }

                    $currencyId = $marginCurrencies[$symbol];

                    // 收集精度更新数据
                    $precisionUpdates[$currencyId] = [
                        'm_price_precision' => $symbolData['pricePrecision'] ?? 0,
                        'm_quantity_precision' => $symbolData['quantityPrecision'] ?? 0,
                    ];

                    // 收集合约过滤器数据
                    $contractFilters[$symbol] = [
                        'currency_id' => $currencyId,
                        'filters' => $symbolData['filters'] ?? [],
                        'trigger_protect' => (int)($symbolData['triggerProtect'] ?? 0),
                        'liquidationFee' => (float)($symbolData['liquidationFee'] ?? 0),
                    ];

                    $processedCount++;
                }

                // 批量更新Currency精度
                $this->batchUpdateCurrencyPrecision($precisionUpdates);
                // 批量保存合约交易配置
                $this->batchSaveTradeConfigs($contractFilters, MarketType::MARGIN->value);

                $this->loggerFactory->get(self::class)->info("成功处理 {$processedCount} 个合约标的配置");
                return true;
            });

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("处理合约数据失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 批量更新Currency精度字段（通用方法）
     * @param array $precisionUpdates [currency_id => [precision_field => value]]
     * @return void
     */
    private function batchUpdateCurrencyPrecision(array $precisionUpdates): void
    {
        try {
            foreach ($precisionUpdates as $currencyId => $precisionData) {
                Currency::query()
                    ->where('id', $currencyId)
                    ->update($precisionData);
            }

            $this->loggerFactory->get(self::class)->info("成功更新 " . count($precisionUpdates) . " 个货币的精度");

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("批量更新Currency精度失败: {$e->getMessage()}");
        }
    }

    /**
     * 同步币安现货交易配置数据
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function syncSpotCurrencyConfig(): bool
    {
        try {
            // 获取币安现货交易信息
            $spotData = $this->getCurrencyData();
            if (empty($spotData) || !isset($spotData['symbols'])) {
                $this->loggerFactory->get(self::class)->error('获取币安现货交易信息失败或数据为空');
                return false;
            }

            // 获取支持现货交易的货币列表
            $spotCurrencies = $this->getSpotCurrencies();
            if (empty($spotCurrencies)) {
                $this->loggerFactory->get(self::class)->info('没有找到支持现货交易的货币');
                return true;
            }

            // 处理现货配置数据
            $result = $this->processSpotConfigData($spotData['symbols'], $spotCurrencies);
            
            $this->loggerFactory->get(self::class)->info($result ? '现货交易配置数据同步完成' : '现货交易配置数据同步失败');
            return $result;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("现货交易配置数据同步失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 获取支持现货交易的货币列表
     * @return array [symbol => currency_id]
     */
    private function getSpotCurrencies(): array
    {
        return Currency::query()
            ->where('is_spotTrade', 1)
            ->where('market_type',MarketType::CRYPTO->value)
            ->pluck('id', 'symbol')
            ->toArray();
    }

    /**
     * 处理现货配置数据
     * @param array $symbols
     * @param array $spotCurrencies [symbol => currency_id]
     * @return bool
     */
    private function processSpotConfigData(array $symbols, array $spotCurrencies): bool
    {
        try {
            return Db::transaction(function () use ($symbols, $spotCurrencies) {
                $precisionUpdates = [];
                $spotFilters = [];
                $processedCount = 0;

                foreach ($symbols as $symbolData) {
                    $symbol = $symbolData['symbol'] ?? '';
                    
                    // 只处理USDT交易对
                    if (strtoupper($symbolData['quoteAsset'] ?? '') !== "USDT") {
                        continue;
                    }

                    if (!isset($spotCurrencies[$symbol])) {
                        continue; // 跳过不支持现货交易的标的
                    }

                    $currencyId = $spotCurrencies[$symbol];

                    // 收集现货精度更新数据
                    $precisionUpdates[$currencyId] = [
                        's_price_precision' => $symbolData['quotePrecision'] ?? 0,
                        's_quantity_precision' => $symbolData['quoteAssetPrecision'] ?? 0,
                    ];

                    // 收集现货过滤器数据
                    $spotFilters[$symbol] = [
                        'currency_id' => $currencyId,
                        'filters' => $symbolData['filters'] ?? [],
                        'trigger_protect' => 0, // 现货默认为0
                    ];

                    $processedCount++;
                }

                // 批量更新Currency现货精度
                $this->batchUpdateCurrencyPrecision($precisionUpdates);

                // 批量保存现货交易配置
                $this->batchSaveTradeConfigs($spotFilters, MarketType::CRYPTO->value);

                $this->loggerFactory->get(self::class)->info("成功处理 {$processedCount} 个现货标的配置");
                return true;
            });

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("处理现货配置数据失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 加载活跃币种数据到Redis
     * 以币种ID为key的hash结构存储
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function loadCurrenciesToRedis(): bool
    {
        try {
            // 查询status=1的币种数据
            $currencies = Currency::query()
                ->where('status', 1)
                ->get();

            if ($currencies->isEmpty()) {
                $this->loggerFactory->get(self::class)->info('没有找到活跃的币种数据');
                return true;
            }

            $successCount = 0;
            $totalCount = $currencies->count();

            foreach ($currencies as $currency) {
                $currencyId = $currency->getId();
                $redisKey = CurrencyConfigKey::CURRENCY_CRYPTO_CONFIG->value.":{$currencyId}";

                // 准备币种数据
                $currencyData = [
                    'id' => $currencyId,
                    'symbol' => $currency->getSymbol(),
                    'base_asset' => $currency->base_asset ?? '',
                    'base_assets_precision' => $currency->base_assets_precision ?? 0,
                    'quote_asset' => $currency->quote_asset ?? '',
                    'quote_assets_id' => $currency->quote_assets_id ?? 0,
                    's_price_precision' => $currency->s_price_precision ?? 0,
                    's_quantity_precision' => $currency->s_quantity_precision ?? 0,
                    'm_price_precision' => $currency->m_price_precision ?? 0,
                    'm_quantity_precision' => $currency->m_quantity_precision ?? 0,
                    'is_spotTrade' => $currency->is_spotTrade ?? 0,
                    'is_marginTrade' => $currency->is_marginTrade ?? 0,
                    'market_type' => $currency->market_type ?? MarketType::CRYPTO->value,
                    'trading_start' => $currency->trading_start ?? '0',
                    'trading_end' => $currency->trading_end ?? '0',
                    'trading_timezone' => $currency->trading_timezone ?? 'UTC+8',
                    'status' => $currency->status ?? 0,
                    'sort' => $currency->sort ?? 0,
                    'decimals' => $currency->decimals ?? 8,
                    'created_at' => $currency->getCreatedAt()?->format('Y-m-d H:i:s') ?? '',
                    'updated_at' => $currency->getUpdatedAt()?->format('Y-m-d H:i:s') ?? '',
                ];

                // 写入Redis Hash
                try {
                    $result = $this->redis->hMSet($redisKey, $currencyData);
                    if ($result) {
                        // 设置过期时间（24小时）
                        $this->redis->expire($redisKey, 86400);
                        $successCount++;
                    }
                } catch (\Throwable $e) {
                    $this->loggerFactory->get(self::class)->error("写入Redis失败，币种ID: {$currencyId}, 错误: {$e->getMessage()}");
                }
            }

            $this->loggerFactory->get(self::class)->info("币种数据加载完成，成功: {$successCount}/{$totalCount}");
            return $successCount === $totalCount;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("加载币种数据到Redis失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 清理Redis中的币种数据
     * @return bool
     */
    public function clearCurrenciesFromRedis(): bool
    {
        try {
            // 查找所有currency:*的key
            $keys = $this->redis->keys('currency:*');
            
            if (empty($keys)) {
                $this->loggerFactory->get(self::class)->info('Redis中没有找到币种数据');
                return true;
            }

            // 批量删除
            $deletedCount = $this->redis->del(...$keys);
            
            $this->loggerFactory->get(self::class)->info("清理Redis币种数据完成，删除: {$deletedCount} 个key");
            return $deletedCount === count($keys);

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("清理Redis币种数据失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 重新加载币种数据到Redis（清理后重新加载）
     * @return bool
     */
    public function reloadCurrenciesToRedis(): bool
    {
        try {
            // 先清理
            $this->clearCurrenciesFromRedis();
            
            // 再加载
            $result = $this->loadCurrenciesToRedis();
            
            $this->loggerFactory->get(self::class)->info($result ? '币种数据重新加载完成' : '币种数据重新加载失败');
            return $result;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("重新加载币种数据失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 同步Bitget币种划转数据
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function syncBitgetCurrencyTransfer(): bool
    {
        try {
            // 获取现货交易币种数据
            $spotCurrencies = $this->getSpotCurrenciesForTransfer();
            if (empty($spotCurrencies)) {
                $this->loggerFactory->get(self::class)->info('没有找到现货交易的币种数据');
                return true;
            }

            // 请求Bitget API获取币种划转数据
            $bitgetData = $this->getBitgetCoinsData();
            if (empty($bitgetData) || !isset($bitgetData['data'])) {
                $this->loggerFactory->get(self::class)->error('获取Bitget币种数据失败或数据为空');
                return false;
            }

            // 处理并保存划转数据
            $result = $this->processBitgetTransferData($bitgetData['data'], $spotCurrencies);
            
            $this->loggerFactory->get(self::class)->info($result ? 'Bitget币种划转数据同步完成' : 'Bitget币种划转数据同步失败');
            return $result;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("同步Bitget币种划转数据失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 获取现货交易币种数据用于划转同步
     * @return array [base_asset => currency_id]
     */
    private function getSpotCurrenciesForTransfer(): array
    {
        return Currency::query()
            ->where('market_type', MarketType::CRYPTO->value)
            ->pluck('id', 'base_asset')
            ->toArray();
    }

    /**
     * 请求Bitget API获取币种数据
     * @return array
     */
    private function getBitgetCoinsData(): array
    {
        try {
            $client = client();
            $response = $client->get('https://api.bitget.com/api/v2/spot/public/coins');
            $result = json_decode($response->getBody(), true);

            if (isset($result['code']) && $result['code'] === '00000') {
                return $result;
            } else {
                $this->loggerFactory->get(self::class)->error("Bitget API返回错误: " . ($result['msg'] ?? '未知错误'));
                return [];
            }

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("请求Bitget API失败: {$e->getMessage()}");
            return [];
        }
    }

    /**
     * 处理Bitget划转数据并保存到数据库
     * @param array $bitgetCoins
     * @param array $spotCurrencies [base_asset => currency_id]
     * @return bool
     */
    private function processBitgetTransferData(array $bitgetCoins, array $spotCurrencies): bool
    {
        try {
            return Db::transaction(function () use ($bitgetCoins, $spotCurrencies) {
                $processedCount = 0;
                $matchedCount = 0;

                foreach ($bitgetCoins as $coinData) {
                    $coin = $coinData['coin'] ?? '';
                    if (empty($coin)) {
                        continue;
                    }

                    // 检查是否在我们的币种列表中
                    if (!isset($spotCurrencies[$coin])) {
                        continue;
                    }

                    $currencyId = $spotCurrencies[$coin];
                    $matchedCount++;

                    // 处理transfer字段：转换为整数（true/false -> 1/0）
                    $transfer = $this->convertTransferValue($coinData['transfer'] ?? 'false');

                    // 处理chains数据
                    $chains = $this->processChainData($coinData['chains'] ?? []);

                    // 保存或更新CurrencyTransfer数据
                    $transferData = [
                        'currency_id' => $currencyId,
                        'transfer' => $transfer,
                        'chains' => $chains,
                    ];

                    CurrencyTransfer::query()->updateOrCreate(
                        ['currency_id' => $currencyId],
                        $transferData
                    );

                    $processedCount++;
                }

                $this->loggerFactory->get(self::class)->info(
                    "Bitget划转数据处理完成，匹配: {$matchedCount}，处理: {$processedCount}"
                );

                return true;
            });

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("处理Bitget划转数据失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 转换transfer字段值
     * @param string $transferValue
     * @return int
     */
    private function convertTransferValue(string $transferValue): int
    {
        return strtolower($transferValue) === 'true' ? 1 : 0;
    }

    /**
     * 处理链数据，添加额外的元数据
     * @param array $chains
     * @return array
     */
    private function processChainData(array $chains): array
    {
        $processedChains = [];

        foreach ($chains as $chain) {
            $processedChain = [
                'chain' => $chain['chain'] ?? '',
                'needTag' => ($chain['needTag'] ?? 'false') === 'true',
                'withdrawable' => ($chain['withdrawable'] ?? 'false') === 'true',
                'rechargeable' => ($chain['rechargeable'] ?? 'false') === 'true',
                'withdrawFee' => (float)($chain['withdrawFee'] ?? 0),
                'extraWithdrawFee' => (float)($chain['extraWithdrawFee'] ?? 0),
                'depositConfirm' => (int)($chain['depositConfirm'] ?? 0),
                'withdrawConfirm' => (int)($chain['withdrawConfirm'] ?? 0),
                'minDepositAmount' => (float)($chain['minDepositAmount'] ?? 0),
                'minWithdrawAmount' => (float)($chain['minWithdrawAmount'] ?? 0),
                'browserUrl' => $chain['browserUrl'] ?? '',
                'contractAddress' => $chain['contractAddress'] ?? null,
                'withdrawStep' => $chain['withdrawStep'] ?? '0',
                'withdrawMinScale' => (int)($chain['withdrawMinScale'] ?? 8),
                'congestion' => $chain['congestion'] ?? 'normal',
            ];

            $processedChains[] = $processedChain;
        }

        return $processedChains;
     }

    /**
     * 同步Bitget币种栏目配置数据
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function syncBitgetCurrencyCategories(): bool
    {
        try {
            // 获取现货币种数据
            $spotCurrencies = $this->getSpotCurrenciesForTransfer();
            if (empty($spotCurrencies)) {
                $this->loggerFactory->get(self::class)->info('没有找到现货交易的币种数据');
                return true;
            }

            // 请求Bitget API获取栏目数据
            $bitgetData = $this->getBitgetCategoryData();
            if (empty($bitgetData) || !isset($bitgetData['data']['item'][0]['categories'])) {
                $this->loggerFactory->get(self::class)->error('获取Bitget栏目数据失败或数据格式错误');
                return false;
            }

            $categories = $bitgetData['data']['item'][0]['categories'];
            // 从第1个元素开始处理（跳过第0个）
            $categoriesToProcess = array_slice($categories, 1);

            if (empty($categoriesToProcess)) {
                $this->loggerFactory->get(self::class)->info('没有找到需要处理的栏目数据');
                return true;
            }

            // 处理栏目数据
            $result = $this->processCategoryData($categoriesToProcess, $spotCurrencies);
            
            $this->loggerFactory->get(self::class)->info($result ? 'Bitget币种栏目数据同步完成' : 'Bitget币种栏目数据同步失败');
            return $result;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("同步Bitget币种栏目数据失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 请求Bitget API获取栏目数据
     * @return array
     */
    private function getBitgetCategoryData(): array
    {
        try {
            $client = client();
            $response = $client->get('https://www.bitget.com/v1/spot/symbol/getAllPartitionV2', [
                'query' => [
                    'md5Value' => '',
                    'region' => 'true',
                    'showWatchGroup' => 'true',
                    'languageType' => '1'
                ]
            ]);
            
            return json_decode($response->getBody(), true) ?? [];

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("请求Bitget栏目API失败: {$e->getMessage()}");
            return [];
        }
    }

    /**
     * 处理栏目数据并保存
     * @param array $categories
     * @param array $spotCurrencies [base_asset => currency_id]
     * @return bool
     */
    private function processCategoryData(array $categories, array $spotCurrencies): bool
    {
        try {
            return Db::transaction(function () use ($categories, $spotCurrencies) {
                $processedCategories = 0;
                $updatedCurrencies = 0;
                $translationCache = []; // 翻译缓存避免重复翻译

                foreach ($categories as $category) {
                    $categoryId = (int)($category['categoryId'] ?? 0);
                    $categoryName = $category['categoryName'] ?? '';
                    $categoryDesc = $category['keyTypePlateDescName'] ?? '';
                    $sort = (int)($category['sort'] ?? 0);
                    $symbolList = $category['symbolList'] ?? [];

                    if (empty($categoryName) || $categoryId <= 0) {
                        continue;
                    }

                    // 翻译栏目名称和描述
                    $translatedName = $this->translateCategoryTexts($categoryName, $translationCache);
                    $translatedDesc = $this->translateCategoryTexts($categoryDesc, $translationCache);

                    // 保存栏目数据
                    $categoryData = [
                        'id' => $categoryId,
                        'cate_name' => $translatedName,
                        'cate_desc' => $translatedDesc,
                        'sort' => $sort,
                        'status' => 1, // 默认启用
                    ];

                    CurrencyCategory::query()->updateOrCreate(
                        ['id' => $categoryId],
                        $categoryData
                    );

                    $processedCategories++;
                    // 更新币种关联
                    $currencyUpdateCount = $this->updateCurrencyMateCategories($symbolList, $categoryId, $spotCurrencies);
                    $updatedCurrencies += $currencyUpdateCount;
                }

                $this->loggerFactory->get(self::class)->info(
                    "栏目数据处理完成，处理栏目: {$processedCategories}，更新币种关联: {$updatedCurrencies}"
                );

                return true;
            });

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("处理栏目数据失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 翻译栏目文本（带缓存）
     * @param string $text
     * @param array &$cache
     * @return array
     */
    private function translateCategoryTexts(string $text, array &$cache): array
    {
        if (empty($text)) {
            return [
                ['lang' => 'zh_cn', 'text' => ''],
                ['lang' => 'en', 'text' => '']
            ];
        }

        // 检查缓存
        if (isset($cache[$text])) {
            return $cache[$text];
        }

        try {
            // 翻译为英文
            $englishText = $this->translationService->translate($text, 'zh', 'en');
            
            $result = [
                ['lang' => 'zh_cn', 'text' => $text],
                ['lang' => 'en', 'text' => $englishText ?: $text] // 翻译失败时使用原文
            ];

            // 缓存结果
            $cache[$text] = $result;
            
            return $result;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("翻译失败，文本: {$text}, 错误: {$e->getMessage()}");
            
            // 翻译失败时返回原文
            $result = [
                ['lang' => 'zh_cn', 'text' => $text],
                ['lang' => 'en', 'text' => $text]
            ];
            
            $cache[$text] = $result;
            return $result;
        }
    }

    /**
     * 更新币种关联分类（增量更新cateIds）
     * @param array $symbolList
     * @param int $categoryId
     * @param array $spotCurrencies [base_asset => currency_id]
     * @return int 更新的币种数量
     */
    private function updateCurrencyMateCategories(array $symbolList, int $categoryId, array $spotCurrencies): int
    {
        $updatedCount = 0;

        try {
            foreach ($symbolList as $symbol) {
                if (empty($symbol)) {
                    continue;
                }

                // 提取base_asset（移除USDT后缀）
                $baseAsset = str_replace('USDT', '', $symbol);
                if (!isset($spotCurrencies[$baseAsset])) {
                    continue;
                }

                $currencyId = $spotCurrencies[$baseAsset];

                // 查询现有的CurrencyMate记录
                $currencyMate = CurrencyMate::query()->where('currency_id', $currencyId)->first();
                if (!$currencyMate) {
                    // 如果没有CurrencyMate记录，创建一个基础记录
                    $currencyMate = new CurrencyMate();
                    $currencyMate->currency_id = $currencyId;
                    $currencyMate->mate_info = [];
                    $currencyMate->description = [];
                    $currencyMate->logo = '';
                    $currencyMate->cateIds = [];
                }

                // 获取现有的cateIds
                $existingCateIds = $currencyMate->getCateIds() ?? [];
                
                // 增量更新：合并新的categoryId
                if (!in_array($categoryId, $existingCateIds)) {
                    $existingCateIds[] = $categoryId;
                    $currencyMate->setCateIds(array_values(array_unique($existingCateIds))); // 去重并重新索引
                    $currencyMate->save();
                    $updatedCount++;
                }
            }

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("更新币种分类关联失败，栏目ID: {$categoryId}, 错误: {$e->getMessage()}");
        }

        return $updatedCount;
    }

}