<?php

declare(strict_types=1);

namespace App\MarketData\Service\MatchEngine;

use App\Enum\MatchEngine\ManagerStreamKey;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

/**
 * Redis Stream 处理器
 * 负责从 Redis Stream 消费消息并转发到管理进程
 */
class RedisStreamHandler
{
    protected Redis $redis;
    protected LoggerInterface $logger;
    protected array $lastMessageIds = [];
    protected bool $isInitialized = false;

    public function __construct(ContainerInterface $container)
    {
        $this->redis = $container->get(Redis::class);
        $this->logger = $container->get(LoggerFactory::class)->get('redis-stream');
        $this->initializeLastMessageIds();
    }

    /**
     * 初始化消息ID跟踪
     */
    protected function initializeLastMessageIds(): void
    {
        foreach (ManagerStreamKey::cases() as $streamKey) {
            $this->lastMessageIds[$streamKey->value] = '0-0';
        }
    }

    /**
     * 初始化 Redis Streams（创建消费者组）
     */
    public function initializeStreams(): void
    {
        if ($this->isInitialized) {
            return;
        }

        foreach (ManagerStreamKey::cases() as $streamKey) {
            try {
                // 尝试创建消费者组（如果不存在）
                $this->redis->xGroup(
                    'CREATE',
                    $streamKey->value,
                    $streamKey->getConsumerGroup(),
                    '0',
                    true // MKSTREAM - 如果流不存在则创建
                );
            } catch (\Throwable $e) {
                // 消费者组可能已存在，忽略错误
            }
        }

        $this->isInitialized = true;
    }

    /**
     * 消费指定市场类型的命令流消息
     */
    public function consumeCommands(string $marketType = 'spot'): array
    {
        $streamKey = ManagerStreamKey::getCommandStreamByMarket($marketType);
        return $streamKey ? $this->consumeStream($streamKey) : [];
    }

    /**
     * 消费指定市场类型的订单流消息
     */
    public function consumeOrders(string $marketType = 'spot'): array
    {
        $streamKey = ManagerStreamKey::getOrderStreamByMarket($marketType);
        return $streamKey ? $this->consumeStream($streamKey) : [];
    }

    /**
     * 消费所有市场类型的命令流消息
     */
    public function consumeAllCommands(): array
    {
        $messages = [];
        foreach (['spot', 'contract', 'futures'] as $marketType) {
            $messages = array_merge($messages, $this->consumeCommands($marketType));
        }
        return $messages;
    }

    /**
     * 消费所有市场类型的订单流消息
     */
    public function consumeAllOrders(): array
    {
        $messages = [];
        foreach (['spot', 'contract', 'futures'] as $marketType) {
            $messages = array_merge($messages, $this->consumeOrders($marketType));
        }
        return $messages;
    }

    /**
     * 消费系统控制流消息（所有市场类型）
     */
    public function consumeSystemControl(): array
    {
        $messages = [];
        $systemStreams = [
            ManagerStreamKey::SPOT_SYSTEM_CONTROL_STREAM,
            ManagerStreamKey::CONTRACT_SYSTEM_CONTROL_STREAM,
            ManagerStreamKey::FUTURES_SYSTEM_CONTROL_STREAM,
        ];
        
        foreach ($systemStreams as $streamKey) {
            $messages = array_merge($messages, $this->consumeStream($streamKey));
        }
        return $messages;
    }

    /**
     * 消费配置更新流消息（所有市场类型）
     */
    public function consumeConfigUpdate(): array
    {
        $messages = [];
        $configStreams = [
            ManagerStreamKey::SPOT_CONFIG_UPDATE_STREAM,
            ManagerStreamKey::CONTRACT_CONFIG_UPDATE_STREAM,
            ManagerStreamKey::FUTURES_CONFIG_UPDATE_STREAM,
        ];
        
        foreach ($configStreams as $streamKey) {
            $messages = array_merge($messages, $this->consumeStream($streamKey));
        }
        return $messages;
    }

    /**
     * 消费指定市场类型的系统控制流消息
     */
    public function consumeSystemControlByMarket(string $marketType): array
    {
        $streamKey = match ($marketType) {
            'spot' => ManagerStreamKey::SPOT_SYSTEM_CONTROL_STREAM,
            'contract' => ManagerStreamKey::CONTRACT_SYSTEM_CONTROL_STREAM,
            'futures' => ManagerStreamKey::FUTURES_SYSTEM_CONTROL_STREAM,
            default => null
        };
        
        return $streamKey ? $this->consumeStream($streamKey) : [];
    }

    /**
     * 消费指定市场类型的配置更新流消息
     */
    public function consumeConfigUpdateByMarket(string $marketType): array
    {
        $streamKey = match ($marketType) {
            'spot' => ManagerStreamKey::SPOT_CONFIG_UPDATE_STREAM,
            'contract' => ManagerStreamKey::CONTRACT_CONFIG_UPDATE_STREAM,
            'futures' => ManagerStreamKey::FUTURES_CONFIG_UPDATE_STREAM,
            default => null
        };
        
        return $streamKey ? $this->consumeStream($streamKey) : [];
    }

    /**
     * 消费指定流的消息
     */
    protected function consumeStream(ManagerStreamKey $streamKey): array
    {
        try {
            $messages = $this->redis->xReadGroup(
                $streamKey->getConsumerGroup(),
                $streamKey->getConsumerName(),
                [$streamKey->value => '>'],
                1, // 每次最多读取1条消息
                100 // 100ms 超时
            );

            if (empty($messages) || empty($messages[$streamKey->value])) {
                return [];
            }

            $processedMessages = [];
            foreach ($messages[$streamKey->value] as $messageId => $fields) {
                $processedMessages[] = [
                    'stream' => $streamKey->value,
                    'message_id' => $messageId,
                    'data' => $fields,
                    'stream_key' => $streamKey
                ];
            }

            return $processedMessages;
        } catch (\Throwable $e) {
            // 记录错误但不抛出异常，避免影响其他流的处理
            $this->logger->error("Redis Stream consume error", [
                'stream' => $streamKey->value,
                'error' => $e->getMessage(),
                'exception' => $e
            ]);
            return [];
        }
    }

    /**
     * 确认消息已处理并删除消息
     */
    public function ackMessage(ManagerStreamKey $streamKey, string $messageId): bool
    {
        try {
            // 先确认消息
            $ackResult = $this->redis->xAck(
                $streamKey->value,
                $streamKey->getConsumerGroup(),
                [$messageId]
            );
            
            if ($ackResult > 0) {
                // 确认成功后删除消息
                $deleteResult = $this->redis->xDel($streamKey->value, [$messageId]);
                if ($deleteResult > 0) {
                    return true;
                                 } else {
                     $this->logger->warning("Redis Stream message ACK successful but delete failed", [
                         'stream' => $streamKey->value,
                         'message_id' => $messageId
                     ]);
                     return true; // ACK成功就算成功，删除失败不影响业务
                 }
            }
            
            return false;
        } catch (\Throwable $e) {
            $this->logger->error("Redis Stream ACK error", [
                'stream' => $streamKey->value,
                'message_id' => $messageId,
                'error' => $e->getMessage(),
                'exception' => $e
            ]);
            return false;
        }
    }



    /**
     * 验证消息格式
     */
    public function validateMessage(array $fields, string $streamValue): bool
    {
        // 通过流值找到对应的枚举
        $streamKey = null;
        foreach (ManagerStreamKey::cases() as $case) {
            if ($case->value === $streamValue) {
                $streamKey = $case;
                break;
            }
        }
        
        if (!$streamKey) {
            return false;
        }
        
        $streamType = $streamKey->getStreamType();
        
        switch ($streamType) {
            case 'commands':
                return isset($fields['type']) && isset($fields['data']);
            
            case 'orders':
                return isset($fields['type']) && isset($fields['data']);
            
            case 'system_control':
                return isset($fields['command']) && isset($fields['data']);
            
            case 'config_update':
                return isset($fields['config_type']) && isset($fields['data']);
            
            default:
                return false;
        }
    }

    /**
     * 格式化消息数据为统一格式
     */
    public function formatMessage(array $fields, ManagerStreamKey $streamKey): array
    {
        $streamType = $streamKey->getStreamType();
        $marketType = $streamKey->getMarketType();
        
        $baseData = [
            'market_type' => $marketType,
            'stream_type' => $streamType,
        ];
        
        switch ($streamType) {
            case 'commands':
                return array_merge($baseData, [
                    'type' => $fields['type'] ?? '',
                    'data' => $fields['data'] ?? '{}',
                    'target_process' => (int)($fields['target_process'] ?? 0),
                    'priority' => (int)($fields['priority'] ?? 0),
                ]);

            case 'orders':
                return array_merge($baseData, [
                    'type' => $fields['type'] ?? 'order',
                    'data' => $fields['data'] ?? '{}',
                    'symbol' => $fields['symbol'] ?? '',
                    'user_id' => $fields['user_id'] ?? '',
                ]);

            case 'system_control':
                return array_merge($baseData, [
                    'type' => 'system_control',
                    'command' => $fields['command'] ?? '',
                    'data' => $fields['data'] ?? '{}',
                ]);

            case 'config_update':
                return array_merge($baseData, [
                    'type' => 'config_update',
                    'config_type' => $fields['config_type'] ?? '',
                    'data' => $fields['data'] ?? '{}',
                ]);

            default:
                return array_merge($baseData, $fields);
        }
    }
} 