<?php

/**
 * DepthSaveService.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/27
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\MarketData\Service\MatchEngine;

use App\Enum\MatchEngine\MatchEngineDepthKey;
use App\Enum\MatchEngine\MatchEngineDepthSendKey;
use App\Enum\MarketType;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use App\Model\Match\MatchOrder;
use App\Model\Currency\Currency;
use App\Enum\TradeSide;
use App\Enum\OrderStatus;
use App\Enum\OrderType;

class DepthSaveService
{
    #[Inject]
    private Redis $redis;

    /**
     * 保存深度数据到redis（简化版：只保存价格和总量）
     * @param string $symbol 交易对符号
     * @param int $marketType 市场类型
     * @param array $depth 深度数据（增量）
     * @return void
     */
    public function asyncSaveRedis(string $symbol, int $marketType, array $depth): void
    {
        if (empty($depth) || !isset($depth['bids']) || !isset($depth['asks'])) {
            return;
        }

        try {
            // 根据市场类型获取Redis key
            $redisKey = $this->getDepthRedisKey($symbol, $marketType);
            if (empty($redisKey)) {
                return;
            }

            // 买单深度hash key
            $bidsKey = "{$redisKey}:bids";
            // 卖单深度hash key
            $asksKey = "{$redisKey}:asks";

            $send_bids = [];
            $send_asks = [];
            $hasWrites = false;

            try {
                $writePipeline = $this->redis->pipeline();

                // 处理买单深度数据
                if (!empty($depth['bids']) && is_array($depth['bids'])) {
                    foreach ($depth['bids'] as $bidLevel) {
                        if (!isset($bidLevel['price'])) {
                            continue;
                        }

                        $price = (string)$bidLevel['price'];
                        $totalQuantity = (float)($bidLevel['total_quantity'] ?? 0);

                        // 如果total_quantity为0，删除这个价位
                        if ($totalQuantity == 0.0) {
                            $writePipeline->hDel($bidsKey, $price);
                            $hasWrites = true;
                            $send_bids[] = [(float)$price, 0.0];
                        } else {
                            // 保存价格和总量
                            $writePipeline->hSet($bidsKey, $price, (string)$totalQuantity);
                            $hasWrites = true;
                            $send_bids[] = [(float)$price, $totalQuantity];
                        }
                    }
                }

                // 处理卖单深度数据
                if (!empty($depth['asks']) && is_array($depth['asks'])) {
                    foreach ($depth['asks'] as $askLevel) {
                        if (!isset($askLevel['price'])) {
                            continue;
                        }

                        $price = (string)$askLevel['price'];
                        $totalQuantity = (float)($askLevel['total_quantity'] ?? 0);

                        // 如果total_quantity为0，删除这个价位
                        if ($totalQuantity == 0.0) {
                            $writePipeline->hDel($asksKey, $price);
                            $hasWrites = true;
                            $send_asks[] = [(float)$price, 0.0];
                        } else {
                            // 保存价格和总量
                            $writePipeline->hSet($asksKey, $price, (string)$totalQuantity);
                            $hasWrites = true;
                            $send_asks[] = [(float)$price, $totalQuantity];
                        }
                    }
                }

                // 只有有写入操作时才执行pipeline
                if ($hasWrites) {
                    $writePipeline->exec();
                }

            } catch (\Throwable $e) {
                error_log("DepthSaveService::asyncSaveRedis write error: " . $e->getMessage());
                return; // 写入失败直接返回，不影响后续流程
            }

            // 异步发布深度更新（只发布有变化的价位）
            if (!empty($send_bids) || !empty($send_asks)) {
                go(function () use ($symbol, $marketType, $send_bids, $send_asks) {
                    try {
                        $this->publishDepthUpdate(
                            $symbol,
                            $marketType,
                            $send_bids,
                            $send_asks
                        );
                    } catch (\Throwable $e) {
                        error_log("DepthSaveService::publishDepthUpdate error: " . $e->getMessage());
                    }
                });
            }

        } catch (\Throwable $e) {
            // 记录错误但不抛出异常，避免影响主流程
            error_log("DepthSaveService::asyncSaveRedis error: " . $e->getMessage());
        }
    }

    /**
     * 获取深度数据（简化版：只返回价格和总量）
     * @param string $symbol 交易对符号
     * @param int $marketType 市场类型
     * @param string $side 方向 'bids' 或 'asks'
     * @return array
     */
    public function getDepth(string $symbol, int $marketType, string $side): array
    {
        try {
            // 根据市场类型获取Redis key
            $redisKey = $this->getDepthRedisKey($symbol, $marketType);
            if (empty($redisKey)) {
                return [];
            }

            $key = "{$redisKey}:{$side}";
            $data = $this->redis->hGetAll($key);

            if (empty($data)) {
                return [];
            }

            $result = [];
            foreach ($data as $price => $totalQuantity) {
                $quantity = (float)$totalQuantity;
                if ($quantity > 0) {
                    $result[] = [
                        'price' => (float)$price,
                        'total_quantity' => $quantity
                    ];
                }
            }

            // 排序：bids降序，asks升序
            if ($side === 'bids') {
                usort($result, function ($a, $b) {
                    return $b['price'] <=> $a['price'];
                });
            } else {
                usort($result, function ($a, $b) {
                    return $a['price'] <=> $b['price'];
                });
            }

            return $result;

        } catch (\Throwable $e) {
            error_log("DepthSaveService::getDepth error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 发布深度数据到Redis管道
     * @param string $symbol 交易对符号
     * @param array $bids 买单深度数据 [[价格, 数量], ...]
     * @param array $asks 卖单深度数据 [[价格, 数量], ...]
     * @return bool
     */
    public function publishDepthUpdate(string $symbol, $market_type, array $bids = [], array $asks = []): bool
    {
        try {
            // 获取发布key
            //$publishKey = $this->getDepthSendKey($symbol, $market_type);

            $publishKey = $this->getPublicDepthKey($market_type);

            // 封装发布数据
            $publishData = [
                'e' => 'depthUpdate',
                's' => strtoupper($symbol),
                'market_type' => $market_type,
                'bids' => $bids,
                'asks' => $asks
            ];

            // 发布到Redis管道
            $result = $this->redis->publish($publishKey, json_encode($publishData, JSON_UNESCAPED_UNICODE));

            return $result !== false;

        } catch (\Throwable $e) {
            error_log("DepthSaveService::publishDepthUpdate error: " . $e->getMessage());
            return false;
        }
    }

    public function getPublicDepthKey(int $market_type): string
    {
        return match ($market_type){
            MarketType::CRYPTO->value => MatchEngineDepthSendKey::SPOT_PUBLIC_DEPTH->value,
            MarketType::MARGIN->value => MatchEngineDepthSendKey::MARGIN_PUBLIC_DEPTH->value
        };
    }

    /**
     * 根据市场类型获取深度数据Redis key
     * @param string $symbol 交易对符号
     * @param int $marketType 市场类型
     * @return string
     */
    private function getDepthRedisKey(string $symbol, int $marketType): string
    {
        return match ($marketType) {
            MarketType::CRYPTO->value => MatchEngineDepthKey::getSpotDepthKey(strtoupper($symbol)),
            MarketType::MARGIN->value => MatchEngineDepthKey::getMarginDepthKey(strtoupper($symbol)),
            default => ''
        };
    }

    private function getDepthSendKey(string $symbol, int $marketType): string
    {
        return match ($marketType) {
            MarketType::CRYPTO->value => MatchEngineDepthSendKey::getCryptoSpotDepthSendKey(strtoupper($symbol)),
            MarketType::MARGIN->value => MatchEngineDepthSendKey::getCryptoMarginDepthSendKey(strtoupper($symbol)),
            default => ''
        };
    }

    /**
     * 从数据库获取处理中的订单并组装为深度数据
     * @param int $currencyId 币种ID
     * @param int $marketType 市场类型
     * @return array
     */
    public function getDepthFromDatabase(int $currencyId, mixed $marketType): array
    {
        try {
            // 获取币种信息
            $currency = Currency::query()->find($currencyId);
            if (!$currency) {
                return $this->getEmptyDepthData('UNKNOWN');
            }

            // 查询处理中的订单（pending和partial_filled状态）
            $orders = MatchOrder::query()
                ->where('currency_id', $currencyId)
                ->when($marketType,function($query)use($marketType){
                    if(is_array($marketType)){
                        $query->whereIn('market_type',$marketType);
                    }else{
                        $query->where('market_type', $marketType);
                    }
                })
                ->whereIn('status', [OrderStatus::CREATED->value,OrderStatus::PENDING->value, OrderStatus::PARTIAL_FILLED->value])
                ->orderBy('price')
                ->orderBy('created_at')
                ->get();

            if ($orders->isEmpty()) {
                return $this->getEmptyDepthData($currency->symbol);
            }

            // 按价格和方向分组订单
            $groupedOrders = $this->groupOrdersByPriceAndSide($orders);

            // 组装深度数据
            $depthData = [
                'symbol' => strtoupper($currency->symbol),
                'bids' => $this->buildDepthLevels($groupedOrders['bids'] ?? []),
                'asks' => $this->buildDepthLevels($groupedOrders['asks'] ?? []),
                'timestamp' => time() * 1000 // 毫秒时间戳
            ];

            return $depthData;

        } catch (\Throwable $e) {
            error_log("DepthSaveService::getDepthFromDatabase error: " . $e->getMessage());
            return $this->getEmptyDepthData('ERROR');
        }
    }

    /**
     * 按价格和方向分组订单
     * @param \Hyperf\Database\Model\Collection $orders
     * @return array
     */
    private function groupOrdersByPriceAndSide($orders): array
    {
        $grouped = [
            'bids' => [], // side = 1 (买单)
            'asks' => []  // side = 0 (卖单)
        ];

        foreach ($orders as $order) {
            $price = (string)$order->price;
            $side = $order->side == intval(TradeSide::BUY_INT->value) ? 'bids' : 'asks';

            if (!isset($grouped[$side][$price])) {
                $grouped[$side][$price] = [];
            }

            $grouped[$side][$price][] = $order;
        }

        return $grouped;
    }

    /**
     * 构建深度档位数据
     * @param array $priceGroups [price => [orders]]
     * @return array
     */
    private function buildDepthLevels(array $priceGroups): array
    {
        $levels = [];

        foreach ($priceGroups as $price => $orders) {
            $totalQuantity = 0;
            $orderDetails = [];

            foreach ($orders as $order) {
                // 计算剩余可成交数量
                $remainingQuantity = $order->quantity - $order->fill_quantity;
                
                if ($remainingQuantity > 0) {
                    $totalQuantity += $remainingQuantity;
                    
                    $orderDetails[] = [
                        'order_id' => $order->order_id,
                        'user_id' => strval($order->user_id),
                        'type' => $this->getOrderTypeString($order->order_type),
                        'side' => TradeSide::getSideString($order->side),
                        'time_in_force' => strtolower($order->order_force),
                        'price' => (float)$order->price,
                        'quantity' => (float)$order->quantity,
                        'filled_quantity' => (float)$order->fill_quantity,
                        'status' => OrderStatus::getOrderStatusString($order->status),
                        'timestamp' => $order->created_at->getTimestamp() * 1000,
                        'leverage' => 1.0 // 暂时固定为1.0，如有杠杆字段可调整
                    ];
                }
            }

            // 只有当总数量大于0时才添加档位
            if ($totalQuantity > 0) {
                $levels[] = [
                    'price' => (float)$price,
                    'total_quantity' => $totalQuantity,
                    'orders' => $orderDetails
                ];
            }
        }

        return $levels;
    }

    /**
     * 获取订单类型字符串
     * @param int $orderType
     * @return string
     */
    private function getOrderTypeString(int $orderType): string
    {
        return match ($orderType) {
            intval(OrderType::MARKET_INT->value) => OrderType::MARKET_STRING->value,
            intval(OrderType::LIMIT_INT->value) => OrderType::LIMIT_STRING->value,
            default => 'unknown'
        };
    }

    /**
     * 获取订单状态字符串
     * @param int $status
     * @return string
     */
    private function getStatusString(int $status): string
    {
        return OrderStatus::getOrderStatusString($status);
    }

    /**
     * 获取空的深度数据结构
     * @param string $symbol
     * @return array
     */
    private function getEmptyDepthData(string $symbol): array
    {
        return [
            'symbol' => strtoupper($symbol),
            'bids' => [],
            'asks' => [],
            'timestamp' => time() * 1000
        ];
    }
}