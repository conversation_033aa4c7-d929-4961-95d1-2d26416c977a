<?php

declare(strict_types=1);

/**
 * AbstractEventHandler.php
 * 抽象事件处理器基类
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/1/15
 * Website:algoquant.org
 */

namespace App\MarketData\Service\MatchEngine\Common;

use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use Psr\Log\LoggerInterface;
use Swoole\CryptoExchange;

abstract class AbstractEventHandler
{
    /**
     * 撮合引擎专用日志记录器
     */
    protected MatchEngineLogger $logger;

    /**
     * 注册事件监听器
     */
    abstract public function registerEventListeners(CryptoExchange $exchange): void;

    /**
     * 处理成交事件
     */
    abstract public function handleTradeEvent(array $event): void;

    /**
     * 处理价格更新事件
     */
    abstract public function handlePriceEvent(array $event): void;

    /**
     * 处理订单放置事件
     */
    abstract public function handleOrderPlacedEvent(array $event): void;

    /**
     * 处理订单完全成交事件
     */
    abstract public function handleOrderFilledEvent(array $event): void;

    /**
     * 处理订单部分成交事件
     */
    abstract public function handleOrderPartialFilledEvent(array $event): void;

    /**
     * 处理订单取消事件
     */
    abstract public function handleOrderCanceledEvent(array $event): void;

    /**
     * 处理深度更新事件
     */
    abstract public function handleDepthUpdatedEvent(array $event): void;

    /**
     * 记录信息日志
     */
    protected function logInfo(string $message, array $context = []): void
    {
        if (isset($this->logger)) {
            $this->logger->logProcessStatus($message, $context);
        }
    }

    /**
     * 记录错误日志
     */
    protected function logError(string $message, ?\Throwable $exception = null): void
    {
        if (isset($this->logger)) {
            $this->logger->logError($message, $exception);
        }
    }

    /**
     * 记录警告日志
     */
    protected function logWarning(string $message, array $context = []): void
    {
        if (isset($this->logger)) {
            $this->logger->logWarning($message, $context);
        }
    }

    /**
     * 记录订单日志
     */
    protected function logOrder(string $action, string $symbol, array $orderData): void
    {
        if (isset($this->logger)) {
            $this->logger->logOrder($action, $symbol, $orderData);
        }
    }

    /**
     * 记录交易日志
     */
    protected function logTrade(string $symbol, array $tradeData): void
    {
        if (isset($this->logger)) {
            $this->logger->logTrade($symbol, $tradeData);
        }
    }
} 