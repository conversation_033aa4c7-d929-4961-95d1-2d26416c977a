<?php

declare(strict_types=1);

/**
 * AbstractMatchEngineService.php
 * 抽象撮合引擎服务基类
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/1/15
 * Website:algoquant.org
 */

namespace App\MarketData\Service\MatchEngine\Common;

use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use Psr\Log\LoggerInterface;
use Swoole\CryptoExchange;

abstract class AbstractMatchEngineService
{
    /**
     * 撮合引擎实例
     */
    protected ?CryptoExchange $exchange = null;

    /**
     * 币种数据映射
     */
    protected array $currencies = [];

    /**
     * 市场引擎映射
     */
    protected array $marketEngines = [];

    /**
     * 撮合引擎专用日志记录器
     */
    protected MatchEngineLogger $logger;

    /**
     * 初始化撮合引擎
     */
    abstract public function initialize(): bool;

    /**
     * 创建市场
     */
    abstract public function createMarkets(array $assignedCurrencies): void;

    /**
     * 添加订单
     */
    abstract public function addOrder(string $symbol, array $orderData): int;

    /**
     * 取消订单
     */
    abstract public function cancelOrder(string $symbol, int $orderId): bool;

    /**
     * 获取深度数据
     */
    abstract public function getDepth(string $symbol, int $levels = 20): array;

    /**
     * 获取市场信息
     */
    public function getMarketsInfo(): array
    {
        $info = [];
        foreach ($this->marketEngines as $symbol => $engine) {
            $info[$symbol] = [
                'symbol' => $symbol,
                'currency_data' => $this->currencies[$symbol] ?? null,
            ];
        }
        return $info;
    }

    /**
     * 获取可用市场列表
     */
    public function getAvailableMarkets(): array
    {
        return array_keys($this->marketEngines);
    }

    /**
     * 检查市场是否存在
     */
    public function hasMarket(string $symbol): bool
    {
        return isset($this->marketEngines[$symbol]);
    }

    /**
     * 获取撮合引擎实例
     */
    public function getExchange(): ?CryptoExchange
    {
        return $this->exchange;
    }

    /**
     * 清理资源
     */
    public function cleanup(): void
    {
        if ($this->exchange) {
            foreach ($this->marketEngines as $symbol => $engine) {
                try {
                    $this->exchange->removeMarket($symbol);
                } catch (\Throwable $e) {
                    $this->logError("Error removing market {$symbol}", $e);
                }
            }
        }
        
        $this->marketEngines = [];
        $this->exchange = null;
    }

    /**
     * 记录信息日志
     */
    protected function logInfo(string $message, array $context = []): void
    {
        if (isset($this->logger)) {
            $this->logger->logProcessStatus($message, $context);
        }
    }

    /**
     * 记录错误日志
     */
    protected function logError(string $message, ?\Throwable $exception = null): void
    {
        if (isset($this->logger)) {
            $this->logger->logError($message, $exception);
        }
    }

    /**
     * 记录警告日志
     */
    protected function logWarning(string $message, array $context = []): void
    {
        if (isset($this->logger)) {
            $this->logger->logWarning($message, $context);
        }
    }

    /**
     * 记录调试日志
     */
    protected function logDebug(string $message, array $context = []): void
    {
        if (isset($this->logger)) {
            $this->logger->logDebug($message, $context);
        }
    }
} 