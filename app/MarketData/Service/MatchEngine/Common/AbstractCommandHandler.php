<?php

declare(strict_types=1);

/**
 * AbstractCommandHandler.php
 * 抽象命令处理器基类
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/1/15
 * Website:algoquant.org
 */

namespace App\MarketData\Service\MatchEngine\Common;

use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use Psr\Log\LoggerInterface;

abstract class AbstractCommandHandler
{
    /**
     * 撮合引擎专用日志记录器
     */
    protected MatchEngineLogger $logger;

    /**
     * 撮合引擎服务实例
     */
    protected AbstractMatchEngineService $matchEngineService;

    /**
     * 处理命令
     */
    abstract public function handleCommand(string $type, array $data): void;

    /**
     * 处理添加订单命令
     */
    abstract public function handleAddOrderCommand(array $data): void;

    /**
     * 处理取消订单命令
     */
    abstract public function handleCancelOrderCommand(array $data): void;

    /**
     * 处理获取深度命令
     */
    abstract public function handleGetDepthCommand(array $data): void;

    /**
     * 处理获取市场列表命令
     */
    abstract public function handleGetMarketsCommand(): void;

    /**
     * 验证命令参数
     */
    protected function validateCommandData(array $data, array $requiredFields): bool
    {
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $this->logWarning("Missing required field: {$field}");
                return false;
            }
        }
        return true;
    }

    /**
     * 标准化交易对符号
     */
    protected function normalizeSymbol(string $symbol): string
    {
        return strtoupper(trim($symbol));
    }

    /**
     * 记录信息日志
     */
    protected function logInfo(string $message, array $context = []): void
    {
        if (isset($this->logger)) {
            $this->logger->logProcessStatus($message, $context);
        }
    }

    /**
     * 记录错误日志
     */
    protected function logError(string $message, ?\Throwable $exception = null): void
    {
        if (isset($this->logger)) {
            $this->logger->logError($message, $exception);
        }
    }

    /**
     * 记录警告日志
     */
    protected function logWarning(string $message, array $context = []): void
    {
        if (isset($this->logger)) {
            $this->logger->logWarning($message, $context);
        }
    }
} 