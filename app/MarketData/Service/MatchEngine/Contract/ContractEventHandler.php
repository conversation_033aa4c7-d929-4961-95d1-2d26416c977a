<?php

declare(strict_types=1);

/**
 * ContractEventHandler.php
 * 合约事件处理器类
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/1/15
 * Website:algoquant.org
 */

namespace App\MarketData\Service\MatchEngine\Contract;

use App\Enum\AsyncExecutorKey;
use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketType;
use App\Job\AsyncFunExecutorJob;
use App\Job\MatchOrder\MatchOrderCancel;
use App\Job\MatchOrder\MatchOrderConfirm;
use App\Job\MatchOrder\MatchOrderFilled;
use App\Job\MatchOrder\MatchOrderPartialFilled;
use App\Job\MatchOrder\MatchOrderTrade;
use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use App\MarketData\Service\MatchEngine\Common\AbstractEventHandler;
use Hyperf\Redis\Redis;
use Swoole\CryptoExchange;

class ContractEventHandler extends AbstractEventHandler
{
    private Redis $redis;

    private array $currency = [];

    public function __construct(MatchEngineLogger $logger)
    {
        $this->logger = $logger;
        $this->redis = redis();
    }

    public function setCurrencyIdMap(array $currency): void
    {
        $this->currency = $currency;
    }

    /**
     * 注册事件监听器
     */
    public function registerEventListeners(CryptoExchange $exchange): void
    {
        try {
            // 成交事件
            $exchange->on('trade', function($event) {
                $this->handleTradeEvent($event);
            });

            // 价格更新事件
            $exchange->on('price', function($event) {
                $this->handlePriceEvent($event);
            });

            // 订单放置事件
            $exchange->on('order_placed', function($event) {
                $this->handleOrderPlacedEvent($event);
            });

            // 订单完全成交事件
            $exchange->on('order_filled', function($event) {
                $this->handleOrderFilledEvent($event);
            });

            // 订单部分成交事件
            $exchange->on('order_partial_filled', function($event) {
                $this->handleOrderPartialFilledEvent($event);
            });

            // 订单取消事件
            $exchange->on('order_canceled', function($event) {
                $this->handleOrderCanceledEvent($event);
            });

            // 深度更新事件
            $exchange->on('depth_updated', function($event) {
                $this->handleDepthUpdatedEvent($event);
            });

            $this->logInfo("Contract event listeners registered successfully");

        } catch (\Throwable $e) {
            $this->logError("Failed to register contract event listeners", $e);
        }
    }

    /**
     * 处理成交事件
     */
    public function handleTradeEvent(array $event): void
    {
        try {
            // 使用专用的交易日志
            $this->logTrade($event['symbol'], $event);
            echo "合约引擎[成交事件]: {$event['symbol']} - 价格: {$event['price']}, 数量: {$event['quantity']}, 成交id: {$event['trade_id']}\n";
            
            // 处理内部数据聚合（异步）
            go(function() use ($event) {
                //成交事件异步处理
                $event['currency_id'] = $this->currency[strtoupper($event['symbol'])]['id'] ?? 0;
                $event['market_type'] = MarketType::MARGIN->value;
                pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderTrade($event));

                try {
                    $klineTradeData = [
                        'currency_id' => $this->currency[strtoupper($event['symbol'])]['id'] ?? 0,
                        'market_type' => MarketType::MARGIN->value, // 合约市场
                        'price' => $event['price'],
                        'quantity' => $event['quantity'],
                        'trade_time' => $event['timestamp'],
                        'out_trade' => 0
                    ];
                    $this->redis->publish(TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::MARGIN->value),json_encode($klineTradeData));
                }catch (\RedisException){
                    //重新建立redis 链接
                    $this->redis = redis();
                }catch (\Exception $e){
                    $this->logError("合约引擎内部成交事件发布redis 失败:{$e->getMessage()}");
                }
            });
            
        } catch (\Throwable $e) {
            $this->logError("Error handling contract trade event", $e);
        }
    }

    /**
     * 处理价格更新事件
     */
    public function handlePriceEvent(array $event): void
    {
        try {
            echo "合约撮合引擎事件[价格变化]: {$event['symbol']} - 价格: {$event['price']} ".json_encode($event)."\n";
        } catch (\Throwable $e) {
            $this->logError("Error handling contract price event", $e);
        }
    }

    /**
     * 处理订单放置事件
     */
    public function handleOrderPlacedEvent(array $event): void
    {
        try {
            echo "合约撮合引擎事件[订单确认]: {$event['symbol']} - {$event['side']} - 订单id: {$event['order_id']}, 用户: {$event['user_id']}, 价格: {$event['price']}, 数量: {$event['remaining_quantity']} \n";
            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderConfirm($event));
        } catch (\Throwable $e) {
            $this->logError("Error handling contract order placed event", $e);
        }
    }

    /**
     * 处理订单完全成交事件
     */
    public function handleOrderFilledEvent(array $event): void
    {
        try {
            echo "合约撮合引擎事件[订单完全成交]: {$event['symbol']} - 订单id: {$event['order_id']}, 用户id: {$event['user_id']}, 成交数量: {$event['filled_quantity']}, 平均价格: {$event['avg_price']} ". json_encode($event)."\n";
            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderFilled($event),1);
        } catch (\Throwable $e) {
            $this->logError("Error handling contract order filled event", $e);
        }
    }

    /**
     * 处理订单部分成交事件
     */
    public function handleOrderPartialFilledEvent(array $event): void
    {
        try {
            echo "合约撮合引擎事件[部分成交]: {$event['symbol']} - 订单id: {$event['order_id']}, 用户: {$event['user_id']}, 成交: {$event['filled_quantity']}, 剩余: {$event['remaining_quantity']} ".json_encode($event)."\n";
            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderPartialFilled($event),1);
        } catch (\Throwable $e) {
            $this->logError("Error handling contract order partial filled event", $e);
        }
    }

    /**
     * 处理订单取消事件
     */
    public function handleOrderCanceledEvent(array $event): void
    {
        try {
            $this->logInfo("合约撮合引擎事件[订单取消]: {$event['symbol']} - OrderID: {$event['order_id']}, User: {$event['user_id']}, Reason: {$event['reason']}, Remaining: {$event['remaining_quantity']}");
            echo "合约撮合引擎事件[订单取消]: {$event['symbol']} - OrderID: {$event['order_id']}, User: {$event['user_id']}, Reason: {$event['reason']}, Remaining: {$event['remaining_quantity']}\n";
            pushAsyncJob(AsyncExecutorKey::MATCH_ORDER_QUEUE->value,new MatchOrderCancel($event),1);
        } catch (\Throwable $e) {
            $this->logError("Error handling contract order canceled event", $e);
        }
    }

    /**
     * 处理深度更新事件
     */
    public function handleDepthUpdatedEvent(array $event): void
    {
        try {
            // 异步保存深度数据到Redis
            go(function() use ($event) {
                pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value, new AsyncFunExecutorJob(
                    'App\MarketData\Service\MatchEngine\DepthSaveService',
                    'asyncSaveRedis',
                    [$event['symbol'], MarketType::MARGIN->value, $event]
                ));
            });
        } catch (\Throwable $e) {
            $this->logError("Error handling contract depth updated event", $e);
        }
    }
} 