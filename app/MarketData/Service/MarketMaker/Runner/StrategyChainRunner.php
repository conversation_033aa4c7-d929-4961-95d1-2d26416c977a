<?php

declare(strict_types=1);

/**
 * StrategyChainRunner.php
 * 做市策略链式执行器
 * Author:chen<PERSON><PERSON> (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:2.0
 * Date:2025/1/16
 * Website:algoquant.org
 */

namespace App\MarketData\Service\MarketMaker\Runner;

use App\MarketData\Service\MarketMaker\Contract\StrategyInterface;

/**
 * 做市策略执行类
 */
class StrategyChainRunner
{
    /**
     * 策略列表（直接保存策略实例）
     */
    protected array $strategies = [];

    /**
     * 暂停的策略索引列表
     */
    protected array $pausedIndexes = [];

    /**
     * 已执行过的策略（用于标记首次运行）
     */
    protected array $executed = [];

    /**
     * 当前执行索引
     */
    protected int $currentIndex = 0;

    public function __construct()
    {
        // 空构造函数，支持动态添加策略
    }

    /**
     * 添加策略
     */
    public function addStrategy(StrategyInterface $strategy): void
    {
        // 检查策略是否已存在
        foreach ($this->strategies as $existingStrategy) {
            if ($existingStrategy === $strategy) {
                return; // 策略已存在
            }
        }
        
        // 直接添加到策略数组
        $this->strategies[] = $strategy;
    }

    /**
     * 移除策略
     */
    public function removeStrategy(StrategyInterface $strategy): void
    {
        foreach ($this->strategies as $index => $existingStrategy) {
            if ($existingStrategy === $strategy) {
                // 如果策略正在运行，先完成它
                $strategy->onComplete();
                
                // 从数组中移除
                array_splice($this->strategies, $index, 1);
                
                // 清理相关数据
                $hash = spl_object_hash($strategy);
                unset($this->executed[$hash]);
                
                // 更新暂停索引列表
                $this->updatePausedIndexesAfterRemoval($index);
                
                // 调整当前索引
                if ($this->currentIndex > $index) {
                    $this->currentIndex--;
                } elseif ($this->currentIndex >= count($this->strategies)) {
                    $this->currentIndex = 0;
                }
                
                break;
            }
        }
    }

    /**
     * 暂停策略
     */
    public function pauseStrategy(StrategyInterface $strategy): void
    {
        foreach ($this->strategies as $index => $existingStrategy) {
            if ($existingStrategy === $strategy) {
                $this->pausedIndexes[$index] = true;
                break;
            }
        }
    }

    /**
     * 恢复策略
     */
    public function resumeStrategy(StrategyInterface $strategy): void
    {
        foreach ($this->strategies as $index => $existingStrategy) {
            if ($existingStrategy === $strategy) {
                unset($this->pausedIndexes[$index]);
                break;
            }
        }
    }

    /**
     * 执行所有活跃策略（串行执行）
     */
    public function execute(): void
    {
        if (empty($this->strategies)) {
            return;
        }

        // 遍历所有策略
        for ($i = 0; $i < count($this->strategies); $i++) {
            // 跳过暂停的策略
            if (isset($this->pausedIndexes[$i])) {
                continue;
            }

            $strategy = $this->strategies[$i];
            
            try {
                // 首次运行时调用 onStart
                if ($this->isFirstRun($strategy)) {
                    $strategy->onStart();
                }

                // 执行策略
                $strategy->execute();

                // 检查策略是否完成
                if ($strategy->isComplete()) {
                    $strategy->onComplete();
                    $this->removeStrategy($strategy);
                    $i--; // 调整索引，因为数组长度变了
                }

            } catch (\Throwable $e) {
                echo "[StrategyRunner] Error executing strategy: {$e->getMessage()}\n";
                // 策略执行出错，可以选择移除或继续
            }
        }
    }

    /**
     * 停止所有策略
     */
    public function stopAll(): void
    {
        foreach ($this->strategies as $strategy) {
            try {
                $strategy->onComplete();
            } catch (\Throwable $e) {
                echo "[StrategyRunner] Error stopping strategy: {$e->getMessage()}\n";
            }
        }
        
        $this->strategies = [];
        $this->pausedIndexes = [];
        $this->executed = [];
        $this->currentIndex = 0;
    }

    /**
     * 获取策略数量
     */
    public function getStrategyCount(): int
    {
        return count($this->strategies);
    }

    /**
     * 获取活跃策略数量
     */
    public function getActiveStrategyCount(): int
    {
        $totalCount = count($this->strategies);
        $pausedCount = count($this->pausedIndexes);
        return $totalCount - $pausedCount;
    }

    /**
     * 获取所有策略状态
     */
    public function getStrategiesStatus(): array
    {
        $status = [];
        foreach ($this->strategies as $index => $strategy) {
            $status[$index] = [
                'index' => $index,
                'status' => isset($this->pausedIndexes[$index]) ? 'paused' : 'active',
                'strategy_class' => get_class($strategy),
                'is_complete' => $strategy->isComplete()
            ];
        }
        return $status;
    }

    /**
     * 更新暂停索引列表（移除策略后）
     */
    private function updatePausedIndexesAfterRemoval(int $removedIndex): void
    {
        $newPausedIndexes = [];
        foreach ($this->pausedIndexes as $index => $value) {
            if ($index < $removedIndex) {
                $newPausedIndexes[$index] = $value;
            } elseif ($index > $removedIndex) {
                $newPausedIndexes[$index - 1] = $value;
            }
            // 跳过 $index === $removedIndex 的情况
        }
        $this->pausedIndexes = $newPausedIndexes;
    }

    /**
     * 检查是否首次运行
     */
    protected function isFirstRun(StrategyInterface $strategy): bool
    {
        $key = spl_object_hash($strategy);
        if (!isset($this->executed[$key])) {
            $this->executed[$key] = true;
            return true;
        }
        return false;
    }

    /**
     * 检查是否有活跃策略
     */
    public function hasActiveStrategies(): bool
    {
        return $this->getActiveStrategyCount() > 0;
    }

    /**
     * 获取当前执行索引
     */
    public function getCurrentIndex(): int
    {
        return $this->currentIndex;
    }

    /**
     * 设置当前执行索引
     */
    public function setCurrentIndex(int $index): void
    {
        $this->currentIndex = max(0, min($index, count($this->strategies) - 1));
    }
}