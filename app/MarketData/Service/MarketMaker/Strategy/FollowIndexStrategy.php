<?php

/**
 * FollowIndexStrategy.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/26
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\MarketData\Service\MarketMaker\Strategy;

use App\MarketData\Service\MarketMaker\Contract\StrategyInterface;

/**
 * 价格跟随策略，所有交易对的默认做市策略，价格根据币安的指数价格进行市场波动
 */
class FollowIndexStrategy implements StrategyInterface
{
    protected array $config;
    protected IndexPriceService $indexService;
    protected OrderService $orderService;
    protected DepthService $depthService;
    protected bool $started = false;

    public function __construct(array $config, IndexPriceService $indexService, OrderService $orderService, DepthService $depthService)
    {
        $this->config = $config;
        $this->indexService = $indexService;
        $this->orderService = $orderService;
        $this->depthService = $depthService;
    }

    public function onStart(): void
    {
        echo "[FollowIndex] Strategy started for {$this->config['symbol']}\n";
        $this->started = true;
    }

    public function execute(): void
    {
        $symbol = $this->config['symbol'];
        $indexPrice = $this->indexService->getIndexPrice($symbol);

        $spread = $this->config['spread'] ?? 0.01;
        $volume = $this->config['volume'] ?? 0.05;

        $buyPrice = round($indexPrice * (1 - $spread), 4);
        $sellPrice = round($indexPrice * (1 + $spread), 4);

        // 挂单前清理旧单（可选实现）
        $this->orderService->cancelBotOrders($symbol);

        // 挂买单和卖单
        $this->orderService->placeLimitBuy($symbol, $buyPrice, $volume);
        $this->orderService->placeLimitSell($symbol, $sellPrice, $volume);

        echo "[FollowIndex] Placed buy at $buyPrice, sell at $sellPrice (index: $indexPrice)\n";
    }

    public function onComplete(): void
    {
        echo "[FollowIndex] Completed (looping strategy, does not terminate).\n";
    }

    public function isComplete(): bool
    {
        return false;
    }

    public function getInterval(): int
    {
        return $this->config['interval'] ?? 3;
    }
}
