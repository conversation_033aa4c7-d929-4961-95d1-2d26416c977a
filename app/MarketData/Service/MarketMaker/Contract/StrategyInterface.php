<?php

namespace App\MarketData\Service\MarketMaker\Contract;

/**
 * 定义做市基础策略接口
 */
interface StrategyInterface
{
    /**
     * 当策略启动时执行（初始化行为）
     */
    public function onStart(): void;

    /**
     * 每个周期执行一次（核心行为逻辑）
     */
    public function execute(): void;

    /**
     * 当策略结束时调用（清理行为）
     */
    public function onComplete(): void;

    /**
     * 当前策略是否完成（用于策略链中切换）
     */
    public function isComplete(): bool;

    /**
     * 执行周期（秒）
     */
    public function getInterval(): int;
}
