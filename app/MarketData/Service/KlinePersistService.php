<?php

namespace App\MarketData\Service;

use App\Enum\MarketData\KlineIndex;
use Hyperf\Di\Annotation\Inject;

use Elasticsearch\Client;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;

/**
 * K线数据持久化服务
 */
class KlinePersistService
{

    
    #[Inject]
    protected LoggerFactory $loggerFactory;
    
    private ?Client $elasticsearch = null;
    private ?LoggerInterface $logger = null;
    
    /**
     * 保存K线数据到ES
     */
    public function saveKlineToES(array $kline): bool
    {
        try {
            $this->initializeClients();
            
            $indexName = KlineIndex::getIndexName($kline['symbol'], $kline['market_type']);
            $docId = KlineIndex::generateKlineId($kline['period'], $kline['open_time']);
            
            $this->elasticsearch->index([
                'index' => $indexName,
                'id' => $docId,
                'body' => [
                    'period' => $kline['period'],
                    'open_time' => $kline['open_time'],
                    'close_time' => $kline['close_time'],
                    'ohlcv' => $kline['ohlcv']
                ]
            ]);
            
            return true;
            
        } catch (\Throwable $e) {
            $this->initializeClients();
            $this->logger->error("Failed to save kline to ES: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量保存K线数据到ES
     */
    public function batchSaveKlinesToES(array $klines): bool
    {
        if (empty($klines)) {
            return true;
        }
        
        try {
            $this->initializeClients();
            
            $body = [];
            foreach ($klines as $kline) {
                $indexName = KlineIndex::getIndexName($kline['symbol'], $kline['market_type']);
                $docId = KlineIndex::generateKlineId($kline['period'], $kline['open_time']);
                
                $body[] = [
                    'index' => [
                        '_index' => $indexName,
                        '_id' => $docId
                    ]
                ];
                
                $body[] = [
                    'period' => $kline['period'],
                    'open_time' => $kline['open_time'],
                    'close_time' => $kline['close_time'],
                    'ohlcv' => $kline['ohlcv']
                ];
            }
            
            if (!empty($body)) {
                $response = $this->elasticsearch->bulk(['body' => $body]);
                
                // 检查是否有错误
                if (isset($response['errors']) && $response['errors']) {
                    $this->logger->error("Bulk save klines has errors");
                    return false;
                }
            }
            
            return true;
            
        } catch (\Throwable $e) {
            $this->initializeClients();
            $this->logger->error("Failed to batch save klines to ES: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 初始化客户端
     */
    private function initializeClients(): void
    {
        if ($this->elasticsearch === null) {
            $this->elasticsearch = elasticsearch();
        }

        if ($this->logger === null) {
            $this->logger = $this->loggerFactory->get('kline-persist');
        }
    }
}
