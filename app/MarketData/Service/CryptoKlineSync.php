<?php

namespace App\MarketData\Service;

use App\Model\Currency\Currency;
use App\Enum\MarketData\KlineIndex;
use Hyperf\Elasticsearch\ClientBuilderFactory;
use Elasticsearch\Client;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;
use Hyperf\Coroutine\Parallel;

class CryptoKlineSync
{
    private Client $elasticsearch;
    private LoggerInterface $logger;

    private const PERIODS = [
        '1m' => ['interval' => '1m', 'days' => 7],
        '3m' => ['interval' => '3m', 'days' => 21],
        '5m' => ['interval' => '5m', 'days' => 35],
        '15m' => ['interval' => '15m', 'days' => 90],
        '30m' => ['interval' => '30m', 'days' => 180],
        '1h' => ['interval' => '1h', 'days' => 360],
        '2h' => ['interval' => '2h', 'days' => 720],
        '4h' => ['interval' => '4h', 'days' => 720],
        '1d' => ['interval' => '1d', 'days' => null],
        '1w' => ['interval' => '1w', 'days' => null],
        '1M' => ['interval' => '1M', 'days' => null]
    ];

    private const OHLCV_STRUCTURE = [
        0 => 'open',
        1 => 'high',
        2 => 'low',
        3 => 'close',
        4 => 'volume',
        5 => 'quote_volume',
        6 => 'trades_count'
    ];

    private const BINANCE_SPOT_API = 'https://api.binance.com';
    private const BINANCE_FUTURES_API = 'https://fapi.binance.com';
    private const BATCH_SIZE = 1000;

    // 速率限制控制
    private static int $lastRateLimitTime = 0;
    private static bool $isRateLimited = false;

    public function __construct(LoggerFactory $loggerFactory, ClientBuilderFactory $clientBuilderFactory)
    {
        $this->logger = $loggerFactory->get('kline-sync');
        $this->elasticsearch = elasticsearch();
    }

    public function syncAllKlines(): void
    {
        $currencies = $this->getCurrencies();
        $batches = array_chunk($currencies->all(), 10);

        foreach ($batches as $batchIndex => $batch) {
            $this->syncCurrencyBatch($batch);

            // 批次间稍微等待，避免过于频繁的请求
            if ($batchIndex < count($batches) - 1) {
                sleep(1);
            }
        }
    }

    private function syncCurrencyBatch(array $currencies): void
    {
        $parallel = new Parallel();

        foreach ($currencies as $currency) {
            $parallel->add(function () use ($currency) {
                try {
                    // $currency 是 Currency 模型对象
                    $this->syncCurrencyKlines($currency);
                } catch (\Throwable $e) {
                    $symbol = is_object($currency) ? $currency->symbol : ($currency['symbol'] ?? 'unknown');
                    $this->logger->error("Failed to sync currency {$symbol}: " . $e->getMessage());
                }
            });
        }

        try {
            $parallel->wait();
        } catch (\Throwable $e) {
            $this->logger->error("Batch sync failed: " . $e->getMessage());
        }
    }

    public function syncCurrencyKlines(Currency $currency): void
    {
        foreach (self::PERIODS as $period => $config) {
            try {
                if ($currency->is_spotTrade) {
                    $this->syncKlineDataWithContinuity($currency, 1, $period, $config);
                }

                if ($currency->is_marginTrade) {
                    $this->syncKlineDataWithContinuity($currency, 5, $period, $config);
                }
            } catch (\Throwable $e) {
                $this->logger->error("Failed to sync {$period} kline for {$currency->symbol}: " . $e->getMessage());
            }
        }
    }

    public function syncIncrementalKlines(): void
    {
        $currencies = $this->getCurrencies();

        foreach ($currencies as $currency) {
            foreach (self::PERIODS as $period => $config) {
                try {
                    if ($currency->is_spotTrade) {
                        $this->syncIncrementalData($currency, 1, $period, $config);
                    }

                    if ($currency->is_marginTrade) {
                        $this->syncIncrementalData($currency, 5, $period, $config);
                    }
                } catch (\Throwable $e) {
                    $this->logger->error("Failed to sync incremental {$period} kline for {$currency->symbol}: " . $e->getMessage());
                }
            }
        }
    }



    private function fetchKlineData(string $symbol, string $interval, int $startTime, int $endTime, int $marketType): array
    {
        // 检查全局速率限制
        $this->checkGlobalRateLimit();

        $params = [
            'symbol' => $symbol,
            'interval' => $interval,
            'startTime' => $startTime,
            'endTime' => $endTime,
            'limit' => self::BATCH_SIZE
        ];

        if ($marketType === 1) {
            $url = self::BINANCE_SPOT_API . '/api/v3/klines?' . http_build_query($params);
        } else {
            $url = self::BINANCE_FUTURES_API . '/fapi/v1/klines?' . http_build_query($params);
        }

        $httpClient = client([
            'timeout' => 30,
            'connect_timeout' => 10,
            'verify' => false,
            'http_errors' => false
        ]);

        $response = $httpClient->get($url);
        $statusCode = $response->getStatusCode();

        // 处理速率限制
        if ($statusCode === 429) {
            $this->handleRateLimit($symbol, 429);
            return [];
        } elseif ($statusCode === 418 || $statusCode === 451) {
            // 其他速率限制状态码
            $this->handleRateLimit($symbol, $statusCode);
            return [];
        } elseif ($statusCode !== 200) {
            $this->logger->error("API request failed with status {$statusCode} for {$symbol}");
            return [];
        }

        $content = $response->getBody()->getContents();
        $data = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->error("Invalid JSON response for {$symbol}: " . json_last_error_msg());
            return [];
        }

        if (!is_array($data)) {
            $this->logger->error("Unexpected response format for {$symbol}");
            return [];
        }

        return $data;
    }

    private function saveToElasticsearch(Currency $currency, int $marketType, string $period, array $klineData): void
    {
        $body = [];
        $indexName = KlineIndex::getIndexName($currency->symbol, $marketType);

        foreach ($klineData as $kline) {
            if (!is_array($kline) || count($kline) < 9) {
                continue;
            }

            $doc = [
                'period' => $period,
                'open_time' => (int)$kline[0],
                'close_time' => (int)$kline[6],
                'ohlcv' => [
                    (float)$kline[1],  // open
                    (float)$kline[2],  // high
                    (float)$kline[3],  // low
                    (float)$kline[4],  // close
                    (float)$kline[5],  // volume
                    (float)$kline[7],  // quote_volume
                    (int)$kline[8]     // trades_count
                ]
            ];

            $docId = "{$currency->symbol}-{$marketType}-{$period}-{$kline[0]}";

            $body[] = [
                'update' => [
                    '_index' => $indexName,
                    '_id' => $docId
                ]
            ];

            $body[] = [
                'doc' => $doc,
                'doc_as_upsert' => true
            ];
        }

        if (!empty($body)) {
            try {
                $this->elasticsearch->bulk(['body' => $body]);
            } catch (\Throwable $e) {
                $this->logger->error("Failed to bulk insert data for {$currency->symbol}: " . $e->getMessage());
                throw $e;
            }
        }
    }



    private function calculateStartTime(?int $days): int
    {
        if ($days === null) {
            return strtotime('2017-01-01') * 1000;
        }
        
        return (time() - ($days * 24 * 3600)) * 1000;
    }

    private function getCurrencies()
    {
        return Currency::where('status', 1)
            ->where(function($query) {
                $query->where('is_spotTrade', 1)
                      ->orWhere('is_marginTrade', 1);
            })
            ->get();
    }

    public function syncSpecificPeriod(string $symbol, string $period, ?int $marketType = null): void
    {
        if (!isset(self::PERIODS[$period])) {
            throw new \InvalidArgumentException("Invalid period: {$period}");
        }

        $currency = Currency::where('symbol', $symbol)
            ->where('status', 1)
            ->first();

        if (!$currency) {
            throw new \RuntimeException("Currency not found: {$symbol}");
        }

        if ($marketType) {
            if ($marketType === 1 && !$currency->is_spotTrade) {
                throw new \RuntimeException("Currency {$symbol} does not support spot trading");
            }
            if ($marketType === 5 && !$currency->is_marginTrade) {
                throw new \RuntimeException("Currency {$symbol} does not support margin trading");
            }
            $this->syncKlineDataWithContinuity($currency, $marketType, $period, self::PERIODS[$period]);
        } else {
            if ($currency->is_spotTrade) {
                $this->syncKlineDataWithContinuity($currency, 1, $period, self::PERIODS[$period]);
            }
            if ($currency->is_marginTrade) {
                $this->syncKlineDataWithContinuity($currency, 5, $period, self::PERIODS[$period]);
            }
        }
    }

    public function getKlineData(string $symbol, int $marketType, string $period, int $startTime, int $endTime, int $size = 1000): array
    {
        $indexName = KlineIndex::getIndexName($symbol, $marketType);

        $params = [
            'index' => $indexName,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => [
                            ['term' => ['period' => $period]],
                            ['range' => [
                                'open_time' => [
                                    'gte' => $startTime,
                                    'lte' => $endTime
                                ]
                            ]]
                        ]
                    ]
                ],
                'sort' => [
                    ['open_time' => ['order' => 'asc']]
                ],
                'size' => $size
            ]
        ];

        $response = $this->elasticsearch->search($params);

        return array_map(function($hit) {
            $source = $hit['_source'];
            return [
                'period' => $source['period'],
                'open_time' => $source['open_time'],
                'close_time' => $source['close_time'],
                'open' => $source['ohlcv'][0],
                'high' => $source['ohlcv'][1],
                'low' => $source['ohlcv'][2],
                'close' => $source['ohlcv'][3],
                'volume' => $source['ohlcv'][4],
                'quote_volume' => $source['ohlcv'][5],
                'trades_count' => $source['ohlcv'][6]
            ];
        }, $response['hits']['hits']);
    }

    public function createIndexTemplate(): void
    {
        $template = [
            'index_patterns' => ['kline-*'],
            'template' => [
                'mappings' => [
                    'properties' => [
                        'period' => ['type' => 'keyword'],
                        'open_time' => [
                            'type' => 'long'  // 使用 long 类型存储毫秒时间戳
                        ],
                        'close_time' => [
                            'type' => 'long'  // 使用 long 类型存储毫秒时间戳
                        ],
                        'ohlcv' => [
                            'type' => 'float',
                            'index' => false
                        ]
                    ]
                ],
                'settings' => [
                    'number_of_shards' => 1,
                    'number_of_replicas' => 0,
                    'refresh_interval' => '30s'
                ]
            ]
        ];

        try {
            $this->elasticsearch->indices()->putIndexTemplate([
                'name' => 'kline-template',
                'body' => $template
            ]);
        } catch (\Throwable $e) {
            $this->logger->error("Failed to create index template: " . $e->getMessage());
            throw $e;
        }
    }

    private function syncKlineDataWithContinuity(Currency $currency, int $marketType, string $period, array $config): void
    {
        $indexName = KlineIndex::getIndexName($currency->symbol, $marketType);

        // 1. 检查并创建索引
        $this->ensureIndexExists($indexName);

        // 2. 检查ES中是否已有该币种的数据
        $hasExistingData = $this->hasExistingData($indexName, $period);

        if ($hasExistingData) {
            // ES中有数据：只执行连续性检查和最新数据补全
            // 检查历史数据连续性并补全缺失
            $this->checkAndFillHistoricalGaps($currency, $marketType, $period);

            // 补全最新数据到当前时间
            $this->fillLatestData($currency, $marketType, $period);
        } else {
            // ES中没有数据：执行配置时间段的全量同步

            $startTime = $this->calculateStartTime($config['days']);
            $endTime = time() * 1000;

            $this->directSyncKlineDataRange($currency, $marketType, $period, $startTime, $endTime);
        }
    }

    private function syncIncrementalData(Currency $currency, int $marketType, string $period, array $config): void
    {
        $startTime = (time() - 3600) * 1000;
        $endTime = time() * 1000;

        $this->syncKlineDataRange($currency, $marketType, $period, $startTime, $endTime);
    }

    private function syncKlineDataRange(Currency $currency, int $marketType, string $period, int $startTime, int $endTime): void
    {
        $symbol = $currency->symbol;
        $interval = self::PERIODS[$period]['interval'];

        $totalSynced = 0;
        $currentStartTime = $startTime;

        while ($currentStartTime < $endTime) {
            $klineData = $this->fetchKlineData($symbol, $interval, $currentStartTime, $endTime, $marketType);

            if (empty($klineData)) {
                break;
            }

            $this->saveToElasticsearch($currency, $marketType, $period, $klineData);

            $totalSynced += count($klineData);
            $lastKline = end($klineData);

            if (is_array($lastKline) && isset($lastKline[6])) {
                $currentStartTime = $lastKline[6] + 1;
            } else {
                $this->logger->error("Invalid kline data format for {$currency->symbol}");
                break;
            }

            usleep(200000);
        }
    }

    private function getLastKlineTimestamp(string $symbol, int $marketType, string $period): ?int
    {
        $indexName = KlineIndex::getIndexName($symbol, $marketType);

        try {
            // 先检查索引是否存在
            if (!$this->elasticsearch->indices()->exists(['index' => $indexName])) {
                return null;
            }

            $params = [
                'index' => $indexName,
                'body' => [
                    'query' => [
                        'term' => ['period' => $period]
                    ],
                    'sort' => [
                        ['open_time' => ['order' => 'desc']]
                    ],
                    'size' => 1
                ]
            ];

            $response = $this->elasticsearch->search($params);

            if (!empty($response['hits']['hits'])) {
                return $response['hits']['hits'][0]['_source']['open_time'];
            }
        } catch (\Throwable $e) {
            $this->logger->warning("Failed to get last timestamp for {$symbol}-{$marketType}-{$period}: " . $e->getMessage());
        }

        return null;
    }

    private function getPeriodMilliseconds(string $period): int
    {
        $periodMap = [
            '1m' => 60 * 1000,
            '3m' => 3 * 60 * 1000,
            '5m' => 5 * 60 * 1000,
            '15m' => 15 * 60 * 1000,
            '30m' => 30 * 60 * 1000,
            '1h' => 60 * 60 * 1000,
            '2h' => 2 * 60 * 60 * 1000,
            '4h' => 4 * 60 * 60 * 1000,
            '1d' => 24 * 60 * 60 * 1000,
            '1w' => 7 * 24 * 60 * 60 * 1000,
            '1M' => 30 * 24 * 60 * 60 * 1000  // 近似值，实际月份天数不同
        ];

        return $periodMap[$period] ?? 60 * 1000;
    }

    public function checkDataContinuity(string $symbol, int $marketType, string $period, int $days = 7): array
    {
        $endTime = time() * 1000;
        $startTime = $endTime - ($days * 24 * 60 * 60 * 1000);
        $periodMs = $this->getPeriodMilliseconds($period);

        $klineData = $this->getKlineData($symbol, $marketType, $period, $startTime, $endTime, 10000);

        $gaps = [];
        $expectedTime = $startTime;

        foreach ($klineData as $kline) {
            if ($kline['open_time'] > $expectedTime) {
                $gaps[] = [
                    'start' => $expectedTime,
                    'end' => $kline['open_time'] - $periodMs,
                    'missing_count' => ($kline['open_time'] - $expectedTime) / $periodMs
                ];
            }
            $expectedTime = $kline['open_time'] + $periodMs;
        }

        return [
            'symbol' => $symbol,
            'market_type' => $marketType,
            'period' => $period,
            'total_gaps' => count($gaps),
            'gaps' => $gaps
        ];
    }

    public function fillDataGaps(string $symbol, int $marketType, string $period): void
    {
        $gaps = $this->checkDataContinuity($symbol, $marketType, $period);

        if (empty($gaps['gaps'])) {
            return;
        }

        $currency = Currency::where('symbol', $symbol)->first();
        if (!$currency) {
            throw new \RuntimeException("Currency not found: {$symbol}");
        }

        foreach ($gaps['gaps'] as $gap) {
            $this->syncKlineDataRange($currency, $marketType, $period, $gap['start'], $gap['end'] + $this->getPeriodMilliseconds($period));
        }
    }

    public function checkAndFillRecentPeriods(string $symbol, int $marketType, string $period, int $periodCount = 2): void
    {
        $periodMs = $this->getPeriodMilliseconds($period);
        $currentTime = time() * 1000;

        $endTime = $currentTime;
        $startTime = $currentTime - ($periodCount * $periodMs);

        $klineData = $this->getKlineData($symbol, $marketType, $period, $startTime, $endTime, $periodCount + 1);

        $gaps = [];
        $expectedTime = $startTime;

        foreach ($klineData as $kline) {
            if ($kline['open_time'] > $expectedTime) {
                $gaps[] = [
                    'start' => $expectedTime,
                    'end' => $kline['open_time'] - $periodMs,
                ];
            }
            $expectedTime = $kline['open_time'] + $periodMs;
        }

        if ($expectedTime < $endTime) {
            $gaps[] = [
                'start' => $expectedTime,
                'end' => $endTime - $periodMs,
            ];
        }

        if (!empty($gaps)) {
            $currency = Currency::where('symbol', $symbol)->first();
            if (!$currency) {
                return;
            }

            foreach ($gaps as $gap) {
                $this->syncKlineDataRange($currency, $marketType, $period, $gap['start'], $gap['end'] + $periodMs);
            }
        }
    }

    public function recreateIndex(string $symbol, int $marketType): void
    {
        $indexName = KlineIndex::getIndexName($symbol, $marketType);

        try {
            // 删除现有索引
            if ($this->elasticsearch->indices()->exists(['index' => $indexName])) {
                $this->elasticsearch->indices()->delete(['index' => $indexName]);
            }

            // 创建新索引（会自动应用模板）
            $this->elasticsearch->indices()->create([
                'index' => $indexName,
                'body' => [
                    'mappings' => [
                        'properties' => [
                            'period' => ['type' => 'keyword'],
                            'open_time' => ['type' => 'long'],
                            'close_time' => ['type' => 'long'],
                            'ohlcv' => ['type' => 'float', 'index' => false]
                        ]
                    ],
                    'settings' => [
                        'number_of_shards' => 1,
                        'number_of_replicas' => 0,
                        'refresh_interval' => '30s'
                    ]
                ]
            ]);
        } catch (\Throwable $e) {
            $this->logger->error("Failed to recreate index {$indexName}: " . $e->getMessage());
            throw $e;
        }
    }

    public function recreateAllIndices(): void
    {
        $currencies = $this->getCurrencies();

        foreach ($currencies as $currency) {
            if ($currency->is_spotTrade) {
                $this->recreateIndex($currency->symbol, 1);
            }

            if ($currency->is_marginTrade) {
                $this->recreateIndex($currency->symbol, 5);
            }
        }
    }

    private function hasExistingData(string $indexName, string $period): bool
    {
        try {
            // 检查索引是否存在
            if (!$this->elasticsearch->indices()->exists(['index' => $indexName])) {
                return false;
            }

            // 检查是否有该周期的数据
            $params = [
                'index' => $indexName,
                'body' => [
                    'query' => ['term' => ['period' => $period]],
                    'size' => 0
                ]
            ];

            $response = $this->elasticsearch->search($params);

            return isset($response['hits']['total']['value']) && $response['hits']['total']['value'] > 0;
        } catch (\Throwable $e) {
            $this->logger->warning("Failed to check existing data for {$indexName}-{$period}: " . $e->getMessage());
            return false;
        }
    }

    private function ensureIndexExists(string $indexName): void
    {
        try {
            // 先检查模板是否存在，不存在则创建
            $this->ensureTemplateExists();

            // 检查索引是否存在
            if (!$this->elasticsearch->indices()->exists(['index' => $indexName])) {
                $this->elasticsearch->indices()->create([
                    'index' => $indexName,
                    'body' => [
                        'mappings' => [
                            'properties' => [
                                'period' => ['type' => 'keyword'],
                                'open_time' => ['type' => 'long'],
                                'close_time' => ['type' => 'long'],
                                'ohlcv' => ['type' => 'float', 'index' => false]
                            ]
                        ],
                        'settings' => [
                            'number_of_shards' => 1,
                            'number_of_replicas' => 0,
                            'refresh_interval' => '30s'
                        ]
                    ]
                ]);
            }
        } catch (\Throwable $e) {
            $this->logger->error("Failed to ensure index exists {$indexName}: " . $e->getMessage());
        }
    }

    private function ensureTemplateExists(): void
    {
        static $templateChecked = false;

        if ($templateChecked) {
            return;
        }

        try {
            // 检查模板是否存在
            $templateExists = $this->elasticsearch->indices()->existsIndexTemplate(['name' => 'kline-template']);

            if (!$templateExists) {
                $this->createIndexTemplate();
            }

            $templateChecked = true;
        } catch (\Throwable $e) {
            $this->logger->warning("Failed to check template existence: " . $e->getMessage());
            // 如果检查失败，尝试创建模板
            try {
                $this->createIndexTemplate();
                $templateChecked = true;
            } catch (\Throwable $createError) {
                $this->logger->error("Failed to create template: " . $createError->getMessage());
            }
        }
    }

    private function checkAndFillHistoricalGaps(Currency $currency, int $marketType, string $period): void
    {
        $indexName = KlineIndex::getIndexName($currency->symbol, $marketType);
        $periodMs = $this->getPeriodMilliseconds($period);

        try {
            // 获取现有数据的时间范围
            $existingData = $this->getExistingDataRange($indexName, $period);

            if (empty($existingData)) {
                return;
            }

            $firstTime = $existingData['first_time'];
            $lastTime = $existingData['last_time'];

            // 检查中间的数据缺失
            $this->fillMiddleGaps($currency, $marketType, $period, $firstTime, $lastTime, $periodMs);

        } catch (\Throwable $e) {
            $this->logger->error("Failed to check historical gaps for {$currency->symbol}-{$marketType}-{$period}: " . $e->getMessage());
        }
    }

    private function fillLatestData(Currency $currency, int $marketType, string $period): void
    {
        $indexName = KlineIndex::getIndexName($currency->symbol, $marketType);
        $periodMs = $this->getPeriodMilliseconds($period);
        $currentTime = time() * 1000;

        try {
            // 获取最新数据时间
            $lastTime = $this->getLastDataTime($indexName, $period);

            if ($lastTime) {
                $expectedNextTime = $lastTime + $periodMs;
                if ($expectedNextTime < $currentTime) {
                    $this->directSyncKlineDataRange($currency, $marketType, $period, $expectedNextTime, $currentTime);
                }
            }
        } catch (\Throwable $e) {
            $this->logger->error("Failed to fill latest data for {$currency->symbol}-{$marketType}-{$period}: " . $e->getMessage());
        }
    }

    private function getExistingDataRange(string $indexName, string $period): array
    {
        try {
            $params = [
                'index' => $indexName,
                'body' => [
                    'query' => ['term' => ['period' => $period]],
                    'aggs' => [
                        'time_range' => [
                            'stats' => ['field' => 'open_time']
                        ]
                    ],
                    'size' => 0
                ]
            ];

            $response = $this->elasticsearch->search($params);

            if (isset($response['aggregations']['time_range']['count']) && $response['aggregations']['time_range']['count'] > 0) {
                return [
                    'first_time' => (int)$response['aggregations']['time_range']['min'],
                    'last_time' => (int)$response['aggregations']['time_range']['max'],
                    'count' => $response['aggregations']['time_range']['count']
                ];
            }
        } catch (\Throwable $e) {
            $this->logger->warning("Failed to get existing data range: " . $e->getMessage());
        }

        return [];
    }

    private function getLastDataTime(string $indexName, string $period): ?int
    {
        try {
            $params = [
                'index' => $indexName,
                'body' => [
                    'query' => ['term' => ['period' => $period]],
                    'sort' => [['open_time' => ['order' => 'desc']]],
                    'size' => 1,
                    '_source' => ['open_time']
                ]
            ];

            $response = $this->elasticsearch->search($params);

            if (!empty($response['hits']['hits'])) {
                return $response['hits']['hits'][0]['_source']['open_time'];
            }
        } catch (\Throwable $e) {
            $this->logger->warning("Failed to get last data time: " . $e->getMessage());
        }

        return null;
    }

    private function fillMiddleGaps(Currency $currency, int $marketType, string $period, int $startTime, int $endTime, int $periodMs): void
    {
        $indexName = KlineIndex::getIndexName($currency->symbol, $marketType);
        $batchSize = 1000;
        $currentTime = $startTime;

        while ($currentTime < $endTime) {
            $batchEndTime = min($currentTime + ($batchSize * $periodMs), $endTime);

            try {
                // 检查这个时间段内的数据
                $params = [
                    'index' => $indexName,
                    'body' => [
                        'query' => [
                            'bool' => [
                                'must' => [
                                    ['term' => ['period' => $period]],
                                    ['range' => [
                                        'open_time' => [
                                            'gte' => $currentTime,
                                            'lt' => $batchEndTime
                                        ]
                                    ]]
                                ]
                            ]
                        ],
                        'sort' => [['open_time' => ['order' => 'asc']]],
                        'size' => $batchSize,
                        '_source' => ['open_time']
                    ]
                ];

                $response = $this->elasticsearch->search($params);
                $existingTimes = array_column($response['hits']['hits'], '_source');
                $existingTimes = array_column($existingTimes, 'open_time');

                // 找出缺失的时间段
                $expectedTime = $currentTime;
                $gaps = [];

                foreach ($existingTimes as $actualTime) {
                    if ($expectedTime < $actualTime) {
                        $gaps[] = ['start' => $expectedTime, 'end' => $actualTime - $periodMs];
                    }
                    $expectedTime = $actualTime + $periodMs;
                }

                // 补全缺失的数据
                foreach ($gaps as $gap) {
                    if ($gap['end'] >= $gap['start']) {
                        // 避免递归调用，直接同步数据
                        $this->directSyncKlineDataRange($currency, $marketType, $period, $gap['start'], $gap['end'] + $periodMs);
                    }
                }

            } catch (\Throwable $e) {
                $this->logger->error("Failed to check middle gaps: " . $e->getMessage());
            }

            $currentTime = $batchEndTime;
        }
    }

    private function directSyncKlineDataRange(Currency $currency, int $marketType, string $period, int $startTime, int $endTime): void
    {
        $symbol = $currency->symbol;
        $interval = self::PERIODS[$period]['interval'];

        $totalSynced = 0;
        $currentStartTime = $startTime;

        while ($currentStartTime < $endTime) {
            $klineData = $this->fetchKlineData($symbol, $interval, $currentStartTime, $endTime, $marketType);

            if (empty($klineData)) {
                break;
            }

            $this->saveToElasticsearch($currency, $marketType, $period, $klineData);

            $totalSynced += count($klineData);
            $lastKline = end($klineData);

            if (is_array($lastKline) && isset($lastKline[6])) {
                $currentStartTime = $lastKline[6] + 1;
            } else {
                break;
            }

            // 请求间隔，避免过于频繁
            usleep(200000); // 200ms
        }

        // 移除成功同步的日志
    }

    private function checkGlobalRateLimit(): void
    {
        if (self::$isRateLimited) {
            $waitTime = self::$lastRateLimitTime + 30 - time();
            if ($waitTime > 0) {
                sleep($waitTime);
            } else {
                self::$isRateLimited = false;
            }
        }
    }

    private function handleRateLimit(string $symbol, int $statusCode): void
    {
        if ($statusCode === 429) {
            // 429错误：所有请求都要等待30秒
            self::$isRateLimited = true;
            self::$lastRateLimitTime = time();
            $this->logger->error("Rate limit 429 hit for {$symbol}, all requests will wait 30 seconds");

            // 立即等待30秒
            sleep(30);
            self::$isRateLimited = false;
        } else {
            // 其他速率限制：等待2秒
            sleep(2);
        }
    }

    private function waitForRateLimit(int $seconds): void
    {
        if ($seconds > 0) {
            sleep($seconds);
        }
    }
}
