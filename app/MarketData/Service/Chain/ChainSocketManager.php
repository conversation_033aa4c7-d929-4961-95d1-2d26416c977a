<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 链式WebSocket订阅管理器
 * 
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\MarketData\Service\Chain;

use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

/**
 * 链式WebSocket订阅管理器
 * 用于管理WebSocket客户端的币种订阅关系
 */
class ChainSocketManager
{
    /**
     * 用户订阅记录Key前缀
     */
    private const USER_SUBSCRIPTIONS_KEY = 'chain_socket:user_subscriptions';

    /**
     * 币种订阅者列表Key前缀
     */
    private const SYMBOL_SUBSCRIBERS_KEY = 'chain_socket:symbol_subscribers:';

    #[Inject]
    private Redis $redis;

    #[Inject]
    private LoggerInterface $logger;

    /**
     * 用户订阅币种
     * 如果用户已经订阅了其他币种，会自动取消之前的订阅
     *
     * @param int $userId 用户ID
     * @param string $symbol 币种代码
     * @return bool 是否订阅成功
     */
    public function subscribe(int $userId, string $symbol): bool
    {
        try {
            // 获取用户当前订阅的币种
            $currentSymbol = $this->getUserSubscription($userId);
            
            // 如果用户已经订阅了相同的币种，直接返回成功
            if ($currentSymbol === $symbol) {
                return true;
            }
            
            // 开始事务
            $this->redis->multi();
            
            // 如果用户之前订阅了其他币种，先取消之前的订阅
            if ($currentSymbol) {
                $this->redis->sRem(self::SYMBOL_SUBSCRIBERS_KEY . $currentSymbol, $userId);
            }
            
            // 设置用户新的订阅
            $this->redis->hSet(self::USER_SUBSCRIPTIONS_KEY, (string)$userId, $symbol);
            
            // 将用户添加到新币种的订阅者列表
            $this->redis->sAdd(self::SYMBOL_SUBSCRIBERS_KEY . $symbol, $userId);
            
            // 提交事务
            $this->redis->exec();
            
            $this->logger->info('用户订阅成功', [
                'user_id' => $userId,
                'symbol' => $symbol,
                'previous_symbol' => $currentSymbol
            ]);
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('用户订阅失败', [
                'user_id' => $userId,
                'symbol' => $symbol,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 取消用户订阅
     *
     * @param int $userId 用户ID
     * @return bool 是否取消成功
     */
    public function unsubscribe(int $userId): bool
    {
        try {
            // 获取用户当前订阅的币种
            $currentSymbol = $this->getUserSubscription($userId);
            
            if (!$currentSymbol) {
                return true; // 用户没有订阅任何币种
            }
            
            // 开始事务
            $this->redis->multi();
            
            // 删除用户订阅记录
            $this->redis->hDel(self::USER_SUBSCRIPTIONS_KEY, (string)$userId);
            
            // 从币种订阅者列表中移除用户
            $this->redis->sRem(self::SYMBOL_SUBSCRIBERS_KEY . $currentSymbol, $userId);
            
            // 提交事务
            $this->redis->exec();
            
            $this->logger->info('用户取消订阅成功', [
                'user_id' => $userId,
                'symbol' => $currentSymbol
            ]);
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('用户取消订阅失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 获取用户当前订阅的币种
     *
     * @param int $userId 用户ID
     * @return string|null 币种代码，如果没有订阅则返回null
     */
    public function getUserSubscription(int $userId): ?string
    {
        try {
            $symbol = $this->redis->hGet(self::USER_SUBSCRIPTIONS_KEY, (string)$userId);
            return $symbol === false ? null : $symbol;
        } catch (\Exception $e) {
            $this->logger->error('获取用户订阅失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * 获取订阅某个币种的所有用户ID
     *
     * @param string $symbol 币种代码
     * @return array 用户ID数组
     */
    public function getSymbolSubscribers(string $symbol): array
    {
        try {
            $subscribers = $this->redis->sMembers(self::SYMBOL_SUBSCRIBERS_KEY . $symbol);
            return array_map('intval', $subscribers);
        } catch (\Exception $e) {
            $this->logger->error('获取币种订阅者失败', [
                'symbol' => $symbol,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * 检查用户是否订阅了指定币种
     *
     * @param int $userId 用户ID
     * @param string $symbol 币种代码
     * @return bool 是否已订阅
     */
    public function isUserSubscribed(int $userId, string $symbol): bool
    {
        try {
            return $this->redis->sIsMember(self::SYMBOL_SUBSCRIBERS_KEY . $symbol, $userId);
        } catch (\Exception $e) {
            $this->logger->error('检查用户订阅状态失败', [
                'user_id' => $userId,
                'symbol' => $symbol,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 获取某个币种的订阅者数量
     *
     * @param string $symbol 币种代码
     * @return int 订阅者数量
     */
    public function getSymbolSubscriberCount(string $symbol): int
    {
        try {
            return $this->redis->sCard(self::SYMBOL_SUBSCRIBERS_KEY . $symbol);
        } catch (\Exception $e) {
            $this->logger->error('获取币种订阅者数量失败', [
                'symbol' => $symbol,
                'error' => $e->getMessage()
            ]);
            
            return 0;
        }
    }

    /**
     * 获取所有有订阅者的币种列表
     *
     * @return array 币种列表
     */
    public function getAllSubscribedSymbols(): array
    {
        try {
            $keys = $this->redis->keys(self::SYMBOL_SUBSCRIBERS_KEY . '*');
            $symbols = [];
            
            foreach ($keys as $key) {
                $symbol = str_replace(self::SYMBOL_SUBSCRIBERS_KEY, '', $key);
                if ($this->redis->sCard($key) > 0) {
                    $symbols[] = $symbol;
                }
            }
            
            return $symbols;
        } catch (\Exception $e) {
            $this->logger->error('获取所有订阅币种失败', [
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * 清理指定币种的所有订阅数据
     *
     * @param string $symbol 币种代码
     * @return bool 是否清理成功
     */
    public function clearSymbolSubscriptions(string $symbol): bool
    {
        try {
            // 获取该币种的所有订阅者
            $subscribers = $this->getSymbolSubscribers($symbol);
            
            // 开始事务
            $this->redis->multi();
            
            // 从用户订阅记录中删除这些用户
            foreach ($subscribers as $userId) {
                $this->redis->hDel(self::USER_SUBSCRIPTIONS_KEY, (string)$userId);
            }
            
            // 删除币种订阅者列表
            $this->redis->del(self::SYMBOL_SUBSCRIBERS_KEY . $symbol);
            
            // 提交事务
            $this->redis->exec();
            
            $this->logger->info('清理币种订阅数据成功', [
                'symbol' => $symbol,
                'subscriber_count' => count($subscribers)
            ]);
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('清理币种订阅数据失败', [
                'symbol' => $symbol,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 获取系统订阅统计信息
     *
     * @return array 统计信息
     */
    public function getSubscriptionStats(): array
    {
        try {
            $totalUsers = $this->redis->hLen(self::USER_SUBSCRIPTIONS_KEY);
            $subscribedSymbols = $this->getAllSubscribedSymbols();
            
            return [
                'total_users' => $totalUsers,
                'total_symbols' => count($subscribedSymbols),
                'subscribed_symbols' => $subscribedSymbols
            ];
        } catch (\Exception $e) {
            $this->logger->error('获取订阅统计信息失败', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'total_users' => 0,
                'total_symbols' => 0,
                'subscribed_symbols' => []
            ];
        }
    }
}