<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 链币种同步服务
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/2
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\MarketData\Service\Chain;

use App\Logger\LoggerFactory;
use App\Model\Chain\ChainCurrencyCate;
use App\Model\Chain\ChainCurrency;
use App\Model\Chain\ChainCurrencyMete;
use App\Service\TranslationService;
use Hyperf\Di\Annotation\Inject;

/**
 * 链币种同步服务
 */
class ChainCurrencySync
{
    #[Inject]
    public LoggerFactory $loggerFactory;

    #[Inject]
    public TranslationService $translationService;

    /**
     * 同步币安 Alpha 币种的链数据
     */
    public function syncChainCategories(): bool
    {
        try {
            // 1. 获取数据
            $client = client(['timeout' => 30]);
            $response = $client->get('https://www.binance.com/bapi/defi/v1/public/wallet-direct/buw/wallet/cex/alpha/chain/list');
            $data = json_decode($response->getBody()->getContents(), true);
            
            if (!isset($data['data']) || !is_array($data['data'])) {
                return false;
            }

            // 2. 解析数据 - 按链分组
            $chainGroups = [];
            foreach ($data['data'] as $token) {
                if (empty($token['chainId'])) {
                    continue;
                }

                $chainId = $token['chainId'];
                if (!isset($chainGroups[$chainId])) {
                    $chainGroups[$chainId] = [
                        'chainId' => $chainId,
                        'chainIcon' => $token['chainIconUrl'] ?? '',
                        'chainName' => $token['chainName'] ?? '',
                        'transformAssets' => []
                    ];
                }
                $chainGroups[$chainId]['transformAssets'][] = $token['transformAssets'];
            }

            // 3. 保存数据
            foreach ($chainGroups as $chainData) {
                $chainData['transformAssets'] = array_unique($chainData['transformAssets']);
                
                ChainCurrencyCate::query()->updateOrCreate(
                    ['chainId' => $chainData['chainId']],
                    $chainData
                );
            }

            $this->loggerFactory->get(self::class)->info('同步完成，共处理 ' . count($chainGroups) . ' 个链');
            return true;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("同步失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 同步币安 Alpha 币种数据
     */
    public function syncChainCurrencies(): bool
    {
        try {
            // 1. 获取数据
            $client = client(['timeout' => 30]);
            $response = $client->get('https://www.binance.com/bapi/defi/v1/public/wallet-direct/buw/wallet/cex/alpha/all/token/list');
            $data = json_decode($response->getBody()->getContents(), true);
            
            if (!isset($data['data']) || !is_array($data['data'])) {
                return false;
            }

            $processedCount = 0;
            $updatedCount = 0;
            $createdCount = 0;

            // 2. 解析并保存数据
            foreach ($data['data'] as $token) {
                if (empty($token['tokenId']) || empty($token['symbol'])) {
                    continue;
                }

                // 使用tokenId查询数据库中是否存在记录
                $existingCurrency = ChainCurrency::query()
                    ->where('tokenId', $token['tokenId'])
                    ->first();

                $currencyData = [
                    'tokenId' => $token['tokenId'],
                    'chainId' => $token['chainId'] ?? '',
                    'contractAddres' => $token['contractAddress'] ?? '',
                    'currency_name' => $token['name'] ?? '',
                    'symbol' => $token['symbol'],
                    'icon' => $token['iconUrl'] ?? '',
                    'narketCap' => (float)($token['marketCap'] ?? 0),
                    'decimals' => (int)($token['decimals'] ?? 0),
                    'hotTag' => $token['hotTag'] ? 1 : 0,
                    'canTransfer' => $token['canTransfer'] ? 1 : 0,
                    'tradeDecimal' => (int)($token['tradeDecimal'] ?? 0),
                    'alphaId' => $token['alphaId'] ?? '',
                    'score' => (int)($token['score'] ?? 0),
                ];

                if ($existingCurrency) {
                    // 对比关键字段，判断是否需要更新
                    $needUpdate = $this->compareKeyFields($existingCurrency, $currencyData);
                    
                    if ($needUpdate) {
                        $existingCurrency->update($currencyData);
                        $updatedCount++;
                        
                        $this->loggerFactory->get(self::class)->info(
                            "更新币种: {$token['symbol']}, tokenId: {$token['tokenId']}"
                        );
                    }
                } else {
                    // 新增记录
                    ChainCurrency::query()->create($currencyData);
                    $createdCount++;
                    
                    $this->loggerFactory->get(self::class)->info(
                        "新增币种: {$token['symbol']}, tokenId: {$token['tokenId']}"
                    );
                }

                $processedCount++;
            }

            $this->loggerFactory->get(self::class)->info(
                "币种同步完成，共处理: {$processedCount}，新增: {$createdCount}，更新: {$updatedCount}"
            );
            return true;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("币种同步失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 对比关键字段，判断是否需要更新
     */
    private function compareKeyFields(ChainCurrency $existing, array $newData): bool
    {
        // 对比的关键字段
        $keyFields = [
            'contractAddres' => 'contractAddres',
            'alphaId' => 'alphaId',
            'chainId' => 'chainId',
            'tokenId' => 'tokenId'
        ];

        foreach ($keyFields as $dbField => $newField) {
            $existingValue = $existing->getAttribute($dbField) ?? '';
            $newValue = $newData[$newField] ?? '';
            
            // 任何一个关键字段有变化，就需要更新
            if ($existingValue !== $newValue) {
                $this->loggerFactory->get(self::class)->info(
                    "检测到字段变化: {$dbField}, 旧值: '{$existingValue}', 新值: '{$newValue}', symbol: {$existing->symbol}"
                );
                return true;
            }
        }

        return false;
    }

    /**
     * 同步币种 Meta 详细信息
     */
    public function syncChainCurrencyMeta(): bool
    {
        try {
            // 1. 加载已保存的币种 chainId 和 contractAddress
            $currencies = ChainCurrency::query()
                ->whereNotNull('chainId')
                ->whereNotNull('contractAddres')
                ->where('chainId', '!=', '')
                ->where('contractAddres', '!=', '')
                ->get(['tokenId', 'chainId', 'contractAddres']);

            if ($currencies->isEmpty()) {
                $this->loggerFactory->get(self::class)->info('没有找到需要同步的币种数据');
                return true;
            }

            // 2. 遍历请求每个币种的详细信息
            $successCount = 0;
            $totalCount = $currencies->count();

            foreach ($currencies as $currency) {
                try {
                    $result = $this->syncSingleCurrencyMeta(
                        $currency->tokenId,
                        $currency->chainId,
                        $currency->contractAddres
                    );

                    if ($result) {
                        $successCount++;
                    }

                    // 添加延迟避免请求过快
                    usleep(200000); // 200ms

                } catch (\Throwable $e) {
                    $this->loggerFactory->get(self::class)->error(
                        "同步币种 Meta 失败，tokenId: {$currency->tokenId}, 错误: {$e->getMessage()}"
                    );
                }
            }

            $this->loggerFactory->get(self::class)->info(
                "币种 Meta 同步完成，成功: {$successCount}/{$totalCount}"
            );
            return $successCount > 0;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("币种 Meta 同步失败: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 同步单个币种的 Meta 信息
     */
    private function syncSingleCurrencyMeta(string $tokenId, string $chainId, string $contractAddress): bool
    {
        try {
            // 1. 请求 API 获取详细信息
            $client = client(['timeout' => 30]);
            $url = 'https://www.binance.com/bapi/defi/v1/public/wallet-direct/buw/wallet/cex/alpha/token/full/info';
            $response = $client->get($url, [
                'query' => [
                    'chainId' => $chainId,
                    'contractAddress' => $contractAddress
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (!isset($data['success']) || $data['success'] !== true) {
                return false;
            }

            if (!isset($data['data']['metaInfo'])) {
                return false;
            }

            $metaInfo = $data['data']['metaInfo'];

            // 2. 处理翻译
            $description = $this->processDescription($metaInfo);

            // 3. 保存到数据库
            $mateData = [
                'tokenId' => $tokenId,
                'mate' => $metaInfo,
                'description' => $description,
            ];

            ChainCurrencyMete::query()->updateOrCreate(
                ['tokenId' => $tokenId],
                $mateData
            );

            return true;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error(
                "请求币种详情失败，tokenId: {$tokenId}, 错误: {$e->getMessage()}"
            );
            return false;
        }
    }

    /**
     * 处理描述信息，翻译中英文
     */
    private function processDescription(array $metaInfo): array
    {
        $cnDescription = $metaInfo['cnDescription'] ?? '';
        $enDescription = $metaInfo['enDescription'] ?? '';

        // 如果有中文描述但没有英文描述，进行翻译
        if (!empty($cnDescription) && empty($enDescription)) {
            try {
                $enDescription = $this->translationService->translate($cnDescription, 'zh', 'en');
            } catch (\Throwable $e) {
                $this->loggerFactory->get(self::class)->error("翻译失败: {$e->getMessage()}");
                $enDescription = $cnDescription; // 翻译失败时使用原文
            }
        }

        // 如果有英文描述但没有中文描述，进行翻译
        if (!empty($enDescription) && empty($cnDescription)) {
            try {
                $cnDescription = $this->translationService->translate($enDescription, 'en', 'zh');
            } catch (\Throwable $e) {
                $this->loggerFactory->get(self::class)->error("翻译失败: {$e->getMessage()}");
                $cnDescription = $enDescription; // 翻译失败时使用原文
            }
        }

        return [
            ['lang' => 'zh_cn', 'text' => $cnDescription],
            ['lang' => 'en', 'text' => $enDescription]
        ];
    }
}