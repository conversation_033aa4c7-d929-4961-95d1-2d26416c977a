<?php

declare(strict_types=1);

namespace App\MarketData\Service;

use App\Enum\AsyncExecutorKey;
use App\Enum\MarketData\KlineIndex;
use App\Enum\MarketData\KlinePeriod;
use App\Enum\MarketType;
use App\Job\MarketData\KlineBulkRetryJob;
use App\Model\Currency\Currency;
use Hyperf\AsyncQueue\Driver\DriverFactory;
use Hyperf\Di\Annotation\Inject;
use Elasticsearch\Client;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Coroutine\Parallel;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use Hyperf\Redis\Redis;

/**
 * K线数据聚合处理服务
 */
class KlineAggregatorService
{
    #[Inject]
    protected ContainerInterface $container;

    private Client $elasticsearch;
    private LoggerInterface $logger;

    #[Inject]
    private Redis $redis;

    private array $currentKlines = [];
    private array $currencies = [];
    private int $marketType;

    // 分离的K线数据：内部成交和外部成交
    private array $innerKlines = [];  // 内部成交K线 (out_trade=0)
    private array $outerKlines = [];  // 外部成交K线 (out_trade=1)

    // K线持久化缓冲区
    private array $persistenceBuffer = [];

    // 上次缓存时间
    private int $lastCacheTime = 0;

    #[Inject]
    private DriverFactory $driverFactory;
    
    // 周期毫秒映射
    private array $periodMilliseconds = [
        '1m' => 60 * 1000,
        '3m' => 3 * 60 * 1000,
        '5m' => 5 * 60 * 1000,
        '15m' => 15 * 60 * 1000,
        '30m' => 30 * 60 * 1000,
        '1h' => 60 * 60 * 1000,
        '2h' => 2 * 60 * 60 * 1000,
        '4h' => 4 * 60 * 60 * 1000,
        '1d' => 24 * 60 * 60 * 1000,
        '1w' => 7 * 24 * 60 * 60 * 1000,
        '1M' => 30 * 24 * 60 * 60 * 1000,
        '1Y' => 365 * 24 * 60 * 60 * 1000
    ];
    
    // 周期执行定时器 ID
    private int $periodicTimerId = 0;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
        $this->elasticsearch = elasticsearch();
        // Redis 已经通过依赖注入获取，不需要手动获取
    }

    public function initializeForMarket(int $marketType): void
    {
        $this->marketType = $marketType;
        $this->logger = $this->container->get(LoggerFactory::class)->get('kline-aggregator-' . $marketType);
        $this->initialize();
    }
    
    private function initialize(): void
    {
        try {
            // 1. 优先从数据库加载币种信息，这是后续所有操作的基础
            $this->loadCurrencies();

            // 2. 尝试从Redis恢复状态
            if ($this->restoreStateFromRedis()) {
                $this->logger->info("Successfully restored KlineAggregatorService state from Redis for market type: {$this->marketType}");
            }else{
                $this->logger->info("No state in Redis, initializing from scratch for market type: {$this->marketType}");
                // 3. 如果无法从Redis恢复，则从ES初始化K线数据
                $this->initializeKlineData();
            }

            // 启动周期性任务，每秒检查
            if ($this->periodicTimerId === 0) {
                $this->periodicTimerId = \Swoole\Timer::tick(1000, function() {
                    try {
                        $this->runPeriodicTasks();
                    } catch (\Throwable $e) {
                        $this->logger->error('Periodic tasks error: ' . $e->getMessage());
                    }
                });
            }

        } catch (\Throwable $e) {
            $this->logger->error("Failed to initialize KlineAggregatorService for market type {$this->marketType}: " . $e->getMessage(), ['exception' => $e]);
            throw $e;
        }
    }
    
    /**
     * 加载币种数据 - 只加载指定市场类型的币种
     */
    private function loadCurrencies(): void
    {
        $query = Currency::query()
            ->where('status', 1)
            ->where('market_type', MarketType::CRYPTO->value);

        // 根据市场类型筛选币种
        if ($this->marketType === 1) {
            // 现货市场
            $query->where('is_spotTrade', 1);
        } elseif ($this->marketType === 5) {
            // 合约市场
            $query->where('is_marginTrade', 1);
        }

        $currencies = $query->get(['id', 'symbol', 'is_spotTrade', 'is_marginTrade']);

        foreach ($currencies as $currency) {
            $this->currencies[$currency->getId()] = $currency;
        }

        $this->logger->info("Loaded " . count($this->currencies) . " currencies for market type: {$this->marketType}");
    }
    
    /**
     * 初始化K线数据 - 使用并发批次处理
     */
    private function initializeKlineData(): void
    {
        // 构建需要初始化的任务 - 只初始化当前市场类型
        $initTasks = [];
        foreach ($this->currencies as $currency) {
            foreach (KlinePeriod::cases() as $period) {
                $periodValue = $period->value;

                // 跳过1Y周期，CryptoKlineSync中没有
                if ($periodValue === '1Y') {
                    continue;
                }

                // 只初始化当前市场类型的数据
                $initTasks[] = [
                    'currency' => $currency,
                    'market_type' => $this->marketType,
                    'period' => $periodValue
                ];
            }
        }

        // 按批次并发处理，每批20个任务
        $batches = array_chunk($initTasks, 20);

        foreach ($batches as $batchIndex => $batch) {
            $this->processBatchInitialization($batch);

            // 批次间稍微延迟，避免ES压力过大
            if ($batchIndex < count($batches) - 1) {
                usleep(100000); // 100ms
            }
        }
    }

    /**
     * 并发处理批次初始化
     */
    private function processBatchInitialization(array $batch): void
    {
        $parallel = new Parallel();

        foreach ($batch as $task) {
            $parallel->add(function () use ($task): void {
                try {
                    $this->initializeKlineForPeriod(
                        $task['currency'],
                        $task['market_type'],
                        $task['period']
                    );
                } catch (\Throwable $e) {
                    $currency = $task['currency'];
                    $this->logger->error("Failed to initialize kline for {$currency->getSymbol()}-{$task['market_type']}-{$task['period']}: " . $e->getMessage());
                }
            });
        }

        try {
            $parallel->wait();
        } catch (\Throwable $e) {
            $this->logger->error("Batch initialization failed: " . $e->getMessage());
        }
    }
    
    /**
     * 初始化单个周期的K线数据
     */
    private function initializeKlineForPeriod(Currency $currency, int $marketType, string $period): void
    {
        try {
            // 从ES获取最新K线
            $latestKline = $this->getLatestKlineFromES($currency, $marketType, $period);
            
            $currentPeriodStart = $this->getCurrentPeriodStartTime($period);
            
            if ($latestKline && $latestKline['open_time'] == $currentPeriodStart) {
                // 最新K线就是当前周期，直接使用
                $klineData = [
                    'currency_id' => $currency->getId(),
                    'symbol' => $currency->getSymbol(),
                    'market_type' => $marketType,
                    'period' => $period,
                    'open_time' => $latestKline['open_time'],
                    'close_time' => $latestKline['close_time'],
                    'ohlcv' => $latestKline['ohlcv'],
                    'is_dirty' => false,
                    'last_update' => time()
                ];
            } else {
                // 初始化当前周期的零值K线
                $klineData = $this->createEmptyKline($currency, $marketType, $period, $currentPeriodStart);
            }
            
            $this->currentKlines[$currency->getId()][$marketType][$period] = $klineData;
            
            // 同时初始化内部和外部K线数据
            $this->innerKlines[$currency->getId()][$marketType][$period] = $this->createEmptyKline($currency, $marketType, $period, $currentPeriodStart);
            $this->outerKlines[$currency->getId()][$marketType][$period] = $this->createEmptyKline($currency, $marketType, $period, $currentPeriodStart);
            
        } catch (\Throwable $e) {
            $this->logger->error("Failed to initialize kline for {$currency->symbol}-{$marketType}-{$period}: " . $e->getMessage());
        }
    }
    
    /**
     * 从ES获取最新K线数据
     */
    private function getLatestKlineFromES(Currency $currency, int $marketType, string $period): ?array
    {
        try {
            $indexName = KlineIndex::getIndexName($currency->getSymbol(), $marketType);
            
            if (!$this->elasticsearch->indices()->exists(['index' => $indexName])) {
                return null;
            }
            
            $params = [
                'index' => $indexName,
                'body' => [
                    'query' => ['term' => ['period' => $period]],
                    'sort' => [['open_time' => ['order' => 'desc']]],
                    'size' => 1
                ]
            ];
            
            $response = $this->elasticsearch->search(params: $params);
            
            if (isset($response['hits']['hits'][0]['_source'])) {
                return $response['hits']['hits'][0]['_source'];
            }
            
            return null;
            
        } catch (\Throwable $e) {
            $this->logger->error('Failed to get latest kline from ES: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 创建空K线数据
     */
    private function createEmptyKline(Currency $currency, int $marketType, string $period, int $openTime, ?float $previousClosePrice = null): array
    {
        $closeTime = $openTime + $this->periodMilliseconds[$period] - 1;

        // 如果有上一根K线的收盘价，使用它作为开盘价，否则使用0.0
        $openPrice = $previousClosePrice ?? 0.0;

        return [
            'currency_id' => $currency->getId(),
            'symbol' => $currency->getSymbol(),
            'market_type' => $marketType,
            'period' => $period,
            'open_time' => $openTime,
            'close_time' => $closeTime,
            'ohlcv' => [$openPrice, $openPrice, $openPrice, $openPrice, 0.0, 0.0, 0], // [open, high, low, close, volume, quote_volume, trades_count]
            'is_dirty' => false,
            'last_update' => 0,
            // 新增时间跟踪字段
            'first_trade_time' => 0,  // 第一笔成交时间
            'last_trade_time' => 0,   // 最后一笔成交时间
        ];
    }
    
    /**
     * 获取当前周期开始时间
     */
    private function getCurrentPeriodStartTime(string $period): int
    {
        $currentTime = time() * 1000; // 转换为毫秒
        $periodMs = $this->periodMilliseconds[$period];
        
        return intval($currentTime / $periodMs) * $periodMs;
    }

    /**
     * 接收成交数据并更新K线
     */
    public function updateFromTrade(array $tradeData): void
    {
        try {
            $currencyId = $tradeData['currency_id'];
            $marketType = (int)($tradeData['market_type'] ?? 1); // 默认现货
            $price = (float)$tradeData['price'];
            $quantity = (float)($tradeData['quantity'] ?? 0);
            $tradeTime = (int)$tradeData['trade_time'];
            $outTrade = (int)($tradeData['out_trade'] ?? 0); // 0=内部成交，1=外部成交

            if (!isset($this->currencies[$currencyId])) {
                return;
            }

            // 根据out_trade字段分别更新内部和外部K线
            $this->updateSeparatedKlines($currencyId, $marketType, $price, $quantity, $tradeTime, $outTrade);

        } catch (\Throwable $e) {
            $this->logger->error("Failed to update kline from trade: " . $e->getMessage());
        }
    }

    /**
     * 分别更新内部和外部K线数据
     */
    private function updateSeparatedKlines(int $currencyId, int $marketType, float $price, float $quantity, int $tradeTime, int $outTrade): void
    {
        // 只处理当前市场类型的数据
        if ($marketType !== $this->marketType) {
            return;
        }

        // 检查该币种和市场类型是否已初始化
        if (!isset($this->innerKlines[$currencyId][$marketType]) || !isset($this->outerKlines[$currencyId][$marketType])) {
            return;
        }

        // 根据out_trade选择更新内部还是外部K线
        if ($outTrade === 0) {
            // 内部成交数据
            $this->updateKlinesByType($this->innerKlines[$currencyId][$marketType], $currencyId, $marketType, $price, $quantity, $tradeTime, 'inner');
        } else {
            // 外部成交数据
            $this->updateKlinesByType($this->outerKlines[$currencyId][$marketType], $currencyId, $marketType, $price, $quantity, $tradeTime, 'outer');
        }

        // 合并内外部数据生成最终K线
        $this->mergeAndUpdateFinalKlines($currencyId, $marketType, $tradeTime);
    }

    /**
     * 更新指定市场的K线数据
     */
    private function updateKlinesForMarket(int $currencyId, int $marketType, float $price, float $quantity, int $tradeTime): void
    {
        // 只处理当前市场类型的数据
        if ($marketType !== $this->marketType) {
            return;
        }

        // 检查该币种和市场类型是否已初始化
        if (!isset($this->currentKlines[$currencyId][$marketType])) {
            return;
        }

        foreach (KlinePeriod::cases() as $period) {
            $periodValue = $period->value;

            // 跳过1Y周期
            if ($periodValue === '1Y') {
                continue;
            }

            if (!isset($this->currentKlines[$currencyId][$marketType][$periodValue])) {
                continue;
            }

            $kline = &$this->currentKlines[$currencyId][$marketType][$periodValue];
            $periodStart = $this->getPeriodStartTime($tradeTime, $periodValue);

            // 检查是否跨周期
            if ($periodStart > $kline['open_time']) {
                // K线周期已完成，如果数据被修改过，则将其移入待持久化缓冲区
                if ($kline['is_dirty']) {
                    $this->persistenceBuffer[] = $kline;
                }

                // 获取上一根K线的收盘价
                $previousClosePrice = ($kline['ohlcv'][6] > 0) ? $kline['ohlcv'][3] : null;

                // 初始化新周期K线
                $currency = $this->currencies[$currencyId];
                $kline = $this->createEmptyKline($currency, $marketType, $periodValue, $periodStart, $previousClosePrice);
                
                // 清理内外部数据聚合（避免使用过期的数据）
                $this->clearSeparatedKlineData($currencyId, $marketType, $periodValue);
            }

            // 更新OHLCV数据
            $this->updateOHLCV($kline, $price, $quantity, $tradeTime);
        }
    }

    /**
     * 按类型更新K线数据（内部或外部）
     */
    private function updateKlinesByType(array &$klines, int $currencyId, int $marketType, float $price, float $quantity, int $tradeTime, string $type): void
    {
        foreach (KlinePeriod::cases() as $period) {
            $periodValue = $period->value;

            // 跳过1Y周期
            if ($periodValue === '1Y') {
                continue;
            }

            $periodStart = $this->getPeriodStartTime($tradeTime, $periodValue);

            // 如果不存在对应周期的K线，或者需要跨周期，则初始化新K线
            if (!isset($klines[$periodValue]) || $periodStart > $klines[$periodValue]['open_time']) {
                // 获取最终K线的收盘价作为开盘价（保证价格连续性）
                $previousClosePrice = null;
                if (isset($this->currentKlines[$currencyId][$marketType][$periodValue]) && 
                    $this->currentKlines[$currencyId][$marketType][$periodValue]['ohlcv'][6] > 0) {
                    $previousClosePrice = $this->currentKlines[$currencyId][$marketType][$periodValue]['ohlcv'][3];
                } elseif (isset($klines[$periodValue]) && $klines[$periodValue]['ohlcv'][6] > 0) {
                    // 如果最终K线没有数据，则使用当前类型K线的收盘价
                    $previousClosePrice = $klines[$periodValue]['ohlcv'][3];
                }

                // 初始化新周期K线
                $currency = $this->currencies[$currencyId];
                $klines[$periodValue] = $this->createEmptyKline($currency, $marketType, $periodValue, $periodStart, $previousClosePrice);
            }

            // 更新OHLCV数据
            $this->updateOHLCV($klines[$periodValue], $price, $quantity, $tradeTime);
        }
    }

    /**
     * 合并内外部K线数据生成最终K线
     */
    private function mergeAndUpdateFinalKlines(int $currencyId, int $marketType, int $tradeTime): void
    {
        if (!isset($this->currentKlines[$currencyId][$marketType])) {
            return;
        }

        foreach (KlinePeriod::cases() as $period) {
            $periodValue = $period->value;

            // 跳过1Y周期
            if ($periodValue === '1Y') {
                continue;
            }

            if (!isset($this->currentKlines[$currencyId][$marketType][$periodValue])) {
                continue;
            }

            $finalKline = &$this->currentKlines[$currencyId][$marketType][$periodValue];
            $periodStart = $this->getPeriodStartTime($tradeTime, $periodValue);

            // 检查是否跨周期
            if ($periodStart > $finalKline['open_time']) {
                // K线周期已完成，如果数据被修改过，则将其移入待持久化缓冲区
                if ($finalKline['is_dirty']) {
                    $this->persistenceBuffer[] = $finalKline;
                }

                // 获取上一根K线的收盘价
                $previousClosePrice = ($finalKline['ohlcv'][6] > 0) ? $finalKline['ohlcv'][3] : null;

                // 初始化新周期K线
                $currency = $this->currencies[$currencyId];
                $finalKline = $this->createEmptyKline($currency, $marketType, $periodValue, $periodStart, $previousClosePrice);
                
                // 清理内外部数据聚合（避免使用过期的数据）
                $this->clearSeparatedKlineData($currencyId, $marketType, $periodValue);
            }

            $innerKline = $this->innerKlines[$currencyId][$marketType][$periodValue] ?? null;
            $outerKline = $this->outerKlines[$currencyId][$marketType][$periodValue] ?? null;

            // 合并内外部数据
            $this->mergeKlineData($finalKline, $innerKline, $outerKline);
        }
    }

    /**
     * 合并内外部K线数据，基于时间差精细化处理OHLC
     */
    private function mergeKlineData(array &$finalKline, ?array $innerKline, ?array $outerKline): void
    {
        $hasInnerData = $innerKline && $innerKline['ohlcv'][6] > 0; // 有内部成交
        $hasOuterData = $outerKline && $outerKline['ohlcv'][6] > 0; // 有外部成交

        if (!$hasInnerData && !$hasOuterData) {
            return; // 都没有数据
        }

        $finalOhlcv = &$finalKline['ohlcv'];
        $wasEmpty = $finalOhlcv[6] == 0; // 最终K线是否为空

        // 开盘价处理：保持K线连续性
        // 如果最终K线已经有开盘价（从上一根K线的收盘价继承），则不再修改
        // 只有当开盘价为0时，才需要设置开盘价
        if ($finalOhlcv[0] == 0.0 && ($hasInnerData || $hasOuterData)) {
            // 优先使用内部数据的开盘价，如果没有内部数据则使用外部数据
            if ($hasInnerData) {
                $finalOhlcv[0] = $innerKline['ohlcv'][0]; // 使用内部开盘价
            } elseif ($hasOuterData) {
                $finalOhlcv[0] = $outerKline['ohlcv'][0]; // 使用外部开盘价
            }
        }

        // 决定收盘价：基于时间差判断
        $closePrice = 0.0;
        if ($hasInnerData && $hasOuterData) {
            $innerLastTime = $innerKline['last_trade_time'] ?? 0;
            $outerLastTime = $outerKline['last_trade_time'] ?? 0;
            
            // 如果时间字段存在且有效，进行时间差判断
            if ($innerLastTime > 0 && $outerLastTime > 0) {
                $timeDiff = abs($innerLastTime - $outerLastTime) / 1000; // 转换为秒
                
                // 如果内外部时间差≤3秒，优先使用内部收盘价
                if ($timeDiff <= 3) {
                    $closePrice = $innerKline['ohlcv'][3]; // 使用内部收盘价
                } else {
                    // 使用最新的成交价格作为收盘价
                    if ($innerLastTime > $outerLastTime) {
                        $closePrice = $innerKline['ohlcv'][3];
                    } else {
                        $closePrice = $outerKline['ohlcv'][3];
                    }
                }
            } else {
                // 如果时间字段无效，优先使用内部收盘价
                $closePrice = $innerKline['ohlcv'][3]; // 使用内部收盘价
            }
        } elseif ($hasInnerData) {
            $closePrice = $innerKline['ohlcv'][3]; // 只有内部数据
        } elseif ($hasOuterData) {
            $closePrice = $outerKline['ohlcv'][3]; // 只有外部数据
        }

        // 计算最高价和最低价：取两个数据源的极值
        $highPrice = 0.0;
        $lowPrice = 0.0;
        
        if ($hasInnerData && $hasOuterData) {
            $highPrice = max($innerKline['ohlcv'][1], $outerKline['ohlcv'][1]);
            $lowPrice = min($innerKline['ohlcv'][2], $outerKline['ohlcv'][2]);
        } elseif ($hasInnerData) {
            $highPrice = $innerKline['ohlcv'][1];
            $lowPrice = $innerKline['ohlcv'][2];
        } elseif ($hasOuterData) {
            $highPrice = $outerKline['ohlcv'][1];
            $lowPrice = $outerKline['ohlcv'][2];
        }

        // 设置HLC（开盘价在上面已经处理）
        $finalOhlcv[1] = max($finalOhlcv[1], $highPrice); // high
        $finalOhlcv[2] = ($finalOhlcv[2] == 0.0) ? $lowPrice : min($finalOhlcv[2], $lowPrice); // low
        $finalOhlcv[3] = $closePrice; // close

        // 累加成交量数据
        $volumeToAdd = 0.0;
        $quoteVolumeToAdd = 0.0;
        $tradesCountToAdd = 0;

        if ($hasInnerData) {
            $volumeToAdd += $innerKline['ohlcv'][4];
            $quoteVolumeToAdd += $innerKline['ohlcv'][5];
            $tradesCountToAdd += $innerKline['ohlcv'][6];
        }

        if ($hasOuterData) {
            $volumeToAdd += $outerKline['ohlcv'][4];
            $quoteVolumeToAdd += $outerKline['ohlcv'][5];
            $tradesCountToAdd += $outerKline['ohlcv'][6];
        }

        $finalOhlcv[4] += $volumeToAdd; // volume
        $finalOhlcv[5] += $quoteVolumeToAdd; // quote_volume
        $finalOhlcv[6] += $tradesCountToAdd; // trades_count

        // 记录时间信息
        if ($hasInnerData || $hasOuterData) {
            $firstTradeTime = 0;
            $lastTradeTime = 0;
            
            if ($hasInnerData && $hasOuterData) {
                $innerFirstTime = $innerKline['first_trade_time'] ?? 0;
                $outerFirstTime = $outerKline['first_trade_time'] ?? 0;
                $innerLastTime = $innerKline['last_trade_time'] ?? 0;
                $outerLastTime = $outerKline['last_trade_time'] ?? 0;
                
                if ($innerFirstTime > 0 && $outerFirstTime > 0) {
                    $firstTradeTime = min($innerFirstTime, $outerFirstTime);
                } elseif ($innerFirstTime > 0) {
                    $firstTradeTime = $innerFirstTime;
                } elseif ($outerFirstTime > 0) {
                    $firstTradeTime = $outerFirstTime;
                }
                
                if ($innerLastTime > 0 && $outerLastTime > 0) {
                    $lastTradeTime = max($innerLastTime, $outerLastTime);
                } elseif ($innerLastTime > 0) {
                    $lastTradeTime = $innerLastTime;
                } elseif ($outerLastTime > 0) {
                    $lastTradeTime = $outerLastTime;
                }
            } elseif ($hasInnerData) {
                $firstTradeTime = $innerKline['first_trade_time'] ?? 0;
                $lastTradeTime = $innerKline['last_trade_time'] ?? 0;
            } elseif ($hasOuterData) {
                $firstTradeTime = $outerKline['first_trade_time'] ?? 0;
                $lastTradeTime = $outerKline['last_trade_time'] ?? 0;
            }
            
            if (($finalKline['first_trade_time'] ?? 0) == 0 && $firstTradeTime > 0) {
                $finalKline['first_trade_time'] = $firstTradeTime;
            }
            if ($lastTradeTime > 0) {
                $finalKline['last_trade_time'] = $lastTradeTime;
            }
        }

        // 标记为需要持久化
        $finalKline['is_dirty'] = true;
        $finalKline['last_update'] = time();
    }

    /**
     * 获取指定时间的周期开始时间
     */
    private function getPeriodStartTime(int $timestamp, string $period): int
    {
        $periodMs = $this->periodMilliseconds[$period];
        return intval($timestamp / $periodMs) * $periodMs;
    }

    /**
     * 清理指定周期的内部和外部K线数据
     */
    private function clearSeparatedKlineData(int $currencyId, int $marketType, string $period): void
    {
        // 清理内部K线数据
        if (isset($this->innerKlines[$currencyId][$marketType][$period])) {
            unset($this->innerKlines[$currencyId][$marketType][$period]);
        }
        
        // 清理外部K线数据
        if (isset($this->outerKlines[$currencyId][$marketType][$period])) {
            unset($this->outerKlines[$currencyId][$marketType][$period]);
        }
    }

    /**
     * 更新OHLCV数据
     */
    private function updateOHLCV(array &$kline, float $price, float $quantity, int $tradeTime): void
    {
        $ohlcv = &$kline['ohlcv'];

        // 首次交易
        if ($ohlcv[6] == 0) { // trades_count == 0
            // 记录第一笔成交时间
            $kline['first_trade_time'] = $tradeTime;
            
            // 如果开盘价为0（没有价格连续性），则设置为当前价格
            if ($ohlcv[0] == 0.0) {
                $ohlcv[0] = $price; // open
            }
            $ohlcv[1] = max($ohlcv[0], $price); // high (考虑开盘价)
            $ohlcv[2] = min($ohlcv[0], $price); // low (考虑开盘价)
            $ohlcv[3] = $price; // close
        } else {
            // 更新高低价和收盘价
            $ohlcv[1] = max($ohlcv[1], $price); // high
            $ohlcv[2] = min($ohlcv[2], $price); // low
            $ohlcv[3] = $price; // close
        }

        // 记录最后一笔成交时间
        $kline['last_trade_time'] = $tradeTime;

        // 累加成交量和成交额
        $ohlcv[4] += $quantity; // volume
        $ohlcv[5] += $price * $quantity; // quote_volume
        $ohlcv[6]++; // trades_count

        // 标记为需要持久化
        $kline['is_dirty'] = true;
        $kline['last_update'] = time();
    }

    /**
     * 周期性地将完成的K线批量持久化到ES.
     */
    private function persistCompletedKlines(): void
    {
        if (empty($this->persistenceBuffer)) {
            return;
        }

        $klinesToPersist = [];
        $remainingKlines = [];
        $now = time() * 1000; // 毫秒

        foreach ($this->persistenceBuffer as $kline) {
            // K线结束时间 + 2秒延迟
            $commitTime = $kline['close_time'] + 2000;

            if ($now >= $commitTime) {
                $klinesToPersist[] = $kline;
            } else {
                $remainingKlines[] = $kline;
            }
        }

        // 更新缓冲区，仅保留未到时间的K线
        $this->persistenceBuffer = $remainingKlines;

        if (! empty($klinesToPersist)) {
            $this->bulkPersistToES($klinesToPersist);
        }
    }

    /**
     * 批量持久化K线数据到ES.
     */
    private function bulkPersistToES(array $klines): void
    {
        if (empty($klines)) {
            return;
        }

        $params = ['body' => []];
        foreach ($klines as $kline) {
            $indexName = KlineIndex::getIndexName($kline['symbol'], $kline['market_type']);
            $doc = $kline;
            $doc['ohlcv'] = array_map('floatval', $doc['ohlcv']);

            try{
                unset($doc['currency_id'], $doc['symbol'], $doc['market_type'],$doc['last_update'],$doc['is_dirty'], $doc['first_trade_time'], $doc['last_trade_time']);
            }catch (\Exception $e){}

            $docId = "{$kline['symbol']}-{$kline['market_type']}-{$kline['period']}-{$kline['open_time']}";

            $params['body'][] = [
                'update' => [ '_index' => $indexName, '_id' => $docId ],
            ];
            $params['body'][] = [
                'doc' => $doc,
                'doc_as_upsert' => true,
            ];
        }

        try {
            $response = $this->elasticsearch->bulk($params);
            if ($response['errors']) {
                $this->logESBulkErrors($response);
                $failedKlines = $this->getFailedKlinesFromBulkResponse($response, $klines);
                $this->requeueFailedKlines($failedKlines);
            }
            //$this->logger->info(sprintf('Successfully persisted %d klines to ES for market %d.', count($klines), $this->marketType));
        } catch (\Throwable $e) {
            $this->logger->error(
                "Failed to bulk persist klines to ES for market {$this->marketType}: " . $e->getMessage(),
                ['exception' => $e]
            );
            $this->requeueFailedKlines($klines);
        }
    }

    /**
     * 将当前聚合状态以Hash结构缓存到Redis.
     */
    private function cacheStateToRedis(): void
    {
        if (empty($this->currentKlines)) {
            return;
        }
        $pipe = $this->redis->pipeline();
        
        foreach ($this->currentKlines as $currencyId => $marketTypes) {
            $currency = $this->currencies[$currencyId] ?? null;
            if (!$currency || !isset($marketTypes[$this->marketType])) {
                continue;
            }
            
            foreach ($marketTypes[$this->marketType] as $period => $kline) {
                // 只缓存最终合并的K线数据
                $key = sprintf('marketdata:aggregator:%s-%d-%s',
                    $currency->getSymbol(),
                    $this->marketType,
                    $period
                );
                // 按字段存储原始 kline 数据，ohlcv 单独 JSON 化
                $fields = [
                    'currency_id'  => $kline['currency_id'],
                    'symbol'       => $kline['symbol'],
                    'market_type'  => $kline['market_type'],
                    'period'       => $kline['period'],
                    'open_time'    => $kline['open_time'],
                    'close_time'   => $kline['close_time'],
                    'ohlcv'        => json_encode($kline['ohlcv']),
                    'is_dirty'     => $kline['is_dirty'] ? 1 : 0,
                    'last_update'  => $kline['last_update'],
                ];
                $pipe->hMSet($key, $fields);
                $pipe->expire($key, 3600);
            }
        }
        $pipe->exec();
    }

    /**
     * 从Redis恢复聚合状态.
     */
    private function restoreStateFromRedis(): bool
    {
        if (empty($this->currencies)) {
            return false;
        }
        $restored = false;
        foreach ($this->currencies as $currency) {
            $currencyId = $currency->getId();
            foreach (KlinePeriod::cases() as $periodEnum) {
                $period = $periodEnum->value;
                if ($period === '1Y') {
                    continue;
                }
                $key = sprintf('marketdata:aggregator:%s-%d-%s',
                    $currency->getSymbol(),
                    $this->marketType,
                    $period
                );
                $data = $this->redis->hGetAll($key);
                if (empty($data)) {
                    continue;
                }
                $this->currentKlines[$currencyId][$this->marketType][$period] = [
                    'currency_id'  => (int)$data['currency_id'],
                    'symbol'       => $data['symbol'],
                    'market_type'  => (int)$data['market_type'],
                    'period'       => $data['period'],
                    'open_time'    => (int)$data['open_time'],
                    'close_time'   => (int)$data['close_time'],
                    'ohlcv'        => json_decode($data['ohlcv'], true),
                    'is_dirty'     => isset($data['is_dirty']) && $data['is_dirty'] == 1,
                    'last_update'  => (int)$data['last_update'],
                ];
                
                // 初始化内部和外部K线数据（从缓存恢复时重新开始分离聚合）
                $currentPeriodStart = $this->getCurrentPeriodStartTime($period);
                $this->innerKlines[$currencyId][$this->marketType][$period] = $this->createEmptyKline($currency, $this->marketType, $period, $currentPeriodStart);
                $this->outerKlines[$currencyId][$this->marketType][$period] = $this->createEmptyKline($currency, $this->marketType, $period, $currentPeriodStart);
                
                $restored = true;
            }
        }
        if ($restored) {
            $this->logger->info("Restored kline state from Redis for market {$this->marketType}.");
        }
        return $restored;
    }



    /**
     * 获取K线数据
     */
    public function getKlineData(int $currencyId, int $marketType, string $period): ?array
    {
        return $this->currentKlines[$currencyId][$marketType][$period] ?? null;
    }

    /**
     * 获取所有K线数据
     */
    public function getAllKlineData(): array
    {
        return $this->currentKlines;
    }

    /**
     * 获取当前市场类型
     */
    public function getMarketType(): int
    {
        return $this->marketType;
    }

    /**
     * 停止服务，强制刷写所有待处理数据.
     */
    public function stop(): void
    {
        // 清理定时器
        if ($this->periodicTimerId > 0) {
            \Swoole\Timer::clear($this->periodicTimerId);
            $this->periodicTimerId = 0;
        }
        // 强制持久化所有在缓冲区的数据
        $this->logger->info("Stopping service for market {$this->marketType}, flushing all buffered klines.");
        if (! empty($this->persistenceBuffer)) {
            $this->bulkPersistToES($this->persistenceBuffer);
            $this->persistenceBuffer = [];
        }

        // 将当前正在聚合的 "脏" K线也刷入
        $dirtyCurrentKlines = [];
        foreach ($this->currentKlines as $currencyBastes) {
            foreach ($currencyBastes as $marketBastes) {
                foreach ($marketBastes as $kline) {
                    if ($kline['is_dirty']) {
                        $dirtyCurrentKlines[] = $kline;
                    }
                }
            }
        }
        if (! empty($dirtyCurrentKlines)) {
            $this->logger->info(message: "Flushing " . count($dirtyCurrentKlines) . " dirty current klines.");
            $this->bulkPersistToES($dirtyCurrentKlines);
        }

        // 最后，缓存一次当前状态
        $this->cacheStateToRedis();
    }

    /**
     * 获取Redis状态缓存的Key.
     */
    private function getRedisKlineHashKey(string $symbol): string
    {
        return "marketdata:{$symbol}:{$this->marketType}";
    }

    /**
     * 记录ES批量写入错误.
     */
    private function logESBulkErrors(array $response): void
    {
        $errors = [];
        foreach ($response['items'] as $item) {
            if (isset($item['update']['error'])) {
                $errors[] = [
                    'id' => $item['update']['_id'],
                    'error' => $item['update']['error'],
                ];
            }
        }
        if (! empty($errors)) {
            $this->logger->error('ES bulk update failed for some documents.', ['errors' => $errors]);
        }
    }

    /**
     * 从ES批量响应中提取失败的K线.
     */
    private function getFailedKlinesFromBulkResponse(array $response, array $originalKlines): array
    {
        $failed = [];
        foreach ($response['items'] as $i => $item) {
            if (isset($item['update']['error'])) {
                if (isset($originalKlines[$i])) {
                    $failed[] = $originalKlines[$i];
                }
            }
        }
        return $failed;
    }

    /**
     * 将失败的K线重新投递到异步队列.
     */
    private function requeueFailedKlines(array $klines): void
    {
        if (empty($klines)) {
            return;
        }
        try {
            // 使用专用的重试队列
            pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value, new KlineBulkRetryJob($klines));
            $this->logger->info('Re-queued ' . count($klines) . ' failed klines for persistence.');
        } catch (\Throwable $e) {
            $this->logger->error('Failed to push kline bulk retry job: ' . $e->getMessage());
        }
    }

    /**
     * 运行周期性任务.
     */
    public function runPeriodicTasks(): void
    {
        try {
            $this->persistCompletedKlines();

            $now = time();
            if (($now - $this->lastCacheTime) >= 5) {
                $this->cacheStateToRedis();
                $this->lastCacheTime = $now;
            }
        } catch (\Throwable $e) {
            $this->logger->error("Error during periodic tasks for market type {$this->marketType}: " . $e->getMessage(), ['exception' => $e]);
        }
    }
}
