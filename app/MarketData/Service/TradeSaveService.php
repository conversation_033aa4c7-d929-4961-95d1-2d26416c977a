<?php

namespace App\MarketData\Service;

use App\Enum\MarketData\TickerSyncKey;
use App\Logger\LoggerFactory;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

class TradeSaveService
{
    #[Inject]
    public Redis $redis;

    public LoggerFactory $loggerFactory;

    /**
     * 批量最新价格记录到redis中
     * @param array $prices
     * @return bool
     */
    public function batchPriceSyncRedis(array $prices): bool
    {
        if (empty($prices)) {
            return true;
        }

        try {
            // 预处理：同一币种只保留最新时间戳的数据
            $latestPrices = $this->getLatestPricesOnly($prices);
            if (empty($latestPrices)) {
                return true;
            }

            // 使用pipeline批量处理
            $pipe = $this->redis->pipeline();
            $operationCount = 0;

            foreach ($latestPrices as $data) {
                $key = str_replace(["{currency_id}", "{market_type}"],
                    [$data['currency_id'], $data['market_type']],
                    TickerSyncKey::TRADE_SYNC_KEY_OUT->value);

                $pipe->hMSet($key, [
                    'price' => $data['price'],
                    'timestamp' => $data['timestamp']
                ]);
                $pipe->expire($key, 3600);

                $operationCount++;

                // 分批执行，避免pipeline过大
                if ($operationCount >= 100) {
                    $pipe->exec();
                    $pipe = $this->redis->pipeline();
                    $operationCount = 0;
                }
            }

            // 执行剩余操作
            if ($operationCount > 0) {
                $result = $pipe->exec();
                return $result !== false;
            }

            return true;

        } catch (\Throwable $t) {
            $this->loggerFactory->get(self::class)->error("批量价格数据写入异常: {$t->getMessage()}");
            return false;
        }
    }

    /**
     * 获取每个币种的最新价格数据（按时间戳）
     * @param array $prices
     * @return array
     */
    private function getLatestPricesOnly(array $prices): array
    {
        $latestData = [];

        foreach ($prices as $priceArray) {
            if (!is_array($priceArray) || !isset($priceArray['currency_id'], $priceArray['price'])) {
                continue;
            }

            $currencyId = (int)$priceArray['currency_id'];
            $price = (float)$priceArray['price'];
            $marketType = (int)($priceArray['market_type'] ?? 1);
            $timestamp = (int)($priceArray['timestamp'] ?? time());

            if ($currencyId <= 0 || $price <= 0) {
                continue;
            }

            // 使用currency_id + market_type作为唯一键
            $uniqueKey = "{$currencyId}_{$marketType}";

            // 只保留时间戳最大的数据
            if (!isset($latestData[$uniqueKey]) || $latestData[$uniqueKey]['timestamp'] < $timestamp) {
                $latestData[$uniqueKey] = [
                    'currency_id' => $currencyId,
                    'market_type' => $marketType,
                    'price' => $price,
                    'timestamp' => $timestamp
                ];
            }
        }

        return array_values($latestData);
    }

    /**
     * 获取币种的最新价格
     * @param int $currencyId
     * @param int $marketType
     * @return array|null
     */
    public function getLatestPrice(int $currencyId, int $marketType = 1): ?array
    {
        try {
            $key = str_replace(["{currency_id}", "{market_type}"],
                [$currencyId, $marketType],
                TickerSyncKey::TRADE_SYNC_KEY_OUT->value);

            $priceData = $this->redis->hGetAll($key);

            if (!empty($priceData) && isset($priceData['price'])) {
                return [
                    'price' => (float)$priceData['price'],
                    'timestamp' => (int)($priceData['timestamp'] ?? 0)
                ];
            }

            return null;

        } catch (\Throwable $t) {
            $this->loggerFactory->get(self::class)->error("获取最新价格异常: {$t->getMessage()}");
            return null;
        }
    }
}
