<?php

/**
 * TickerSaveService.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/24
 * Website:algoquant.org
 */

namespace App\MarketData\Service;

use App\Enum\MarketData\TickerSyncKey;
use App\Logger\LoggerFactory;
use App\model\currency\CurrencyTicker;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

class TickerSaveService
{
    #[Inject]
    public Redis $redis;

    public LoggerFactory $loggerFactory;

    /**
     * ticker 数据记录到redis中
     * @param CurrencyTicker $ticker
     * @param string|null $expireAt 指定过期时间点，格式如 '08:00' 表示每天早上8点过期，null表示不设置过期
     * @return bool|\Redis
     */
    public function tickerSyncRedis(CurrencyTicker $ticker, ?string $expireAt = null): bool|\Redis
    {
        try {
            $key = str_replace(["{currency_id}","{market_type}"], [$ticker->getCurrencyId(),$ticker->getMarketType()],TickerSyncKey::TICKER_SYNC_KEY->value);
            $result = $this->redis->hMset($key,$ticker->toArray());
            
            if ($result && $expireAt !== null) {
                $expireTimestamp = $this->calculateExpireTimestamp($expireAt);
                if ($expireTimestamp > time()) {
                    $this->redis->expireAt($key, $expireTimestamp);
                }
            }
            
            return $result;
        }catch (\Throwable $t){
            $this->loggerFactory->get(self::class)->error("ticker 数据写入异常 : {$t->getMessage()}");
            return false;
        }
    }

    /**
     * 计算指定时间点的时间戳
     * @param string $timeStr 时间字符串，如 '08:00'
     * @return int
     */
    private function calculateExpireTimestamp(string $timeStr): int
    {
        $today = date('Y-m-d');
        $expireDateTime = $today . ' ' . $timeStr . ':00';
        $expireTimestamp = strtotime($expireDateTime);
        
        // 如果指定时间已经过了今天，则设置为明天的该时间
        if ($expireTimestamp <= time()) {
            $expireTimestamp = strtotime('+1 day', $expireTimestamp);
        }
        
        return $expireTimestamp;
    }

    /**
     * 批量ticker数据记录到redis中
     * @param array $tickers
     * @return bool
     */
    public function batchTickerSyncRedis(array $tickers): bool
    {
        if (empty($tickers)) {
            return true;
        }

        try {
            $pipe = $this->redis->pipeline();
            foreach ($tickers as $tickerArray) {
                if (is_array($tickerArray) && isset($tickerArray['currency_id'], $tickerArray['market_type'])) {
                    $key = str_replace(["{currency_id}","{market_type}"],
                        [$tickerArray['currency_id'], $tickerArray['market_type']],
                        TickerSyncKey::TICKER_SYNC_KEY_OUT->value);
                    $pipe->hMset($key, $tickerArray);
                }
            }
            $result = $pipe->exec();
            return $result !== false;
        } catch (\Throwable $t) {
            $this->loggerFactory->get(self::class)->error("批量ticker数据写入异常 : {$t->getMessage()}");
            return false;
        }
    }
}