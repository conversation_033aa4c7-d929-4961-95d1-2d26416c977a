<?php

declare(strict_types=1);

/**
 * TradeMessageAggregatorProcess.php
 * Trade消息聚合进程 - 订阅内部和外部trade数据，按优先级推送
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/1
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

 // TODO 控盘的时候暂停外部数据的推送

namespace App\MarketData\Process\Websocket;

use App\Enum\MarketData\TradeSubscribeKey;
use App\Enum\MarketType;
use App\Model\Currency\Currency;
use App\Model\WebsocketData\MarketData\TradeMessageFormat;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\AbstractProcess;
use Hyperf\Process\Annotation\Process;
use Hyperf\Redis\Redis;
use Swoole\Coroutine;
use Swoole\Coroutine\Channel;

#[Process(name: 'trade-message-aggregator')]
class TradeMessageAggregatorProcess extends AbstractProcess
{
    public int $nums = 1;
    public bool $enableCoroutine = true;

    #[Inject]
    protected Redis $redis;
    
    #[Inject]
    protected TradeMessageFormat $tradeMessageFormat;

    #[Inject] 
    protected \App\Service\WebSocket\MarketDataService $marketDataService;

    /**
     * 内部成交数据队列
     */
    protected Channel $innerTradeQueue;

    /**
     * 外部成交数据队列
     */
    protected Channel $outerTradeQueue;

    /**
     * 货币ID到Symbol的映射
     * [currency_id => symbol]
     */
    protected array $currencySymbolMap = [];

    /**
     * 最近内部成交记录 [symbol => timestamp]
     * 用于外部数据的优先级判断
     */
    protected array $recentInnerTrades = [];

    /**
     * 聚合缓冲区 - 0.5秒内的成交数据聚合
     * 结构: [currency_id:market_type:price:out_trade => aggregated_data]
     */
    protected array $aggregationBuffer = [];

    /**
     * 内部成交数据有效期（秒）
     */
    protected int $innerTradeValidPeriod = 5;

    /**
     * 队列大小限制
     */
    protected int $queueSize = 5000;

    /**
     * 实时推送标志（移除批次推送，改为实时推送）
     */
    protected bool $realTimePush = true;

    /**
     * 运行标志
     */
    protected bool $running = true;

    public function handle(): void
    {
        echo "Trade Message Aggregator Process started\n";
        
        try {
            $this->loadCurrencyData();
            $this->initializeQueues();
            $this->startWorkerCoroutines();
            $this->startAggregationTimer();
            $this->startRedisSubscription();

            while (true){
                Coroutine::sleep(1);
            }
        } catch (\Throwable $e) {
            echo "Trade aggregator startup error: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 初始化队列
     */
    protected function initializeQueues(): void
    {
        $this->innerTradeQueue = new Channel($this->queueSize);
        $this->outerTradeQueue = new Channel($this->queueSize);
    }

    /**
     * 启动工作协程
     */
    protected function startWorkerCoroutines(): void
    {
        Coroutine::create(function() {
            $this->processTradeQueuesUnified();
        });
    }

    /**
     * 启动聚合定时器（500ms间隔）
     */
    protected function startAggregationTimer(): void
    {
        Coroutine::create(function() {
            while ($this->running) {
                Coroutine::sleep(0.5); // 500ms
                $this->flushAggregationBuffer();
            }
        });
    }

    /**
     * 启动Redis订阅
     */
    protected function startRedisSubscription(): void
    {
        // 使用一个连接订阅所有market的成交数据
        Coroutine::create(function() {
            $this->subscribeAllMarketTrades();
        });
    }

    /**
     * 订阅所有市场的成交数据（优化版：一个连接订阅多个channel）
     */
    protected function subscribeAllMarketTrades(): void
    {
        $channels = [
            TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::CRYPTO->value),
            TradeSubscribeKey::getCryptoTradeChannelKey(MarketType::MARGIN->value)
        ];
        
        while ($this->running) {
            try {
                $this->redis->subscribe($channels, function($redis, $channel, $message) {
                    $this->handleTradeMessage($channel, $message);
                });
            } catch (\Throwable $e) {
                Coroutine::sleep(2); // 等待2秒后重试
            }
        }
    }

    /**
     * 处理成交消息
     */
    protected function handleTradeMessage(string $channel, string $message): void
    {
        try {
            $data = json_decode($message, true);
            if (!$data || !isset($data['currency_id'], $data['market_type'], $data['out_trade'])) {
                return;
            }

            $currencyId = $data['currency_id'];
            $marketType = $data['market_type'];
            $symbol = $this->currencySymbolMap[$currencyId] ?? null;
            
            if (!$symbol) {
                return;
            }

            $subscriptionKey = "ws:trade:{$marketType}:{$symbol}";
            try {
                $subscriberCount = $this->redis->sCard($subscriptionKey);
                if ($subscriberCount === 0) {
                    return;
                }
            } catch (\Throwable $e) {
                return;
            }

            $tradeData = [
                'currency_id' => $currencyId,
                'market_type' => $marketType,
                'price' => (float)$data['price'],
                'quantity' => (float)$data['quantity'],
                'trade_time' => $data['trade_time'],
                'out_trade' => intval($data['out_trade']),
                'received_at' => microtime(true)
            ];

            $this->addToAggregationBuffer($tradeData);
            
        } catch (\Throwable $e) {
            echo "Trade message handling error: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 添加数据到聚合缓冲区
     */
    protected function addToAggregationBuffer(array $tradeData): void
    {
        $key = sprintf(
            '%d:%d:%.8f:%d',
            $tradeData['currency_id'],
            $tradeData['market_type'],
            $tradeData['price'],
            $tradeData['out_trade']
        );

        if (isset($this->aggregationBuffer[$key])) {
            $this->aggregationBuffer[$key]['quantity'] += $tradeData['quantity'];
            $this->aggregationBuffer[$key]['trade_time'] = max(
                $this->aggregationBuffer[$key]['trade_time'],
                $tradeData['trade_time']
            );
        } else {
            $this->aggregationBuffer[$key] = $tradeData;
        }
    }

    /**
     * 刷新聚合缓冲区到队列
     */
    protected function flushAggregationBuffer(): void
    {
        if (empty($this->aggregationBuffer)) {
            return;
        }

        foreach ($this->aggregationBuffer as $key => $aggregatedData) {
            $queueData = [
                'currency_id' => $aggregatedData['currency_id'],
                'market_type' => $aggregatedData['market_type'],
                'price' => $aggregatedData['price'],
                'quantity' => $aggregatedData['quantity'],
                'trade_time' => $aggregatedData['trade_time']
            ];

            if ($aggregatedData['out_trade'] === 0) {
                $this->enqueueWithFallback($this->innerTradeQueue, $queueData, 'inner');
            } else {
                $this->enqueueWithFallback($this->outerTradeQueue, $queueData, 'outer');
            }
        }

        $this->aggregationBuffer = [];
    }

    /**
     * 强制入队，必要时丢弃旧数据
     */
    protected function enqueueWithFallback(Channel $queue, array $data, string $queueType): void
    {
        $maxRetries = 3;
        $retryCount = 0;
        
        while ($retryCount < $maxRetries) {
            if ($queue->push($data, 0.001)) {
                return;
            }

            $droppedMessage = $queue->pop(0.001);
            if ($droppedMessage === false) {
                Coroutine::sleep(0.001);
            }
            
            $retryCount++;
        }
    }

    /**
     * 统一处理成交队列 - 同时监听内部和外部数据
     */
    protected function processTradeQueuesUnified(): void
    {
        while ($this->running) {
            try {
                // 同时检查两个队列（非阻塞）
                $innerTrade = $this->innerTradeQueue->pop(0.001); // 1ms超时
                $outerTrade = $this->outerTradeQueue->pop(0.001); // 1ms超时

                if ($innerTrade !== false) {
                    $this->processInnerTrade($innerTrade);

                    if ($outerTrade !== false) {
                        $currencyId = $outerTrade['currency_id'] ?? 0;
                        $symbol = $this->currencySymbolMap[$currencyId] ?? 'unknown';
                    }
                    
                } elseif ($outerTrade !== false) {
                    $this->processOuterTrade($outerTrade);
                    
                } else {
                    Coroutine::sleep(0.01); // 10ms
                }
                
            } catch (\Throwable $e) {
                Coroutine::sleep(0.1);
            }
        }
    }

    /**
     * 处理内部成交数据（高优先级）
     */
    protected function processInnerTrade(array $trade): void
    {
        $currencyId = $trade['currency_id'] ?? 0;
        $symbol = $this->currencySymbolMap[$currencyId] ?? null;
        
        if ($symbol) {
            $this->recentInnerTrades[$symbol] = time();

            $this->pushTradeImmediately($symbol, [$trade], 'INNER');
        }
    }

    /**
     * 处理外部成交数据（低优先级，需要检查内部数据）
     */
    protected function processOuterTrade(array $trade): void
    {
        $currencyId = $trade['currency_id'] ?? 0;
        $symbol = $this->currencySymbolMap[$currencyId] ?? null;
        
        if ($symbol && $this->shouldPushOuterTrade($symbol)) {
            $this->pushTradeImmediately($symbol, [$trade], 'OUTER');
        }
    }

    /**
     * 判断是否应该推送外部成交数据
     */
    protected function shouldPushOuterTrade(string $symbol): bool
    {
        $lastInnerTime = $this->recentInnerTrades[$symbol] ?? 0;
        $currentTime = time();
        return ($currentTime - $lastInnerTime) > $this->innerTradeValidPeriod;
    }

    /**
     * 立即推送成交数据
     */
    protected function pushTradeImmediately(string $symbol, array $trades, string $source = 'UNKNOWN'): void
    {
        if (empty($trades) || empty($symbol)) {
            return;
        }
        
        try {
            $marketType = $trades[0]['market_type'] ?? 1;
            
            $messageData = [
                'type' => 'trade',
                'symbol' => $symbol,
                'market_type' => $marketType,
                'data' => $trades,
                'count' => count($trades),
                'timestamp' => time() * 1000
            ];
            
            $this->tradeMessageFormat->fill($messageData);
            $binaryMessage = $this->tradeMessageFormat->toBinary();
            
            $this->marketDataService->pushTradeData($marketType, $symbol, $binaryMessage);
        } catch (\Throwable $e) {
            echo "Real-time trade push failed for {$symbol} ({$source}): " . $e->getMessage() . "\n";
        }
    }

    /**
     * 加载货币数据映射
     */
    protected function loadCurrencyData(): void
    {
        try {
            $currencies = Currency::query()
                ->where('status', 1)
                ->whereIn('market_type', [MarketType::CRYPTO->value, MarketType::MARGIN->value])
                ->get(['id', 'symbol']);

            $this->currencySymbolMap = [];
            foreach ($currencies as $currency) {
                $this->currencySymbolMap[$currency->getId()] = strtoupper($currency->getSymbol());
            }

        } catch (\Throwable $e) {
            echo "Failed to load currency data: " . $e->getMessage() . "\n";
        }
    }



    /**
     * 检查是否启用
     */
    public function isEnable($server): bool
    {
        return (bool)env('API_SERVER', false);
    }

    /**
     * 进程停止时的清理
     */
    protected function cleanup(): void
    {
        $this->running = false;
        
        if ($this->innerTradeQueue) {
            $this->innerTradeQueue->close();
        }
        
        if ($this->outerTradeQueue) {
            $this->outerTradeQueue->close();
        }
        
        echo "Trade Message Aggregator Process cleaned up\n";
    }
}