<?php

declare(strict_types=1);

/**
 * TickerMessageAggregatorProcess.php
 * Ticker消息聚合进程 - 订阅内部和外部ticker数据，智能聚合并批量推送
 * Author    chenmaq (<EMAIL>)
 * Version   2.0
 * Date      2025/7/1
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\MarketData\Process\Websocket;

use App\Enum\MarketData\TickerSubscribeKey;
use App\Model\WebsocketData\MarketData\TickerMessageFormat;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\AbstractProcess;
use Hyperf\Process\Annotation\Process;
use Hyperf\Redis\Redis;
use Swoole\Coroutine;
use Swoole\Coroutine\Channel;
use Swoole\Timer;

#[Process(name: 'ticker-message-aggregator')]
class TickerMessageAggregatorProcess extends AbstractProcess
{
    public int $nums = 1;
    public bool $enableCoroutine = true;

    #[Inject]
    protected Redis $redis;
    
    #[Inject]
    protected TickerMessageFormat $tickerMessageFormat;

    #[Inject] 
    protected \App\Service\WebSocket\MarketDataService $marketDataService;

    /**
     * 主数据存储
     * 结构: [currency_id:market_type => ticker_info]
     */
    protected array $tickerData = [];

    /**
     * 上次重置检查时间
     */
    protected int $lastResetCheck = 0;

    /**
     * 消息队列 - 用于异步处理ticker消息
     */
    protected Channel $messageQueue;

    /**
     * 消息队列大小限制
     */
    protected int $queueSize = 5;

    public function handle(): void
    {
        // 初始化消息队列
        $this->messageQueue = new Channel($this->queueSize);
        
        // 启动消息订阅协程（负责接收消息并入队）
        go(function() {
            $this->subscribeMessage();
        });
        
        // 启动消息处理协程（负责消费队列中的消息）
        go(function() {
            $this->processMessageQueue();
        });
        
        // 启动推送定时器（1秒间隔）
        Timer::tick(1000, function() {
            $this->pushDirtyData();
        });
        
        // 启动重置检查定时器（每分钟检查一次8点重置）
        Timer::tick(60000, function() {
            $this->checkAndResetDailyData();
        });
        
        // 保持进程运行
        while (true) {
            Coroutine::sleep(1);
        }
    }

    /**
     * 订阅Redis消息
     */
    protected function subscribeMessage(): void
    {
        try {
            $channels = [
                TickerSubscribeKey::INNER_TICKER_MESSAGE->value,
                TickerSubscribeKey::OUT_TICKER_MESSAGE->value
            ];
            
            $this->redis->subscribe($channels, function($redis, $channel, $message) {
                $this->handleMessage($channel, $message);
            });
            
        } catch (\RedisException $e) {
            error_log("Redis ticker subscription error: " . $e->getMessage());
            Coroutine::sleep(1);
            $this->redis = redis();
            $this->subscribeMessage();
        }
    }

    /**
     * 处理接收到的消息（快速入队，避免阻塞Redis接收）
     */
    protected function handleMessage(string $channel, string $message): void
    {
        try {
            $data = json_decode($message, true);
            if (!$data || !isset($data['currency_id'], $data['market_type'])) {
                return;
            }

            $messagePacket = [
                'channel' => $channel,
                'data' => $data,
                'received_at' => microtime(true)
            ];
        
            $this->forceEnqueue($messagePacket, $data);
            
        } catch (\Throwable $e) {
            error_log("Ticker message handling error: " . $e->getMessage());
        }
    }

    /**
     * 处理消息队列中的消息（在独立协程中运行）
     */
    protected function processMessageQueue(): void
    {
        while (true) {
            try {
                $messagePacket = $this->messageQueue->pop(1.0);
                
                if ($messagePacket === false) {
                    continue;
                }
                
                $this->processTickerMessage($messagePacket);
                
            } catch (\Throwable $e) {
                error_log("Message queue processing error: " . $e->getMessage());
                Coroutine::sleep(0.1);
            }
        }
    }

    /**
     * 处理单个ticker消息（原handleMessage的核心逻辑）
     */
    protected function processTickerMessage(array $messagePacket): void
    {
        $channel = $messagePacket['channel'];
        $data = $messagePacket['data'];
        
        $marketType = $data['market_type'] ?? 0;
        $subscriptionKey = "ws:ticker:{$marketType}";
        
        try {
            $subscriberCount = $this->redis->sCard($subscriptionKey);
            if ($subscriberCount === 0) {
                return;
            }
        } catch (\Throwable $e) {
            error_log("Failed to check ticker subscribers for market {$marketType}: " . $e->getMessage());
        }
        
        $key = $data['currency_id'] . ':' . $data['market_type'];
        $isInner = ($channel === TickerSubscribeKey::INNER_TICKER_MESSAGE->value);
        
        $this->updateTickerData($key, $isInner, $data);
        $this->aggregateData($key);
    }

    /**
     * 强制入队新数据（ticker数据激进清理队列）
     */
    protected function forceEnqueue(array $messagePacket, array $data): void
    {
        $isInner = ($messagePacket['channel'] === TickerSubscribeKey::INNER_TICKER_MESSAGE->value);
        
        if ($isInner) {
            // 内部ticker数据优先级最高，清空队列确保立即处理
            while (!$this->messageQueue->isEmpty()) {
                $this->messageQueue->pop(0.001);
            }
        } else {
            // 外部ticker数据，只保留最新3条，清理老数据
            $queueLength = $this->messageQueue->length();
            if ($queueLength > 2) {
                $dropCount = $queueLength - 2;
                for ($i = 0; $i < $dropCount; $i++) {
                    $this->messageQueue->pop(0.001);
                }
            }
        }
        
        // 强制入队新消息
        $maxRetries = 5;
        $retryCount = 0;
        
        while ($retryCount < $maxRetries) {
            if ($this->messageQueue->push($messagePacket, 0.001)) {
                return;
            }
            
            // 如果还是失败，继续清理队列
            $this->messageQueue->pop(0.001);
            $retryCount++;
        }
    }



    /**
     * 更新ticker数据
     */
    private function updateTickerData(string $key, bool $isInner, array $data): void
    {
        if (!isset($this->tickerData[$key])) {
            $this->tickerData[$key] = [
                'inner' => [
                    'data' => [],
                    'timestamp' => 0,
                    'reset_period' => 0,
                    'valid' => false
                ],
                'outer' => [
                    'data' => [],
                    'timestamp' => 0
                ],
                'aggregated' => [],
                'last_push' => 0,
                'has_inner' => false
            ];
        }

        $timestamp = time();
        
        if ($isInner) {
            $current8am = $this->get8amTimestamp(time() * 1000);
            $this->tickerData[$key]['inner'] = [
                'data' => $data,
                'timestamp' => $timestamp,
                'reset_period' => $current8am,
                'valid' => true
            ];
            $this->tickerData[$key]['has_inner'] = true;
        } else {
            $this->tickerData[$key]['outer'] = [
                'data' => $data,
                'timestamp' => $timestamp
            ];
        }
    }

    /**
     * 聚合ticker数据
     */
    private function aggregateData(string $key): void
    {
        $ticker = &$this->tickerData[$key];
        $inner = $ticker['inner']['data'] ?? [];
        $outer = $ticker['outer']['data'] ?? [];
        
        // 检查内部数据是否在有效期内
        $innerValid = $this->isInnerDataValid($ticker['inner'] ?? []);
        
        $aggregated = [];
        
        if ($innerValid && !empty($inner)) {
            // 有有效内部数据：使用内部数据的最高价、最低价
            $aggregated = [
                'currency_id' => $inner['currency_id'],
                'market_type' => $inner['market_type'],
                'high_price' => (float)$inner['high_price'],
                'low_price' => (float)$inner['low_price'],
                'volume' => (float)($inner['volume'] ?? 0),
            ];
            
            // 成交量累加外部数据
            if (!empty($outer)) {
                $aggregated['volume'] += (float)($outer['volume'] ?? 0);
                // 使用外部数据的最新价格信息
                $aggregated['last_price'] = (float)$outer['last_price'];
                $aggregated['last_qty'] = (float)($outer['last_qty'] ?? 0);
                $aggregated['open_price'] = (float)$outer['open_price'];
                $aggregated['pre_close_price'] = (float)$outer['pre_close_price'];
                $aggregated['timestamp'] = $outer['timestamp'] ?? time() * 1000;
            } else {
                // 没有外部数据，使用内部数据
                $aggregated['last_price'] = (float)$inner['last_price'];
                $aggregated['last_qty'] = (float)($inner['last_qty'] ?? 0);
                $aggregated['open_price'] = (float)$inner['open_price'];
                $aggregated['pre_close_price'] = (float)$inner['pre_close_price'];
                $aggregated['timestamp'] = $inner['timestamp'] ?? time() * 1000;
            }
        } else {
            // 无有效内部数据，仅使用外部数据
            if (!empty($outer)) {
                $aggregated = [
                    'currency_id' => $outer['currency_id'],
                    'market_type' => $outer['market_type'],
                    'open_price' => (float)$outer['open_price'],
                    'high_price' => (float)$outer['high_price'],
                    'low_price' => (float)$outer['low_price'],
                    'pre_close_price' => (float)$outer['pre_close_price'],
                    'last_price' => (float)$outer['last_price'],
                    'last_qty' => (float)($outer['last_qty'] ?? 0),
                    'volume' => (float)($outer['volume'] ?? 0),
                    'timestamp' => $outer['timestamp'] ?? time() * 1000,
                ];
            }
        }
        
        // 重新计算价格变化和百分比
        if (!empty($aggregated) && isset($aggregated['last_price'], $aggregated['pre_close_price']) && $aggregated['pre_close_price'] > 0) {
            $aggregated['price_change'] = $aggregated['last_price'] - $aggregated['pre_close_price'];
            $aggregated['price_changeP'] = ($aggregated['price_change'] / $aggregated['pre_close_price']) * 100;
        } else {
            $aggregated['price_change'] = 0.0;
            $aggregated['price_changeP'] = 0.0;
        }
        
        if (!empty($aggregated)) {
            $ticker['aggregated'] = $aggregated;
        }
    }

    /**
     * 检查并重置每日数据（基于8点周期）
     */
    private function checkAndResetDailyData(): void
    {
        $current8am = $this->get8amTimestamp(time() * 1000);
        $resetCount = 0;
        
        foreach ($this->tickerData as $key => &$ticker) {
            $innerResetPeriod = $ticker['inner']['reset_period'] ?? 0;
            
            if ($current8am !== $innerResetPeriod) {
                $ticker['inner'] = [
                    'data' => [],
                    'timestamp' => 0,
                    'reset_period' => $current8am,
                    'valid' => false
                ];
                $ticker['has_inner'] = false;
                
                $this->aggregateData($key);
                $resetCount++;
            }
        }
        
        if ($resetCount > 0) {
            echo "Reset {$resetCount} ticker data for new 8AM period\n";
        }
    }

    /**
     * 推送ticker数据
     */
    private function pushDirtyData(): void
    {
        $pushData = [];
        
        foreach ($this->tickerData as $key => &$ticker) {
            if (!empty($ticker['aggregated'])) {
                if ($ticker['has_inner'] && $this->isInnerDataValid($ticker['inner'])) {
                    $pushData[] = $ticker['aggregated'];
                    $ticker['last_push'] = time();
                } elseif (!$ticker['has_inner'] && !empty($ticker['outer']['data'])) {
                    $pushData[] = $ticker['aggregated'];
                    $ticker['last_push'] = time();
                }
            }
        }
        
        if (empty($pushData)) {
            return;
        }
        
        $groupedByMarket = $this->groupTickersByMarketType($pushData);
        
        foreach ($groupedByMarket as $marketType => $tickers) {
            $this->pushTickersForMarketType($marketType, $tickers);
        }
    }
    
    /**
     * 按市场类型分组ticker数据
     */
    private function groupTickersByMarketType(array $tickers): array
    {
        $grouped = [];
        
        foreach ($tickers as $ticker) {
            $marketType = $ticker['market_type'] ?? 0;
            if (!isset($grouped[$marketType])) {
                $grouped[$marketType] = [];
            }
            $grouped[$marketType][] = $ticker;
        }
        
        return $grouped;
    }
    
    /**
     * 推送特定市场类型的ticker数据
     */
    private function pushTickersForMarketType(int $marketType, array $tickers): void
    {
        if (empty($tickers)) {
            return;
        }
        
        $subscriptionKey = "ws:ticker:{$marketType}";
        try {
            $subscriberCount = $this->redis->sCard($subscriptionKey);
            if ($subscriberCount === 0) {
                return;
            }
        } catch (\Throwable $e) {
            error_log("Failed to check ticker subscribers before push for market {$marketType}: " . $e->getMessage());
        }
        
        $batches = array_chunk($tickers, 50);
        
        foreach ($batches as $batch) {
            try {
                $messageData = [
                    'type' => 'ticker',
                    'market_type' => $marketType,
                    'data' => $batch,
                    'count' => count($batch),
                    'timestamp' => time() * 1000
                ];
                $this->tickerMessageFormat->fill($messageData);
                $binaryMessage = $this->tickerMessageFormat->toBinary();
                $this->marketDataService->pushTickerData($marketType, $binaryMessage);
                
            } catch (\Throwable $e) {
                error_log("Ticker push failed for market {$marketType}: " . $e->getMessage());
            }
        }
    }

    /**
     * 获取指定时间戳对应的当天早上8点的时间戳（秒）
     */
    private function get8amTimestamp(int $timestampMs): int
    {
        $timestampSec = intval($timestampMs / 1000);
        $date = date('Y-m-d', $timestampSec);
        return strtotime($date . ' 08:00:00');
    }

    /**
     * 检查内部数据是否在有效期内
     */
    private function isInnerDataValid(array $innerData): bool
    {
        if (empty($innerData['data']) || !$innerData['valid']) {
            return false;
        }
        
        $current8am = $this->get8amTimestamp(time() * 1000);
        $dataResetPeriod = $innerData['reset_period'] ?? 0;
        
        return $current8am === $dataResetPeriod;
    }

    public function isEnable($server): bool
    {
        return (bool)env('API_SERVER', false);
    }
}