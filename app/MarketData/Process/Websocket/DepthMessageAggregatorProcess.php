<?php

declare(strict_types=1);

/**
 * DepthMessageAggregatorProcess.php
 * 深度数据聚合进程 - 订阅现货和合约深度数据，实时推送给前端
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/1
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\MarketData\Process\Websocket;

use App\Enum\MatchEngine\MatchEngineDepthSendKey;
use App\Enum\MarketType;
use App\Model\WebsocketData\MarketData\DepthMessageFormat;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\AbstractProcess;
use Hyperf\Process\Annotation\Process;
use Hyperf\Redis\Redis;
use Swoole\Coroutine;

#[Process(name: 'depth-message-aggregator')]
class DepthMessageAggregatorProcess extends AbstractProcess
{
    public int $nums = 1;
    public bool $enableCoroutine = true;

    #[Inject]
    protected Redis $redis;

    #[Inject] 
    protected \App\Service\WebSocket\MarketDataService $marketDataService;

    #[Inject]
    protected DepthMessageFormat $depthMessageFormat;



    /**
     * 运行标志
     */
    protected bool $running = true;

    public function handle(): void
    {
        try {
            $this->startRedisSubscription();
            
            // 保持进程运行
            while ($this->running) {
                Coroutine::sleep(1);
            }
        } catch (\Throwable $e) {
            error_log("Depth aggregator startup error: " . $e->getMessage());
        }
    }

    /**
     * 启动Redis订阅
     */
    protected function startRedisSubscription(): void
    {
        Coroutine::create(function() {
            $this->subscribeDepthChannels();
        });
    }

    /**
     * 订阅深度数据频道
     */
    protected function subscribeDepthChannels(): void
    {
        $channels = [
            MatchEngineDepthSendKey::SPOT_PUBLIC_DEPTH->value,
            MatchEngineDepthSendKey::MARGIN_PUBLIC_DEPTH->value
        ];
        
        while ($this->running) {
            try {
                $this->redis->subscribe($channels, function($redis, $channel, $message) {
                    $this->handleDepthMessage($channel, $message);
                });
            } catch (\Throwable $e) {
                error_log("Redis depth subscription error: " . $e->getMessage());
                Coroutine::sleep(2);
            }
        }
    }

    /**
     * 处理深度消息
     */
    protected function handleDepthMessage(string $channel, string $message): void
    {
        try {
            $data = json_decode($message, true);
            if (!$data || !isset($data['s'], $data['bids'], $data['asks'], $data['market_type'])) {
                return;
            }

            $symbol = strtoupper($data['s']);
            $marketType = (int)$data['market_type'];
            
            // 检查是否有订阅者
            $subscriptionKey = "ws:depth:{$marketType}:{$symbol}";
            try {
                $subscriberCount = $this->redis->sCard($subscriptionKey);
                if ($subscriberCount === 0) {
                    return;
                }
            } catch (\Throwable $e) {
                return;
            }

            $this->pushDepthData($symbol, $marketType, $data);
            
        } catch (\Throwable $e) {
            error_log("Depth message handling error: " . $e->getMessage());
        }
    }



    /**
     * 推送深度数据
     */
    protected function pushDepthData(string $symbol, int $marketType, array $data): void
    {
        try {
            // 直接使用原始数据，已经是正确格式
            $this->depthMessageFormat->fill($data);
            $this->marketDataService->pushDepthData($marketType, $symbol, $this->depthMessageFormat->toBinary());
            
        } catch (\Throwable $e) {
            error_log("Depth push failed for {$symbol}: " . $e->getMessage());
        }
    }



    /**
     * 检查是否启用
     */
    public function isEnable($server): bool
    {
        return (bool)env('API_SERVER', false);
    }

    /**
     * 进程停止时的清理
     */
    protected function cleanup(): void
    {
        $this->running = false;
    }
}