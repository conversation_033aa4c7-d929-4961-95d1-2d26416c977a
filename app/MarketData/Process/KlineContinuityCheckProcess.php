<?php

declare(strict_types=1);

namespace App\MarketData\Process;

use App\Enum\MarketType;
use App\Enum\ProcessCmdKey;
use App\MarketData\Service\CryptoKlineSync;
use App\Model\Currency\Currency;
use App\Process\BaseProcess;
use Hyperf\Process\Annotation\Process;
use Psr\Container\ContainerInterface;
use Swoole\Coroutine;
use Swoole\Timer;

#[Process(name: "kline-continuity-check")]
class KlineContinuityCheckProcess extends BaseProcess
{
    public bool $enableCoroutine = true;
    public int $nums = 1;
    public string $cmd_key = '';

    private CryptoKlineSync $klineSync;
    private array $currencies = [];
    private array $periods = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '1d', '1w', '1M'];
    private int $mainTimerId = 0;
    private array $lastCheckTimes = [];
    private array $periodMilliseconds = [
        '1m' => 60 * 1000,
        '3m' => 3 * 60 * 1000,
        '5m' => 5 * 60 * 1000,
        '15m' => 15 * 60 * 1000,
        '30m' => 30 * 60 * 1000,
        '1h' => 60 * 60 * 1000,
        '2h' => 2 * 60 * 60 * 1000,
        '4h' => 4 * 60 * 60 * 1000,
        '1d' => 24 * 60 * 60 * 1000,
        '1w' => 7 * 24 * 60 * 60 * 1000,
        '1M' => 30 * 24 * 60 * 60 * 1000,
    ];

    public function __construct(ContainerInterface $container)
    {
        $this->cmd_key = ProcessCmdKey::KLINE_CONTINUITY_CHECK_CMD->value ?? 'kline_continuity_check';
        parent::__construct($container);
    }

    public function handle(): void
    {
        parent::handle();
    }

    public function runBusinessLogic(): void
    {
        try {
            $this->klineSync = $this->container->get(CryptoKlineSync::class);
            $this->loadActiveCurrencies();

            if (empty($this->currencies)) {
                return;
            }
            
            //暂停近7天的数据检查，开发环境
            //$this->performInitialContinuityCheck();
            $this->startMainTimer();

            while (true) {
                Coroutine::sleep(60);
            }

        } catch (\Throwable $e) {
            $this->logger->get('kline-continuity')->error("Process init failed: " . $e->getMessage());
        }
    }

    public function parse_cmd(array $message): void
    {
        if (isset($message['type'])) {
            switch ($message['type']) {
                case 'reload_currencies':
                    $this->loadActiveCurrencies();
                    break;
                case 'force_check':
                    $symbol = $message['symbol'] ?? null;
                    $period = $message['period'] ?? null;
                    $marketType = $message['market_type'] ?? null;
                    $this->performForceCheck($symbol, $period, $marketType);
                    break;
            }
        }
    }

    public function isEnable($server): bool
    {
        return false;//(bool)env('MARKET_DATA_SERVER',false);
    }

    private function loadActiveCurrencies(): void
    {
        try {
            $currencies = Currency::query()
                ->where('status', 1)
                ->where('market_type', MarketType::CRYPTO->value)
                ->where(function($query) {
                    $query->where('is_spotTrade', 1)
                          ->orWhere('is_marginTrade', 1);
                })
                ->get(['id', 'symbol', 'is_spotTrade', 'is_marginTrade']);

            $this->currencies = [];
            foreach ($currencies as $currency) {
                $this->currencies[] = [
                    'id' => $currency->getId(),
                    'symbol' => $currency->getSymbol(),
                    'is_spotTrade' => $currency->getIsSpotTrade(),
                    'is_marginTrade' => $currency->getIsMarginTrade(),
                    'currency_obj' => $currency
                ];
            }

        } catch (\Throwable $e) {

        }
    }

    private function performInitialContinuityCheck(): void
    {
        foreach ($this->currencies as $currencyData) {
            foreach ($this->periods as $period) {
                try {
                    if ($currencyData['is_spotTrade']) {
                        $this->checkAndFillGaps($currencyData['symbol'], 1, $period, 7);
                    }

                    if ($currencyData['is_marginTrade']) {
                        $this->checkAndFillGaps($currencyData['symbol'], 5, $period, 7);
                    }

                    Coroutine::sleep(0.1);

                } catch (\Throwable $e) {

                }
            }
        }
    }

    private function startMainTimer(): void
    {
        foreach ($this->periods as $period) {
            $this->lastCheckTimes[$period] = 0;
        }

        $this->mainTimerId = Timer::tick(5000, function() {
            try {
                $this->checkPeriodEndpoints();
            } catch (\Throwable $e) {
                $this->logger->get('kline-continuity')->error("Timer task failed: " . $e->getMessage());
            }
        });
    }

    private function checkPeriodEndpoints(): void
    {
        $currentTime = time() * 1000;

        foreach ($this->periods as $period) {
            try {
                $periodMs = $this->periodMilliseconds[$period];
                $currentPeriodStart = intval($currentTime / $periodMs) * $periodMs;

                if ($this->lastCheckTimes[$period] < $currentPeriodStart) {
                    $this->lastCheckTimes[$period] = $currentPeriodStart;
                    $this->performPeriodicCheck($period);
                }
            } catch (\Throwable $e) {

            }
        }
    }

    private function performPeriodicCheck(string $period): void
    {
        foreach ($this->currencies as $currencyData) {
            try {
                if ($currencyData['is_spotTrade']) {
                    $this->checkAndFillRecentGaps($currencyData['symbol'], 1, $period);
                }

                if ($currencyData['is_marginTrade']) {
                    $this->checkAndFillRecentGaps($currencyData['symbol'], 5, $period);
                }

                Coroutine::sleep(0.05);

            } catch (\Throwable $e) {

            }
        }
    }

    private function checkAndFillGaps(string $symbol, int $marketType, string $period, int $days): void
    {
        try {
            $continuityResult = $this->klineSync->checkDataContinuity($symbol, $marketType, $period, $days);

            if (!empty($continuityResult['gaps'])) {
                $this->klineSync->fillDataGaps($symbol, $marketType, $period);
            }

        } catch (\Throwable $e) {

        }
    }

    private function checkAndFillRecentGaps(string $symbol, int $marketType, string $period): void
    {
        try {
            $this->klineSync->checkAndFillRecentPeriods($symbol, $marketType, $period, 2);
        } catch (\Throwable $e) {

        }
    }



    private function performForceCheck(?string $symbol = null, ?string $period = null, ?int $marketType = null): void
    {
        $currenciesToCheck = $symbol ?
            array_filter($this->currencies, fn($c) => $c['symbol'] === $symbol) :
            $this->currencies;

        $periodsToCheck = $period ? [$period] : $this->periods;

        foreach ($currenciesToCheck as $currencyData) {
            foreach ($periodsToCheck as $checkPeriod) {
                if ($marketType) {
                    if (($marketType === 1 && $currencyData['is_spotTrade']) ||
                        ($marketType === 5 && $currencyData['is_marginTrade'])) {
                        $this->checkAndFillGaps($currencyData['symbol'], $marketType, $checkPeriod, 7);
                    }
                } else {
                    if ($currencyData['is_spotTrade']) {
                        $this->checkAndFillGaps($currencyData['symbol'], 1, $checkPeriod, 7);
                    }
                    if ($currencyData['is_marginTrade']) {
                        $this->checkAndFillGaps($currencyData['symbol'], 5, $checkPeriod, 7);
                    }
                }
            }
        }
    }

    public function __destruct()
    {
        if ($this->mainTimerId > 0) {
            Timer::clear($this->mainTimerId);
        }
    }
}
