<?php

declare(strict_types=1);

/**
 * SpotMatchEngineProcess.php
 * 现货撮合引擎工作进程
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:3.0
 * Date:2025/06/29
 * Website:algoquant.org
 */

namespace App\MarketData\Process\MatchEngine;

use App\MarketData\Process\MatchEngine\Logger\MatchEngineLogger;
use App\MarketData\Service\MatchEngine\Spot\SpotCommandHandler;
use App\MarketData\Service\MatchEngine\Spot\SpotEventHandler;
use App\MarketData\Service\MatchEngine\Spot\SpotMarketMakerService;
use App\MarketData\Service\MatchEngine\Spot\SpotMatchEngineService;
use App\Process\MatchEngineBaseProcess;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\Annotation\Process;
use Psr\Container\ContainerInterface;
use Swoole\Coroutine;

#[Process(name: "spot-match-engine")]
class SpotMatchEngineProcess extends MatchEngineBaseProcess
{
    public bool $enableCoroutine = true;

    /**
     * 是否正在运行
     */
    private bool $isRunning = true;

    /**
     * 现货撮合引擎服务
     */
    private SpotMatchEngineService $spotMatchEngineService;

    /**
     * 现货事件处理器
     */
    private SpotEventHandler $spotEventHandler;

    /**
     * 现货命令处理器
     */
    private SpotCommandHandler $spotCommandHandler;

    /**
     * 现货做市机器人服务
     */
    private SpotMarketMakerService $spotMarketMakerService;

    #[Inject]
    protected ContainerInterface $container;

    public function __construct(ContainerInterface $container)
    {
        // 初始化服务层组件
        $this->initializeServices();

        // 先初始化服务以加载币种数据
        $this->spotMatchEngineService->loadSpotCurrenciesData();

        // 设置为现货市场类型
        $this->setMarketType(\App\Enum\MarketType::CRYPTO->value); // 1

        // 传递币种数据给基类
        parent::__construct($container, $this->spotMatchEngineService->getAllCurrencies());
    }

    /**
     * 初始化服务层组件
     */
    private function initializeServices(): void
    {
        try {
            // 获取撮合引擎专用日志记录器
            $logger = $this->container->get(MatchEngineLogger::class);
            
            // 设置进程信息到日志器（这里先设置默认值，后面会更新）
            MatchEngineLogger::setProcessInfo(0, 'worker');
            
            // 初始化现货撮合引擎服务
            $this->spotMatchEngineService = new SpotMatchEngineService($logger);
            
            // 初始化现货事件处理器
            $this->spotEventHandler = new SpotEventHandler($logger);
            
            // 初始化现货命令处理器
            $this->spotCommandHandler = new SpotCommandHandler($logger, $this->spotMatchEngineService);
            
            // 初始化现货做市机器人服务
            $this->spotMarketMakerService = new SpotMarketMakerService($logger);
            
        } catch (\Throwable $e) {
            $this->logError("Failed to initialize services", $e);
            throw $e;
        }
    }

    public function handle(): void
    {
        if ($this->isManagerProcess()) {
            // 管理进程：基类已经处理了主循环，这里不需要额外操作
            return;
        } else {
            $this->runAsMatchEngineWorker();
        }
    }

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }
    
    /**
     * 重写基类的工作进程处理方法
     */
    protected function handleWorkerProcess(): void
    {
        $this->runAsMatchEngineWorker();
    }

    /**
     * 作为撮合引擎工作进程运行
     */
    private function runAsMatchEngineWorker(): void
    {
        try {
            $processIndex = $this->getCurrentProcessIndex();
            $assignedCurrencies = $this->getAssignedCurrencies();

            // 设置进程索引到服务
            $this->spotMatchEngineService->setProcessIndex($processIndex);
            
            // 更新日志器的进程信息
            MatchEngineLogger::setProcessInfo($processIndex, 'worker');
            
            // 初始化撮合引擎服务
            if (!$this->spotMatchEngineService->initialize()) {
                throw new \RuntimeException("Failed to initialize spot match engine service");
            }

            // 创建市场
            $assignedCurrencyData = $this->getAssignedCurrencyData();
            $this->spotMatchEngineService->createMarkets($assignedCurrencyData);
            
            // 注册事件监听器
            $exchange = $this->spotMatchEngineService->getExchange();
            if ($exchange) {
                // 设置内部数据聚合服务到事件处理器
                $this->spotEventHandler->setCurrencyIdMap($assignedCurrencyData);
                $this->spotEventHandler->registerEventListeners($exchange);
            }
            
            // 启动命令处理协程
            if ($this->enableCoroutine) {
                $this->startCommandProcessing();
            }

            // 初始化做市机器人服务（可以传入策略配置）
//            if(is_array($assignedCurrencyData) && isset($assignedCurrencyData['BTCUSDT'])){
//                $strategiesConfig = [
//                    'ExternalIndexFollowStrategy' => [
//                        'symbol' => 'BTCUSDT',
//                        'spread_percent' => 1,
//                        'order_levels' => 20,
//                        'base_precision' => $assignedCurrencyData['BTCUSDT']['s_price_precision'],
//                        'quote_precision' => $assignedCurrencyData['BTCUSDT']['s_quantity_precision'],
//                    ],
//                ];
//                $this->spotMarketMakerService->initialize($strategiesConfig);
//                $this->spotMarketMakerService->startEventLoop();
//            }
            // 主循环
            while ($this->isRunning) {
                if ($this->enableCoroutine) {
                    Coroutine::sleep(1);
                } else {
                    sleep(1);
                }
            }

        } catch (\Throwable $e) {
            $this->logError("Failed to initialize match engine worker process", $e);
        }
    }



    /**
     * 启动命令处理协程（用于接收基类分发的命令）
     */
    private function startCommandProcessing(): void
    {
        go(function() {
            while ($this->isRunning) {
                try {
                    $this->processWorkerCommands();
                    Coroutine::sleep(0.01); // 10ms检查一次
                } catch (\Throwable $e) {
                    $this->logError("Command processing error", $e);
                    Coroutine::sleep(0.1);
                }
            }
        });
    }



    /**
     * 处理工作进程专用命令（基类会分发命令到这里）
     */
    private function processWorkerCommands(): void
    {
        // 获取基类的命令表
        $commandTable = $this->getCommandTable();
        if ($commandTable === null) {
            return;
        }
        
        $currentProcessIndex = $this->getCurrentProcessIndex();
        $processedCommands = [];
        
        // 处理命令
        foreach ($commandTable as $key => $command) {
            // 只处理目标是当前进程且未被处理的命令
            if ($command['processed'] == 0 && $command['target_process'] == $currentProcessIndex) {
                try {
                    // 直接处理命令
                    $this->handleWorkerCommand($command);
                    $processedCommands[] = $key;
                    
                } catch (\Throwable $e) {
                    $this->logError("Worker command processing error for command {$key}: " . $e->getMessage(), $e);
                    $processedCommands[] = $key; // 即使出错也要删除
                }
            }
        }
        
        // 删除已处理的命令
        foreach ($processedCommands as $key) {
            $commandTable->del($key);
        }
    }

    /**
     * 处理工作进程命令
     */
    private function handleWorkerCommand(array $command): void
    {
        $type = $command['type'];
        $data = json_decode($command['data'], true);
        
        // 委托给命令处理器
        $this->spotCommandHandler->handleCommand($type, $data);
    }



    /**
     * 重写基类的自定义命令处理
     */
    protected function handleCustomCommand(string $type, array $data): void
    {
        // 委托给命令处理器
        $this->spotCommandHandler->handleCommand($type, $data);
    }

    // ========== 公共API方法 ==========

    /**
     * 停止运行
     */
    public function stop(): void
    {
        $this->isRunning = false;
        
        // 清理做市机器人服务
        if ($this->spotMarketMakerService) {
            $this->spotMarketMakerService->cleanup();
        }
        
        // 清理撮合引擎资源
        if ($this->spotMatchEngineService) {
            $this->spotMatchEngineService->cleanup();
        }
        
        $this->logInfo("Match engine worker process {$this->getCurrentProcessIndex()} stopped");
    }

    /**
     * 获取市场信息
     */
    public function getMarketsInfo(): array
    {
        if ($this->spotMatchEngineService) {
            return $this->spotMatchEngineService->getMarketsInfo();
        }
        return [];
    }

    /**
     * 获取做市机器人服务
     */
    public function getMarketMakerService(): SpotMarketMakerService
    {
        return $this->spotMarketMakerService;
    }
}