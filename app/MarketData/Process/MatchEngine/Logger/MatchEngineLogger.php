<?php

declare(strict_types=1);

namespace App\MarketData\Process\MatchEngine\Logger;

use Hyperf\Logger\LoggerFactory;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

/**
 * 撮合引擎专用日志工厂
 */
class MatchEngineLogger
{
    private LoggerFactory $loggerFactory;
    private static ?int $processId = null;
    private static ?string $processType = null;

    public function __construct(ContainerInterface $container)
    {
        $this->loggerFactory = $container->get(LoggerFactory::class);
    }

    /**
     * 设置进程信息
     */
    public static function setProcessInfo(int $processId, string $processType): void
    {
        self::$processId = $processId;
        self::$processType = $processType;
        
        // 同时设置到ProcessIdProcessor
        ProcessIdProcessor::setProcessId($processId);
        ProcessIdProcessor::setProcessType($processType);
    }

    /**
     * 获取主进程日志器
     */
    public function getManagerLogger(): LoggerInterface
    {
        return $this->loggerFactory->get('match-engine-manager','match-engine-manager');
    }

    /**
     * 获取子进程日志器
     */
    public function getChildLogger(): LoggerInterface
    {
        return $this->loggerFactory->get('match-engine-child','match-engine-child');
    }

    /**
     * 获取订单日志器
     */
    public function getOrderLogger(): LoggerInterface
    {
        return $this->loggerFactory->get('match-engine-order','match-engine-order');
    }

    /**
     * 获取交易日志器
     */
    public function getTradeLogger(): LoggerInterface
    {
        return $this->loggerFactory->get('match-engine-trade','match-engine-trade');
    }

    /**
     * 记录订单日志
     */
    public function logOrder(string $action, string $symbol, array $orderData): void
    {
        $this->getOrderLogger()->info($action, [
            'symbol' => $symbol,
            'order' => $orderData,
            'process_id' => self::$processId,
            'process_type' => self::$processType,
        ]);
    }

    /**
     * 记录交易日志
     */
    public function logTrade(string $symbol, array $tradeData): void
    {
        $this->getTradeLogger()->info("Trade executed for {$symbol}", [
            'symbol' => $symbol,
            'trade' => $tradeData,
            'process_id' => self::$processId,
            'process_type' => self::$processType,
        ]);
    }

    /**
     * 记录进程状态日志
     */
    public function logProcessStatus(string $message, array $context = []): void
    {
        $context['process_id'] = self::$processId;
        $context['process_type'] = self::$processType;
        $context['pid'] = getmypid();

        if (self::$processType === 'manager') {
            $this->getManagerLogger()->info($message, $context);
        } else {
            $this->getChildLogger()->info($message, $context);
        }
    }

    /**
     * 记录错误日志
     */
    public function logError(string $message, \Throwable|array $exceptionOrContext = null, array $context = []): void
    {
        $context['process_id'] = self::$processId;
        $context['process_type'] = self::$processType;
        $context['pid'] = getmypid();
        
        // 处理第二个参数
        if ($exceptionOrContext instanceof \Throwable) {
            // 如果是异常对象，添加到context中
            $context['exception'] = [
                'message' => $exceptionOrContext->getMessage(),
                'file' => $exceptionOrContext->getFile(),
                'line' => $exceptionOrContext->getLine(),
                'trace' => $exceptionOrContext->getTraceAsString(),
            ];
        } elseif (is_array($exceptionOrContext)) {
            // 如果是数组，合并到context中
            $context = array_merge($context, $exceptionOrContext);
        }
        
        if (self::$processType === 'manager') {
            $this->getManagerLogger()->error($message, $context);
        } else {
            $this->getChildLogger()->error($message, $context);
        }
    }

    /**
     * 记录调试日志
     */
    public function logDebug(string $message, array $context = []): void
    {
        $context['process_id'] = self::$processId;
        $context['process_type'] = self::$processType;
        
        if (self::$processType === 'manager') {
            $this->getManagerLogger()->debug($message, $context);
        } else {
            $this->getChildLogger()->debug($message, $context);
        }
    }

    /**
     * 记录警告日志
     */
    public function logWarning(string $message, array $context = []): void
    {
        $context['process_id'] = self::$processId;
        $context['process_type'] = self::$processType;
        
        if (self::$processType === 'manager') {
            $this->getManagerLogger()->warning($message, $context);
        } else {
            $this->getChildLogger()->warning($message, $context);
        }
    }
}
