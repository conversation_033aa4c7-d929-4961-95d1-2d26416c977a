<?php

declare(strict_types=1);

namespace App\MarketData\Process\MatchEngine\Logger;

use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;

/**
 * 进程ID处理器
 * 为日志记录添加进程ID信息
 */
class ProcessIdProcessor implements ProcessorInterface
{
    private static ?int $processId = null;
    private static ?string $processType = null;

    /**
     * 设置进程ID
     */
    public static function setProcessId(int $processId): void
    {
        self::$processId = $processId;
    }

    /**
     * 设置进程类型
     */
    public static function setProcessType(string $processType): void
    {
        self::$processType = $processType;
    }

    /**
     * 获取进程ID
     */
    public static function getProcessId(): ?int
    {
        return self::$processId;
    }

    /**
     * 获取进程类型
     */
    public static function getProcessType(): ?string
    {
        return self::$processType;
    }

    /**
     * 处理日志记录
     */
    public function __invoke(LogRecord $record): LogRecord
    {
        $extra = $record->extra;
        
        // 添加进程信息
        $extra['process_id'] = self::$processId ?? 'unknown';
        $extra['process_type'] = self::$processType ?? 'unknown';
        $extra['pid'] = getmypid();
        
        return $record->with(extra: $extra);
    }
}
