<?php

/**
 * AlphaSocketProcess.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/3
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\MarketData\Process\Chain;

use App\Enum\ProcessCmdKey;
use App\Logger\LoggerFactory;
use App\MarketData\Service\Chain\ChainSocketManager;
use App\Process\BaseProcess;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\Annotation\Process;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;
use Swoole\Coroutine\Http\Client;

#[Process(name:'alpha-socket-process')]
class AlphaSocketProcess extends BaseProcess
{

    public string $cmd_key = '';

    #[Inject]
    public Redis $redis;

    #[Inject]
    public ChainSocketManager $chainSocketManager;

    protected string $socketUrl = "wss://nbstream.binance.com";

    protected bool $isConnected = false;

    protected ?Client $wsClient = null;

    /**
     * 订阅ID计数器
     */
    protected int $subscribeId = 0;

    /**
     * 用户FD映射 [user_id => fd]
     */
    protected array $userFdMap = [];

    /**
     * 全局已订阅的币种流 [symbol => subscribe_count]
     */
    protected array $globalSubscribedSymbols = [];

    #[Inject]
    public LoggerFactory $logger;

    /**
     * 全局订阅流Redis Key
     */
    protected const GLOBAL_SUBSCRIBED_KEY = 'alpha_socket:global_subscribed';

    public function __construct(ContainerInterface $container)
    {
        $this->cmd_key = ProcessCmdKey::CHAIN_SOCKET_CMD->value;

        parent::__construct($container);
        
        // 初始化时加载全局订阅流状态
        $this->loadGlobalSubscribedSymbols();
    }

    public function parse_cmd(array $message): void
    {
        if (!isset($message['type'])) {
            $this->logger->get('process')->warning('接收到无效的消息格式', ['message' => $message]);
            return;
        }

        switch ($message['type']) {
            case 'subscribe':
                $this->handleSubscribe($message);
                break;
            case 'unsubscribe':
                $this->handleUnsubscribe($message);
                break;
            default:
                $this->logger->get('process')->warning('未知的消息类型', ['type' => $message['type']]);
                break;
        }
    }

    /**
     * 处理订阅消息
     */
    protected function handleSubscribe(array $message): void
    {
        $userId = $message['user_id'] ?? null;
        $fd = $message['fd'] ?? null;
        $contract = $message['contract'] ?? null;
        $chain = $message['chain'] ?? null;

        if (!$userId || !$fd || !$contract || !$chain) {
            $this->logger->get('process')->error('订阅消息参数不完整', ['message' => $message]);
            return;
        }

        try {
            // 构造币种标识符
            $symbol = $this->buildSymbol($contract, $chain);
            
            // 记录用户FD映射
            $this->userFdMap[$userId] = $fd;
            
            // 检查是否需要发送WebSocket订阅消息
            $needWebSocketSubscribe = !$this->isSymbolGloballySubscribed($symbol);
            
            // 使用ChainSocketManager记录订阅关系
            $success = $this->chainSocketManager->subscribe($userId, $symbol);
            
            if (!$success) {
                $this->logger->get('process')->error('订阅失败', [
                    'user_id' => $userId,
                    'symbol' => $symbol,
                    'contract' => $contract,
                    'chain' => $chain
                ]);
                return;
            }

            // 如果该币种还没有被全局订阅，则发送WebSocket订阅消息
            if ($needWebSocketSubscribe) {
                $this->sendSubscribeMessage($contract, $chain);
                $this->addGlobalSubscribedSymbol($symbol);

                $this->logger->get('process')->info('发送WebSocket订阅消息', [
                    'symbol' => $symbol,
                    'contract' => $contract,
                    'chain' => $chain
                ]);
            }

            $this->logger->get('process')->info('用户订阅成功', [
                'user_id' => $userId,
                'fd' => $fd,
                'symbol' => $symbol,
                'contract' => $contract,
                'chain' => $chain,
                'websocket_subscribe' => $needWebSocketSubscribe
            ]);
            
        } catch (\Exception $e) {
            $this->logger->get('process')->error('处理订阅消息异常', [
                'message' => $message,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理取消订阅消息
     */
    protected function handleUnsubscribe(array $message): void
    {
        $userId = $message['user_id'] ?? null;

        if (!$userId) {
            $this->logger->get('process')->error('取消订阅消息参数不完整', ['message' => $message]);
            return;
        }

        try {
            // 获取用户当前订阅的币种
            $currentSymbol = $this->chainSocketManager->getUserSubscription($userId);
            
            // 使用ChainSocketManager取消订阅
            $success = $this->chainSocketManager->unsubscribe($userId);
            
            // 清理用户FD映射
            unset($this->userFdMap[$userId]);
            
            if ($success && $currentSymbol) {
                // 检查该币种是否还有其他订阅者
                $remainingSubscribers = $this->chainSocketManager->getSymbolSubscribers($currentSymbol);
                
                // 如果没有其他订阅者，则发送取消订阅消息到WebSocket
                if (empty($remainingSubscribers)) {
                    $this->sendUnsubscribeMessage($currentSymbol);
                    $this->removeGlobalSubscribedSymbol($currentSymbol);

                    $this->logger->get('process')->info('发送WebSocket取消订阅消息', [
                        'symbol' => $currentSymbol,
                        'reason' => '没有剩余订阅者'
                    ]);
                }

                $this->logger->get('process')->info('用户取消订阅成功', [
                    'user_id' => $userId,
                    'symbol' => $currentSymbol,
                    'remaining_subscribers' => count($remainingSubscribers)
                ]);
            } else {
                $this->logger->get('process')->error('用户取消订阅失败', ['user_id' => $userId]);
            }
            
        } catch (\Exception $e) {
            $this->logger->get('process')->error('处理取消订阅消息异常', [
                'message' => $message,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 构造币种标识符
     */
    protected function buildSymbol(string $contract, string $chain): string
    {
        return strtoupper($contract) . '@' . $chain;
    }

    /**
     * 发送订阅消息到WebSocket
     */
    protected function sendSubscribeMessage(string $contract, string $chain): void
    {
        if (!$this->isConnected || !$this->wsClient) {
            $this->logger->get('process')->error('WebSocket未连接，无法发送订阅消息');
            return;
        }

        $this->subscribeId++;
        
        // 确保contract是小写格式
        $contractLower = strtolower($contract);
        
        $subscribeData = [
            'method' => 'SUBSCRIBE',
            'params' => [
                "w3w@{$contractLower}@{$chain}@ticker24h",
                "w3w@{$contractLower}@{$chain}@kline_1s"
            ],
            'id' => $this->subscribeId
        ];

        $jsonData = json_encode($subscribeData);
        
        if (!$this->wsClient->push($jsonData)) {
            $this->logger->get('process')->error('发送订阅消息失败', [
                'contract' => $contract,
                'chain' => $chain,
                'subscribe_data' => $subscribeData
            ]);
        } else {
            $this->logger->get('process')->info('发送订阅消息成功', [
                'contract' => $contract,
                'chain' => $chain,
                'subscribe_id' => $this->subscribeId
            ]);
        }
    }

    /**
     * 获取用户FD
     */
    public function getUserFd(int $userId): ?int
    {
        return $this->userFdMap[$userId] ?? null;
    }

    /**
     * 获取所有用户FD映射
     */
    public function getAllUserFds(): array
    {
        return $this->userFdMap;
    }

    /**
     * 加载全局订阅状态
     */
    protected function loadGlobalSubscribedSymbols(): void
    {
        try {
            $data = $this->redis->get(self::GLOBAL_SUBSCRIBED_KEY);
            if ($data) {
                $this->globalSubscribedSymbols = json_decode($data, true) ?: [];
            }

            $this->logger->get('process')->info('加载全局订阅状态', [
                'symbols' => array_keys($this->globalSubscribedSymbols)
            ]);
        } catch (\Exception $e) {
            $this->logger->get('process')->error('加载全局订阅状态失败', [
                'error' => $e->getMessage()
            ]);
            $this->globalSubscribedSymbols = [];
        }
    }

    /**
     * 保存全局订阅状态
     */
    protected function saveGlobalSubscribedSymbols(): void
    {
        try {
            $this->redis->set(self::GLOBAL_SUBSCRIBED_KEY, json_encode($this->globalSubscribedSymbols), 3600);
        } catch (\Exception $e) {
            $this->logger->get('process')->error('保存全局订阅状态失败', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查币种是否已全局订阅
     */
    protected function isSymbolGloballySubscribed(string $symbol): bool
    {
        return isset($this->globalSubscribedSymbols[$symbol]);
    }

    /**
     * 添加全局订阅币种
     */
    protected function addGlobalSubscribedSymbol(string $symbol): void
    {
        $this->globalSubscribedSymbols[$symbol] = time();
        $this->saveGlobalSubscribedSymbols();

        $this->logger->get('process')->debug('添加全局订阅币种', [
            'symbol' => $symbol,
            'total_subscribed' => count($this->globalSubscribedSymbols)
        ]);
    }

    /**
     * 移除全局订阅币种
     */
    protected function removeGlobalSubscribedSymbol(string $symbol): void
    {
        unset($this->globalSubscribedSymbols[$symbol]);
        $this->saveGlobalSubscribedSymbols();

        $this->logger->get('process')->debug('移除全局订阅币种', [
            'symbol' => $symbol,
            'total_subscribed' => count($this->globalSubscribedSymbols)
        ]);
    }

    /**
     * 发送取消订阅消息到WebSocket
     */
    protected function sendUnsubscribeMessage(string $symbol): void
    {
        if (!$this->isConnected || !$this->wsClient) {
            $this->logger->get('process')->error('WebSocket未连接，无法发送取消订阅消息');
            return;
        }

        // 解析币种标识符获取合约和链信息
        $symbolParts = explode('@', $symbol);
        
        if (count($symbolParts) < 2) {
            $this->logger->get('process')->error('无效的币种标识符格式', ['symbol' => $symbol]);
            return;
        }
        
        $contract = strtolower($symbolParts[0]);
        $chain = $symbolParts[1];

        $this->subscribeId++;
        
        $unsubscribeData = [
            'method' => 'UNSUBSCRIBE',
            'params' => [
                "w3w@{$contract}@{$chain}@ticker24h",
                "w3w@{$contract}@{$chain}@kline_1s"
            ],
            'id' => $this->subscribeId
        ];

        $jsonData = json_encode($unsubscribeData);
        
        if (!$this->wsClient->push($jsonData)) {
            $this->logger->get('process')->error('发送取消订阅消息失败', [
                'symbol' => $symbol,
                'contract' => $contract,
                'chain' => $chain,
                'unsubscribe_data' => $unsubscribeData
            ]);
        } else {
            $this->logger->get('process')->info('发送取消订阅消息成功', [
                'symbol' => $symbol,
                'contract' => $contract,
                'chain' => $chain,
                'subscribe_id' => $this->subscribeId
            ]);
        }
    }

    /**
     * 获取全局订阅状态统计
     */
    public function getGlobalSubscriptionStats(): array
    {
        return [
            'total_symbols' => count($this->globalSubscribedSymbols),
            'subscribed_symbols' => array_keys($this->globalSubscribedSymbols),
            'subscription_times' => $this->globalSubscribedSymbols
        ];
    }

    public function runBusinessLogic(): void
    {
        $this->connectSocket();
    }

    public function connectSocket()
    {
        $this->wsClient?->close();

        $this->wsClient = new Client($this->socketUrl,443,true);

        $config = [
            'timeout' => 10,
            'keep_alive' => true,
        ];

        if (env('REQUEST_PROXY', false)) {
            $httpProxy = env('HTTP_PROXY', 'http://127.0.0.1:1087');
            $proxyUrl = parse_url($httpProxy);
            $config['http_proxy_host'] = $proxyUrl['host'] ?? '127.0.0.1';
            $config['http_proxy_port'] = $proxyUrl['port'] ?? 1087;
        }
        $this->wsClient->set($config);

        if(!$this->wsClient->upgrade("/w3w/stream")){
            throw new \Exception("alpha socket connect error");
        }

        $this->isConnected = true;

        $this->handleMessage();
    }

    public function handleMessage(): void
    {
        while ($this->isConnected){
            try {
                $frame = $this->wsClient->recv();
                if(!$frame){
                    //主动发送消息检查链接状况
                    continue;
                }
                
                // 处理接收到的WebSocket消息
                $this->processWebSocketMessage($frame->data);
                
            }catch (\Throwable $e){
                $this->logger->get('process')->error('处理WebSocket消息异常', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                break;
            }
        }
    }

    /**
     * 处理WebSocket消息
     */
    protected function processWebSocketMessage(string $data): void
    {
        try {
            $message = json_decode($data, true);
            
            if (!$message) {
                $this->logger->get('process')->warning('接收到无效的JSON消息', ['data' => $data]);
                return;
            }

            // 检查是否是订阅数据
            if (isset($message['stream'])) {
                $this->handleStreamMessage($message);
            } else {
                $this->logger->get('process')->debug('接收到非流数据消息', ['message' => $message]);
            }
            
        } catch (\Exception $e) {
            $this->logger->get('process')->error('处理WebSocket消息异常', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理流数据消息
     */
    protected function handleStreamMessage(array $message): void
    {
        $stream = $message['stream'] ?? '';
        $data = $message['data'] ?? [];
        
        if (!$stream || !$data) {
            return;
        }
        
        // 解析流名称获取合约和链信息
        // 格式: w3w@0x22b1458e780f8fa71e2f84502cee8b5a3cc731fa@56@ticker24h
        $streamParts = explode('@', $stream);
        
        if (count($streamParts) < 4) {
            $this->logger->warning('无效的流名称格式', ['stream' => $stream]);
            return;
        }
        
        $contract = $streamParts[1];
        $chain = $streamParts[2];
        $dataType = $streamParts[3];
        
        // 构造币种标识符
        $symbol = $this->buildSymbol($contract, $chain);
        
        // 获取订阅该币种的用户列表
        $subscribers = $this->chainSocketManager->getSymbolSubscribers($symbol);
        
        if (empty($subscribers)) {
            return;
        }
        
        // 构造推送消息
        $pushMessage = [
            'type' => 'market_data',
            'symbol' => $symbol,
            'contract' => $contract,
            'chain' => $chain,
            'data_type' => $dataType,
            'data' => $data,
            'timestamp' => time()
        ];
        
        // 推送给所有订阅者
        $this->pushToSubscribers($subscribers, $pushMessage);
    }

    /**
     * 推送消息给订阅者
     */
    protected function pushToSubscribers(array $subscribers, array $message): void
    {
        $jsonMessage = json_encode($message);
        
        foreach ($subscribers as $userId) {
            $fd = $this->getUserFd($userId);
            
            if ($fd) {
                try {
                    // 这里需要通过进程通信将消息发送到WebSocket服务器
                    // 可以使用Redis发布订阅或者其他方式
                    $this->sendToWebSocketServer($fd, $jsonMessage);
                    
                } catch (\Exception $e) {
                    $this->logger->get('process')->error('推送消息给用户失败', [
                        'user_id' => $userId,
                        'fd' => $fd,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
    }

    /**
     * 发送消息到WebSocket服务器
     */
    protected function sendToWebSocketServer(int $fd, string $message): void
    {
        // 通过Redis发布消息到WebSocket服务器
        $pushData = [
            'type' => 'push',
            'fd' => $fd,
            'message' => $message
        ];
        
        $this->redis->publish('websocket_push', json_encode($pushData));
    }

    public function isEnable($server): bool
    {
        return false;//(bool)env('MARKET_DATA_SERVER',false);
    }

}