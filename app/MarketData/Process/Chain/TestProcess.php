<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 测试进程 - 用于测试多币种WebSocket订阅
 * 
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\MarketData\Process\Chain;

use App\Logger\LoggerFactory;
use App\Model\Chain\ChainCurrency;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Process\AbstractProcess;
use Hyperf\Process\Annotation\Process;
use Swoole\Coroutine;
use Swoole\Coroutine\Http\Client;
use Swoole\Timer;

#[Process(name: 'test-process')]
class TestProcess extends AbstractProcess
{

    public bool $enableCoroutine = true;

    #[Inject]
    protected LoggerFactory $logger;

    /**
     * WebSocket连接URL
     */
    protected string $socketUrl = "nbstream.binance.com";

    /**
     * WebSocket客户端连接池
     */
    protected array $wsClients = [];

    /**
     * 每个连接的状态
     */
    protected array $connectionStates = [];

    /**
     * 每个连接最大Stream数
     */
    protected const MAX_STREAMS_PER_CONNECTION = 200;

    /**
     * 每秒最大订阅数
     */
    protected const MAX_SUBSCRIPTIONS_PER_SECOND = 10;

    /**
     * 订阅ID计数器
     */
    protected int $subscribeId = 0;

    /**
     * 从数据库加载的币种列表
     */
    protected array $currencySymbols = [];

    /**
     * 消息接收统计 [symbol => last_received_time]
     */
    protected array $messageStats = [];

    /**
     * 统计定时器ID
     */
    protected int $statsTimerId = 0;

    public function handle(): void
    {
        $this->logger->get('test-process')->info('开始启动测试进程');
        
        try {
            // 加载数据库币种数据
            $this->loadCurrencySymbols();
            
            // 计算需要的连接数
            $totalSymbols = count($this->currencySymbols);
            $requiredConnections = (int)ceil($totalSymbols / self::MAX_STREAMS_PER_CONNECTION);
            
            echo "📊 总币种数: {$totalSymbols}, 需要连接数: {$requiredConnections} (每连接最多".self::MAX_STREAMS_PER_CONNECTION."个)\n";
            
            // 创建多个WebSocket连接
            $this->createMultipleConnections($requiredConnections);
            
            // 批量订阅币种（分布到多个连接）
            $this->batchSubscribeSymbolsWithMultipleConnections();
            
            // 启动统计定时器
            $this->startStatsTimer();

            // 启动一个简单的测试定时器来验证定时器功能
            $this->startTestTimer();

            // 开始处理所有连接的消息
            $this->handleAllMessages();
            
        } catch (\Exception $e) {
            $this->logger->get('test-process')->error('测试进程异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 加载数据库中的币种数据
     */
    protected function loadCurrencySymbols(): void
    {
        try {
            echo "📊 正在加载数据库币种数据...\n";
            
            $currencies = ChainCurrency::query()
                ->whereNotNull('contractAddres')
                ->whereNotNull('chainId')
                ->where('contractAddres', '!=', '')
                ->where('chainId', '!=', '')
                ->select(['contractAddres', 'chainId', 'symbol'])
                ->limit(10)
                ->get();

            foreach ($currencies as $currency) {
                $this->currencySymbols[] = [
                    'contract' => $currency->contractAddres,
                    'chain' => $currency->chainId,
                    'symbol' => $currency->symbol
                ];
            }

            echo "✅ 加载完成，共 " . count($this->currencySymbols) . " 个币种\n";
            
            $this->logger->get('test-process')->info('加载币种数据完成', [
                'total_count' => count($this->currencySymbols)
            ]);

        } catch (\Exception $e) {
            echo "❌ 加载币种数据失败: {$e->getMessage()}\n";
            throw $e;
        }
    }

        /**
     * 创建多个WebSocket连接
     */
    protected function createMultipleConnections(int $connectionCount): void
    {
        echo "🔗 正在创建 {$connectionCount} 个WebSocket连接...\n";
        
        for ($i = 0; $i < $connectionCount; $i++) {
            try {
                echo "🔄 创建连接 #" . ($i + 1) . "...\n";
                $client = $this->createSingleConnection($i);
                
                $this->wsClients[$i] = $client;
                $this->connectionStates[$i] = [
                    'connected' => true,
                    'streams_count' => 0,
                    'last_subscribe_time' => 0,
                    'subscribe_count_in_second' => 0
                ];
                
                echo "✅ 连接 #" . ($i + 1) . " 创建成功\n";
                
                // 连接间隔，避免过快
                if ($i < $connectionCount - 1) {
                    usleep(500000); // 500ms
                }
                
            } catch (\Exception $e) {
                echo "❌ 连接 #" . ($i + 1) . " 创建失败: {$e->getMessage()}\n";
                $this->logger->get('test-process')->error('创建连接失败', [
                    'connection_index' => $i,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        }
        
        echo "🎉 所有 {$connectionCount} 个连接创建完成!\n";
    }

    /**
     * 创建单个WebSocket连接
     */
    protected function createSingleConnection(int $index): Client
    {
        echo "🔗 正在连接到 nbstream.binance.com:443...\n";
        $client = new Client('nbstream.binance.com', 443, true);

        $config = [
            'timeout' => 30,
            'keep_alive' => true,
            'ssl_verify_peer' => false,
            'ssl_verify_host' => false,
            'ssl_allow_self_signed' => true,
        ];

        if (env('REQUEST_PROXY', false)) {
            $httpProxy = env('HTTP_PROXY', 'http://127.0.0.1:1087');
            $proxyUrl = parse_url($httpProxy);
            $config['http_proxy_host'] = $proxyUrl['host'] ?? '127.0.0.1';
            $config['http_proxy_port'] = $proxyUrl['port'] ?? 1087;
            echo "🌐 使用代理: {$config['http_proxy_host']}:{$config['http_proxy_port']}\n";
        }

        $client->set($config);

        echo "🔄 尝试升级到WebSocket协议...\n";
        if (!$client->upgrade("/w3w/stream")) {
            $errorCode = $client->getStatusCode();
            $errorMsg = $client->getBody();
            echo "❌ WebSocket升级失败: HTTP状态码 {$errorCode}, 响应: {$errorMsg}\n";
            throw new \Exception("连接 #{$index} 升级失败: HTTP状态码 {$errorCode}, 响应: {$errorMsg}");
        }

        echo "✅ WebSocket连接升级成功\n";
        return $client;
    }

    /**
     * 测试HTTP连接
     */
    protected function testHttpConnection(): void
    {
        try {
            echo "🔍 测试HTTP连接...\n";
            
            $testClient = new Client('nbstream.binance.com', 443, true);
            $testClient->set([
                'timeout' => 10,
                'ssl_verify_peer' => false,
                'ssl_verify_host' => false,
            ]);
            
            $testClient->get('/');
            $statusCode = $testClient->getStatusCode();
            
            echo "📡 HTTP测试结果: 状态码 {$statusCode}\n";
            
            if ($statusCode > 0) {
                echo "✅ 基础网络连接正常，问题可能在WebSocket升级\n";
            } else {
                echo "❌ 基础网络连接失败\n";
            }
            
            $testClient->close();
            
        } catch (\Exception $e) {
            echo "❌ HTTP测试失败: {$e->getMessage()}\n";
        }
    }

    /**
     * 批量订阅币种（多连接分布式）
     */
    protected function batchSubscribeSymbolsWithMultipleConnections(): void
    {
        $totalCount = count($this->currencySymbols);
        $connectionCount = count($this->wsClients);
        
        echo "🚀 开始分布式批量订阅 {$totalCount} 个币种到 {$connectionCount} 个连接...\n";
        echo "⚠️  限制：每秒最多 " . self::MAX_SUBSCRIPTIONS_PER_SECOND . " 个订阅，每连接最多 " . self::MAX_STREAMS_PER_CONNECTION . " 个Stream\n";
        
        $this->logger->get('test-process')->info('开始分布式批量订阅', [
            'total_symbols' => $totalCount,
            'connection_count' => $connectionCount
        ]);

        $subscribedCount = 0;
        $currentConnectionIndex = 0;
        $subscriptionsInCurrentSecond = 0;
        $lastSubscribeSecond = time();
        
        foreach ($this->currencySymbols as $index => $symbol) {
            $symbolKey = $symbol['contract'] . '@' . $symbol['chain'];
            
            // 初始化统计数据
            $this->messageStats[$symbolKey] = [
                'subscribed_at' => time(),
                'last_received' => null,
                'message_count' => 0,
                'symbol_name' => $symbol['symbol'] ?? '',
                'connection_index' => $currentConnectionIndex
            ];
            
            // 检查当前连接是否已达到最大Stream数
            if ($this->connectionStates[$currentConnectionIndex]['streams_count'] >= self::MAX_STREAMS_PER_CONNECTION) {
                $currentConnectionIndex++;
                if ($currentConnectionIndex >= $connectionCount) {
                    echo "⚠️  所有连接都已达到最大Stream数限制\n";
                    break;
                }
                echo "🔄 切换到连接 #" . ($currentConnectionIndex + 1) . "\n";
            }
            
            // 控制每秒订阅数限制
            $currentSecond = time();
            if ($currentSecond !== $lastSubscribeSecond) {
                $subscriptionsInCurrentSecond = 0;
                $lastSubscribeSecond = $currentSecond;
            }
            
            if ($subscriptionsInCurrentSecond >= self::MAX_SUBSCRIPTIONS_PER_SECOND) {
                echo "⏳ 达到每秒订阅限制，等待1秒...\n";
                sleep(1);
                $subscriptionsInCurrentSecond = 0;
                $lastSubscribeSecond = time();
            }
            
            // 执行订阅
            $success = $this->subscribeSymbolToConnection(
                $symbol['contract'], 
                $symbol['chain'], 
                $currentConnectionIndex
            );
            
            if ($success) {
                $subscribedCount++;
                $subscriptionsInCurrentSecond++;
                $this->connectionStates[$currentConnectionIndex]['streams_count'] += 2; // ticker24h + kline_1s
                
                // 进度显示
                if (($index + 1) % 100 === 0) {
                    echo "📊 已订阅 " . ($index + 1) . "/{$totalCount} 个币种 (当前连接: #" . ($currentConnectionIndex + 1) . ")\n";
                }
            }
            
            // 小间隔避免请求过快
            usleep(50000); // 50ms
        }

        echo "✅ 完成订阅所有 {$subscribedCount} 个币种\n";
        $this->outputConnectionStats();
        
        $this->logger->get('test-process')->info('完成分布式订阅', [
            'subscribed_count' => $subscribedCount,
            'used_connections' => $currentConnectionIndex + 1
        ]);
    }

    /**
     * 输出连接统计信息
     */
    protected function outputConnectionStats(): void
    {
        echo "\n📈 ========== 连接统计信息 ==========\n";
        foreach ($this->connectionStates as $index => $state) {
            $connectionNum = $index + 1;
            $streamsCount = $state['streams_count'];
            $status = $state['connected'] ? '✅' : '❌';
            echo "连接 #{$connectionNum}: {$status} 已订阅 {$streamsCount}/" . self::MAX_STREAMS_PER_CONNECTION . " 个Streams\n";
        }
        echo "=====================================\n\n";
    }

    /**
     * 启动统计定时器
     */
    protected function startStatsTimer(): void
    {
        echo "📈 启动统计定时器，每10秒输出一次统计信息...\n";

        // 立即输出一次初始统计
        $this->outputStats();

        // 每10秒输出一次统计
        $this->statsTimerId = Timer::tick(10000, function () {
            echo "⏰ 定时器触发 - " . date('Y-m-d H:i:s') . "\n";
            $this->outputStats();
        });

        echo "✅ 统计定时器已启动，ID: {$this->statsTimerId}\n";
    }

    /**
     * 启动测试定时器（用于验证定时器功能）
     */
    protected function startTestTimer(): void
    {
        echo "🧪 启动测试定时器，每5秒输出一次...\n";

        $testTimerId = Timer::tick(5000, function () {
            static $count = 0;
            $count++;
            echo "🧪 测试定时器触发 #{$count} - " . date('Y-m-d H:i:s') . "\n";
        });

        echo "✅ 测试定时器已启动，ID: {$testTimerId}\n";
    }

    /**
     * 输出统计信息
     */
    protected function outputStats(): void
    {
        $totalSymbols = count($this->messageStats);
        $receivedCount = 0;
        $currentTime = time();
        
        foreach ($this->messageStats as $stats) {
            if ($stats['last_received'] !== null) {
                $receivedCount++;
            }
        }
        
        $notReceivedCount = $totalSymbols - $receivedCount;
        $receiveRate = $totalSymbols > 0 ? round(($receivedCount / $totalSymbols) * 100, 2) : 0;
        
        echo "\n📊 ========== 消息接收统计 ==========\n";
        echo "⏰ 统计时间: " . date('Y-m-d H:i:s', $currentTime) . "\n";
        echo "📈 总订阅币种: {$totalSymbols}\n";
        echo "✅ 已收到消息: {$receivedCount} ({$receiveRate}%)\n";
        echo "❌ 未收到消息: {$notReceivedCount}\n";
        echo "=====================================\n\n";
        
        // 记录到日志
        $this->logger->get('test-process')->info('消息接收统计', [
            'total_symbols' => $totalSymbols,
            'received_count' => $receivedCount,
            'not_received_count' => $notReceivedCount,
            'receive_rate' => $receiveRate
        ]);
        
        // 显示最近收到消息的币种（最多显示5个）
        $recentReceived = [];
        foreach ($this->messageStats as $symbolKey => $stats) {
            if ($stats['last_received'] !== null) {
                $recentReceived[] = [
                    'symbol' => $symbolKey,
                    'name' => $stats['symbol_name'],
                    'last_received' => $stats['last_received'],
                    'message_count' => $stats['message_count']
                ];
            }
        }
        
        // 按最后接收时间排序
        usort($recentReceived, function ($a, $b) {
            return $b['last_received'] - $a['last_received'];
        });
        
        $showCount = min(5, count($recentReceived));
        if ($showCount > 0) {
            echo "🔥 最近收到消息的币种 (前{$showCount}个):\n";
            for ($i = 0; $i < $showCount; $i++) {
                $item = $recentReceived[$i];
                $timeDiff = $currentTime - $item['last_received'];
                echo "   {$item['name']} ({$item['symbol']}) - {$timeDiff}秒前 - 共{$item['message_count']}条\n";
            }
            echo "\n";
        }
    }

    /**
     * 向指定连接订阅币种
     */
    protected function subscribeSymbolToConnection(string $contract, string $chain, int $connectionIndex): bool
    {
        if (!isset($this->wsClients[$connectionIndex])) {
            echo "❌ 连接 #{$connectionIndex} 不存在\n";
            return false;
        }

        $client = $this->wsClients[$connectionIndex];
        
        $this->subscribeId++;
        
        // 确保contract是小写格式
        $contractLower = strtolower($contract);
        
        $subscribeData = [
            'method' => 'SUBSCRIBE',
            'params' => [
                "w3w@{$contractLower}@{$chain}@ticker24h",
                "w3w@{$contractLower}@{$chain}@kline_1s"
            ],
            'id' => $this->subscribeId
        ];

        $jsonData = json_encode($subscribeData);
        
        try {
            if ($client->push($jsonData)) {
                $this->logger->get('test-process')->debug('订阅消息发送成功', [
                    'contract' => $contract,
                    'chain' => $chain,
                    'connection_index' => $connectionIndex,
                    'subscribe_data' => $subscribeData
                ]);
                return true;
            } else {
                $this->logger->get('test-process')->error('订阅消息发送失败', [
                    'contract' => $contract,
                    'chain' => $chain,
                    'connection_index' => $connectionIndex,
                    'subscribe_data' => $subscribeData
                ]);
                return false;
            }
        } catch (\Exception $e) {
            $this->logger->get('test-process')->error('发送订阅消息异常', [
                'error' => $e->getMessage(),
                'contract' => $contract,
                'chain' => $chain,
                'connection_index' => $connectionIndex
            ]);
            return false;
        }
    }

    /**
     * 处理所有连接的WebSocket消息
     */
    protected function handleAllMessages(): void
    {
        echo "🔄 开始监听所有WebSocket连接的消息...\n";
        $connectionCount = count($this->wsClients);
        echo "📡 监听 {$connectionCount} 个连接...\n";

        // 输出连接状态调试信息
        foreach ($this->connectionStates as $index => $state) {
            $status = $state['connected'] ? '✅ 已连接' : '❌ 未连接';
            echo "🔍 连接 #" . ($index + 1) . " 状态: {$status}\n";
        }

        foreach ($this->wsClients as $index => $client) {
            go(function () use ($client, $index) {
                try {
                    echo "🔄 连接 #" . ($index + 1) . " 开始监听消息...\n";
                    $loopCount = 0;
                    while ($this->connectionStates[$index]['connected']) {
                        try {
                            $frame = $client->recv(1.0); // 1秒超时
                            if (!$frame) {
                                $loopCount++;
                                // 每60次循环（约1分钟）输出一次调试信息
//                                if ($loopCount % 60 === 0) {
//                                    echo "🔍 连接 #" . ($index + 1) . " 仍在监听中... (循环 {$loopCount} 次)\n";
//                                }
                                continue;
                            }
                            $this->processWebSocketMessage($frame->data, $index);
                        } catch (\Throwable $e) {
                            $this->logger->get('test-process')->error('连接消息处理异常', [
                                'connection_index' => $index,
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString()
                            ]);
                            $this->connectionStates[$index]['connected'] = false;
                            break;
                        }
                    }
                    echo "⚠️  连接 #" . ($index + 1) . " 停止监听\n";
                } catch (\Throwable $e) {
                    echo "❌ 连接 #" . ($index + 1) . " 监听异常: {$e->getMessage()}\n";
                }
            });
        }

        // 主协程保持活跃，定时器协程才能正常调度
        echo "🔄 主循环开始运行，保持进程活跃...\n";
        $mainLoopCount = 0;
        while (true) {
            Coroutine::sleep(1);
            $mainLoopCount++;

            // 每60秒输出一次主循环状态
            if ($mainLoopCount % 60 === 0) {
                echo "💓 主循环运行中... (已运行 {$mainLoopCount} 秒)\n";
                echo "📊 定时器状态: ID={$this->statsTimerId}, 活跃连接数=" . count(array_filter($this->connectionStates, fn($s) => $s['connected'])) . "\n";
            }
        }
    }

    /**
     * 处理WebSocket消息
     */
    protected function processWebSocketMessage(string $data, int $connectionIndex = 0): void
    {
        try {
            $message = json_decode($data, true);
            
            if (!$message) {
                echo "⚠️  连接 #" . ($connectionIndex + 1) . " 接收到无效的JSON消息\n";
                return;
            }

            // 检查是否是订阅确认消息
            if (isset($message['result']) && $message['result'] === null && isset($message['id'])) {
                echo "✅ 连接 #" . ($connectionIndex + 1) . " 订阅确认: ID {$message['id']}\n";
                $this->logger->get('test-process')->info('收到订阅确认', [
                    'id' => $message['id'],
                    'connection_index' => $connectionIndex
                ]);
                return;
            }

            // 检查是否是流数据消息
            if (isset($message['stream'])) {
                $this->handleStreamMessage($message, $connectionIndex);
            } else {
                echo "ℹ️  连接 #" . ($connectionIndex + 1) . " 其他消息类型: " . json_encode($message, JSON_UNESCAPED_UNICODE) . "\n";
            }
            
        } catch (\Exception $e) {
            $this->logger->get('test-process')->error('处理WebSocket消息异常', [
                'data' => $data,
                'error' => $e->getMessage(),
                'connection_index' => $connectionIndex
            ]);
            echo "❌ 连接 #" . ($connectionIndex + 1) . " 消息处理异常: {$e->getMessage()}\n";
        }
    }

    /**
     * 处理流数据消息
     */
    protected function handleStreamMessage(array $message, int $connectionIndex = 0): void
    {
        $stream = $message['stream'] ?? '';
        $data = $message['data'] ?? [];
        
        if (!$stream || !$data) {
            return;
        }
        
        // 解析流名称获取合约和链信息
        // 格式: w3w@0x22b1458e780f8fa71e2f84502cee8b5a3cc731fa@56@ticker24h
        $streamParts = explode('@', $stream);
        
        if (count($streamParts) < 4) {
            echo "⚠️  连接 #" . ($connectionIndex + 1) . " 无效的流名称格式: {$stream}\n";
            return;
        }
        
        $contract = $streamParts[1];
        $chain = $streamParts[2];
        $dataType = $streamParts[3];
        $symbolKey = $contract . '@' . $chain;
        
        // 更新统计数据
        if (isset($this->messageStats[$symbolKey])) {
            $this->messageStats[$symbolKey]['last_received'] = time();
            $this->messageStats[$symbolKey]['message_count']++;
        }
        
        // 简化输出，避免刷屏（每100条消息输出一次）
        static $messageCounter = 0;
        $messageCounter++;
        
        if ($messageCounter % 100 === 0) {
            $symbol = $data['s'] ?? $symbolKey;
            echo "📊 已收到 {$messageCounter} 条消息，最新: {$symbol} ({$dataType}) - 连接 #" . ($connectionIndex + 1) . "\n";
        }
        
        // 记录详细日志
        $this->logger->get('test-process')->debug('收到流数据', [
            'stream' => $stream,
            'contract' => $contract,
            'chain' => $chain,
            'data_type' => $dataType,
            'symbol_key' => $symbolKey,
            'connection_index' => $connectionIndex
        ]);
    }

    /**
     * 清理资源
     */
    public function __destruct()
    {
        if ($this->statsTimerId > 0) {
            Timer::clear($this->statsTimerId);
        }
        
        // 关闭所有WebSocket连接
        foreach ($this->wsClients as $index => $client) {
            try {
                $client->close();
                echo "🔒 连接 #" . ($index + 1) . " 已关闭\n";
            } catch (\Exception $e) {
                echo "⚠️  关闭连接 #" . ($index + 1) . " 失败: {$e->getMessage()}\n";
            }
        }
    }

    /**
     * 检查是否启用进程
     */
    public function isEnable($server): bool
    {
        return false;
    }
}