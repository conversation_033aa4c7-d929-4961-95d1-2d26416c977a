<?php

declare(strict_types=1);

namespace App\Job\Margin;

use App\Http\Api\Service\V1\Margin\MarginPositionCacheService;
use App\Http\Api\Service\V1\Margin\MarginRiskService;
use App\Model\Trade\TradeMarginPosition;
use App\Model\User\UserMarginBorrow;
use App\Model\Enums\Trade\Margin\MarginPositionStatus;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\Trade\Margin\MarginBorrowStatus;
use App\Enum\CurrencyConfigKey;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

/**
 * 借贷监控延时任务
 */
class BorrowingMonitoringJob extends Job
{
    protected int $userId;
    protected int $currencyId;
    protected int $marginType;
    protected string $changeType;
    protected float $amount;

    public function __construct(int $userId, int $currencyId, int $marginType, string $changeType, float $amount)
    {
        $this->userId = $userId;
        $this->currencyId = $currencyId;
        $this->marginType = $marginType;
        $this->changeType = $changeType;
        $this->amount = $amount;
        
        // 设置延时执行（5秒后执行，确保主事务已提交）
        $this->delay = 5;
    }

    public function handle(): void
    {
        try {
            // 使用容器获取实例
            $container = ApplicationContext::getContainer();
            $logger = $container->get(LoggerInterface::class);
            $positionCacheService = $container->get(MarginPositionCacheService::class);

            $logger->debug('开始处理借贷监控任务', [
                'user_id' => $this->userId,
                'currency_id' => $this->currencyId,
                'margin_type' => $this->marginType,
                'change_type' => $this->changeType,
                'amount' => $this->amount
            ]);

            // 查找相关的仓位
            $positions = $this->findRelatedPositions($this->userId, $this->currencyId, $this->marginType);

            foreach ($positions as $positionData) {
                // 重新查询仓位对象
                $position = TradeMarginPosition::find($positionData['id']);
                
                if (!$position || $position->getStatus() !== MarginPositionStatus::HOLDING) {
                    continue;
                }

                switch ($this->changeType) {
                    case 'created':
                    case 'increased':
                        // 新增或增加借贷，将相关仓位加入监控
                        $this->addPositionToMonitoring($position, "borrowing_{$this->changeType}", $positionCacheService, $logger);
                        break;
                        
                    case 'repaid':
                    case 'decreased':
                        // 还款或减少借贷，重新评估是否还需要监控
                        $this->reevaluatePositionMonitoring($position, "borrowing_{$this->changeType}", $positionCacheService, $logger);
                        break;
                }
            }

        } catch (\Exception $e) {
            $container = ApplicationContext::getContainer();
            $logger = $container->get(LoggerInterface::class);
            
            $logger->error('借贷监控任务执行失败', [
                'user_id' => $this->userId,
                'currency_id' => $this->currencyId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e; // 重新抛出异常，让队列处理重试
        }
    }

    /**
     * 查找相关仓位
     */
    protected function findRelatedPositions(int $userId, int $currencyId, int $marginType): array
    {
        // 将account_type转换为MarginType枚举值
        $marginTypeEnum = $this->getMarginTypeFromAccountType($marginType);
        
        return TradeMarginPosition::where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_type', $marginTypeEnum)
            ->where('status', MarginPositionStatus::HOLDING)
            ->get()
            ->toArray();
    }

    /**
     * 将account_type转换为MarginType枚举值
     */
    protected function getMarginTypeFromAccountType(int $accountType): int
    {
        return match ($accountType) {
            2 => MarginType::CROSS->value,    // 全仓杠杆
            3 => MarginType::ISOLATED->value, // 逐仓杠杆
            default => MarginType::ISOLATED->value, // 默认逐仓
        };
    }

    /**
     * 将仓位加入监控
     */
    protected function addPositionToMonitoring(
        TradeMarginPosition $position,
        string $reason,
        MarginPositionCacheService $positionCacheService,
        LoggerInterface $logger
    ): void {
        $isInCache = $positionCacheService->isPositionInCache($position);

        // 获取维持保证金率
        $marginBracket = $this->getMarginBracket($position, $logger);
        $keepRate = $marginBracket ? $marginBracket->getKeepRate() : 0.05; // 默认5%

        if (!$isInCache) {
            // 同步仓位数据到缓存（包含维持保证金率）
            $positionCacheService->syncPositionToCacheWithMarginBracket($position, $marginBracket);

            $logger->info('因借贷变化将仓位加入风险监控', [
                'position_id' => $position->getId(),
                'user_id' => $position->getUserId(),
                'currency_id' => $position->getCurrencyId(),
                'reason' => $reason,
                'leverage' => $position->getLeverage(),
                'keep_rate' => $keepRate,
                'margin_bracket_level' => $marginBracket?->getLevel()
            ]);
        } else {
            // 已在监控中，更新缓存数据（包含最新的维持保证金率）
            $positionCacheService->syncPositionToCacheWithMarginBracket($position, $marginBracket);

            $logger->debug('因借贷变化更新仓位监控数据', [
                'position_id' => $position->getId(),
                'reason' => $reason,
                'keep_rate' => $keepRate
            ]);
        }
    }

    /**
     * 重新评估仓位监控需求
     */
    protected function reevaluatePositionMonitoring(
        TradeMarginPosition $position, 
        string $reason, 
        MarginPositionCacheService $positionCacheService,
        LoggerInterface $logger
    ): void {
        // 检查是否还有借贷
        $hasBorrowing = $this->checkIfHasBorrowing(
            $position->getUserId(),
            $position->getCurrencyId(),
            $position->getMarginType()
        );

        if ($hasBorrowing) {
            // 仍有借贷，确保在监控中
            $this->addPositionToMonitoring($position, $reason, $positionCacheService, $logger);
        } else {
            // 无借贷，从监控中移除
            $isInCache = $positionCacheService->isPositionInCache($position);
            
            if ($isInCache) {
                $positionCacheService->removePositionFromCache($position);
                
                $logger->info('因无借贷将仓位移出风险监控', [
                    'position_id' => $position->getId(),
                    'user_id' => $position->getUserId(),
                    'currency_id' => $position->getCurrencyId(),
                    'reason' => $reason
                ]);
            }
        }
    }

    /**
     * 检查是否有借贷（查询计价币的借贷）
     * 注意：这里的currencyId是基础币ID，需要获取计价币ID来查询借贷
     */
    protected function checkIfHasBorrowing(int $userId, int $baseCurrencyId, MarginType $marginType): bool
    {
        // 使用容器获取实例
        $container = ApplicationContext::getContainer();
        $redis = $container->get(Redis::class);

        // 获取计价币ID
        $quoteCurrencyId = $this->getQuoteCurrencyId($baseCurrencyId, $redis);

        if (!$quoteCurrencyId) {
            return false;
        }

        // 将MarginType转换为对应的account_type
        $accountType = match ($marginType) {
            MarginType::CROSS => 2,    // 全仓杠杆
            MarginType::ISOLATED => 3, // 逐仓杠杆
        };

        return UserMarginBorrow::where('user_id', $userId)
            ->where('currency_id', $quoteCurrencyId) // 使用计价币ID
            ->where('account_type', $accountType)
            ->where('status', MarginBorrowStatus::ACTIVE) // 只查询借贷中的记录
            ->where('borrow_amount', '>', 0)
            ->exists();
    }

    /**
     * 获取维持保证金率信息
     */
    protected function getMarginBracket(TradeMarginPosition $position, LoggerInterface $logger): ?object
    {
        try {
            // 使用容器获取MarginRiskService
            $container = ApplicationContext::getContainer();
            $marginRiskService = $container->get(MarginRiskService::class);

            $marginBracket = $marginRiskService->getMarginBracket(
                $position->getUserId(),
                $position->getCurrencyId(),
                $position->getMarginType()
            );

            $logger->debug('借贷监控获取维持保证金率', [
                'position_id' => $position->getId(),
                'keep_rate' => $marginBracket?->getKeepRate(),
                'level' => $marginBracket?->getLevel()
            ]);

            return $marginBracket;

        } catch (\Exception $e) {
            $logger->warning('借贷监控获取维持保证金率失败', [
                'position_id' => $position->getId(),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取计价币ID
     */
    protected function getQuoteCurrencyId(int $baseCurrencyId, Redis $redis): ?int
    {
        try {
            $currencyKey = CurrencyConfigKey::getCurrencyKey($baseCurrencyId);
            $currencyConfig = $redis->hGetAll($currencyKey);

            if (empty($currencyConfig) || !isset($currencyConfig['quote_assets_id'])) {
                return null;
            }

            return (int)$currencyConfig['quote_assets_id'];

        } catch (\Exception $e) {
            return null;
        }
    }
}
