<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 风险预警任务
 */

namespace App\Job\Margin;

use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Psr\Log\LoggerInterface;

class RiskWarningJob extends Job
{
    /**
     * 任务执行失败后的重试次数，即最大执行次数为 $maxAttempts+1 次
     */
    protected int $maxAttempts = 2;

    /**
     * 通知数据
     */
    protected array $notificationData;

    public function __construct(array $notificationData)
    {
        $this->notificationData = $notificationData;
    }

    public function handle(): void
    {
        $container = ApplicationContext::getContainer();
        $logger = $container->get(LoggerInterface::class);

        try {
            $logger->info('开始执行风险预警通知任务', [
                'user_id' => $this->notificationData['user_id'],
                'type' => $this->notificationData['type']
            ]);

            // 这里可以集成具体的通知渠道实现
            // 例如：WebSocket推送、短信、邮件、APP推送等
            $this->sendNotification($this->notificationData);

            $logger->info('风险预警通知发送成功', [
                'user_id' => $this->notificationData['user_id'],
                'type' => $this->notificationData['type']
            ]);

        } catch (\Exception $e) {
            $logger->error('风险预警通知发送失败', [
                'user_id' => $this->notificationData['user_id'],
                'type' => $this->notificationData['type'],
                'error' => $e->getMessage()
            ]);
            
            throw $e; // 重新抛出异常以便队列重试
        }
    }

    /**
     * 发送通知的具体实现
     */
    private function sendNotification(array $data): void
    {
        // TODO: 实现具体的通知发送逻辑
        // 1. WebSocket实时推送
        // 2. APP推送通知
        // 3. 短信通知（高风险时）
        // 4. 邮件通知
        
        // 示例：记录通知日志
        $container = ApplicationContext::getContainer();
        $logger = $container->get(LoggerInterface::class);
        
        $logger->info('风险预警通知内容', [
            'user_id' => $data['user_id'],
            'title' => $data['title'],
            'message' => $data['message'],
            'data' => $data['data']
        ]);
    }
} 