<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 通知推送任务
 */

namespace App\Job\Margin;

use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Psr\Log\LoggerInterface;

class NotificationJob extends Job
{
    /**
     * 任务执行失败后的重试次数，即最大执行次数为 $maxAttempts+1 次
     */
    protected int $maxAttempts = 3;

    /**
     * 通知数据
     */
    protected array $notificationData;

    public function __construct(array $notificationData)
    {
        $this->notificationData = $notificationData;
    }

    public function handle(): void
    {
        $container = ApplicationContext::getContainer();
        $logger = $container->get(LoggerInterface::class);

        try {
            $logger->info('开始执行通知推送任务', [
                'user_id' => $this->notificationData['user_id'],
                'type' => $this->notificationData['type'],
                'channels' => $this->notificationData['channels'] ?? []
            ]);

            // 根据配置的渠道发送通知
            $channels = $this->notificationData['channels'] ?? ['websocket'];
            
            foreach ($channels as $channel) {
                $this->sendNotificationByChannel($channel, $this->notificationData);
            }

            $logger->info('通知推送任务完成', [
                'user_id' => $this->notificationData['user_id'],
                'type' => $this->notificationData['type']
            ]);

        } catch (\Exception $e) {
            $logger->error('通知推送任务失败', [
                'user_id' => $this->notificationData['user_id'],
                'type' => $this->notificationData['type'],
                'error' => $e->getMessage()
            ]);
            
            throw $e; // 重新抛出异常以便队列重试
        }
    }

    /**
     * 根据渠道发送通知
     */
    private function sendNotificationByChannel(string $channel, array $data): void
    {
        $container = ApplicationContext::getContainer();
        $logger = $container->get(LoggerInterface::class);

        switch ($channel) {
            case 'websocket':
                $this->sendWebSocketNotification($data);
                break;
                
            case 'app_push':
                $this->sendAppPushNotification($data);
                break;
                
            case 'sms':
                $this->sendSmsNotification($data);
                break;
                
            case 'email':
                $this->sendEmailNotification($data);
                break;
                
            default:
                $logger->warning('未知的通知渠道', ['channel' => $channel]);
                break;
        }
    }

    /**
     * WebSocket通知
     */
    private function sendWebSocketNotification(array $data): void
    {
        // TODO: 实现WebSocket推送
        $container = ApplicationContext::getContainer();
        $logger = $container->get(LoggerInterface::class);
        
        $logger->info('WebSocket通知发送', [
            'user_id' => $data['user_id'],
            'title' => $data['title'],
            'message' => $data['message']
        ]);
    }

    /**
     * APP推送通知
     */
    private function sendAppPushNotification(array $data): void
    {
        // TODO: 集成APP推送服务（如极光推送、个推等）
        $container = ApplicationContext::getContainer();
        $logger = $container->get(LoggerInterface::class);
        
        $logger->info('APP推送通知发送', [
            'user_id' => $data['user_id'],
            'title' => $data['title'],
            'message' => $data['message']
        ]);
    }

    /**
     * 短信通知
     */
    private function sendSmsNotification(array $data): void
    {
        // TODO: 集成短信服务（如阿里云短信、腾讯云短信等）
        $container = ApplicationContext::getContainer();
        $logger = $container->get(LoggerInterface::class);
        
        $logger->info('短信通知发送', [
            'user_id' => $data['user_id'],
            'message' => $data['message']
        ]);
    }

    /**
     * 邮件通知
     */
    private function sendEmailNotification(array $data): void
    {
        // TODO: 集成邮件服务
        $container = ApplicationContext::getContainer();
        $logger = $container->get(LoggerInterface::class);
        
        $logger->info('邮件通知发送', [
            'user_id' => $data['user_id'],
            'title' => $data['title'],
            'message' => $data['message']
        ]);
    }
} 