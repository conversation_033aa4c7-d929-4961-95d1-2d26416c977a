<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 强平执行任务
 */

namespace App\Job\Margin;

use App\Process\Margin\Service\MarginLiquidationService;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Psr\Log\LoggerInterface;

class LiquidationJob extends Job
{
    /**
     * 任务执行失败后的重试次数，即最大执行次数为 $maxAttempts+1 次
     */
    protected int $maxAttempts = 3;

    /**
     * 任务数据
     */
    protected array $riskInfo;

    public function __construct(array $riskInfo)
    {
        $this->riskInfo = $riskInfo;
    }

    public function handle(): void
    {
        $container = ApplicationContext::getContainer();
        $liquidationService = $container->get(MarginLiquidationService::class);
        $logger = $container->get(LoggerInterface::class);

        try {
            $logger->info('开始执行强平任务', [
                'position_id' => $this->riskInfo['position_id'],
                'user_id' => $this->riskInfo['user_id']
            ]);

            $success = $liquidationService->executeLiquidation($this->riskInfo);

            if ($success) {
                $logger->info('强平任务执行成功', [
                    'position_id' => $this->riskInfo['position_id']
                ]);
            } else {
                $logger->warning('强平任务执行失败', [
                    'position_id' => $this->riskInfo['position_id']
                ]);
            }

        } catch (\Exception $e) {
            $logger->error('强平任务执行异常', [
                'position_id' => $this->riskInfo['position_id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e; // 重新抛出异常以便队列重试
        }
    }
} 