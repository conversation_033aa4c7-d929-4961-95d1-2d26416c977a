<?php

declare(strict_types=1);

namespace App\Job\Margin;

use App\Http\Api\Service\V1\Margin\MarginPositionCacheService;
use App\Http\Api\Service\V1\Margin\MarginRiskService;
use App\Model\Trade\TradeMarginPosition;
use App\Model\User\UserMarginBorrow;
use App\Model\Enums\Trade\Margin\MarginPositionStatus;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\Trade\Margin\MarginBorrowStatus;
use App\Enum\CurrencyConfigKey;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

/**
 * 仓位监控延时任务
 */
class PositionMonitoringJob extends Job
{
    protected int $positionId;
    protected string $changeType;
    protected array $changeData;

    public function __construct(int $positionId, string $changeType, array $changeData = [])
    {
        $this->positionId = $positionId;
        $this->changeType = $changeType;
        $this->changeData = $changeData;
        
        // 设置延时执行（5秒后执行，确保主事务已提交）
        $this->delay = 5;
    }

    public function handle(): void
    {
        try {
            // 使用容器获取实例
            $container = ApplicationContext::getContainer();
            $logger = $container->get(LoggerInterface::class);
            $positionCacheService = $container->get(MarginPositionCacheService::class);

            $logger->debug('开始处理仓位监控任务', [
                'position_id' => $this->positionId,
                'change_type' => $this->changeType,
                'change_data' => $this->changeData
            ]);

            // 重新查询仓位数据（确保获取到最新的已提交数据）
            $position = TradeMarginPosition::find($this->positionId);

            $logger->info('查询仓位结果', [
                'position_id' => $this->positionId,
                'position_found' => $position ? 'yes' : 'no',
                'position_data' => $position ? [
                    'user_id' => $position->getUserId(),
                    'currency_id' => $position->getCurrencyId(),
                    'status' => $position->getStatus(),
                    'margin_type' => $position->getMarginType()->value
                ] : null
            ]);

            if (!$position) {
                $logger->warning('仓位监控任务：仓位不存在', [
                    'position_id' => $this->positionId
                ]);
                return;
            }

            $logger->info('开始处理变更类型', [
                'change_type' => $this->changeType,
                'position_id' => $position->getId()
            ]);

            // 根据变更类型处理
            switch ($this->changeType) {
                case 'created':
                case 'increased':
                case 'reversed':
                    $logger->info('执行仓位监控评估', ['change_type' => $this->changeType]);
                    $this->evaluatePositionForMonitoring($position, $this->changeType, $positionCacheService, $logger);
                    break;

                case 'decreased':
                    $logger->info('执行减仓重新评估', ['change_type' => $this->changeType]);
                    // 减仓时重新评估是否还需要监控
                    $this->reevaluatePositionMonitoring($position, $this->changeType, $positionCacheService, $logger);
                    break;

                case 'closed':
                    $logger->info('执行平仓移除监控', ['change_type' => $this->changeType]);
                    // 平仓时从监控中移除
                    $this->removePositionFromMonitoring($position, $this->changeType, $positionCacheService, $logger);
                    break;

                default:
                    $logger->warning('未知的变更类型', [
                        'change_type' => $this->changeType,
                        'position_id' => $position->getId()
                    ]);
                    break;
            }

            $logger->info('仓位监控任务处理完成', [
                'position_id' => $this->positionId,
                'change_type' => $this->changeType
            ]);

        } catch (\Exception $e) {
            $container = ApplicationContext::getContainer();
            $logger = $container->get(LoggerInterface::class);
            
            $logger->error('仓位监控任务执行失败', [
                'position_id' => $this->positionId,
                'change_type' => $this->changeType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e; // 重新抛出异常，让队列处理重试
        }
    }

    /**
     * 评估仓位是否需要加入监控
     */
    protected function evaluatePositionForMonitoring(
        TradeMarginPosition $position,
        string $reason,
        MarginPositionCacheService $positionCacheService,
        LoggerInterface $logger
    ): void {
        $logger->info('开始评估仓位监控需求', [
            'position_id' => $position->getId(),
            'user_id' => $position->getUserId(),
            'currency_id' => $position->getCurrencyId(),
            'margin_type' => $position->getMarginType()->value,
            'reason' => $reason
        ]);

        // 检查是否有借贷（需要查询计价币的借贷）
        $hasBorrowing = $this->checkIfHasBorrowing(
            $position->getUserId(),
            $position->getCurrencyId(), // 基础币ID，用于获取计价币ID
            $position->getMarginType()
        );

        $logger->info('借贷检查结果', [
            'position_id' => $position->getId(),
            'has_borrowing' => $hasBorrowing ? 'yes' : 'no'
        ]);

        if ($hasBorrowing) {
            $this->addPositionToMonitoring($position, $reason, $positionCacheService, $logger);
        } else {
            // 确保不在监控中（可能之前有借贷，现在还清了）
            $this->removePositionFromMonitoring($position, "no_borrowing_{$reason}", $positionCacheService, $logger);
        }
    }

    /**
     * 重新评估仓位监控需求
     */
    protected function reevaluatePositionMonitoring(
        TradeMarginPosition $position, 
        string $reason, 
        MarginPositionCacheService $positionCacheService,
        LoggerInterface $logger
    ): void {
        $this->evaluatePositionForMonitoring($position, "reevaluate_{$reason}", $positionCacheService, $logger);
    }

    /**
     * 将仓位加入监控
     */
    protected function addPositionToMonitoring(
        TradeMarginPosition $position,
        string $reason,
        MarginPositionCacheService $positionCacheService,
        LoggerInterface $logger
    ): void {
        $isInCache = $positionCacheService->isPositionInCache($position);

        // 获取维持保证金率
        $marginBracket = $this->getMarginBracket($position, $logger);
        $keepRate = $marginBracket ? $marginBracket->getKeepRate() : 0.05; // 默认5%

        if (!$isInCache) {
            // 同步仓位数据到缓存（包含维持保证金率）
            $positionCacheService->syncPositionToCacheWithMarginBracket($position, $marginBracket);

            $logger->info('仓位加入风险监控', [
                'position_id' => $position->getId(),
                'user_id' => $position->getUserId(),
                'currency_id' => $position->getCurrencyId(),
                'reason' => $reason,
                'leverage' => $position->getLeverage(),
                'margin_type' => $position->getMarginType()->value,
                'keep_rate' => $keepRate,
                'margin_bracket_level' => $marginBracket?->getLevel()
            ]);
        } else {
            // 已在监控中，更新缓存数据（包含最新的维持保证金率）
            $positionCacheService->syncPositionToCacheWithMarginBracket($position, $marginBracket);

            $logger->debug('更新监控中的仓位数据', [
                'position_id' => $position->getId(),
                'reason' => $reason,
                'keep_rate' => $keepRate
            ]);
        }
    }

    /**
     * 从监控中移除仓位
     */
    protected function removePositionFromMonitoring(
        TradeMarginPosition $position, 
        string $reason, 
        MarginPositionCacheService $positionCacheService,
        LoggerInterface $logger
    ): void {
        $isInCache = $positionCacheService->isPositionInCache($position);
        
        if ($isInCache) {
            $positionCacheService->removePositionFromCache($position);
            
            $logger->info('仓位移出风险监控', [
                'position_id' => $position->getId(),
                'user_id' => $position->getUserId(),
                'currency_id' => $position->getCurrencyId(),
                'reason' => $reason
            ]);
        }
    }

    /**
     * 检查是否有借贷（查询计价币的借贷）
     */
    protected function checkIfHasBorrowing(int $userId, int $baseCurrencyId, MarginType $marginType): bool
    {
        // 使用容器获取实例
        $container = ApplicationContext::getContainer();
        $logger = $container->get(LoggerInterface::class);
        $redis = $container->get(Redis::class);

        // 获取计价币ID
        $quoteCurrencyId = $this->getQuoteCurrencyId($baseCurrencyId, $redis, $logger);

        if (!$quoteCurrencyId) {
            $logger->warning('无法获取计价币ID', [
                'base_currency_id' => $baseCurrencyId
            ]);
            return false;
        }

        // 将MarginType转换为对应的account_type
        $accountType = $this->getAccountTypeFromMarginType($marginType);

        $logger->info('检查借贷条件', [
            'user_id' => $userId,
            'base_currency_id' => $baseCurrencyId,
            'quote_currency_id' => $quoteCurrencyId,
            'margin_type' => $marginType->value,
            'account_type' => $accountType,
            'active_status' => MarginBorrowStatus::ACTIVE
        ]);

        // 先查询用户在该计价币的所有借贷记录用于调试
        $allRecords = UserMarginBorrow::where('user_id', $userId)
            ->where('currency_id', $quoteCurrencyId)
            ->get();

        $logger->info('用户计价币借贷记录', [
            'user_id' => $userId,
            'quote_currency_id' => $quoteCurrencyId,
            'total_records' => $allRecords->count(),
            'records' => $allRecords->map(function($record) {
                return [
                    'id' => $record->getId(),
                    'currency_id' => $record->getCurrencyId(),
                    'account_type' => $record->getAccountType(),
                    'status' => $record->getStatus(),
                    'borrow_amount' => $record->getBorrowAmount()
                ];
            })->toArray()
        ]);

        // 查询计价币的借贷记录
        $result = UserMarginBorrow::where('user_id', $userId)
            ->where('currency_id', $quoteCurrencyId) // 使用计价币ID
            ->where('account_type', $accountType)
            ->where('status', MarginBorrowStatus::ACTIVE) // 只查询借贷中的记录
            ->where('borrow_amount', '>', 0)
            ->exists();

        $logger->info('借贷检查最终结果', [
            'user_id' => $userId,
            'base_currency_id' => $baseCurrencyId,
            'quote_currency_id' => $quoteCurrencyId,
            'account_type' => $accountType,
            'has_borrowing' => $result
        ]);

        return $result;
    }

    /**
     * 获取计价币ID
     */
    protected function getQuoteCurrencyId(int $baseCurrencyId, Redis $redis, LoggerInterface $logger): ?int
    {
        try {
            $currencyKey = CurrencyConfigKey::getCurrencyKey($baseCurrencyId);
            $currencyConfig = $redis->hGetAll($currencyKey);

            $logger->info('币种配置信息', [
                'base_currency_id' => $baseCurrencyId,
                'currency_key' => $currencyKey,
                'config_exists' => !empty($currencyConfig),
                'quote_assets_id' => $currencyConfig['quote_assets_id'] ?? null
            ]);

            if (empty($currencyConfig) || !isset($currencyConfig['quote_assets_id'])) {
                $logger->warning('币种配置不存在或缺少quote_assets_id', [
                    'base_currency_id' => $baseCurrencyId,
                    'currency_config' => $currencyConfig
                ]);
                return null;
            }

            return (int)$currencyConfig['quote_assets_id'];

        } catch (\Exception $e) {
            $logger->error('获取计价币ID异常', [
                'base_currency_id' => $baseCurrencyId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取维持保证金率信息
     */
    protected function getMarginBracket(TradeMarginPosition $position, LoggerInterface $logger): ?object
    {
        try {
            // 使用容器获取MarginRiskService
            $container = ApplicationContext::getContainer();
            $marginRiskService = $container->get(MarginRiskService::class);

            $logger->debug('开始获取维持保证金率', [
                'position_id' => $position->getId(),
                'user_id' => $position->getUserId(),
                'currency_id' => $position->getCurrencyId(),
                'margin_type' => $position->getMarginType()->value
            ]);

            $marginBracket = $marginRiskService->getMarginBracket(
                $position->getUserId(),
                $position->getCurrencyId(),
                $position->getMarginType()
            );

            $logger->info('获取维持保证金率成功', [
                'position_id' => $position->getId(),
                'user_id' => $position->getUserId(),
                'currency_id' => $position->getCurrencyId(),
                'keep_rate' => $marginBracket?->getKeepRate(),
                'level' => $marginBracket?->getLevel()
            ]);

            return $marginBracket;

        } catch (\Exception $e) {
            $logger->warning('获取维持保证金率失败', [
                'position_id' => $position->getId(),
                'user_id' => $position->getUserId(),
                'currency_id' => $position->getCurrencyId(),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 将MarginType转换为对应的account_type
     */
    protected function getAccountTypeFromMarginType(MarginType $marginType): int
    {
        return match ($marginType) {
            MarginType::CROSS => 3,    // 全仓杠杆
            MarginType::ISOLATED => 7, // 逐仓杠杆
        };
    }
}
