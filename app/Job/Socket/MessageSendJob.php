<?php

/**
 * MessageSendJob.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/8
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Job\Socket;

use App\Model\WebsocketData\AbstractMessage;
use App\Service\WebSocket\MarketDataService;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;

class MessageSendJob extends Job
{
    public function __construct(
        public int $user_id = 0,
        public array $message = [],
        public ?AbstractMessage $messageFormat = null
    )
    {}

    /**
     * 只负责消息转发
     * @return void
     */
    public function handle(): void
    {
        try {
            go(function (){
                $this->messageFormat->fill($this->message);
                ApplicationContext::getContainer()->get(MarketDataService::class)->pushMessageToUser($this->user_id,$this->messageFormat->toJsonString());
            });
        }catch (\Throwable){}
    }
}