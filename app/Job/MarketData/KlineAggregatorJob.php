<?php

/**
 * KlineAggregatorJob.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/25
 * Website:algoquant.org
 */

namespace App\Job\MarketData;

use App\MarketData\Service\KlineAggregatorService;
use App\MarketData\Service\KlinePersistService;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;

class KlineAggregatorJob extends Job
{
    public function __construct(public array $kline){}
    public function handle()
    {
        try {
            ApplicationContext::getContainer()->get(KlinePersistService::class)->saveKlineToES($this->kline);
        }catch (\Throwable){}
    }
}