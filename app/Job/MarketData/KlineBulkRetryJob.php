<?php

declare(strict_types=1);

namespace App\Job\MarketData;

use App\Enum\MarketData\KlineIndex;
use Elasticsearch\Client;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;

class KlineBulkRetryJob extends Job
{
    public function __construct(public array $klines)
    {
        // 设置最大重试次数为 3
        $this->maxAttempts = 3;
    }

    public function handle()
    {
        if (empty($this->klines)) {
            return;
        }

        /** @var Client $elasticsearch */
        $elasticsearch = ApplicationContext::getContainer()->get(Client::class);

        $params = ['body' => []];
        foreach ($this->klines as $kline) {
            // 确保kline是数组
            if (! is_array($kline) || ! isset($kline['symbol'], $kline['market_type'], $kline['period'], $kline['open_time'])) {
                continue;
            }
            $indexName = KlineIndex::getIndexName($kline['symbol'], $kline['market_type']);
            $docId = "{$kline['symbol']}-{$kline['market_type']}-{$kline['period']}-{$kline['open_time']}";

            $params['body'][] = [
                'update' => [
                    '_index' => $indexName,
                    '_id' => $docId,
                ],
            ];
            $params['body'][] = [
                'doc' => $kline,
                'doc_as_upsert' => true,
            ];
        }
        
        if (empty($params['body'])) {
            return;
        }

        try {
            $response = $elasticsearch->bulk($params);
            if ($response['errors']) {
                throw new \Exception('Bulk persistence failed on retry.');
            }
        } catch (\Throwable $e) {
            throw $e;
        }
    }
} 