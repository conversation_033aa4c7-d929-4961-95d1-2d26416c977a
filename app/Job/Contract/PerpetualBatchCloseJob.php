<?php

declare(strict_types=1);
/**
 * 永续合约批量平仓异步任务
 */

namespace App\Job\Contract;

use App\Http\Api\Service\V1\Contract\PerpetualTradeService;
use App\Model\Trade\TradePerpetualPosition;
use App\Model\Enums\Trade\Perpetual\PositionStatus;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Model\Enums\Trade\Perpetual\TimeInForce;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;

class PerpetualBatchCloseJob extends Job
{
    protected int $userId;
    protected ?int $currencyId;
    protected ?int $marginMode;
    protected array $positionIds;

    public function __construct(int $userId, ?int $currencyId = null, ?int $marginMode = null, array $positionIds = [])
    {
        $this->userId = $userId;
        $this->currencyId = $currencyId;
        $this->marginMode = $marginMode;
        $this->positionIds = $positionIds;
    }

    public function handle(): void
    {
        $container = ApplicationContext::getContainer();
        $logger = $container->get(LoggerFactory::class)->get('合约交易', 'perpetual-logs');
        $tradeService = $container->get(PerpetualTradeService::class);

        try {
            $logger->info('开始执行永续合约批量平仓任务', [
                'user_id' => $this->userId,
                'currency_id' => $this->currencyId,
                'margin_mode' => $this->marginMode,
                'position_ids' => $this->positionIds
            ]);

            // 查询需要平仓的仓位
            $positions = $this->getPositionsToClose();

            $totalCount = $positions->count();
            $successCount = 0;
            $failedCount = 0;
            $errors = [];

            if ($totalCount === 0) {
                $logger->info('没有找到需要平仓的仓位', [
                    'user_id' => $this->userId
                ]);
                return;
            }

            // 逐个处理仓位平仓
            foreach ($positions as $position) {
                try {
                    $this->processPositionClose($position, $tradeService, $logger);
                    $successCount++;
                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "仓位 {$position->id} 平仓失败: " . $e->getMessage();
                    $logger->error('仓位平仓失败', [
                        'user_id' => $this->userId,
                        'position_id' => $position->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            $logger->info('永续合约批量平仓任务完成', [
                'user_id' => $this->userId,
                'total_count' => $totalCount,
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            $logger->error('永续合约批量平仓任务执行失败', [
                'user_id' => $this->userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取需要平仓的仓位
     */
    private function getPositionsToClose()
    {
        $query = TradePerpetualPosition::query()
            ->where('user_id', $this->userId)
            ->where('status', PositionStatus::HOLDING->value)
            ->where('available_quantity', '>', 0);

        // 如果指定了仓位ID，只处理指定的仓位
        if (!empty($this->positionIds)) {
            $query->whereIn('id', $this->positionIds);
        } else {
            // 如果指定了币种，添加币种过滤条件
            if ($this->currencyId !== null) {
                $query->where('currency_id', $this->currencyId);
            }

            // 如果指定了保证金模式，添加保证金模式过滤条件
            if ($this->marginMode !== null) {
                $query->where('margin_mode', $this->marginMode);
            }
        }

        return $query->get();
    }

    /**
     * 处理单个仓位平仓
     */
    private function processPositionClose(TradePerpetualPosition $position, PerpetualTradeService $tradeService, LoggerInterface $logger): void
    {
        // 确定平仓方向
        $closeSide = $this->getCloseSide($position->side);

        // 构建平仓订单数据
        $orderData = [
            'currency_id' => $position->currency_id,
            'side' => $closeSide,
            'order_type' => ContractOrderType::MARKET->value, // 使用市价单快速平仓
            'quantity' => $position->available_quantity,
            'margin_mode' => $position->margin_mode,
            'leverage' => $position->leverage,
            'time_in_force' => TimeInForce::GTC->value, // 立即成交或撤销
            'reduce_only' => 1, // 只减仓
        ];

        $logger->info('提交平仓订单', [
            'user_id' => $this->userId,
            'position_id' => $position->id,
            'order_data' => $orderData
        ]);

        // 调用交易服务创建平仓订单
        $result = $tradeService->createOrder($this->userId, $orderData);

        $logger->info('平仓订单创建成功', [
            'user_id' => $this->userId,
            'position_id' => $position->id,
            'order_id' => $result['order_id'] ?? null,
            'match_order_id' => $result['match_order_id'] ?? null
        ]);
    }

    /**
     * 根据仓位方向确定平仓方向
     */
    private function getCloseSide(int $positionSide): int
    {
        return match($positionSide) {
            1 => ContractSide::SELL_CLOSE->value, // 多头仓位 -> 卖出平多
            2 => ContractSide::BUY_CLOSE->value,  // 空头仓位 -> 买入平空
            default => throw new \InvalidArgumentException("无效的仓位方向: {$positionSide}")
        };
    }
}
