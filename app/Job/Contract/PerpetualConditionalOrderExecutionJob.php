<?php

declare(strict_types=1);
/**
 * 永续合约委托单执行异步任务
 */

namespace App\Job\Contract;

use App\Model\Trade\TradePerpetualConditionalOrder;
use App\Model\Enums\Trade\Perpetual\ConditionalOrderStatus;
use App\Model\Enums\Trade\Perpetual\ConditionalOrderType;
use App\Model\Enums\Trade\Perpetual\ExecutionMode;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use App\Http\Api\Service\V1\Contract\PerpetualTradeService;
use App\Http\Api\Service\V1\Contract\PerpetualConditionalCacheService;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;
use Psr\Log\LoggerInterface;
use Carbon\Carbon;

class PerpetualConditionalOrderExecutionJob extends Job
{
    protected int $conditionalOrderId;
    protected array $triggerData;

    public function __construct(int $conditionalOrderId, array $triggerData = [])
    {
        $this->conditionalOrderId = $conditionalOrderId;
        $this->triggerData = $triggerData;
    }

    public function handle(): void
    {
        $container = ApplicationContext::getContainer();
        $logger = logger('合约委托','perpetual-logs.log');
        $tradeService = $container->get(PerpetualTradeService::class);
        $cacheService = $container->get(PerpetualConditionalCacheService::class);

        try {
            $logger->info('开始执行委托单', [
                'conditional_order_id' => $this->conditionalOrderId,
                'trigger_data' => $this->triggerData
            ]);

            // 使用数据库事务确保数据一致性
            Db::transaction(function () use ($tradeService, $logger) {
                // 1. 查询委托单并加锁
                $conditionalOrder = TradePerpetualConditionalOrder::query()
                    ->where('id', $this->conditionalOrderId)
                    ->lockForUpdate()
                    ->first();

                if (!$conditionalOrder) {
                    throw new BusinessException(ResultCode::FAIL, '委托单不存在');
                }

                // 2. 检查委托单状态
                if ((int)$conditionalOrder->status !== ConditionalOrderStatus::WAITING->value) {
                    $logger->warning('委托单状态不允许执行', [
                        'order_id' => $this->conditionalOrderId,
                        'status' => $conditionalOrder->status
                    ]);
                    return;
                }

                // 3. 更新委托单状态为已触发
                $conditionalOrder->update([
                    'status' => ConditionalOrderStatus::TRIGGERED->value,
                    'triggered_at' => Carbon::now()
                ]);

                // 4. 构建普通订单数据
                $orderData = $this->buildOrderData($conditionalOrder);

                // 5. 执行下单
                $result = $tradeService->createOrder($conditionalOrder->user_id, $orderData);

                // 6. 更新委托单状态为已执行
                $conditionalOrder->update([
                    'status' => ConditionalOrderStatus::EXECUTED->value,
                    'executed_at' => Carbon::now(),
                    'executed_order_id' => $result['order_id'] ?? null,
                ]);

                $logger->info('委托单执行成功', [
                    'conditional_order_id' => $this->conditionalOrderId,
                    'executed_order_id' => $result['order_id'] ?? null,
                    'order_data' => $orderData
                ]);
            });

            // 7. 清理缓存（事务外执行）
            $cacheService->releaseExecutionLock($this->conditionalOrderId);

        } catch (\Throwable $e) {
            $this->handleExecutionFailure($e, $logger, $cacheService);
        }
    }

    /**
     * 构建普通订单数据
     * @param TradePerpetualConditionalOrder $conditionalOrder
     * @return array
     */
    private function buildOrderData(TradePerpetualConditionalOrder $conditionalOrder): array
    {
        // 计算执行价格
        $triggerPrice = $this->triggerData['trigger_price'] ?? $conditionalOrder->trigger_price;
        $executionPrice = $this->calculateExecutionPrice($conditionalOrder, $triggerPrice);

        return [
            'currency_id' => $conditionalOrder->currency_id,
            'side' => $conditionalOrder->side,
            'order_type' => $conditionalOrder->execution_type,
            'quantity' => $conditionalOrder->quantity,
            'price' => $executionPrice,
            'margin_mode' => $conditionalOrder->margin_mode,
            'leverage' => $conditionalOrder->leverage,
            'reduce_only' => $conditionalOrder->reduce_only,
            'time_in_force' => $conditionalOrder->time_in_force,
        ];
    }

    /**
     * 计算执行价格
     * @param TradePerpetualConditionalOrder $order
     * @param string $triggerPrice
     * @return string
     */
    private function calculateExecutionPrice(TradePerpetualConditionalOrder $order, string $triggerPrice): string
    {
        // 市价单直接返回0（由撮合引擎处理）
        if ((int)$order->execution_type === ContractOrderType::MARKET->value) {
            return '0';
        }

        // 如果是固定价格模式，直接返回设置的执行价格
        if ((int)$order->execution_mode === ExecutionMode::FIXED_PRICE->value) {
            return (string)$order->execution_price ?? $triggerPrice;
        }

        // 回调浮动模式，根据委托单类型和方向计算执行价格
        if ((int)$order->execution_mode === ExecutionMode::CALLBACK_FLOAT->value) {
            return $this->calculateCallbackPrice($order, $triggerPrice);
        }

        // 默认返回触发价格
        return $triggerPrice;
    }

    /**
     * 计算回调浮动价格
     * @param TradePerpetualConditionalOrder $order
     * @param string $triggerPrice
     * @return string
     */
    private function calculateCallbackPrice(TradePerpetualConditionalOrder $order, string $triggerPrice): string
    {
        $callbackRate = (float)$order->callback_rate / 100; // 转换为小数
        $conditionalType = (int)$order->conditional_type;
        $side = (int)$order->side;

        // 判断是多头还是空头
        $isLong = in_array($side, [ContractSide::BUY_OPEN->value, ContractSide::BUY_CLOSE->value]);

        switch ($conditionalType) {
            case ConditionalOrderType::TAKE_PROFIT->value: // 止盈
                if ($isLong) {
                    // 多头止盈: 执行价格 = 触发价格 × (1 + 回调幅度)
                    return bcmul($triggerPrice, (string)(1 + $callbackRate), 8);
                } else {
                    // 空头止盈: 执行价格 = 触发价格 × (1 - 回调幅度)
                    return bcmul($triggerPrice, (string)(1 - $callbackRate), 8);
                }

            case ConditionalOrderType::STOP_LOSS->value: // 止损
                if ($isLong) {
                    // 多头止损: 执行价格 = 触发价格 × (1 - 回调幅度)
                    return bcmul($triggerPrice, (string)(1 - $callbackRate), 8);
                } else {
                    // 空头止损: 执行价格 = 触发价格 × (1 + 回调幅度)
                    return bcmul($triggerPrice, (string)(1 + $callbackRate), 8);
                }

            case ConditionalOrderType::TRAILING->value: // 追踪委托
                // 追踪委托根据价格变动方向计算
                if ($isLong) {
                    // 多头追踪: 执行价格 = 触发价格 × (1 - 回调幅度)
                    return bcmul($triggerPrice, (string)(1 - $callbackRate), 8);
                } else {
                    // 空头追踪: 执行价格 = 触发价格 × (1 + 回调幅度)
                    return bcmul($triggerPrice, (string)(1 + $callbackRate), 8);
                }

            default:
                // 计划委托或其他类型，直接使用触发价格
                return $triggerPrice;
        }
    }

    /**
     * 处理执行失败
     * @param \Throwable $e
     * @param LoggerInterface $logger
     * @param PerpetualConditionalCacheService $cacheService
     * @return void
     */
    private function handleExecutionFailure(
        \Throwable $e,
        LoggerInterface $logger,
        PerpetualConditionalCacheService $cacheService
    ): void {
        try {
            $conditionalOrder = TradePerpetualConditionalOrder::findOrFail($this->conditionalOrderId);

            // 根据异常类型获取错误信息
            $errorInfo = $this->extractErrorInfo($e);

            // 更新委托单状态为执行失败
            $conditionalOrder->update([
                'status' => ConditionalOrderStatus::FAILED->value,
                'failure_reason' => $errorInfo['message'],
                'updated_at' => Carbon::now()
            ]);

            // 释放执行锁
            $cacheService->releaseExecutionLock($this->conditionalOrderId);

            $logger->error('委托单执行失败', [
                'conditional_order_id' => $this->conditionalOrderId,
                'error_type' => $errorInfo['type'],
                'error_code' => $errorInfo['code'],
                'error_message' => $errorInfo['message'],
                'trace' => $e->getTraceAsString()
            ]);

        } catch (\Throwable $updateException) {
            $logger->error('更新委托单失败状态时发生异常', [
                'conditional_order_id' => $this->conditionalOrderId,
                'original_error' => $e->getMessage(),
                'update_error' => $updateException->getMessage()
            ]);
        }
    }

    /**
     * 提取异常错误信息
     * @param \Throwable $e
     * @return array
     */
    private function extractErrorInfo(\Throwable $e): array
    {
        if ($e instanceof BusinessException) {
            // BusinessException特殊处理
            $response = $e->getResponse();
            return [
                'type' => 'BusinessException',
                'code' => $response->code->value ?? 'UNKNOWN',
                'message' => $response->message ?? $e->getMessage(),
                'data' => $response->data ?? []
            ];
        }

        // 其他异常的通用处理
        return [
            'type' => get_class($e),
            'code' => $e->getCode(),
            'message' => $e->getMessage(),
            'data' => []
        ];
    }
}
