<?php

/**
 * MatchOrderModifyFail.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/8
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Job\MatchOrder;

use App\Service\MatchEngineOrderService;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;

class MatchOrderModifyFail extends Job
{
    public function __construct(
        public array $event
    )
    {

    }

    public function handle()
    {
        go(function () {
            ApplicationContext::getContainer()->get(MatchEngineOrderService::class)->dispatcher(
                "OrderModifyFailedEvent",
                $this->event['market_type'],
                [$this->event]
            );
        });
    }
}