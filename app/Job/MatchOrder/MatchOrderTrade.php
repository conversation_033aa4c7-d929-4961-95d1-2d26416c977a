<?php

/**
 * MatchOrderTrade.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/30
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Job\MatchOrder;

use App\Service\MatchEngineOrderService;
use Carbon\Carbon;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;

class MatchOrderTrade extends Job
{
    public int $uniqid = 0;
    public function __construct(public array $event)
    {
        $this->uniqid = Carbon::now()->getTimestampMs()+rand(10,1000000);
    }

    public function handle(): void
    {
        go(function(){
            ApplicationContext::getContainer()->get(MatchEngineOrderService::class)->orderTradeEvent($this->event);
        });
    }
}