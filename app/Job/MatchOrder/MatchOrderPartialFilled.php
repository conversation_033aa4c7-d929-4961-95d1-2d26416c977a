<?php

/**
 * MatchOrderPartialFiller.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/30
 * Website:algoquant.org
 */

namespace App\Job\MatchOrder;

use App\Service\MatchEngineOrderService;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;

class MatchOrderPartialFilled extends Job
{
    public function __construct(public array $order)
    {
    }

    public function handle(): void
    {
        go(function () {
            ApplicationContext::getContainer()->get(MatchEngineOrderService::class)->orderPartialFilledEvent($this->order);
        });
    }
}