<?php

/**
 * MatchOrderConfrim.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/30
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Job\MatchOrder;

use App\Service\MatchEngineOrderService;
use Hyperf\AsyncQueue\Job;
use Hyperf\Context\ApplicationContext;

class MatchOrderConfirm extends Job
{
    protected int $maxAttempts = 3;
    public function __construct(public array $event){}

    public function handle(): void
    {
        go(function(){
            ApplicationContext::getContainer()->get(MatchEngineOrderService::class)->confirmOrderEvent($this->event['order_id']);
        });
    }
}