<?php

declare(strict_types=1);

namespace App\Command\MarketData;

use App\MarketData\Service\Chain\ChainCurrencySync;
use Hyperf\Command\Annotation\Command;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Context\ApplicationContext;
use Psr\Container\ContainerInterface;

#[Command]
class SyncChainCurrency extends HyperfCommand
{
    public function __construct(protected ContainerInterface $container)
    {
        parent::__construct('chain:sync-currency');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('Hyperf Demo Command');
    }

    public function handle(): void
    {
        //检查更新币安的alpha币种数据同步
        ApplicationContext::getContainer()->get(ChainCurrencySync::class)->syncChainCurrencies();
    }
}
