<?php

namespace App\Command\MarketData;

use App\Model\Currency\Currency;
use App\MarketData\Service\CryptoKlineSync;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputArgument;

#[Command]
class KlineSyncCommand extends HyperfCommand
{
    #[Inject]
    protected ContainerInterface $container;

    public function __construct()
    {
        parent::__construct('kline:sync');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('Sync kline data for specific currency by ID')
            ->addArgument('currency_id', InputArgument::REQUIRED, 'Currency ID from currency table');
    }

    public function handle()
    {
        $currencyId = $this->input->getArgument('currency_id');

        // 查询币种
        $currency = Currency::find($currencyId);
        if (!$currency) {
            $this->error("Currency with ID {$currencyId} not found");
            return 1;
        }

        try {
            $klineSync = $this->container->get(CryptoKlineSync::class);
            $klineSync->syncCurrencyKlines($currency);
            
            return 0;
        } catch (\Throwable $e) {
            $this->error("Sync failed: " . $e->getMessage());
            return 1;
        }
    }
}
