<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆配置同步命令
 * @link     bbbtrade.net
 * @document bbbtrade.net
 * @contact  <EMAIL>
 */

namespace App\Command;

use App\Service\CollectData\MarginConfigCollect;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputOption;

#[Command]
class MarginConfigSyncCommand extends HyperfCommand
{
    protected ContainerInterface $container;

    protected MarginConfigCollect $marginConfigCollect;

    /**
     * 命令名称
     */
    protected ?string $name = 'margin:sync';

    /**
     * 命令描述
     */
    protected string $description = '同步币安杠杆借贷配置数据';

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
        $this->marginConfigCollect = $container->get(MarginConfigCollect::class);
        parent::__construct();
    }

    /**
     * 配置命令
     */
    public function configure(): void
    {
        parent::configure();
        $this->setDescription('同步币安杠杆借贷配置数据到本地数据库');
        $this->addOption('type', 't', InputOption::VALUE_OPTIONAL, '同步类型: isolated(逐仓), cross(全仓), brackets(全仓档位), isolated-tiers(逐仓档位), all(全部)', 'all');
        $this->addOption('cache', 'c', InputOption::VALUE_NONE, '同步完成后写入Redis缓存');
        $this->addOption('clear-cache', null, InputOption::VALUE_NONE, '清除Redis缓存');
        $this->addOption('cache-only', null, InputOption::VALUE_NONE, '仅写入缓存（不执行同步）');
    }

    /**
     * 执行命令
     */
    public function handle(): void
    {
        $type = $this->input->getOption('type');
        $cache = $this->input->getOption('cache');
        $clearCache = $this->input->getOption('clear-cache');
        $cacheOnly = $this->input->getOption('cache-only');
        
        try {
            // 清除缓存
            if ($clearCache) {
                $this->info('🗑️  清除Redis缓存...');
                $success = $this->marginConfigCollect->clearMarginConfigCache();
                if ($success) {
                    $this->info('✅ Redis缓存清除成功');
                } else {
                    $this->error('❌ Redis缓存清除失败');
                }
                return;
            }
            
            // 仅写入缓存
            if ($cacheOnly) {
                $this->info('📝 仅写入Redis缓存...');
                $this->writeCacheData();
                return;
            }
            
            $this->info("开始同步币安杠杆借贷配置数据 (类型: {$type})...");
            
            // 显示当前统计信息
            $this->showCurrentStats();
            
            // 执行同步
            $startTime = microtime(true);
            $result = false;
            
            switch ($type) {
                case 'isolated':
                    $this->info('🔄 执行逐仓杠杆数据同步...');
                    $result = $this->marginConfigCollect->syncBinanceMarginConfig();
                    break;
                case 'cross':
                    $this->info('🔄 执行全仓杠杆数据同步...');
                    $result = $this->marginConfigCollect->syncBinanceCrossMarginConfig();
                    break;
                case 'brackets':
                    $this->info('🔄 执行全仓杠杆风险档位数据同步...');
                    $result = $this->marginConfigCollect->syncBinanceMarginBrackets();
                    break;
                case 'isolated-tiers':
                    $this->info('🔄 执行逐仓杠杆档位配置数据同步...');
                    $result = $this->marginConfigCollect->syncBinanceIsolatedMarginTiers();
                    break;
                case 'all':
                default:
                    $this->info('🔄 同步逐仓杠杆数据...');
                    $isolatedResult = $this->marginConfigCollect->syncBinanceMarginConfig();
                    
                    $this->info('🔄 同步全仓杠杆数据...');
                    $crossResult = $this->marginConfigCollect->syncBinanceCrossMarginConfig();
                    
                    $this->info('🔄 同步全仓杠杆风险档位数据...');
                    $bracketsResult = $this->marginConfigCollect->syncBinanceMarginBrackets();
                    
                    $this->info('🔄 同步逐仓杠杆档位配置数据...');
                    $isolatedTiersResult = $this->marginConfigCollect->syncBinanceIsolatedMarginTiers();
                    
                    $result = $isolatedResult && $crossResult && $bracketsResult && $isolatedTiersResult;
                    break;
            }
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            
            if ($result) {
                $this->info("✅ 同步成功! 耗时: {$executionTime}秒");
                
                // 显示同步后的统计信息
                $this->showCurrentStats();
                
                // 写入缓存
                if ($cache) {
                    $this->writeCacheData();
                }
            } else {
                $this->error('❌ 同步失败，请查看日志获取详细信息');
            }
            
        } catch (\Throwable $e) {
            $this->error('❌ 执行过程中发生异常: ' . $e->getMessage());
            $this->error('文件: ' . $e->getFile() . ':' . $e->getLine());
        }
    }

    /**
     * 写入缓存数据
     */
    private function writeCacheData(): void
    {
        try {
            $this->info('📝 写入Redis缓存...');
            $startTime = microtime(true);
            
            $success = $this->marginConfigCollect->cacheMarginConfigToRedis();
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            
            if ($success) {
                $this->info("✅ Redis缓存写入成功! 耗时: {$executionTime}秒");
            } else {
                $this->error('❌ Redis缓存写入失败，请查看日志获取详细信息');
            }
            
        } catch (\Throwable $e) {
            $this->error('❌ 写入Redis缓存异常: ' . $e->getMessage());
        }
    }

    /**
     * 显示当前统计信息
     */
    private function showCurrentStats(): void
    {
        try {
            $stats = $this->marginConfigCollect->getMarginConfigStats();
            
            if (empty($stats)) {
                $this->warn('⚠️  无法获取统计信息');
                return;
            }
            
            $this->info('📊 当前杠杆配置统计信息:');
            $this->table(
                ['项目', '数量'],
                [
                    ['总记录数', $stats['total_records'] ?? 0],
                    ['全仓杠杆记录', $stats['cross_margin_count'] ?? 0],
                    ['逐仓杠杆记录', $stats['isolated_margin_count'] ?? 0],
                    ['基础币可借记录', $stats['base_borrowable_count'] ?? 0],
                    ['计价币可借记录', $stats['quote_borrowable_count'] ?? 0],
                    ['最后更新时间', $stats['last_updated'] ?? '无'],
                ]
            );
            
        } catch (\Throwable $e) {
            $this->warn('⚠️  获取统计信息失败: ' . $e->getMessage());
        }
    }
} 