<?php

declare(strict_types=1);

/**
 * %CLASS%
 * Author:%AUTHOR%
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:%DATE%
 * Website:%WEBSITE%
 */

namespace %NAMESPACE%;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use %REQUESTCLASS%;
use %MODELCLASS%;
use %SERVICE%;

#[Controller(prefix: "%PREFIX%")]
#[Middleware(TokenMiddleware::class)]
class %CLASS% extends AbstractController
{
    #[Inject]
    protected %SERVICECLASS% $%SERVICEVARIABLENAME%;

    /**
     * 说明：
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(%REQUEST% $request): Result
    {
        $result = $this->%SERVICEVARIABLENAME%->list($request);
        return $this->success($result);
    }

    /**
     * 说明：详情
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
     #[GetMapping("show/{id}")]
    public function show(int $id, %REQUEST% $request): Result
    {
        $data = %MODEL%::query()->where('id', $id)->first();
        return $this->success($data);
    }

    /**
     * 说明：创建
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
     #[PostMapping("store")]
    public function store(%REQUEST% $request): Result
    {
        $result = $this->%SERVICEVARIABLENAME%->store($request->all());
        return $this->success($result);
    }

    /**
     * 说明：修改
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
     #[PutMapping("update/{id}")]
    public function update(int $id, %REQUEST% $request): Result
    {
        $result = $this->%SERVICEVARIABLENAME%->update($id, $request->all());
        return $this->success($result);
    }

    /**
     * 说明：删除
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
     #[DeleteMapping("destroy/{id}")]
    public function destroy(int $id): Result
    {
        $this->%SERVICEVARIABLENAME%->destroy($id);
        return $this->success();
    }
}
