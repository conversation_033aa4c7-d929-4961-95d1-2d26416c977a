<?php

declare(strict_types=1);

namespace App\Command\Margin;

use App\Model\Enums\Trade\Margin\MarginBorrowStatus;
use App\Model\Enums\Trade\Margin\MarginInterestStatus;
use App\Model\User\UserMarginBorrow;
use App\Model\User\UserMarginInterest;
use Carbon\Carbon;
use Hyperf\Command\Annotation\Command;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Logger\LoggerFactory;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Input\InputOption;

#[Command]
class MarginInterestCalculateCommand extends HyperfCommand
{
    /**
     * 计算精度
     */
    private const CALCULATION_PRECISION = 8;
    
    /**
     * 每天小时数
     */
    private const HOURS_PER_DAY = 24;

    /**
     * 分块处理大小
     */
    private const CHUNK_SIZE = 100;

    #[Inject]
    protected ContainerInterface $container;

    protected LoggerInterface $logger;

    public function __construct()
    {
        parent::__construct('margin:interest-calculate');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('计算杠杆借贷利息并写入数据表')
            ->addOption('dry-run', null, InputOption::VALUE_NONE, '试运行模式，不实际写入数据')
            ->addOption('user-id', null, InputOption::VALUE_OPTIONAL, '指定用户ID进行计算')
            ->addOption('currency-id', null, InputOption::VALUE_OPTIONAL, '指定币种ID进行计算')
            ->addOption('force', null, InputOption::VALUE_NONE, '强制重新计算（忽略last_interest_time）');
    }

    public function handle()
    {
        $this->logger = $this->container->get(LoggerFactory::class)->get('margin-interest');
        
        $startTime = microtime(true);
        $currentHour = Carbon::now()->startOfHour();
        
        $isDryRun = $this->input->getOption('dry-run');
        $userId = $this->input->getOption('user-id');
        $currencyId = $this->input->getOption('currency-id');
        $force = $this->input->getOption('force');
        
        $this->info("开始执行杠杆利息计算任务...");
        $this->info("执行时间: {$currentHour->toDateTimeString()}");
        $this->info("模式: " . ($isDryRun ? '试运行' : '正式执行'));
        
        if ($userId) {
            $this->info("指定用户ID: {$userId}");
        }
        if ($currencyId) {
            $this->info("指定币种ID: {$currencyId}");
        }
        if ($force) {
            $this->info("强制模式: 忽略last_interest_time");
        }

        try {
            // 使用分块处理借贷记录
            $processedCount = 0;
            $totalInterest = '0';
            $errorCount = 0;
            $totalRecords = 0;

            // 先统计总记录数
            $totalRecords = $this->getBorrowRecordsCount($currentHour, $userId, $currencyId, $force);

            if ($totalRecords === 0) {
                $this->info("没有需要计息的借贷记录");
                return 0;
            }

            $this->info("找到 {$totalRecords} 条需要计息的借贷记录，将分块处理");

            // 分块处理数据
            $this->processBorrowRecordsInChunks(
                $currentHour,
                $userId,
                $currencyId,
                $force,
                $isDryRun,
                $processedCount,
                $totalInterest,
                $errorCount
            );

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            $this->info("执行完成!");
            $this->info("处理记录数: {$processedCount}");
            $this->info("错误记录数: {$errorCount}");
            $this->info("总利息金额: {$totalInterest}");
            $this->info("执行耗时: {$executionTime}秒");

            $this->logger->info('杠杆利息计算任务完成', [
                'execution_time' => $currentHour->toDateTimeString(),
                'processed_count' => $processedCount,
                'error_count' => $errorCount,
                'total_interest' => $totalInterest,
                'duration' => $executionTime,
                'dry_run' => $isDryRun
            ]);

            return 0;

        } catch (\Throwable $e) {
            $this->error("执行过程中发生异常: {$e->getMessage()}");
            $this->logger->error('杠杆利息计算任务异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * 获取需要计息的借贷记录数量
     */
    private function getBorrowRecordsCount(Carbon $currentHour, ?string $userId = null, ?string $currencyId = null, bool $force = false): int
    {
        $query = $this->buildBorrowQuery($currentHour, $userId, $currencyId, $force);
        return $query->count();
    }

    /**
     * 分块处理借贷记录
     */
    private function processBorrowRecordsInChunks(
        Carbon $currentHour,
        ?string $userId,
        ?string $currencyId,
        bool $force,
        bool $isDryRun,
        int &$processedCount,
        string &$totalInterest,
        int &$errorCount
    ): void {
        $query = $this->buildBorrowQuery($currentHour, $userId, $currencyId, $force);

        $query->chunk(self::CHUNK_SIZE, function ($borrowRecords) use ($currentHour, $isDryRun, &$processedCount, &$totalInterest, &$errorCount) {
            foreach ($borrowRecords as $borrowRecord) {
                try {
                    $interestAmount = $this->calculateInterest($borrowRecord);

                    if (!$isDryRun) {
                        $this->processInterestRecord($borrowRecord, $interestAmount, $currentHour);
                    }

                    $totalInterest = bcadd($totalInterest, $interestAmount, self::CALCULATION_PRECISION);
                    $processedCount++;

                    $this->line("✓ 用户ID: {$borrowRecord->user_id}, 币种ID: {$borrowRecord->currency_id}, 利息: {$interestAmount}");

                } catch (\Throwable $e) {
                    $errorCount++;
                    $this->error("✗ 处理借贷记录失败 ID: {$borrowRecord->id}, 错误: {$e->getMessage()}");
                    $this->logger->error('处理借贷记录失败', [
                        'borrow_id' => $borrowRecord->id,
                        'user_id' => $borrowRecord->user_id,
                        'currency_id' => $borrowRecord->currency_id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
        });
    }

    /**
     * 构建借贷记录查询
     */
    private function buildBorrowQuery(Carbon $currentHour, ?string $userId = null, ?string $currencyId = null, bool $force = false)
    {
        $query = UserMarginBorrow::query()
            ->where(UserMarginBorrow::FIELD_STATUS, MarginBorrowStatus::ACTIVE);

        // 如果不是强制模式，则检查last_interest_time
        if (!$force) {
            $query->where(function ($q) use ($currentHour) {
                $q->whereNull(UserMarginBorrow::FIELD_LAST_INTEREST_TIME)
                  ->orWhere(UserMarginBorrow::FIELD_LAST_INTEREST_TIME, '<', $currentHour);
            });
        }

        // 可选的用户ID过滤
        if ($userId) {
            $query->where(UserMarginBorrow::FIELD_USER_ID, $userId);
        }

        // 可选的币种ID过滤
        if ($currencyId) {
            $query->where(UserMarginBorrow::FIELD_CURRENCY_ID, $currencyId);
        }

        return $query;
    }

    /**
     * 计算利息金额
     */
    private function calculateInterest(UserMarginBorrow $borrowRecord): string
    {
        // 计算实际借贷金额（借贷金额 - 已还金额）
        $actualBorrowAmount = bcsub(
            $this->formatNumberForBC($borrowRecord->borrow_amount),
            $this->formatNumberForBC($borrowRecord->repaid_amount),
            self::CALCULATION_PRECISION
        );

        // 如果实际借贷金额为0或负数，则不计息
        if (bccomp($actualBorrowAmount, '0', self::CALCULATION_PRECISION) <= 0) {
            return '0';
        }

        // 计算小时利息：实际借贷金额 × 日利率 ÷ 24
        $dailyRateFormatted = $this->formatNumberForBC($borrowRecord->daily_rate);
        $hourlyRate = bcdiv(
            $dailyRateFormatted,
            (string)self::HOURS_PER_DAY,
            self::CALCULATION_PRECISION
        );

        $interestAmount = bcmul(
            $actualBorrowAmount,
            $hourlyRate,
            self::CALCULATION_PRECISION
        );

        return $interestAmount;
    }

    /**
     * 处理利息记录（写入数据库）
     */
    private function processInterestRecord(UserMarginBorrow $borrowRecord, string $interestAmount, Carbon $currentHour): void
    {
        Db::transaction(function () use ($borrowRecord, $interestAmount, $currentHour) {
            // 创建利息记录
            $interestRecord = new UserMarginInterest();
            $interestRecord->user_id = $borrowRecord->user_id;
            $interestRecord->borrow_id = $borrowRecord->id;
            $interestRecord->currency_id = $borrowRecord->currency_id;
            $interestRecord->account_type = $borrowRecord->account_type;
            $interestRecord->interest_amount = (float)$interestAmount;
            $interestRecord->interest_rate = $borrowRecord->daily_rate;
            $interestRecord->calculation_period = 1; // 1小时
            $interestRecord->start_time = $borrowRecord->last_interest_time ?? $borrowRecord->borrow_time;
            $interestRecord->end_time = $currentHour->toDateTimeString();
            $interestRecord->status = MarginInterestStatus::PENDING;
            
            if (!$interestRecord->save()) {
                throw new \RuntimeException('创建利息记录失败');
            }

            // 更新借贷记录的最后计息时间
            $borrowRecord->last_interest_time = $currentHour;
            if (!$borrowRecord->save()) {
                throw new \RuntimeException('更新借贷记录失败');
            }
        });
    }

    /**
     * 格式化数字为BC数学库可用的字符串格式
     * 处理科学计数法等特殊格式
     */
    private function formatNumberForBC($number): string
    {
        if ($number === null) {
            return '0';
        }

        // 使用sprintf将科学计数法转换为普通小数格式
        // %.20f 确保有足够的精度来处理小数
        $formatted = sprintf('%.20f', (float)$number);

        // 移除末尾的零和小数点（如果需要）
        $formatted = rtrim($formatted, '0');
        $formatted = rtrim($formatted, '.');

        // 如果结果为空，返回0
        if ($formatted === '' || $formatted === '-') {
            return '0';
        }

        return $formatted;
    }
}
