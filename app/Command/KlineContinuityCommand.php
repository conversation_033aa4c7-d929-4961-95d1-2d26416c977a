<?php

declare(strict_types=1);

namespace App\Command;

use App\Enum\ProcessCmdKey;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;

/**
 * K线连续性检查进程管理命令
 */
#[Command]
class KlineContinuityCommand extends HyperfCommand
{
    protected ContainerInterface $container;
    protected Redis $redis;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
        $this->redis = $container->get(Redis::class);
        parent::__construct('kline:continuity');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('K线数据连续性检查进程管理命令');
        $this->addArgument('action', null, '操作类型: reload|force-check|status');
        $this->addOption('symbol', 's', null, '指定币种符号 (用于force-check)');
        $this->addOption('period', 'p', null, '指定时间周期 (用于force-check)');
        $this->addOption('market-type', 'm', null, '指定市场类型: 1=现货, 5=合约 (用于force-check)');
    }

    public function handle()
    {
        $action = $this->input->getArgument('action');
        
        switch ($action) {
            case 'reload':
                $this->reloadCurrencies();
                break;
            case 'force-check':
                $this->forceCheck();
                break;
            case 'status':
                $this->showStatus();
                break;
            default:
                $this->showHelp();
                break;
        }
    }

    /**
     * 重新加载币种数据
     */
    private function reloadCurrencies(): void
    {
        try {
            $message = json_encode(['type' => 'reload_currencies']);
            $this->redis->publish(ProcessCmdKey::KLINE_CONTINUITY_CHECK_CMD->value, $message);
            
            $this->line('<info>已发送重新加载币种命令到K线连续性检查进程</info>');
        } catch (\Throwable $e) {
            $this->line('<error>发送命令失败: ' . $e->getMessage() . '</error>');
        }
    }

    /**
     * 强制执行检查
     */
    private function forceCheck(): void
    {
        try {
            $symbol = $this->input->getOption('symbol');
            $period = $this->input->getOption('period');
            $marketType = $this->input->getOption('market-type');

            if ($marketType && !in_array($marketType, ['1', '5'])) {
                $this->line('<error>市场类型必须是 1 (现货) 或 5 (合约)</error>');
                return;
            }

            $message = [
                'type' => 'force_check',
                'symbol' => $symbol,
                'period' => $period,
                'market_type' => $marketType ? (int)$marketType : null
            ];

            $this->redis->publish(ProcessCmdKey::KLINE_CONTINUITY_CHECK_CMD->value, json_encode($message));
            
            $this->line('<info>已发送强制检查命令到K线连续性检查进程</info>');
            
            if ($symbol) {
                $this->line("  - 币种: {$symbol}");
            }
            if ($period) {
                $this->line("  - 周期: {$period}");
            }
            if ($marketType) {
                $marketTypeName = $marketType === '1' ? '现货' : '合约';
                $this->line("  - 市场类型: {$marketTypeName}");
            }
            
        } catch (\Throwable $e) {
            $this->line('<error>发送命令失败: ' . $e->getMessage() . '</error>');
        }
    }

    /**
     * 显示进程状态
     */
    private function showStatus(): void
    {
        $this->line('<info>K线连续性检查进程状态</info>');
        $this->line('进程名称: kline-continuity-check');
        $this->line('Redis频道: ' . ProcessCmdKey::KLINE_CONTINUITY_CHECK_CMD->value);
        
        // 这里可以添加更多状态信息，比如从Redis获取进程运行状态等
        $this->line('<comment>注意: 进程状态需要通过系统进程管理工具查看</comment>');
    }

    /**
     * 显示帮助信息
     */
    private function showHelp(): void
    {
        $this->line('<info>K线连续性检查进程管理命令使用说明:</info>');
        $this->line('');
        $this->line('<comment>可用操作:</comment>');
        $this->line('  reload              重新加载币种数据');
        $this->line('  force-check         强制执行连续性检查');
        $this->line('  status              显示进程状态');
        $this->line('');
        $this->line('<comment>force-check 选项:</comment>');
        $this->line('  --symbol, -s        指定币种符号 (如: BTCUSDT)');
        $this->line('  --period, -p        指定时间周期 (如: 1m, 1h, 1d)');
        $this->line('  --market-type, -m   指定市场类型 (1=现货, 5=合约)');
        $this->line('');
        $this->line('<comment>使用示例:</comment>');
        $this->line('  php bin/hyperf.php kline:continuity reload');
        $this->line('  php bin/hyperf.php kline:continuity force-check');
        $this->line('  php bin/hyperf.php kline:continuity force-check --symbol=BTCUSDT --period=1h');
        $this->line('  php bin/hyperf.php kline:continuity force-check --symbol=BTCUSDT --market-type=1');
        $this->line('  php bin/hyperf.php kline:continuity status');
    }
}
