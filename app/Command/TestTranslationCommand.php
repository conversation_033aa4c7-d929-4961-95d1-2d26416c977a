<?php

declare(strict_types=1);

namespace App\Command;

use App\Service\TranslationService;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;

#[Command]
class TestTranslationCommand extends HyperfCommand
{
    #[Inject]
    protected TranslationService $translationService;

    public function __construct(protected ContainerInterface $container)
    {
        parent::__construct('test:translation');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('测试翻译服务');
    }

    public function handle()
    {
        $this->line('开始测试翻译服务...', 'info');
        
        // 1. 检查服务可用性
        $this->line('1. 检查翻译服务可用性...', 'comment');
        if ($this->translationService->isAvailable()) {
            $this->line('✅ 翻译服务可用', 'info');
        } else {
            $this->line('❌ 翻译服务不可用', 'error');
            return;
        }
        
        // 2. 获取支持的语言
        $this->line('2. 获取支持的语言列表...', 'comment');
        $languages = $this->translationService->getSupportedLanguages();
        if (!empty($languages)) {
            $this->line('✅ 支持的语言数量: ' . count($languages), 'info');
            foreach (array_slice($languages, 0, 5) as $lang) {
                $this->line("   - {$lang['code']}: {$lang['name']}", 'comment');
            }
        } else {
            $this->line('❌ 无法获取语言列表', 'error');
        }
        
        // 3. 测试单个翻译
        $this->line('3. 测试单个文本翻译...', 'comment');
        $testTexts = [
            'Bitcoin is a decentralized digital currency.',
            'Ethereum is a blockchain platform.',
            'DeFi protocols are revolutionizing finance.',
        ];
        
        foreach ($testTexts as $text) {
            $translated = $this->translationService->translate($text);
            $this->line("原文: {$text}", 'comment');
            $this->line("译文: {$translated}", 'info');
            $this->line('---');
        }
        
        // 4. 测试加密货币专用翻译
        $this->line('4. 测试加密货币专用翻译...', 'comment');
        $cryptoText = 'Bitcoin and Ethereum are leading cryptocurrencies in the DeFi ecosystem.';
        $cryptoTranslated = $this->translationService->translateCrypto($cryptoText);
        $this->line("原文: {$cryptoText}", 'comment');
        $this->line("译文: {$cryptoTranslated}", 'info');
        
        // 5. 测试批量翻译
        $this->line('5. 测试批量翻译...', 'comment');
        $batchTexts = [
            'text1' => 'Cryptocurrency exchange',
            'text2' => 'Trading pairs',
            'text3' => 'Market data synchronization',
        ];
        
        $batchResults = $this->translationService->batchTranslate($batchTexts);
        foreach ($batchResults as $key => $translated) {
            $this->line("{$key}: {$batchTexts[$key]} -> {$translated}", 'info');
        }
        
        $this->line('翻译服务测试完成！', 'info');
    }
}
