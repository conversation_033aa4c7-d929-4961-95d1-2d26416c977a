<?php

declare(strict_types=1);
/**
 * TipoffsRequest
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Request\Tipoffs;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class TipoffsRequest extends BaseFormRequest
{

    /**
     * 举报验证规则
     * Summary of createRules
     * @return array{dynamics_id: string[]}
     */
    public function createRules(): array{
        return [
            'dynamics_id' => ['required','integer'],
            'content'=> ['required','string'],
        ]; 
    }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [
            'content'=>'举报内容',
            'dynamics_id'=>'举报动态id'
        ];
    }
}
