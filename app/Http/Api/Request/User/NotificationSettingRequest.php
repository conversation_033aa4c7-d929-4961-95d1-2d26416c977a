<?php

declare(strict_types=1);
/**
 * NotificationSettingRequest
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-07
 * Website:xxx
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class NotificationSettingRequest extends BaseFormRequest
{
    public function updateRules(): array
    {
        return [
            'system_message_enabled' => ['required', 'in:0,1'],
            'trading_message_enabled' => ['required', 'in:0,1'],
            'security_message_enabled' => ['required', 'in:0,1'],
            'promotion_message_enabled' => ['required', 'in:0,1'],
            'email_login_enabled' => ['required', 'in:0,1'],
            'email_trading_enabled' => ['required', 'in:0,1'],
            'email_withdrawal_enabled' => ['required', 'in:0,1'],
            'email_security_enabled' => ['required', 'in:0,1'],
            'email_promotion_enabled' => ['required', 'in:0,1'],
            'email_news_enabled' => ['required', 'in:0,1'],
            'push_login_enabled' => ['required', 'in:0,1'],
            'push_trading_enabled' => ['required', 'in:0,1'],
            'push_price_alert_enabled' => ['required', 'in:0,1'],
            'push_security_enabled' => ['required', 'in:0,1'],
            'push_promotion_enabled' => ['required', 'in:0,1'],
            'push_news_enabled' => ['required', 'in:0,1'],
            'sms_login_enabled' => ['required', 'in:0,1'],
            'sms_trading_enabled' => ['required', 'in:0,1'],
            'sms_withdrawal_enabled' => ['required', 'in:0,1'],
            'sms_security_enabled' => ['required', 'in:0,1'],
        ];
    }

    /**
     * 规则
     */
    public function rules(): array
    {
        // Rule::unique(Model::getTable())->ignore($this->input('id'))
        // Rule::unique(Model::getTable())
        // 获取指定 key 的参数值
        // $name = $this->input('name')
        // Rule::exists(Model::getTable(), 'id')
        // Rule::exists(Model::getTable())->where(function ($query) {
        //    $query->where('account_id', 1);
        //}),
        // Rule::in(['first-zone', 'second-zone'])
        // Rule::notIn(['sprinkles', 'cherries'])
        // Rule::requiredIf($this->input('name') == 'admin')
        // required_if:name,admin,...
        // required_array_keys:key1,key2,... // 验证的字段必须是一个数组，并且必须至少包含指定的键。

        return [];
    }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [];
    }
}
