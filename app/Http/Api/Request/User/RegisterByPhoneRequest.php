<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;

class RegisterByPhoneRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'phone' => 'required|string|max:15|unique:cpx_user,phone',
            'code' => 'required|string|max:15',
            'sms_code' => 'required|string|max:6',
            'invite_code' => 'nullable|string|max:16',
            'password' => 'required|string|min:6|max:32',
            'confirm_password' => 'required|same:password',
        ];
    }

    public function attributes(): array
    {
        return [
            'phone' => '手机号',
            'code' => '国家码',
            'sms_code' => '短信验证码',
            'invite_code' => '邀请码',
            'password' => '密码',
            'confirm_password' => '确认密码',
        ];
    }
}
