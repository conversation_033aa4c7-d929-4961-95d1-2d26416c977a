<?php

declare(strict_types=1);
/**
 * BindRequest.php
 * Author    chenmqq (<EMAIL>)
 * Version   1.0
 * Date      2025/3/25
 * website  algoquant.org
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;

class BindRequest extends BaseFormRequest
{
    public function bindPhoneRules(): array
    {
        return [
            'phone' => 'required|string|unique:cpx_user,phone',
            'code' => 'required|string',
            'sms_code' => 'required|string',
        ];
    }

    public function bindEmailRules(): array
    {
        return [
            'email' => 'required|string|unique:cpx_user,email',
            'email_code' => 'required|string',
        ];
    }

    /**
     * 获取谷歌验证器密钥及二维码
     */
    public function google2faRules(): array
    {
        return [
            'email_code' => 'nullable|string|size:6',
            'sms_code' => 'nullable',
            'password' => 'required|string|min:6|max:32',
        ];
    }

    /**
     * 绑定谷歌验证器
     */
    public function bindGoogle2faRules(): array
    {
        return [
            'google2fa_secret' => 'required|string',
            'google2fa_code' => 'required|string',
        ];
    }

    public function attributes(): array
    {
        return [
            'email' => '邮箱',
            'email_code' => '邮箱验证码',
            'phone' => '手机号',
            'code' => '手机国家代码',
            'sms_code' => '短信验证码',
            'password' => '登录密码',
            'google2fa_secret' => '谷歌验证器密钥',
            'google2fa_code' => '谷歌验证器验证码',
        ];
    }
}
