<?php

declare(strict_types=1);
/**
 * KycVerificationRequest
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-05
 * Website:xxx
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class KycVerificationRequest extends BaseFormRequest
{
    public function submitRules(): array
    {
        return [
            'first_name' => 'required|string',
            'middle_name' => 'nullable|string',
            'last_name' => 'required|string',
            'document_type' => 'required|string|in:passport,id_card,driver_license',
            'document_number' => 'required|string',
            'document_country' => 'required|string',
            'document_expiry_date' => 'nullable|date',
            'document_front_image' => 'required|string',
            'document_back_image' => 'required|string',
            'selfie_image' => 'nullable|string',
            'birthday' => 'nullable|date',
        ];
    }
    /**
     * 规则
     */
    public function rules(): array
    {
        // Rule::unique(Model::getTable())->ignore($this->input('id'))
        // Rule::unique(Model::getTable())
        // 获取指定 key 的参数值
        // $name = $this->input('name')
        // Rule::exists(Model::getTable(), 'id')
        // Rule::exists(Model::getTable())->where(function ($query) {
        //    $query->where('account_id', 1);
        //}),
        // Rule::in(['first-zone', 'second-zone'])
        // Rule::notIn(['sprinkles', 'cherries'])
        // Rule::requiredIf($this->input('name') == 'admin')
        // required_if:name,admin,...
        // required_array_keys:key1,key2,... // 验证的字段必须是一个数组，并且必须至少包含指定的键。

        return [];
    }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [];
    }
}
