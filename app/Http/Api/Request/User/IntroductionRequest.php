<?php

declare(strict_types=1);
/**
 * IntroductionRequest.php
 * Author    chenmqq (<EMAIL>)
 * Version   1.0
 * Date      2025/3/25
 * website  algoquant.org
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;

class IntroductionRequest extends BaseFormRequest
{
    /**
     * 简介设置 验证规则
     *
     * @return array
     */
    public function setIntroductionRules(): array
    {
        return [
            'introduction' => ['required','string']
        ];
    }

}
