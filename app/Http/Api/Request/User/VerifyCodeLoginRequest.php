<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Request\User;

use Hyperf\Validation\Request\FormRequest;

class VerifyCodeLoginRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => 'nullable',
            'phone' => 'nullable',
            'code' => 'nullable',
            'email_code' => 'nullable',
            'sms_code' => 'nullable',
            'username' => 'nullable',
            'google2fa_code' => 'nullable',
        ];
    }

    public function attributes(): array
    {
        return [
            'email' => '邮箱',
            'phone' => '手机号',
            'code' => '手机国家代码',
            'email_code' => '邮箱验证码',
            'sms_code' => '短信验证码',
            'username' => '用户名',
            'google2fa_code' => '谷歌验证码',
        ];
    }
}
