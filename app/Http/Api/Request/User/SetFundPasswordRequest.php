<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;

class SetFundPasswordRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'email_code' => 'nullable|string|size:6',
            'sms_code' => 'nullable',
            'google2fa_code' => 'nullable',
            'fund_password' => 'required|string|min:6|max:32',
            'confirm_fund_password' => 'required|string|same:fund_password'
        ];
    }

    public function attributes(): array
    {
        return [
            'email_code' => '邮箱验证码',
            'sms_code' => '短信验证码',
            'google2fa_code' => '谷歌验证码',
            'fund_password' => '资金密码',
            'confirm_fund_password' => '确认资金密码',
        ];
    }
}
