<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;

class ResetPasswordRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'email' => 'nullable|email|max:100',
            'email_code' => 'nullable|string|size:6',
            'phone' => 'nullable',
            'code' => 'nullable',
            'sms_code' => 'nullable',
            'google2fa_code' => 'nullable',
            'password' => 'required|string|min:6|max:32',
            'confirm_password' => 'required|string|same:password'
        ];
    }

    public function attributes(): array
    {
        return [
            'email' => '邮箱',
            'email_code' => '邮箱验证码',
            'phone' => '手机号',
            'code' => '手机国家代码',
            'sms_code' => '短信验证码',
            'google2fa_code' => '谷歌验证码',
            'password' => '密码',
            'confirm_password' => '确认密码',
        ];
    }
}
