<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;

class RegisterByEmailRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'email' => 'required|email|max:64|unique:cpx_user,email',
            'email_code' => 'required|string|max:6',
            'invite_code' => 'nullable|string|max:16',
            'password' => 'required|string|min:6|max:32',
            'confirm_password' => 'required|same:password',
        ];
    }

    public function attributes(): array
    {
        return [
            'email' => '邮箱',
            'email_code' => '邮箱验证码',
            'invite_code' => '邀请码',
            'password' => '密码',
            'confirm_password' => '确认密码',
        ];
    }
}
