<?php

declare(strict_types=1);

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;

class VipLevelRequest extends BaseFormRequest
{
    /**
     * VIP等级列表验证规则
     */
    public function listRules(): array
    {
        return [];
    }

    /**
     * 字段映射名称
     */
    public function attributes(): array
    {
        return [
            'page' => '页码',
            'page_size' => '每页数量',
        ];
    }
}
