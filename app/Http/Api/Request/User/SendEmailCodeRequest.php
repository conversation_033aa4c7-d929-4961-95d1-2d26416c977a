<?php

declare(strict_types=1);
/**
 * SendEmailCodeRequest.php
 * Author    chenmqq (<EMAIL>)
 * Version   1.0
 * Date      2025/3/25
 * website  algoquant.org
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;

class SendEmailCodeRequest extends BaseFormRequest
{
    public function sendEmailCodeRules(): array
    {
        return [
            'email' => 'required|email|max:100',
            'type' => 'required|string|in:register,reset_password,login',
        ];
    }

    public function sendEmailCodeByLoginRules(): array
    {
        return [
            'email' => 'required|email|max:100',
            'type' => 'required|string|in:bind_email,bind_google2fa,set_fund_password', // 后续可能有扩展
        ];
    }

    public function attributes(): array
    {
        return [
            'email' => '邮箱',
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => '请输入邮箱',
            'email.email' => '邮箱格式不正确',
            'email.max' => '邮箱长度不能超过100个字符',
            'email.unique' => '该邮箱已被注册',
        ];
    }
}
