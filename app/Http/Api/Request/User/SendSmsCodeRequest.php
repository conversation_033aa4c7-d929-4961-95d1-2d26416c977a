<?php

declare(strict_types=1);
/**
 * SendSmsCodeRequest.php
 * Author    chenmqq (<EMAIL>)
 * Version   1.0
 * Date      2025/3/25
 * website  algoquant.org
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;

class SendSmsCodeRequest extends BaseFormRequest
{
    public function sendSmsCodeRules(): array
    {
        return [
            'phone' => 'required|string|max:15',
            'code' => 'required|string|max:15',
            'type' => 'required|string|in:register,reset_password,login',
        ];
    }

    public function sendSmsCodeByLoginRules(): array
    {
        return [
            'phone' => 'required|string|max:15',
            'code' => 'required|string|max:15',
            'type' => 'required|string|in:bind_phone,bind_google2fa,set_fund_password', // 后续可能有扩展
        ];
    }

    public function attributes(): array
    {
        return [
            'phone' => '手机号',
            'code' => '国家码',
            'type' => '验证码类型',
        ];
    }
}
