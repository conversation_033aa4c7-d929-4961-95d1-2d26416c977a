<?php

declare(strict_types=1);
/**
 * WithdrawalSettingRequest
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class WithdrawalSettingRequest extends BaseFormRequest
{
    public function updateRules(): array
    {
        return [
            'small_withdrawal_enabled' => 'nullable|in:0,1',
            'small_withdrawal_limit' => 'required_if:small_withdrawal_enabled,1|numeric|min:1',
            'withdrawal_whitelist_enabled' => 'nullable|in:0,1',
            'withdrawal_whitelist' => 'required_if:withdrawal_whitelist_enabled,1|array',
            'withdrawal_revoke_enabled' => 'nullable|in:0,1',
            'preferred_networks' => 'nullable|array',
        ];
    }

    /**
     * 规则
     */
    public function rules(): array
    {
        // Rule::unique(Model::getTable())->ignore($this->input('id'))
        // Rule::unique(Model::getTable())
        // 获取指定 key 的参数值
        // $name = $this->input('name')
        // Rule::exists(Model::getTable(), 'id')
        // Rule::exists(Model::getTable())->where(function ($query) {
        //    $query->where('account_id', 1);
        //}),
        // Rule::in(['first-zone', 'second-zone'])
        // Rule::notIn(['sprinkles', 'cherries'])
        // Rule::requiredIf($this->input('name') == 'admin')
        // required_if:name,admin,...
        // required_array_keys:key1,key2,... // 验证的字段必须是一个数组，并且必须至少包含指定的键。

        return [];
    }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [
            'small_withdrawal_enabled' => '小额免密提现',
            'small_withdrawal_limit' => '小额免密提现限额',
            'withdrawal_whitelist_enabled' => '提现白名单',
            'withdrawal_whitelist' => '提现白名单',
            'withdrawal_revoke_enabled' => '撤销提现',
            'preferred_networks' => '偏好网络',
        ];
    }
}
