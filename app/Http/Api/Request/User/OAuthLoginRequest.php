<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Request\User;

use Hyperf\Validation\Request\FormRequest;

class OAuthLoginRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'provider' => 'required|string|in:google,apple',
            'token' => 'required|string',
            'display_name' => 'nullable|string|max:50',
            'avatar' => 'nullable|string',
            'invite_code' => 'nullable|string|exists:cpx_user,invite_code',
        ];
    }

    public function attributes(): array
    {
        return [
            'provider' => '提供商',
            'token' => '令牌',
            'nickname' => '昵称',
            'avatar' => '头像',
            'invite_code' => '邀请码',
        ];
    }
}
