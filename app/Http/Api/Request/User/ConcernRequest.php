<?php

declare(strict_types=1);
/**
 * Concern.php
 * Author    chenmqq (<EMAIL>)
 * Version   1.0
 * Date      2025/3/25
 * website  algoquant.org
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;

class ConcernRequest extends BaseFormRequest
{
    /**
     * 关注 验证规则
     *
     * @return array
     */
    public function concernRules(): array
    {
        return [
            'user_id' => ['required','integer']
        ];
    }

    /**
     * 取消关注 验证规则
     *
     * @return array
     */
    public function unConcernRules(): array
    {
        return [
            'user_id' => ['required','integer']
        ];
    }

    public function attributes(): array
    {
        return [
            'user_id' => '用户ID',
        ];
    }
}
