<?php

declare(strict_types=1);
/**
 * PassKeyRequest
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-07
 * Website:xxx
 */

namespace App\Http\Api\Request\User;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class PassKeyRequest extends BaseFormRequest
{

    /**
     * 注册验证规则
     * 验证客户端提交的注册响应数据
     */
    public function registerVerifyRules(): array
    {
        return [
            'response' => ['required'],
        ];
    }

    /**
     * 登录选项验证规则
     * 验证可选的用户名参数
     */
    public function loginOptionsRules(): array
    {
        return [
            'username' => ['required', 'string'],
        ];
    }

    /**
     * 登录验证规则
     * 验证客户端提交的认证响应数据
     */
    public function loginVerifyRules(): array
    {
        return [
            'response' => ['required'],
            'challenge_key' => ['required', 'string'],
        ];
    }

    /**
     * 删除通行密钥验证规则
     * 验证可选的凭证ID参数
     */
    public function deleteRules(): array
    {
        return [
            'credential_id' => ['sometimes', 'string'],
        ];
    }

    /**
     * 字段映射名称
     * 用于错误消息显示
     */
    public function attributes(): array
    {
        return [
            'response' => '客户端响应',
            'response.id' => '凭证ID',
            'response.rawId' => '原始凭证ID',
            'response.type' => '凭证类型',
            'response.response.clientDataJSON' => '客户端数据',
            'response.response.attestationObject' => '认证对象',
            'response.response.authenticatorData' => '认证器数据',
            'response.response.signature' => '签名',
            'response.response.userHandle' => '用户句柄',
            'challenge_key' => '挑战码密钥',
            'username' => '用户名',
            'credential_id' => '凭证ID',
        ];
    }
}
