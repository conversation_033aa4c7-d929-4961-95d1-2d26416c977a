<?php

declare(strict_types=1);
/**
 * CategoryRequest

 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-06-24
 * Website:xxx
 */

namespace App\Http\Api\Request\Category;

use App\Http\Api\Request\BaseFormRequest;

class CategoryRequest extends BaseFormRequest
{

    public function __construct(\Psr\Container\ContainerInterface $container)
    {
        parent::__construct($container);
    }
    /**
     * 验证规则
     *
     * @return array
     */
    public function getRules(): array
    {
        return [
            'key' => ['required','string']
        ];
    }

    // public function rules(): array
    // {
    //     return [
    //         // 'key' => ['required','string']
    //     ];
    // }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [
            'key' => trans('category.key') ?: '分类标识',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}
