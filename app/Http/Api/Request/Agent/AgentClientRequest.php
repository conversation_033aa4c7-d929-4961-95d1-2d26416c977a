<?php

declare(strict_types=1);
/**
 * AgentClientRequest
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-11
 * Website:xxx
 */

namespace App\Http\Api\Request\Agent;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class AgentClientRequest extends BaseFormRequest
{
    public function updateRemark(): array
    {
        return [
            'remark' => 'required|string',
        ];
    }
    /**
     * 规则
     */
    public function rules(): array
    {
        // Rule::unique(Model::getTable())->ignore($this->input('id'))
        // Rule::unique(Model::getTable())
        // 获取指定 key 的参数值
        // $name = $this->input('name')
        // Rule::exists(Model::getTable(), 'id')
        // Rule::exists(Model::getTable())->where(function ($query) {
        //    $query->where('account_id', 1);
        //}),
        // Rule::in(['first-zone', 'second-zone'])
        // Rule::notIn(['sprinkles', 'cherries'])
        // Rule::requiredIf($this->input('name') == 'admin')
        // required_if:name,admin,...
        // required_array_keys:key1,key2,... // 验证的字段必须是一个数组，并且必须至少包含指定的键。

        return [];
    }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [];
    }
}
