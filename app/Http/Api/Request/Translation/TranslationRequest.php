<?php

declare(strict_types=1);
/**
 * TranslationRequest
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Request\Translation;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class TranslationRequest extends BaseFormRequest
{
    
    /**
     * 翻译 验证规则
     * Summary of infoRules
     * @return array{dynamics_id: string[]}
     */
    public function infoRules(): array{
        return [
            'content'=> ['required','string'],
            'from'=> ['required','string'],
            'to'=> ['required','string'],
        ]; 
    }
    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [];
    }
}
