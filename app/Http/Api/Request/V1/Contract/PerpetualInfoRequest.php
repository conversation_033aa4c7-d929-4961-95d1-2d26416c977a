<?php

declare(strict_types=1);
/**
 * 永续合约信息请求验证
 */

namespace App\Http\Api\Request\V1\Contract;

use App\Http\Api\Request\BaseFormRequest;
use Hyperf\Validation\Rule;

class PerpetualInfoRequest extends BaseFormRequest
{
    /**
     * 获取交易规则验证规则
     */
    public function exchangeInfoRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取合约列表验证规则
     */
    public function contractsRules(): array
    {
        return [];
    }

    /**
     * 获取杠杆档位配置验证规则
     */
    public function leverageBracketRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取24小时统计验证规则
     */
    public function ticker24hrRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取最新价格验证规则
     */
    public function tickerPriceRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取标记价格验证规则
     */
    public function markPriceRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取资金费率验证规则
     */
    public function fundingRateRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取资金费率历史验证规则
     */
    public function fundingRateHistoryRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:1000'],
            'start_time' => ['nullable', 'integer', 'min:1'],
            'end_time' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取深度信息验证规则
     */
    public function depthRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:5', 'max:1000'],
        ];
    }

    /**
     * 获取K线数据验证规则
     */
    public function klinesRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'interval' => ['required', 'string', 'in:1m,3m,5m,15m,30m,1h,2h,4h,6h,8h,12h,1d,3d,1w,1M'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:1500'],
            'start_time' => ['nullable', 'integer', 'min:1'],
            'end_time' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取最近成交记录验证规则
     */
    public function recentTradesRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:1000'],
        ];
    }
}