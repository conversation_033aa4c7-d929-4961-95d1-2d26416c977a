<?php

declare(strict_types=1);
/**
 * 永续合约委托单请求验证
 */

namespace App\Http\Api\Request\V1\Contract;

use App\Http\Api\Request\BaseFormRequest;
use App\Model\Enums\Trade\Perpetual\ConditionalOrderType;
use App\Model\Enums\Trade\Perpetual\ConditionalOrderStatus;
use App\Model\Enums\Trade\Perpetual\TriggerCondition;
use App\Model\Enums\Trade\Perpetual\ExecutionMode;
use App\Model\Enums\Trade\Perpetual\MarginMode;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Model\Enums\Trade\Perpetual\TimeInForce;
use Hyperf\Validation\Rule;

class PerpetualConditionalOrderRequest extends BaseFormRequest
{
    /**
     * 创建委托单验证规则
     */
    public function createRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'conditional_type' => ['required', 'integer', Rule::in([
                ConditionalOrderType::TAKE_PROFIT->value,
                ConditionalOrderType::STOP_LOSS->value,
                ConditionalOrderType::SCHEDULED->value,
                ConditionalOrderType::TRAILING->value,
            ])],
            'margin_mode' => ['required', 'integer', Rule::in([
                MarginMode::CROSS->value,
                MarginMode::ISOLATED->value,
            ])],
            'side' => ['required', 'integer', Rule::in([
                ContractSide::BUY_OPEN->value,
                ContractSide::SELL_OPEN->value,
                ContractSide::BUY_CLOSE->value,
                ContractSide::SELL_CLOSE->value,
            ])],
            'quantity' => ['required', 'numeric', 'min:0.00000001'],
            'leverage' => ['required', 'numeric', 'min:1', 'max:200'],
            'reduce_only' => ['nullable', 'boolean'],
            'time_in_force' => ['nullable', 'integer', Rule::in([
                TimeInForce::GTC->value,
                TimeInForce::IOC->value,
                TimeInForce::FOK->value,
                TimeInForce::GTD->value,
            ])],

            // 触发条件
            'trigger_price' => ['required', 'numeric', 'min:0.00000001'],
            'trigger_condition' => ['required', 'integer', Rule::in([
                TriggerCondition::GREATER_THAN_OR_EQUAL->value,
                TriggerCondition::LESS_THAN_OR_EQUAL->value,
            ])],

            // 执行方式
            'execution_type' => ['required', 'integer', Rule::in([
                ContractOrderType::LIMIT->value,
                ContractOrderType::MARKET->value,
            ])],
            'execution_price' => ['nullable', 'numeric', 'min:0.00000001'],

            // 执行模式（止盈止损使用）
            'execution_mode' => ['nullable', 'integer', Rule::in([
                ExecutionMode::FIXED_PRICE->value,
                ExecutionMode::CALLBACK_FLOAT->value,
            ])],
            'callback_rate' => ['nullable', 'numeric', 'min:0.01', 'max:50'],

            // 过期时间
            'expires_at' => ['nullable', 'date', 'after:now'],
        ];
    }

    /**
     * 撤销委托单验证规则
     */
    public function cancelRules(): array
    {
        return [
            'conditional_order_id' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * 修改委托单验证规则
     */
    public function updateRules(): array
    {
        return [
            'conditional_order_id' => ['required', 'integer', 'min:1'],
            'quantity' => ['nullable', 'numeric', 'min:0.00000001'],
            'trigger_price' => ['nullable', 'numeric', 'min:0.00000001'],
            'execution_price' => ['nullable', 'numeric', 'min:0.00000001'],
            'callback_rate' => ['nullable', 'numeric', 'min:0.01', 'max:50'],
            'expires_at' => ['nullable', 'date', 'after:now'],
        ];
    }

    /**
     * 查询委托单列表验证规则
     */
    public function listRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'conditional_type' => ['nullable', 'integer', Rule::in([
                ConditionalOrderType::TAKE_PROFIT->value,
                ConditionalOrderType::STOP_LOSS->value,
                ConditionalOrderType::SCHEDULED->value,
                ConditionalOrderType::TRAILING->value,
            ])],
            'status' => ['nullable', 'integer', Rule::in([
                ConditionalOrderStatus::WAITING->value,
                ConditionalOrderStatus::TRIGGERED->value,
                ConditionalOrderStatus::EXECUTED->value,
                ConditionalOrderStatus::CANCELLED->value,
                ConditionalOrderStatus::EXPIRED->value,
                ConditionalOrderStatus::FAILED->value,
            ])],
            'page' => ['nullable', 'integer', 'min:1'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * 查询委托单详情验证规则
     */
    public function detailRules(): array
    {
        return [
            'conditional_order_id' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * 手动执行委托单验证规则
     */
    public function executeRules(): array
    {
        return [
            'conditional_order_id' => ['required', 'integer', 'min:1'],
        ];
    }
}
