<?php

/**
 * TradeSpotRequest.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/4
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Request\V1;

use App\Enum\Config\TradeConfigKey;
use App\Enum\CurrencyConfigKey;
use App\Model\Trade\TradeSpotOrder;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Hyperf\Validation\Request\FormRequest;
use Hyperf\Validation\Rule;

class TradeSpotRequest extends FormRequest
{
    #[Inject]
    protected Redis $redis;

    public function authorize()
    {
        return true;
    }

    /**
     * 现货下单接口请求验证规则
     * @return array
     */
    public function orderRules(): array
    {
        $currencyId = $this->input('currency_id');
        $quantityRules = ['required', 'numeric', 'min:0.00000001'];

        // 如果有 currency_id，添加基于 Redis 配置的数量验证
        if ($currencyId) {
            $quantityRules[] = function ($attribute, $value, $fail) use ($currencyId) {
                $this->validateQuantityRange($attribute, $value, $fail, $currencyId);
            };
        }

        return [
            'currency_id' => 'required|integer',
            'side' => 'required|in:buy,sell',
            'type' => 'required|in:market,limit',
            'quantity' => $quantityRules,
            'price' => 'required_if:type,limit|numeric|min:0.00000001',
            'time_in_force' => 'nullable|in:gtc,ioc,fok',
            'take_profit_price' => 'nullable|string|regex:/^\d+(\.\d{1,8})?$/|min:0.00000001',
            'stop_loss_price' => 'nullable|string|regex:/^\d+(\.\d{1,8})?$/|min:0.00000001'
        ];
    }



    /**
     * 现货撤单接口请求验证规则
     * @return array
     */
    public function cancelRules(): array
    {
        return [
            'order_id' => 'required|integer|min:1'
        ];
    }

    public function configRules(): array
    {
        return [
            'currency_id' => 'required|integer'
        ];
    }

    /**
     * 查询挂单接口请求验证规则
     * @return array
     */
    public function pendingOrdersRules(): array
    {
        return [
            'currency_id' => 'nullable|integer|min:1',
            'page' => 'nullable|integer|min:1',
            'page_size' => 'nullable|integer|min:1|max:100'
        ];
    }

    /**
     * 修改挂单接口请求验证规则
     * @return array
     */
    public function modifyOrderRules(): array
    {
        $orderId = $this->input('order_id');
        
        // 基础验证规则
        $priceRules = [
            'required',
            'numeric',
            'min:0.00000001',
            'max:99999999.99999999',
            'regex:/^\d+(\.\d{1,8})?$/' // 最多8位小数
        ];
        
        $quantityRules = [
            'required',
            'numeric',
            'min:0.00000001',
            'max:99999999.99999999',
            'regex:/^\d+(\.\d{1,8})?$/' // 最多8位小数
        ];

        // 如果有 order_id，添加基于订单币种配置的验证
        if ($orderId) {
            $priceRules[] = function ($attribute, $value, $fail) use ($orderId) {
                $this->validatePriceRangeByOrder($attribute, $value, $fail, $orderId);
            };
            
            $quantityRules[] = function ($attribute, $value, $fail) use ($orderId) {
                $this->validateQuantityRangeByOrder($attribute, $value, $fail, $orderId);
            };
        }

        return [
            'order_id' => 'required|integer|min:1',
            'price' => $priceRules,
            'quantity' => $quantityRules
        ];
    }

    /**
     * 成交明细查询接口请求验证规则
     * @return array
     */
    public function tradeHistoryRules(): array
    {
        return [
            'currency_id' => 'nullable|integer|min:1',
            'page' => 'nullable|integer|min:1',
            'page_size' => 'nullable|integer|min:1|max:100'
        ];
    }

    /**
     * 批量撤单接口请求验证规则
     * @return array
     */
    public function cancelAllOrdersRules(): array
    {
        $currencyId = $this->input('currency_id');
        $currencyRules = ['nullable', 'integer', 'min:1'];

        // 如果有 currency_id，添加基于 Redis 配置的币种存在性验证
        if ($currencyId) {
            $currencyRules[] = function ($attribute, $value, $fail) use ($currencyId) {
                $this->validateCurrencyExists($attribute, $value, $fail, $currencyId);
            };
        }

        return [
            'currency_id' => $currencyRules // 可选参数，指定撤销特定币种的所有订单
        ];
    }

    /**
     * 验证数量范围
     * @param string $attribute
     * @param mixed $value
     * @param \Closure $fail
     * @param int $currencyId
     * @return void
     */
    protected function validateQuantityRange(string $attribute, float $value, \Closure $fail, int $currencyId): void
    {
        try {
            // 使用 TradeConfigKey::getTradeConfigKey 获取 Redis key，第二个参数固定为 1
            $redisKey = TradeConfigKey::getTradeConfigKey($currencyId, 1);

            // 检查 Redis key 是否存在
            if (!$this->redis->exists($redisKey)) {
                $fail('交易配置不存在，请联系管理员');
                return;
            }

            // 从 Redis 中获取配置数据
            $config = $this->redis->hGetAll($redisKey);

            if (empty($config)) {
                $fail('交易配置数据为空，请联系管理员');
                return;
            }

            // 获取最小和最大交易数量
            $minTradeNum = $config['min_trade_num'] ?? null;
            $maxTradeNum = $config['max_trade_num'] ?? null;

            // 验证最小数量
            if ($minTradeNum !== null && $value < (float)$minTradeNum) {
                $fail("交易数量不能小于 {$minTradeNum}");
                return;
            }

            // 验证最大数量
            if ($maxTradeNum !== null && $value > (float)$maxTradeNum) {
                $fail("交易数量不能大于 {$maxTradeNum}");
                return;
            }

        } catch (\Exception $e) {
            $fail('验证交易数量时发生错误，请稍后重试');
        }
    }

    /**
     * 根据订单ID验证价格范围
     * @param string $attribute
     * @param mixed $value
     * @param \Closure $fail
     * @param int $orderId
     * @return void
     */
    protected function validatePriceRangeByOrder(string $attribute, float $value, \Closure $fail, int $orderId): void
    {
        try {
            $currencyId = $this->getCurrencyIdByOrderId($orderId);
            if (!$currencyId) {
                $fail('订单不存在或无法获取币种信息');
                return;
            }

            // 使用 TradeConfigKey::getTradeConfigKey 获取 Redis key
            $redisKey = TradeConfigKey::getTradeConfigKey($currencyId, 1);

            // 检查 Redis key 是否存在
            if (!$this->redis->exists($redisKey)) {
                $fail('交易配置不存在，请联系管理员');
                return;
            }

            // 从 Redis 中获取配置数据
            $config = $this->redis->hGetAll($redisKey);

            if (empty($config)) {
                $fail('交易配置数据为空，请联系管理员');
                return;
            }

            // 获取最小和最大价格
            $minPrice = $config['min_price'] ?? null;
            $maxPrice = $config['max_price'] ?? null;

            // 验证最小价格
            if ($minPrice !== null && $value < (float)$minPrice) {
                $fail("交易价格不能小于 {$minPrice}");
                return;
            }

            // 验证最大价格
            if ($maxPrice !== null && $value > (float)$maxPrice) {
                $fail("交易价格不能大于 {$maxPrice}");
                return;
            }

        } catch (\Exception $e) {
            $fail('验证交易价格时发生错误，请稍后重试');
        }
    }

    /**
     * 根据订单ID验证数量范围
     * @param string $attribute
     * @param mixed $value
     * @param \Closure $fail
     * @param int $orderId
     * @return void
     */
    protected function validateQuantityRangeByOrder(string $attribute, float $value, \Closure $fail, int $orderId): void
    {
        try {
            $currencyId = $this->getCurrencyIdByOrderId($orderId);
            if (!$currencyId) {
                $fail('订单不存在或无法获取币种信息');
                return;
            }

            // 复用现有的数量验证逻辑
            $this->validateQuantityRange($attribute, $value, $fail, $currencyId);

        } catch (\Exception $e) {
            $fail('验证交易数量时发生错误，请稍后重试');
        }
    }

    /**
     * 根据订单ID获取币种ID
     * @param int $orderId
     * @return int|null
     */
    protected function getCurrencyIdByOrderId(int $orderId): ?int
    {
        try {
            // 从数据库查询订单信息获取币种ID
            $spotOrder = TradeSpotOrder::query()
                ->where('id', $orderId)
                ->first(['currency_id']);

            return $spotOrder ? $spotOrder->currency_id : null;

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 验证币种是否存在
     * @param string $attribute
     * @param mixed $value
     * @param \Closure $fail
     * @param int $currencyId
     * @return void
     */
    protected function validateCurrencyExists(string $attribute, int $value, \Closure $fail, int $currencyId): void
    {
        try {
            // 使用 CurrencyConfigKey::getCurrencyKey 获取币种配置的 Redis key
            $redisKey = CurrencyConfigKey::getCurrencyKey($currencyId);

            // 检查 Redis key 是否存在
            if (!$this->redis->exists($redisKey)) {
                $fail('指定的币种不存在或未开启交易');
                return;
            }

        } catch (\Exception $e) {
            $fail('验证币种信息时发生错误，请稍后重试');
        }
    }


}