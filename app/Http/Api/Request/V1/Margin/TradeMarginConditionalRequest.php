<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆委托订单请求验证
 */

namespace App\Http\Api\Request\V1\Margin;

use App\Http\Api\Request\BaseFormRequest;
use App\Model\Enums\Trade\Margin\ConditionalOrderType;
use App\Model\Enums\Trade\Margin\ConditionalOrderStatus;
use App\Model\Enums\Trade\Margin\TriggerType;
use App\Model\Enums\Trade\Margin\ExecutionType;
use Hyperf\Validation\Rule;

class TradeMarginConditionalRequest extends BaseFormRequest
{
    /**
     * 创建委托订单验证规则
     */
    public function placeOrderRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'margin_type' => ['required', 'integer', 'in:1,2'], // 1=全仓,2=逐仓
            'position_side' => ['required', 'integer', 'in:1,2'], // 1=做多,2=做空
            'order_type' => ['required', Rule::in(array_column(ConditionalOrderType::cases(), 'value'))],
            'trigger_price' => ['required', 'numeric', 'min:0.00000001'],
            'close_quantity' => ['required', 'numeric', 'min:0.00000001'],
            'trigger_type' => ['required', Rule::in(array_column(TriggerType::cases(), 'value'))],
            'execution_type' => ['required', Rule::in(array_column(ExecutionType::cases(), 'value'))],
            'execution_price' => ['nullable', 'numeric', 'min:0.00000001', 'required_if:execution_type,2'], // 限价时必填
        ];
    }

    /**
     * 修改委托订单验证规则
     * 只允许修改：委托数量、触发价格、执行价格、执行方式
     */
    public function updateOrderRules(): array
    {
        return [
            'close_quantity' => ['nullable', 'numeric', 'min:0.00000001'], // 委托数量
            'trigger_price' => ['nullable', 'numeric', 'min:0.00000001'], // 触发价格
            'execution_price' => ['nullable', 'numeric', 'min:0.00000001'], // 执行价格
            'execution_type' => ['nullable', Rule::in(array_column(ExecutionType::cases(), 'value'))], // 执行方式
        ];
    }

    /**
     * 撤销委托订单验证规则
     */
    public function cancelOrderRules(): array
    {
        return [
            // 路径参数无需验证
        ];
    }

    /**
     * 查询委托订单列表验证规则
     */
    public function listOrdersRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'order_type' => ['nullable', Rule::in(array_column(ConditionalOrderType::cases(), 'value'))],
            'status' => ['nullable', Rule::in(array_column(ConditionalOrderStatus::cases(), 'value'))],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 查询委托订单详情验证规则
     */
    public function detailOrderRules(): array
    {
        return [
            // 路径参数无需验证
        ];
    }
} 