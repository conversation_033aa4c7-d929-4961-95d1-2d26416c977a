<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆交易请求验证
 */

namespace App\Http\Api\Request\V1\Margin;

use App\Http\Api\Request\BaseFormRequest;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\Trade\Margin\PositionSide;
use App\Enum\OrderStatus;
use Hyperf\Validation\Rule;

class TradeMarginRequest extends BaseFormRequest
{
    /**
     * 杠杆下单验证规则
     */
    public function orderRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'side' => ['required', Rule::in(array_column(PositionSide::cases(), 'value'))],
            'margin_type' => ['required', Rule::in(array_column(MarginType::cases(), 'value'))],
            'leverage' => ['required', 'numeric', 'min:1', 'max:100'],
            'quantity' => ['required', 'numeric', 'min:0.00000001'],
            'price' => ['nullable', 'numeric', 'min:0.00000001'],
            'order_type' => ['required', 'string', 'in:limit,market'],
        ];
    }

    /**
     * 撤单验证规则
     */
    public function cancelRules(): array
    {
        return [
            'order_id' => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * 修改订单验证规则
     */
    public function modifyOrderRules(): array
    {
        return [
            'order_id' => ['required', 'integer', 'min:1'],
            'quantity' => ['nullable', 'numeric', 'min:0.00000001'],
            'price' => ['nullable', 'numeric', 'min:0.00000001'],
            'leverage' => ['nullable', 'numeric', 'min:1', 'max:100'],
        ];
    }

    /**
     * 查询订单列表验证规则
     */
    public function orderListRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'margin_type' => ['nullable', Rule::in(array_column(MarginType::cases(), 'value'))],
            'side' => ['nullable', Rule::in(array_column(PositionSide::cases(), 'value'))],
            'status' => ['nullable', Rule::in(array_column(OrderStatus::cases(), 'value'))],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 查询仓位列表验证规则
     */
    public function positionListRules(): array
    {
        return [
            'currency_id' => ['nullable', 'integer', 'min:1'],
            'margin_type' => ['nullable', Rule::in(array_column(MarginType::cases(), 'value'))],
            'side' => ['nullable', Rule::in(array_column(PositionSide::cases(), 'value'))],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 查询账户资产验证规则
     */
    public function accountAssetRules(): array
    {
        return [
            'margin_type' => ['nullable', Rule::in(array_column(MarginType::cases(), 'value'))],
            'currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 获取借款配置验证规则
     */
    public function borrowConfigRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'margin_type' => ['required', Rule::in(array_column(MarginType::cases(), 'value'))],
        ];
    }

    /**
     * 获取杠杆配置验证规则
     */
    public function leverageConfigRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'margin_type' => ['required', Rule::in(array_column(MarginType::cases(), 'value'))],
        ];
    }

    /**
     * 获取杠杆利率验证规则
     */
    public function marginRatesRules(): array
    {
        return [
            'margin_type' => ['required', Rule::in(array_column(MarginType::cases(), 'value'))],
        ];
    }

    /**
     * 杠杆借款验证规则
     */
    public function borrowRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'margin_type' => ['required', Rule::in(array_column(MarginType::cases(), 'value'))],
            'borrow_amount' => ['required', 'numeric', 'min:0.00000001'],
            'base_currency_id' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * 杠杆还款验证规则
     */
    public function repayRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'margin_type' => ['required', Rule::in(array_column(MarginType::cases(), 'value'))],
            'repay_amount' => ['required', 'numeric', 'min:0.00000001'],
            'repay_currency_id' => [
                'required_if:margin_type,' . MarginType::ISOLATED->value,
                'nullable',
                'integer',
                'min:1'
            ],
        ];
    }

    /**
     * 借款汇总验证规则
     */
    public function borrowSummaryRules(): array
    {
        return [
            'currency_id' => ['required', 'integer', 'min:1'],
            'margin_type' => ['required', Rule::in(array_column(MarginType::cases(), 'value'))],
        ];
    }
} 