<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 委托订单请求验证器
 */

namespace App\Http\Api\Request\V1;

use App\Enum\CurrencyConfigKey;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\MarketType;
use App\Model\Trade\Enums\CommissionOrderType;
use App\Model\Trade\Enums\TriggerCondition;
use App\Model\Trade\Enums\TriggerType;
use App\Model\Trade\TradeSpotCommission;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Hyperf\Validation\Request\FormRequest;
use Hyperf\Validation\Rule;

class TradeSpotCommissionRequest extends FormRequest
{
    #[Inject]
    protected Redis $redis;

    public function authorize()
    {
        return true;
    }

    /**
     * 创建委托订单验证规则
     */
    public function placeOrderRules(): array
    {
        return [
            'currency_id' => [
                'required',
                'integer',
                'min:1',
                function ($attribute, $value, $fail) {
                    $this->validateCurrencyExists($attribute, (int)$value, $fail);
                }
            ],
            'side' => [
                'required',
                'in:1,-1' // 1买，-1卖
            ],
            'order_type' => [
                'required',
                Rule::in(array_column(CommissionOrderType::cases(), 'value'))
            ],
            'trigger_condition' => [
                'required',
                Rule::in(array_column(TriggerCondition::cases(), 'value'))
            ],
            'trigger_price' => [
                'required',
                'string',
                'regex:/^\d+(\.\d{1,8})?$/', // 最多8位小数
                function ($attribute, $value, $fail) {
                    if (!$this->validateDecimalRange($value, '0.00000001', '99999999.99999999')) {
                        $fail('触发价格必须在 0.00000001 到 99999999.99999999 之间');
                        return;
                    }
                    
                    $currencyId = (int)$this->input('currency_id');
                    if ($currencyId) {
                        $this->validateTriggerPrice($attribute, $value, $fail, $currencyId);
                    }
                }
            ],
            'amount' => [
                'required',
                'string',
                'regex:/^\d+(\.\d{1,8})?$/', // 最多8位小数
                function ($attribute, $value, $fail) {
                    if (!$this->validateDecimalRange($value, '0.00000001', '99999999.99999999')) {
                        $fail('委托数量必须在 0.00000001 到 99999999.99999999 之间');
                        return;
                    }
                    
                    $currencyId = (int)$this->input('currency_id');
                    if ($currencyId) {
                        $this->validateAmount($attribute, $value, $fail, $currencyId);
                    }
                }
            ],
            'trigger_type' => [
                'required',
                Rule::in(array_column(TriggerType::cases(), 'value'))
            ],
            'place_price' => [
                'required_if:trigger_type,' . TriggerType::LIMIT->value,
                'nullable',
                'string',
                'regex:/^\d+(\.\d{1,8})?$/', // 最多8位小数
                function ($attribute, $value, $fail) {
                    if ($value === null || $value === '') {
                        return;
                    }
                    
                    if (!$this->validateDecimalRange($value, '0.00000001', '99999999.99999999')) {
                        $fail('下单价格必须在 0.00000001 到 99999999.99999999 之间');
                        return;
                    }
                    
                    $triggerType = (int)$this->input('trigger_type');
                    $currencyId = (int)$this->input('currency_id');
                    
                    if ($currencyId && $triggerType == TriggerType::LIMIT->value) {
                        $this->validatePlacePrice($attribute, $value, $fail, $currencyId);
                    }
                }
            ]
        ];
    }

    /**
     * 取消委托订单验证规则
     */
    public function cancelOrderRules(): array
    {
        return [
            'commission_id' => [
                'required',
                'integer',
                'min:1'
            ]
        ];
    }

    /**
     * 查询委托订单验证规则
     */
    public function queryOrdersRules(): array
    {
        return [
            'currency_id' => 'nullable|integer|min:1',
            'order_type' => [
                'nullable',
                Rule::in(array_column(CommissionOrderType::cases(), 'value'))
            ],
            'status' => 'nullable|integer',
            'page' => 'nullable|integer|min:1',
            'page_size' => 'nullable|integer|min:1|max:100'
        ];
    }

    /**
     * 修改委托订单验证规则
     */
    public function updateOrderRules(): array
    {
        return [
            'commission_id' => [
                'required',
                'integer',
                'min:1'
            ],
            'trigger_price' => [
                'sometimes',
                'string',
                'regex:/^\d+(\.\d{1,8})?$/', // 最多8位小数
                function ($attribute, $value, $fail) {
                    if ($value !== null && !$this->validateDecimalRange($value, '0.00000001', '99999999.99999999')) {
                        $fail('触发价格必须在 0.00000001 到 99999999.99999999 之间');
                    }
                }
            ],
            'amount' => [
                'sometimes',
                'string',
                'regex:/^\d+(\.\d{1,8})?$/', // 最多8位小数
                function ($attribute, $value, $fail) {
                    if ($value !== null && !$this->validateDecimalRange($value, '0.00000001', '99999999.99999999')) {
                        $fail('交易数量必须在 0.00000001 到 99999999.99999999 之间');
                    }
                }
            ],
            'place_price' => [
                'sometimes',
                'nullable',
                'string',
                'regex:/^\d+(\.\d{1,8})?$/', // 最多8位小数
                function ($attribute, $value, $fail) {
                    if ($value !== null && $value !== '' && !$this->validateDecimalRange($value, '0.00000001', '99999999.99999999')) {
                        $fail('委托价格必须在 0.00000001 到 99999999.99999999 之间');
                    }
                }
            ],
            'trigger_type' => [
                'sometimes',
                Rule::in(array_column(TriggerType::cases(), 'value'))
            ]
        ];
    }

    /**
     * 委托订单详情验证规则
     */
    public function orderDetailRules(): array
    {
        return [
            'commission_id' => [
                'required',
                'integer',
                'min:1'
            ]
        ];
    }

    /**
     * 验证币种是否存在
     */
    protected function validateCurrencyExists(string $attribute, int $value, \Closure $fail): void
    {
        try {
            $redisKey = CurrencyConfigKey::getCurrencyKey($value);
            
            if (!$this->redis->exists($redisKey)) {
                $fail('指定的币种不存在或未开启交易');
                return;
            }

        } catch (\Exception $e) {
            $fail('验证币种信息时发生错误，请稍后重试');
        }
    }

    /**
     * 验证触发价格合理性
     */
    protected function validateTriggerPrice(string $attribute, string $value, \Closure $fail, int $currencyId): void
    {
        try {
            // 获取当前市价进行合理性检查
            $currentPrice = $this->getCurrentPrice($currencyId);
            if ($currentPrice) {
                // 触发价格不能偏离当前价格太远（比如50%）
                $maxDeviation = '0.5'; // 50%
                $minPrice = bcmul($currentPrice, bcsub('1', $maxDeviation, 8), 8);
                $maxPrice = bcmul($currentPrice, bcadd('1', $maxDeviation, 8), 8);
                
                if (bccomp($value, $minPrice, 8) < 0 || bccomp($value, $maxPrice, 8) > 0) {
                    $fail("触发价格偏离当前市价过远，当前价格：{$currentPrice}");
                }
            }

        } catch (\Exception $e) {
            // 价格验证失败不阻断，只记录日志
            error_log("触发价格验证失败: " . $e->getMessage());
        }
    }

    /**
     * 验证委托数量
     */
    protected function validateAmount(string $attribute, string $value, \Closure $fail, int $currencyId): void
    {
        try {
            // 这里可以添加最小/最大委托数量的验证
            // 暂时使用基础验证，后续可根据币种配置进行更详细的验证
            
        } catch (\Exception $e) {
            $fail('验证委托数量时发生错误，请稍后重试');
        }
    }

    /**
     * 验证下单价格
     */
    protected function validatePlacePrice(string $attribute, string $value, \Closure $fail, int $currencyId): void
    {
        try {
            // 验证下单价格的合理性
            // 可以参考现有的价格验证逻辑
            
        } catch (\Exception $e) {
            $fail('验证下单价格时发生错误，请稍后重试');
        }
    }

    /**
     * 获取当前市价
     */
    protected function getCurrentPrice(int $currencyId): ?string
    {
        try {
            // 使用TickerSyncKey获取Redis中的实时价格
            $key = TickerSyncKey::getOuterTradeKey($currencyId, MarketType::CRYPTO->value);
            $price = $this->redis->hGet($key, 'price');
            
            if ($price === false || $price === null) {
                return null;
            }
            
            return (string)$price;
            
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 验证字符串格式的数字范围
     */
    protected function validateDecimalRange(string $value, string $min, string $max): bool
    {
        try {
            // 使用BCMath进行精确的数值比较
            return bccomp($value, $min, 8) >= 0 && bccomp($value, $max, 8) <= 0;
        } catch (\Exception $e) {
            return false;
        }
    }
} 