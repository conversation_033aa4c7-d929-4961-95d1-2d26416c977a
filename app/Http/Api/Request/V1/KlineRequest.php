<?php

/**
 * KlineRequest.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/30
 * Website:algoquant.org
 */

namespace App\Http\Api\Request\V1;

use Hyperf\Validation\Request\FormRequest;

class KlineRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }
    public function rules(): array
    {
        return [
            'currency_id' => 'required|integer',
            'market_type' => 'required|in:1,2,3,4,5',
            'period' => 'required|string',
            'start_time' => 'date',
            'end_time' => 'date',
            'limit' => 'integer|max:1000',
        ];
    }
}