<?php

declare(strict_types=1);
/**
 * DynamicsRequest
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-04
 * Website:xxx
 */

namespace App\Http\Api\Request\Dynamics;

use App\Http\Api\Request\BaseFormRequest;

class DynamicsRequest extends BaseFormRequest
{

    /**
     * 列表验证规则
     *
     * @return array
     */
    public function listRules(): array
    {
        return [
            'page' => ['required','integer'],
            'page_size' => ['required','integer'],
        ];
    }

    /**
     * 详情验证规则
     *
     * @return array
     */
    public function detailRules(): array
    {
        return [
            'dynamics_id' => ['required','integer','exists:cpx_dynamics,id']
        ];
    }

    /**
     * 发布动态验证规则
     *
     * @return array
     */
    public function createRules(): array
    {
        return [
            'content' => ['required','string']
        ];
    }

    /**
     * 修改动态验证规则
     *
     * @return array
     */
    public function updateRules(): array
    {
        return [
            'id' => ['required','integer'],
            'content' => ['required','string']
        ];
    }

    /**
     * 删除动态验证规则
     *
     * @return array
     */
    public function deleteRules(): array
    {
        return [
            'id' => ['required','integer']
        ];
    }

    /**
     * 评论验证规则
     *
     * @return array
     */
    public function commentRules(): array
    {
        return [
            'dynamics_id' => ['required','integer'],
            'content' => ['required','string']
        ];
    }

    /**
     * 评论列表验证规则
     *
     * @return array
     */
    public function commentListRules(): array
    {
        return [
            'dynamics_id' => ['required','integer'],
            'page' => ['required','integer'],
            'page_size' => ['required','integer'],
        ];
    }

    /**
     * 点赞验证规则
     *
     * @return array
     */
    public function likedRules(): array
    {
        return [
            'dynamics_id' => ['required','integer']
        ];
    }

    /**
     * 取消点赞验证规则
     *
     * @return array
     */
    public function unLikedRules(): array
    {
        return [
            'dynamics_id' => ['required','integer']
        ];
    }

    /**
     * 用户信息验证规则
     *
     * @return array
     */
    public function userInfoRules(): array
    {
        return [
            'user_id' => ['required','integer']
        ];
    }

    /**
     * 文章验证规则
     *
     * @return array
     */
    public function userArircleRules(): array
    {
        return [
            'user_id' => ['required','integer']
        ];
    }

    /**
     * 某个人的动态列表，验证规则
     *
     * @return array
     */
    public function userDynamicsRules(): array
    {
        return [
            'user_id' => ['required','integer'],
            'type' => ['required','integer']
        ];
    }

    /**
     * 登录用户的动态列表，验证规则
     *
     * @return array
     */
    public function meDynamicsRules(): array
    {
        return [
            'type' => ['required','integer']
        ];
    }


    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [
            'type'=>'动态类型',
            'user_id'=>'用户id',
            'dynamics_id'=>'动态id',
            'content'=>'动态内容'
        ];
    }
}
