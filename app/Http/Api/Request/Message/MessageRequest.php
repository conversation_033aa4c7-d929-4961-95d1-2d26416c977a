<?php

declare(strict_types=1);
/**
 * MessageRequest
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-03
 * Website:xxx
 */

namespace App\Http\Api\Request\Message;

use App\Http\Api\Request\BaseFormRequest;

class MessageRequest extends BaseFormRequest
{

    /**
     * 列表验证规则
     *
     * @return array
     */
    public function listRules(): array
    {
        return [
            'category_id' => ['required','integer','exists:cpx_category,id'],
            'page' => ['required','integer'],
            'page_size' => ['required','integer'],
        ];
    }

    /**
     * 详情验证规则
     *
     * @return array
     */
    public function detailRules(): array
    {
        return [
            'id' => ['required','integer','exists:cpx_message,id']
        ];
    }

    /**
     * 字段映射名称
     * return array
     */
    public function attributes(): array
    {
        return [];
    }
}
