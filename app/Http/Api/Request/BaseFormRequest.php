<?php

declare(strict_types=1);

namespace App\Http\Api\Request;

use App\Http\Common\Request\Traits\ApiActionRulesTrait;
use App\Model\User\User;
use Hyperf\Validation\Request\FormRequest;

class BaseFormRequest extends FormRequest
{
    use ApiActionRulesTrait;

    /**
     * 是否授权访问
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取当前登录用户
     */
    public function user(): ?User
    {
        return $this->getAttribute('user');
    }

    /**
     * 获取当前登录用户ID
     */
    public function userId(): ?int
    {
        return $this->getAttribute('user_id');
    }
}
