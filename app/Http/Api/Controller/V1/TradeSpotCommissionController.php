<?php

declare(strict_types=1);
/**
 * TradeSpotCommissionController.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/26
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Controller\V1;

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\V1\TradeSpotCommissionRequest;
use App\Http\Api\Service\V1\TradeSpotCommissionService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Resource\BaseResource;
use App\Http\Common\ResultCode;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;

#[Controller(prefix: '/api/v1/spot/commission')]
#[Middleware(middleware: TokenMiddleware::class)]
class TradeSpotCommissionController extends AbstractController
{
    /**
     * @var TradeSpotCommissionService $service
     */
    public mixed $service;

    /**
     * @var TradeSpotCommissionRequest $rules
     */
    public mixed $rules;

    /**
     * 创建委托订单
     */
    #[PostMapping('place-order')]
    public function placeOrder(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->placeOrderRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $currencyId = (int)$this->request->input('currency_id');
        $side = (int)$this->request->input('side');
        $orderType = (int)$this->request->input('order_type');
        $triggerCondition = (int)$this->request->input('trigger_condition');
        $triggerPrice = (string)$this->request->input('trigger_price');
        $amount = (string)$this->request->input('amount');
        $triggerType = (int)$this->request->input('trigger_type');
        $placePrice = $this->request->input('place_price') ? (string)$this->request->input('place_price') : null;

        $commission = $this->service->placeOrder(
            $userId,
            $currencyId,
            $side,
            $orderType,
            $triggerCondition,
            $triggerPrice,
            $amount,
            $triggerType,
            $placePrice
        );

        return $this->success(
            BaseResource::make($commission)->toArray(),
            '委托订单创建成功'
        );
    }

    /**
     * 修改委托订单
     */
    #[PutMapping('update')]
    public function updateOrder(): \App\Http\Common\Result
    {
        $commissionId = (int)$this->request->input('commission_id');

        $this->request = $this->request->withParsedBody(
            array_merge($this->request->getParsedBody(), ['commission_id' => $commissionId])
        );
        
        $this->requestValidate($this->rules->updateOrderRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $triggerPrice = $this->request->input('trigger_price');
            $amount = $this->request->input('amount');
            $placePrice = $this->request->input('place_price');
            $triggerType = $this->request->input('trigger_type') ? (int)$this->request->input('trigger_type') : null;

            $commission = $this->service->updateOrder(
                $userId,
                $commissionId,
                $triggerPrice,
                $amount,
                $placePrice,
                $triggerType
            );

            return $this->success(
                BaseResource::make($commission)->toArray(),
                '委托订单修改成功'
            );

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量取消委托订单
     */
    #[DeleteMapping('batch')]
    public function batchCancelOrders(): \App\Http\Common\Result
    {
        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            
            // 获取用户的等待执行委托订单
            $pendingOrders = $this->service->queryOrders(
                $userId,
                $currencyId,
                null,
                0, // 等待执行状态
                1,
                1000 // 最多处理1000个
            );

            $cancelledCount = 0;
            $failedCount = 0;
            $errors = [];

            foreach ($pendingOrders->items() as $order) {
                try {
                    $result = $this->service->cancelOrder($userId, $order->id);
                    if ($result) {
                        $cancelledCount++;
                    } else {
                        $failedCount++;
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "订单 {$order->id} 取消失败: " . $e->getMessage();
                }
            }

            return $this->success([
                'cancelled_count' => $cancelledCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ], "批量取消完成，成功 {$cancelledCount} 个，失败 {$failedCount} 个");

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 取消委托订单
     */
    #[DeleteMapping('cancel')]
    public function cancelOrder(): \App\Http\Common\Result
    {
        $userId = (int)$this->request->getAttribute('user_id');
        $commissionId = (int)$this->request->input('commission_id');
        $result = $this->service->cancelOrder($userId, $commissionId);

        if ($result) {
            return $this->success([], '委托订单取消成功');
        } else {
            return $this->error('委托订单取消失败');
        }
    }

    /**
     * 查询委托订单列表
     */
    #[GetMapping('list')]
    public function queryOrders(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->queryOrdersRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $orderType = $this->request->input('order_type') ? (int)$this->request->input('order_type') : null;
            $status = $this->request->input('status') !== null ? (int)$this->request->input('status') : null;
            $page = (int)($this->request->input('page', 1));
            $pageSize = (int)($this->request->input('page_size', 20));

            $result = $this->service->queryOrders(
                $userId,
                $currencyId,
                $orderType,
                $status,
                $page,
                $pageSize
            );

            return $this->success([
                'list' => BaseResource::collection($result->items())->toArray(),
                'pagination' => [
                    'current_page' => $result->currentPage(),
                    'per_page' => $result->perPage(),
                    'total' => $result->total(),
                    'last_page' => $result->lastPage(),
                    'has_more' => $result->hasMorePages()
                ]
            ], '查询成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取委托订单详情
     */
    #[GetMapping('{commission_id}')]
    public function getOrderDetail(int $commissionId): \App\Http\Common\Result
    {
        try {
            $userId = (int)$this->request->getAttribute('user_id');
            
            $commission = $this->service->getOrderDetail($userId, $commissionId);

            return $this->success(
                BaseResource::make($commission)->toArray(),
                '查询成功'
            );

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
} 