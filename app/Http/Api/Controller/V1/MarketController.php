<?php

/**
 * MarketController.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/2
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Controller\V1;

use App\Enum\MarketType;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\V1\MarketRequest;
use App\Http\Api\Service\V1\MarketService;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/v1/market')]
class MarketController extends AbstractController
{

    /**
     * @var MarketService $service;
     */
    public mixed $service;

    /**
     * @var MarketRequest $rules
     */
    public mixed $rules;

    #[GetMapping('currency')]
    public function currency()
    {
        return $this->success(
            $this->service->getAllCurrency()
        );
    }

    #[GetMapping('update-currency')]
    public function updateCurrency(): \App\Http\Common\Result
    {
        return $this->success(
            $this->service->isNewCurrency()
        );
    }

    #[GetMapping('currency-cate')]
    public function category()
    {
        return $this->success(
            $this->service->getCategory()
        );
    }

    #[GetMapping('market-type')]
    public function marketType(): \App\Http\Common\Result
    {
        return $this->success(
            MarketType::getMarketType()
        );
    }


    /**
     * 行情市场用户自定义自选币种栏目
     * @return
     */
    #[GetMapping('diy-cate')]
    #[Middleware(TokenMiddleware::class)]
    public function diyCate(): \App\Http\Common\Result
    {
        return $this->success(
            $this->service->getDiyCate($this->request->userId() ?? 0)
        );
    }

    /**
     * 链上交易支持的链
     * @return \App\Http\Common\Result
     */
    #[GetMapping('chain-cate')]
    public function chainCate(): \App\Http\Common\Result
    {
        return $this->success(
            $this->service->getChainCate()
        );
    }
}