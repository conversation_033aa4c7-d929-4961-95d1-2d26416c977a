<?php

/**
 * KlineController.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/26
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Controller\V1;

use App\Http\Api\Request\V1\KlineRequest;
use App\Http\Api\Service\V1\KlineService;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;

#[Controller(prefix: 'api/v1/kline')]
class KlineController extends AbstractController
{
    /**
     * @var KlineService $service;
     */
    public mixed $service;

    /**
     * @var KlineRequest $rules
     */
    public mixed $rules;

    #[GetMapping('kline')]
    public function history(): \App\Http\Common\Result
    {

        $this->requestValidate(
            $this->rules->rules()
        );

        $currencyId = (int) $this->request->input('currency_id');
        $marketType = (int) $this->request->input('market_type');
        $period = $this->request->input('period', '1m');
        $startTime = $this->request->input('start_time') ? (int) $this->request->input('start_time') : null;
        $endTime = $this->request->input('end_time') ? (int) $this->request->input('end_time') : null;
        $limit = (int) $this->request->input('limit', 1000);

        return $this->success(
            $this->service->getHistoryKline($currencyId, $marketType, $period, $startTime, $endTime, $limit)
        );
    }
}