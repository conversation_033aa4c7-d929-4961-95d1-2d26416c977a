<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆委托订单控制器
 */

namespace App\Http\Api\Controller\V1\Margin;

use App\Http\Common\Controller\AbstractController;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Resource\BaseResource;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Common\Result;
use App\Exception\BusinessException;

#[Controller(prefix: 'api/v1/margin/conditional')]
#[Middleware(middleware: TokenMiddleware::class)]
class TradeMarginConditionalController extends AbstractController
{

    /**
     * 创建委托订单
     */
    #[PostMapping('place-order')]
    public function placeOrder(): Result
    {
        $data = $this->requestValidate($this->rules->placeOrderRules());
        
        $userId = (int)$this->request->getAttribute('user_id');
        $conditionalOrder = $this->service->createConditionalOrder($userId, $data);
        
        return $this->success([
            'id' => $conditionalOrder->getId(),
            'user_id' => $conditionalOrder->getUserId(),
            'currency_id' => $conditionalOrder->getCurrencyId(),
            'margin_type' => $conditionalOrder->getMarginType(),
            'position_side' => $conditionalOrder->getPositionSide(),
            'order_type' => $conditionalOrder->getOrderType(),
            'trigger_price' => $conditionalOrder->getTriggerPrice(),
            'close_quantity' => $conditionalOrder->getCloseQuantity(),
            'trigger_type' => $conditionalOrder->getTriggerType(),
            'execution_type' => $conditionalOrder->getExecutionType(),
            'execution_price' => $conditionalOrder->getExecutionPrice(),
            'status' => $conditionalOrder->getStatus(),
            'created_at' => $conditionalOrder->getCreatedAt()?->format('Y-m-d H:i:s'),
        ], '委托订单创建成功');
    }

    /**
     * 修改委托订单
     */
    #[PutMapping('modify-order')]
    public function updateOrder(): Result
    {
        $data = $this->requestValidate($this->rules->updateOrderRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $orderId = (int)$this->request->input('id');

        $conditionalOrder = $this->service->updateConditionalOrder($userId, $orderId, $data);

        return $this->success(
            BaseResource::make($conditionalOrder)->toArray(),
            '委托订单修改成功'
        );
    }

    /**
     * 撤销委托订单
     */
    #[DeleteMapping('cancel-order')]
    public function cancelOrder(): Result
    {
        $this->requestValidate($this->rules->cancelOrderRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $orderId = (int)$this->request->input('id');

        $result = $this->service->cancelConditionalOrder($userId, $orderId);

        return $result 
            ? $this->success([], '委托订单撤销成功') 
            : $this->error('撤销委托订单失败');
    }

    /**
     * 查询委托订单列表
     */
    #[GetMapping('list')]
    public function listOrders(): Result
    {
        $data = $this->requestValidate($this->rules->listOrdersRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$data['currency_id'] ?? null;
            $orderType = (int)$data['order_type'] ?? null;
            $status = (int)$data['status'] ?? null;
            $perPage = (int)$data['per_page'] ?? 20;
            $page = (int)$data['page'] ?? 1;

            $result = $this->service->getConditionalOrderList(
                $userId, 
                $currencyId, 
                $orderType, 
                $status, 
                $perPage, 
                $page
            );

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询委托订单详情
     */
    #[GetMapping('{id}')]
    public function detailOrder(): Result
    {
        $this->requestValidate($this->rules->detailOrderRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $orderId = (int)$this->request->route('id');

            $conditionalOrder = $this->service->getConditionalOrderDetail($userId, $orderId);

            return $this->success(
                BaseResource::make($conditionalOrder)->toArray(),
                '查询成功'
            );
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
} 