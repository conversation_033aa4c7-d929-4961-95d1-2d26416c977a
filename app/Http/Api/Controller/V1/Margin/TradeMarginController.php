<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆交易控制器
 */

namespace App\Http\Api\Controller\V1\Margin;

use App\Http\Common\Controller\AbstractController;
use App\Http\Api\Request\V1\Margin\TradeMarginRequest;
use App\Http\Api\Service\V1\Margin\TradeMarginService;
use App\Http\Api\Middleware\TokenMiddleware;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/v1/margin')]
class TradeMarginController extends AbstractController
{
    /**
     * @var TradeMarginService $service;
     */
    public mixed $service;

    /**
     * @var TradeMarginRequest $rules
     */
    public mixed $rules;

    /**
     * 创建杠杆订单
     */
    #[PostMapping('place-order')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function placeOrder(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->orderRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $orderData = $this->request->all();

        $result = $this->service->createMarginOrder(array_merge($orderData, ['user_id' => $userId]));

        return $this->success($result, '杠杆下单成功');
    }

    /**
     * 撤销杠杆订单
     */
    #[DeleteMapping('cancel-order')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function cancelOrder(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->cancelRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $orderId = (int)$this->request->input('order_id');

            $result = $this->service->cancelMarginOrder($userId, $orderId);

            return $result ? $this->success([], '撤单成功') : $this->error('撤单失败');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 修改杠杆订单
     */
    #[PostMapping('modify-order')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function modifyOrder(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->modifyOrderRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $orderId = (int)$this->request->input('order_id');
        $modifyData = $this->request->all();

        $result = $this->service->modifyMarginOrder($userId, $orderId, $modifyData);

        return $this->success($result, '修改订单成功');
    }

    /**
     * 查询杠杆订单列表
     */
    #[GetMapping('orders')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getOrders(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->orderListRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $marginType = $this->request->input('margin_type') ? (int)$this->request->input('margin_type') : null;
            $side = $this->request->input('side') ? (int)$this->request->input('side') : null;
            $status = $this->request->input('status') ? (int)$this->request->input('status') : null;
            $perPage = (int)($this->request->input('per_page', 20));
            $page = (int)($this->request->input('page', 1));

            $result = $this->service->getOrderList($userId, $currencyId, $marginType, $side, $status, $perPage, $page);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询杠杆仓位列表
     */
    #[GetMapping('positions')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getPositions(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->positionListRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $marginType = $this->request->input('margin_type') ? (int)$this->request->input('margin_type') : null;
            $side = $this->request->input('side') ? (int)$this->request->input('side') : null;
            $perPage = (int)($this->request->input('per_page', 20));
            $page = (int)($this->request->input('page', 1));

            $result = $this->service->getPositionList($userId, $currencyId, $marginType, $side, $perPage, $page);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询杠杆账户资产
     */
    #[GetMapping('account')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getAccount(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->accountAssetRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $marginType = $this->request->input('margin_type') ? (int)$this->request->input('margin_type') : null;
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->getAccountAssets($userId, $marginType, $currencyId);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取借款配置
     */
    #[GetMapping('borrow-config')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getBorrowConfig(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->borrowConfigRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$this->request->input('currency_id');
            $marginType = (int)$this->request->input('margin_type');

            $result = $this->service->getBorrowConfig($userId, $currencyId, $marginType);

            return $this->success($result, '获取借款配置成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取杠杆配置
     */
    #[GetMapping('leverage-config')]
    public function getLeverageConfig(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->leverageConfigRules());

        try {
            // 获取用户ID（如果已登录）
            $userId = null;
            if ($this->request->hasHeader('Authorization')) {
                $userId = (int)$this->request->getAttribute('user_id');
            }
            $currencyId = (int)$this->request->input('currency_id');
            $marginType = (int)$this->request->input('margin_type');
            

            $result = $this->service->getMarginLeverage( $currencyId, $marginType,$userId);

            return $this->success($result, '获取杠杆配置成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取杠杆利率数据
     */
    #[GetMapping('rates')]
    public function getMarginRates(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->marginRatesRules());

        try {
            $marginType = (int)$this->request->input('margin_type');

            $result = $this->service->getMarginRates($marginType);

            return $this->success($result, '获取杠杆利率数据成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 杠杆借款
     */
    #[PostMapping('borrow')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function borrowMargin(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->borrowRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $currencyId = (int)$this->request->input('currency_id');
        $marginType = (int)$this->request->input('margin_type');
        $borrowAmount = (float)$this->request->input('borrow_amount');
        $baseCurrencyId = $this->request->input('base_currency_id') ? (int)$this->request->input('base_currency_id') : null;

        $result = $this->service->borrowMargin($userId, $currencyId, $marginType, $borrowAmount, $baseCurrencyId);

        return $this->success($result, '借款成功');
    }

    /**
     * 获取借款汇总
     */
    #[GetMapping('borrow-summary')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getBorrowSummary(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->borrowSummaryRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$this->request->input('currency_id');
            $marginType = (int)$this->request->input('margin_type');

            $result = $this->service->getBorrowSummary($userId, $currencyId, $marginType);

            return $this->success($result, '获取借款汇总成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 杠杆还款
     */
    #[PostMapping('repay')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function repayMargin(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->repayRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$this->request->input('currency_id');
            $marginType = (int)$this->request->input('margin_type');
            $repayAmount = (float)$this->request->input('repay_amount');
            $repayCurrencyId = $this->request->input('repay_currency_id') ? (int)$this->request->input('repay_currency_id') : null;

            $result = $this->service->repayMargin($userId, $currencyId, $marginType, $repayAmount, $repayCurrencyId);

            return $this->success($result, '还款成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
} 