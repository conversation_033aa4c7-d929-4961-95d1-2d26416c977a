<?php

/**
 * TradeSpotController.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/4
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Controller\V1;

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\V1\TradeSpotRequest;
use App\Http\Api\Service\V1\TradeSpotService;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;

#[Controller(prefix: 'api/v1/spot')]
class TradeSpotController extends AbstractController
{
    /**
     * @var TradeSpotService $service;
     */
    public mixed $service;

    /**
     * @var TradeSpotRequest $rules
     */
    public mixed $rules;


    #[GetMapping('trade-config')]
    public function spotTradeConfig(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->configRules());
        $result = $this->service->getTradeConfig((int)$this->request->input('currency_id'));
        return $result ? $this->success($result) : $this->error("not found currency");
    }

    /**
     * 现货交易下单接口
     */
    #[PostMapping('place-order')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function placeOrder(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->orderRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $orderData = $this->request->all();

            $result = $this->service->placeOrder($userId, $orderData);

            // 根据是否有止盈止损委托来显示不同的消息
            $message = '下单成功';
            if (!empty($result['commission_orders'])) {
                $commissionCount = count($result['commission_orders']);
                $message = "下单成功，已创建 {$commissionCount} 个止盈止损委托订单";
            }

            return $this->success($result, $message);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 现货交易撤单接口
     */
    #[DeleteMapping('cancel-order')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function cancelOrder()
    {
        $this->requestValidate($this->rules->cancelRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $orderId = (int)$this->request->input('order_id');

            $result = $this->service->cancelOrder($userId, $orderId);

            return $result ? $this->success([], '撤单成功') : $this->error('撤单失败');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询挂单（未完成订单）接口
     */
    #[GetMapping('pending-orders')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getPendingOrders(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->pendingOrdersRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id');
            $page = (int)($this->request->input('page', 1));
            $pageSize = (int)($this->request->input('page_size', 20));

            $result = $this->service->getPendingOrders($userId, $currencyId, $page, $pageSize);

            return $this->success($result, '查询成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 修改挂单接口
     */
    #[PutMapping('modify-order')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function modifyOrder(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->modifyOrderRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $orderId = (int)$this->request->input('order_id');
            $price = (float)$this->request->input('price');
            $quantity = (float)$this->request->input('quantity');

            $result = $this->service->modifyOrder($userId, $orderId, $price, $quantity);

            return $this->success($result, '订单修改成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量撤单接口 - 撤销所有未成交的挂单
     */
    #[DeleteMapping('cancel-all-orders')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function cancelAllOrders(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->cancelAllOrdersRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id'); // 可选参数，指定币种

            $result = $this->service->cancelAllOrders($userId, $currencyId);

            $message = $result['total_count'] > 0 
                ? "成功撤销 {$result['success_count']} 个订单" 
                : "没有找到可撤销的订单";

            return $this->success($result, $message);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询成交明细接口
     */
    #[GetMapping('trade-history')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getTradeHistory(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->tradeHistoryRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $page = (int)($this->request->input('page', 1));
            $pageSize = (int)($this->request->input('page_size', 20));

            $result = $this->service->getTradeHistory($userId, $currencyId, $page, $pageSize);

            return $this->success($result, '查询成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

}