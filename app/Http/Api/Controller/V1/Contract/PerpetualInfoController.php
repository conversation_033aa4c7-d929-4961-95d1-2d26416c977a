<?php

declare(strict_types=1);
/**
 * 永续合约信息控制器
 */

namespace App\Http\Api\Controller\V1\Contract;

use App\Http\Common\Controller\AbstractController;
use App\Http\Api\Request\V1\Contract\PerpetualInfoRequest;
use App\Http\Api\Service\V1\Contract\PerpetualConfigService;
use App\Http\Api\Middleware\TokenMiddleware;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/v1/contract/perpetual/info')]
class PerpetualInfoController extends AbstractController
{
    /**
     * @var PerpetualConfigService $service;
     */
    public mixed $service;

    /**
     * @var PerpetualInfoRequest $rules
     */
    public mixed $rules;

    /**
     * 获取合约交易规则
     */
    #[GetMapping('exchange-info')]
    public function getExchangeInfo(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->exchangeInfoRules());

        try {
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->getExchangeInfo($currencyId);

            return $this->success($result, '获取交易规则成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取合约列表
     */
    #[GetMapping('contracts')]
    public function getContracts(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->contractsRules());

        try {
            $result = $this->service->getContracts();

            return $this->success($result, '获取合约列表成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取杠杆档位配置
     */
    #[GetMapping('leverage-bracket')]
    public function getLeverageBracket(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->leverageBracketRules());

        try {
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->getLeverageBracket($currencyId);

            return $this->success($result, '获取杠杆档位配置成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取24小时价格变动统计
     */
    #[GetMapping('ticker/24hr')]
    public function get24hrTicker(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->ticker24hrRules());

        try {
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->get24hrTicker($currencyId);

            return $this->success($result, '获取24小时统计成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取最新价格
     */
    #[GetMapping('ticker/price')]
    public function getTickerPrice(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->tickerPriceRules());

        try {
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->getTickerPrice($currencyId);

            return $this->success($result, '获取最新价格成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取标记价格
     */
    #[GetMapping('mark-price')]
    public function getMarkPrice(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->markPriceRules());

        try {
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->getMarkPrice($currencyId);

            return $this->success($result, '获取标记价格成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取资金费率
     */
    #[GetMapping('funding-rate')]
    public function getFundingRate(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->fundingRateRules());

        try {
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->getFundingRate($currencyId);

            return $this->success($result, '获取资金费率成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取资金费率历史
     */
    #[GetMapping('funding-rate/history')]
    public function getFundingRateHistory(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->fundingRateHistoryRules());

        try {
            $currencyId = (int)$this->request->input('currency_id');
            $limit = (int)($this->request->input('limit', 100));
            $startTime = $this->request->input('start_time') ? (int)$this->request->input('start_time') : null;
            $endTime = $this->request->input('end_time') ? (int)$this->request->input('end_time') : null;

            $result = $this->service->getFundingRateHistory($currencyId, $limit, $startTime, $endTime);

            return $this->success($result, '获取资金费率历史成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取深度信息
     */
    #[GetMapping('depth')]
    public function getDepth(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->depthRules());

        try {
            $currencyId = (int)$this->request->input('currency_id');
            $limit = (int)($this->request->input('limit', 100));

            $result = $this->service->getDepth($currencyId, $limit);

            return $this->success($result, '获取深度信息成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取K线数据
     */
    #[GetMapping('klines')]
    public function getKlines(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->klinesRules());

        try {
            $currencyId = (int)$this->request->input('currency_id');
            $interval = $this->request->input('interval');
            $limit = (int)($this->request->input('limit', 500));
            $startTime = $this->request->input('start_time') ? (int)$this->request->input('start_time') : null;
            $endTime = $this->request->input('end_time') ? (int)$this->request->input('end_time') : null;

            $result = $this->service->getKlines($currencyId, $interval, $limit, $startTime, $endTime);

            return $this->success($result, '获取K线数据成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取最近成交记录
     */
    #[GetMapping('trades')]
    public function getRecentTrades(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->recentTradesRules());

        try {
            $currencyId = (int)$this->request->input('currency_id');
            $limit = (int)($this->request->input('limit', 500));

            $result = $this->service->getRecentTrades($currencyId, $limit);

            return $this->success($result, '获取最近成交记录成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}