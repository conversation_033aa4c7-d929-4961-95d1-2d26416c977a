<?php

declare(strict_types=1);
/**
 * 永续合约交易控制器
 */

namespace App\Http\Api\Controller\V1\Contract;

use App\Http\Common\Controller\AbstractController;
use App\Http\Api\Request\V1\Contract\PerpetualTradeRequest;
use App\Http\Api\Service\V1\Contract\PerpetualTradeService;
use App\Http\Api\Middleware\TokenMiddleware;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/v1/contract/perpetual')]
class PerpetualTradeController extends AbstractController
{
    /**
     * @var PerpetualTradeService $service;
     */
    public mixed $service;

    /**
     * @var PerpetualTradeRequest $rules
     */
    public mixed $rules;

    /**
     * 创建永续合约订单
     */
    #[PostMapping('place-order')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function placeOrder(): \App\Http\Common\Result
    {
        $this->requestValidate(rules: $this->rules->orderRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $orderData = $this->request->all();

        $result = $this->service->createOrder($userId,array_merge($orderData, ['user_id' => $userId]));

        return $this->success($result, '合约下单成功');
    }

    /**
     * 撤销永续合约订单
     */
    #[DeleteMapping('cancel-order')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function cancelOrder(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->cancelRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $orderId = (int)$this->request->input('order_id');

        $result = $this->service->cancelOrder($userId, $orderId);

        return $result ? $this->success([], '撤单成功') : $this->error('撤单失败');
    }

    /**
     * 查询用户订单列表
     */
    #[GetMapping('orders')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getOrders(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->orderListRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $marginMode = $this->request->input('margin_mode') ? (int)$this->request->input('margin_mode') : null;
            $side = $this->request->input('side') ? (int)$this->request->input('side') : null;
            $status = $this->request->input('status') ? (int)$this->request->input('status') : null;
            $perPage = (int)($this->request->input('per_page', 20));
            $page = (int)($this->request->input('page', 1));

            $result = $this->service->getOrderList($userId, $currencyId, $marginMode, $side, $status, $perPage, $page);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询订单详情
     */
    #[GetMapping('order-detail')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getOrderDetail(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->orderDetailRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $orderId = (int)$this->request->input('order_id');

            $result = $this->service->getOrderDetail($userId, $orderId);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询合约交易历史
     */
    #[GetMapping('trades')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getTrades(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->tradeHistoryRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $marginMode = $this->request->input('margin_mode') ? (int)$this->request->input('margin_mode') : null;
            $perPage = (int)($this->request->input('per_page', 20));
            $page = (int)($this->request->input('page', 1));

            $result = $this->service->getTradeHistory($userId, $currencyId, $marginMode, $perPage, $page);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量撤销订单
     */
    #[PostMapping('cancel-all-orders')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function cancelAllOrders(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->cancelAllRules());

        try {
            $userId = (int)$this->request->getAttribute(name: 'user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->cancelAllOrders($userId, $currencyId);

            return $this->success($result, '批量撤单成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 全部平仓接口
     */
    #[PostMapping('close-all-positions')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function closeAllPositions(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->closeAllPositionsRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;

            $result = $this->service->closeAllPositions($userId, $currencyId);

            return $this->success($result, '全部平仓任务已提交');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 反手开仓接口
     */
    #[PostMapping('reverse-position')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function reversePosition(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->reversePositionRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $positionId = (int)$this->request->input('position_id');
            $quantity = $this->request->input('quantity') ? (float)$this->request->input('quantity') : null;
            $reduceOnly = (bool)$this->request->input('reduce_only', false);

            $result = $this->service->reversePosition($userId, $positionId, $quantity, $reduceOnly);

            $message = $reduceOnly ? '平仓成功' : '反手开仓成功';
            return $this->success($result, $message);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

}