<?php

declare(strict_types=1);
/**
 * 永续合约仓位管理控制器
 */

namespace App\Http\Api\Controller\V1\Contract;

use App\Http\Common\Controller\AbstractController;
use App\Http\Api\Request\V1\Contract\PerpetualPositionRequest;
use App\Http\Api\Service\V1\Contract\PerpetualPositionService;
use App\Http\Api\Service\V1\Contract\PerpetualUserConfigService;
use App\Http\Api\Middleware\TokenMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/v1/contract/perpetual/position')]
class PerpetualPositionController extends AbstractController
{
    /**
     * @var PerpetualPositionService $service;
     */
    public mixed $service;

    /**
     * @var PerpetualUserConfigService $configService;
     */
    #[Inject]
    public PerpetualUserConfigService $configService;

    /**
     * @var PerpetualPositionRequest $rules
     */
    public mixed $rules;

    /**
     * 查询用户持仓列表
     */
    #[GetMapping('list')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getPositions(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->positionListRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $marginMode = $this->request->input('margin_mode') ? (int)$this->request->input('margin_mode') : null;
            $side = $this->request->input('side') ? (int)$this->request->input('side') : null;
            $perPage = (int)($this->request->input('per_page', 20));
            $page = (int)($this->request->input('page', 1));

            $result = $this->service->getPositionList($userId, $currencyId, $marginMode, $side, $perPage, $page);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询单个持仓详情
     */
    #[GetMapping('detail')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getPositionDetail(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->positionDetailRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$this->request->input('currency_id');
            $marginMode = (int)$this->request->input('margin_mode');

            $result = $this->service->getPositionDetail($userId, $currencyId, $marginMode);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 调整保证金
     */
    #[PostMapping('adjust-margin')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function adjustMargin(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->adjustMarginRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$this->request->input('currency_id');
            $marginMode = (int)$this->request->input('margin_mode');
            $amount = (float)$this->request->input('amount');
            $type = (int)$this->request->input('type'); // 1-增加 2-减少

            $result = $this->service->adjustMargin($userId, $currencyId, $marginMode, $amount, $type);

            return $this->success($result, '调整保证金成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 调整杠杆倍数
     */
    #[PostMapping('adjust-leverage')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function adjustLeverage(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->adjustLeverageRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$this->request->input('currency_id');
            $marginMode = (int)$this->request->input('margin_mode');
            $leverage = (float)$this->request->input('leverage');

            $result = $this->service->adjustLeverage($userId, $currencyId, $marginMode, $leverage);

            return $this->success($result, '调整杠杆成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 切换保证金模式
     */
    #[PostMapping('switch-margin-mode')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function switchMarginMode(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->switchMarginModeRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$this->request->input('currency_id');
            $marginMode = (int)$this->request->input('margin_mode');

            $result = $this->service->switchMarginMode($userId, $currencyId, $marginMode);

            return $this->success($result, '切换保证金模式成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询持仓风险
     */
    #[GetMapping('risk')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getPositionRisk(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->positionRiskRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $marginMode = $this->request->input('margin_mode') ? (int)$this->request->input('margin_mode') : null;

            $result = $this->service->getPositionRisk($userId, $currencyId, $marginMode);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询强平历史
     */
    #[GetMapping('liquidation-history')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getLiquidationHistory(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->liquidationHistoryRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $perPage = (int)($this->request->input('per_page', 20));
            $page = (int)($this->request->input('page', 1));

            $result = $this->service->getLiquidationHistory($userId, $currencyId, $perPage, $page);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询ADL历史
     */
    #[GetMapping('adl-history')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getAdlHistory(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->adlHistoryRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = $this->request->input('currency_id') ? (int)$this->request->input('currency_id') : null;
            $perPage = (int)($this->request->input('per_page', 20));
            $page = (int)($this->request->input('page', 1));

            $result = $this->service->getAdlHistory($userId, $currencyId, $perPage, $page);

            return $this->success($result, '查询成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询用户合约交易配置
     */
    #[GetMapping('config')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function getUserConfig(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->configGetRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$this->request->input('currency_id');
            
            $result = $this->configService->getUserConfig($userId, $currencyId);

            return $this->success($result, '查询配置成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新用户合约交易配置
     */
    #[PutMapping('config')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function updateUserConfig(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->configUpdateRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$this->request->input('currency_id');
            $configData = $this->request->all();
            
            $result = $this->configService->updateUserConfig($userId, $currencyId, $configData);

            return $this->success($result, '更新配置成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 重置用户合约交易配置为默认值
     */
    #[PostMapping('config/reset')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function resetUserConfig(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->configResetRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $currencyId = (int)$this->request->input('currency_id');
            
            $result = $this->configService->resetUserConfig($userId, $currencyId);

            return $this->success($result, '重置配置成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}