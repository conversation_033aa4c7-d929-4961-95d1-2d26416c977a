<?php

declare(strict_types=1);
/**
 * 永续合约委托单控制器
 */

namespace App\Http\Api\Controller\V1\Contract;

use App\Http\Common\Controller\AbstractController;
use App\Http\Api\Request\V1\Contract\PerpetualConditionalOrderRequest;
use App\Http\Api\Service\V1\Contract\PerpetualConditionalOrderService;
use App\Http\Api\Middleware\TokenMiddleware;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/v1/contract/perpetual/conditional')]
class PerpetualConditionalOrderController extends AbstractController
{
    /**
     * @var PerpetualConditionalOrderService $service;
     */
    public mixed $service;

    /**
     * @var PerpetualConditionalOrderRequest $rules
     */
    public mixed $rules;

    /**
     * 创建委托单
     */
    #[PostMapping('create')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function create(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->createRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $orderData = $this->request->all();

        $result = $this->service->createConditionalOrder($userId, $orderData);

        return $this->success($result, '委托单创建成功');
    }

    /**
     * 撤销委托单
     */
    #[PostMapping('cancel')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function cancel(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->cancelRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $conditionalOrderId = (int)$this->request->input('conditional_order_id');

        $result = $this->service->cancelConditionalOrder($userId, $conditionalOrderId);

        return $this->success($result, '委托单撤销成功');
    }

    /**
     * 修改委托单
     */
    #[PostMapping('update')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function update(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->updateRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $conditionalOrderId = (int)$this->request->input('conditional_order_id');
            $allData = $this->request->all();
            unset($allData['conditional_order_id']);
            $updateData = $allData;

            $result = $this->service->updateConditionalOrder($userId, $conditionalOrderId, $updateData);

            return $this->success($result, '委托单修改成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 查询委托单列表
     */
    #[GetMapping('list')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function list(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->listRules());

        $userId = (int)$this->request->getAttribute('user_id');
        $filters = $this->request->all();

        $result = $this->service->getConditionalOrders($userId, $filters);

        return $this->success($result);
    }

    /**
     * 查询委托单详情
     */
    #[GetMapping('detail')]
    #[Middleware(middleware: TokenMiddleware::class)]
    public function detail(): \App\Http\Common\Result
    {
        $this->requestValidate($this->rules->detailRules());

        try {
            $userId = (int)$this->request->getAttribute('user_id');
            $conditionalOrderId = (int)$this->request->input('conditional_order_id');

            $result = $this->service->getConditionalOrderDetail($userId, $conditionalOrderId);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

}
