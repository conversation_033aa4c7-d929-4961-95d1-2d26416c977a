<?php

declare(strict_types=1);

/**
 * TipoffsController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Controller\SysConfig;

use App\Http\Api\Request\SysConfig\SysConfigRequest;
use App\Http\Common\Result;
use App\Service\SystemConfigService;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\Di\Annotation\Inject;

#[Controller(prefix: "api/sysconfig")]
#[Middleware(TokenMiddleware::class)]
class SysConfigController extends AbstractController
{
    #[Inject]
    protected SystemConfigService $systemConfigService;

    /**
     * 说明：配置信息
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("configInfo")]
    public function configInfo(SysConfigRequest $request): Result
    {
        $result = $this->systemConfigService->configInfo($request->input('code'));
        return $this->success($result);
    }

    /**
     * 说明：配置信息列表
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("configList")]
    public function configList(SysConfigRequest $request): Result
    {
        $result = $this->systemConfigService->configList($request->input('code'));
        return $this->success($result);
    }
}
