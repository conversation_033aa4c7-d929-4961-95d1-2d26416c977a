<?php

declare(strict_types=1);

/**
 * ArticleController
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-01
 * Website:xxx
 */

namespace App\Http\Api\Controller\Article;

use App\Http\Api\Service\Article\ArticleService;
use App\Http\Common\Result;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Article\ArticleRequest;

/**
 * 资讯管理
 */
#[Controller(prefix: "api/article/article")]
class ArticleController extends AbstractController
{

    #[Inject]
    protected ArticleService $articleService;

    /**
     * 说明：资讯列表
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(ArticleRequest $request): Result
    {
        $result = $this->articleService->list($request);
        return $this->success($result);
    }

     /**
     * 获取详情
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("detail")]
    public function detail(ArticleRequest $request): Result
    {
        $result = $this->articleService->detail($request->input('id'));
        return $this->success($result);
    }
}
