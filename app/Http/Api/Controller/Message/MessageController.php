<?php

declare(strict_types=1);

/**
 * MessageController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-03
 * Website:xxx
 */

namespace App\Http\Api\Controller\Message;

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Service\Message\MessageService;
use App\Http\Common\Result;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Message\MessageRequest;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * 说明：消息api
 */
#[Controller(prefix: "api/message/message")]
class MessageController extends AbstractController
{

    #[Inject]
    protected MessageService $messageService;
    /**
     * 说明：消息列表
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(MessageRequest $request): Result
    {
        $result = $this->messageService->list($request);
        return $this->success($result);
    }

     /**
     * 说明：私人消息列表
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list_private")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function listPrivate(MessageRequest $request): Result
    {
        $result = $this->messageService->listPrivate($request);
        return $this->success($result);
    }

    /**
     * 获取消息详情
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("detail")]
    public function detail(MessageRequest $request): Result
    {
        $result = $this->messageService->detail($request->input('id'));
        return $this->success($result);
    }
}
