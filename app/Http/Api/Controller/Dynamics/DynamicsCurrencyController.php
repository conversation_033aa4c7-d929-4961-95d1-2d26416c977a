<?php

declare(strict_types=1);

/**
 * DynamicsCurrencyController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-09
 * Website:xxx
 */

namespace App\Http\Api\Controller\Dynamics;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Dynamics\DynamicsCurrencyRequest;
use \App\Http\Api\Service\Dynamics\DynamicsCurrencyService;


/**
 * 发布动态，币种选择
 */
#[Controller(prefix: "api/dynamics/dynamicsCurrency")]
#[Middleware(TokenMiddleware::class)]
class DynamicsCurrencyController extends AbstractController
{
    #[Inject]
    protected DynamicsCurrencyService $dynamicsCurrencyService;

    /**
     * 说明：
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(DynamicsCurrencyRequest $request): Result
    {
        $result = $this->dynamicsCurrencyService->list($request);
        return $this->success($result);
    }
}
