<?php

declare(strict_types=1);

/**
 * DynamicsController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-04
 * Website:xxx
 */

namespace App\Http\Api\Controller\Dynamics;

use App\Http\Common\Result;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use App\Http\Api\Request\Dynamics\DynamicsRequest;
use \App\Http\Api\Service\Dynamics\DynamicsService;
use Hyperf\HttpServer\Annotation\PostMapping;

/**
 * 动态api
 * @Controller
 */
#[Controller(prefix: "api/dynamics/dynamics")]
class DynamicsController extends AbstractController
{
    
    
    #[Inject]
    protected DynamicsService $dynamicsService;

    /**
     * 说明：动态列表
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->list($request);
        return $this->success($result);
    }

    /**
     * 说明：动态详情
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("detail")]
    public function detail(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->detail($request->input('dynamics_id'));
        return $this->success($result);
    }

    /**
     * 说明：动态发布
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("create")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function create(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->create($request);
        return $this->success($result);
    }

    /**
     * 说明：动态修改
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("update")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function update(DynamicsRequest $request): Result
    {
        $this->dynamicsService->update($request);
        return $this->success();
    }

    /**
     * 说明：动态删除
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("delete")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function delete(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->delete($request);
        return $this->success($result);
    }

    /**
     * 说明：评论
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("comment")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function comment(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->comment($request);
        return $this->success($result);
    }

    /**
     * 说明：评论列表
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("commentList")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function commentList(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->commentList($request);
        return $this->success($result);
    }

    /**
     * 说明：点赞
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("liked")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function liked(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->liked($request);
        return $this->success($result);
    }

    /**
     * 说明：取消点赞
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("unLiked")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function unLiked(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->unLiked($request);
        return $this->success($result);
    }

    /**
     * 说明：转发
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("forward")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function forward(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->forward($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：取消转发
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("unForward")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function unForward(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->unForward($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：收藏
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("collect")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function collect(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->collect($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：取消收藏
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("unCollect")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function unCollect(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->unCollect($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：他人信息
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("userInfo")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function userInfo(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->userInfo($request->input('user_id'));
        if (!$result) {
            return $this->error();
        }
        return $this->success($result);
    }

    /**
     * 说明：我的信息
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("meInfo")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function meInfo(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->userInfo($request->userId());
        if (!$result) {
            return $this->error();
        }
        return $this->success($result);
    }

    /**
     * 某个人的动态列表
     * @param DynamicsRequest $request
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("userDynamics")]
    public function userDynamics(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->userDynamics($request,$request->input('user_id'));
        if (!$result) {
            return $this->error();
        }
        return $this->success($result);
    }

    /**
     * 某个人的文章列表
     * @param DynamicsRequest $request
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("userArircle")]
    public function userArircle(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->userArircle($request,$request->input('user_id'));
        if (!$result) {
            return $this->error();
        }
        return $this->success($result);
    }

    /**
     * 登录用户的文章列表
     * @param DynamicsRequest $request
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("meArircle")]
    public function meArircle(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->userArircle($request,$request->userId());
        if (!$result) {
            return $this->error();
        }
        return $this->success($result);
    }

    /**
     * 登录用户的动态列表
     * @param DynamicsRequest $request
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("meDynamics")]
    public function meDynamics(DynamicsRequest $request): Result
    {
        $result = $this->dynamicsService->userDynamics($request,$request->userId());
        if (!$result) {
            return $this->error();
        }
        return $this->success($result);
    }

}
