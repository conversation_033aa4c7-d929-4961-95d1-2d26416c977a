<?php

declare(strict_types=1);

/**
 * PassKeyController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-07
 * Website:xxx
 */

namespace App\Http\Api\Controller\User;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\User\PassKeyRequest;
use App\Http\Api\Service\User\PassKeyService;

#[Controller(prefix: "api/user/passKey")]
class PassKeyController extends AbstractController
{
    #[Inject]
    protected PassKeyService $passKeyService;

    /**
     * 生成注册选项
     *
     * 为当前用户生成WebAuthn通行密钥注册所需的选项
     * 包括挑战码、支持的算法、认证器要求等
     */
    #[PostMapping("register/options")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function registerOptions(PassKeyRequest $request): Result
    {
        $result = $this->passKeyService->registerOptions();
        return $this->success($result);
    }

    /**
     * 验证注册结果
     *
     * 验证用户完成通行密钥注册后返回的认证响应
     * 所有业务逻辑在Service层处理
     */
    #[PostMapping("register/verify")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function registerVerify(PassKeyRequest $request): Result
    {
        $result = $this->passKeyService->registerVerify($request);
        return $this->success($result);
    }

    /**
     * 生成登录选项
     *
     * 为用户登录生成WebAuthn认证选项
     * 支持指定用户名和无用户名登录模式
     */
    #[PostMapping("login/options")]
    public function loginOptions(PassKeyRequest $request): Result
    {
        $result = $this->passKeyService->loginOptions($request);
        return $this->success($result);
    }

    /**
     * 验证登录
     *
     * 验证用户的通行密钥认证响应
     * 所有业务逻辑在Service层处理
     */
    #[PostMapping("login/verify")]
    public function loginVerify(PassKeyRequest $request): Result
    {
        $result = $this->passKeyService->loginVerify($request);
        return $this->success($result);
    }

    /**
     * 删除通行密钥
     *
     * 删除用户指定的通行密钥凭证
     * 支持删除单个凭证或所有凭证
     */
    #[DeleteMapping("delete")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function delete(PassKeyRequest $request): Result
    {
        $result = $this->passKeyService->delete($request);
        return $this->success($result);
    }

    /**
     * 获取用户通行密钥列表
     *
     * 返回当前用户的所有通行密钥信息
     * 用于管理界面显示
     */
    #[GetMapping("list")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function list(): Result
    {
        $result = $this->passKeyService->getUserPasskeys();
        return $this->success($result);
    }
}
