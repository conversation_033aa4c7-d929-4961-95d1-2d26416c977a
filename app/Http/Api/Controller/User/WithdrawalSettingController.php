<?php

declare(strict_types=1);

/**
 * WithdrawalSettingController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Controller\User;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\User\WithdrawalSettingRequest;
use \App\Http\Api\Service\User\WithdrawalSettingService;

#[Controller(prefix: "api/user/withdrawalSetting")]
#[Middleware(TokenMiddleware::class)]
class WithdrawalSettingController extends AbstractController
{
    #[Inject]
    protected WithdrawalSettingService $withdrawalSettingService;

    /**
     * 说明：
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("show")]
    public function show(): Result
    {
        $result = $this->withdrawalSettingService->show();
        return $this->success($result);
    }

    /**
     * 提现设置
     */
    #[PutMapping("update")]
    public function update(WithdrawalSettingRequest $request): Result
    {
        $result = $this->withdrawalSettingService->update($request->all());
        return $this->success($result);
    }
}
