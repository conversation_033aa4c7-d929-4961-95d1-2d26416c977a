<?php

declare(strict_types=1);

/**
 * LoginLogController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Controller\User;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\User\LoginLogRequest;
use \App\Http\Api\Service\User\LoginLogService;

#[Controller(prefix: "api/user/loginLog")]
#[Middleware(TokenMiddleware::class)]
class LoginLogController extends AbstractController
{
    #[Inject]
    protected LoginLogService $loginLogService;

    /**
     * 说明：
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(LoginLogRequest $request): Result
    {
        $result = $this->loginLogService->list($request);
        return $this->success($result);
    }
}
