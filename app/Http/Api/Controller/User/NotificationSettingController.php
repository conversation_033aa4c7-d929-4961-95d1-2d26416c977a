<?php

declare(strict_types=1);

/**
 * NotificationSettingController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-07
 * Website:xxx
 */

namespace App\Http\Api\Controller\User;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\User\NotificationSettingRequest;
use \App\Http\Api\Service\User\NotificationSettingService;

#[Controller(prefix: "api/user/notificationSetting")]
#[Middleware(TokenMiddleware::class)]
class NotificationSettingController extends AbstractController
{
    #[Inject]
    protected NotificationSettingService $notificationSettingService;

    /**
     * 说明：
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("show")]

    public function show(): Result
    {
        $result = $this->notificationSettingService->show();
        return $this->success($result);
    }

    /**
     * 通知设置
     */
    #[PutMapping("update")]
    public function update(NotificationSettingRequest $request): Result
    {
        $result = $this->notificationSettingService->update($request->all());
        return $this->success($result);
    }
}
