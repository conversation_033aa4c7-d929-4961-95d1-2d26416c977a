<?php

declare(strict_types=1);

namespace App\Http\Api\Controller\User;

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\User\VipLevelRequest;
use App\Http\Api\Service\User\VipLevelService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;

#[Controller(prefix: 'api/user/vipLevel')]
#[Middleware(middleware: TokenMiddleware::class, priority: 100)]
final class VipLevelController extends AbstractController
{
    #[Inject]
    private readonly VipLevelService $vipLevelService;

    /**
     * VIP等级列表
     */
    #[GetMapping('list')]
    public function list(VipLevelRequest $request): Result
    {
        $result = $this->vipLevelService->list($request);
        return $this->success($result);
    }
}
