<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Controller\User;

use App\Http\Api\Middleware\RefreshTokenMiddleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Api\Request\User\BindRequest;
use App\Http\Api\Request\User\ConcernRequest;
use App\Http\Api\Request\User\IntroductionRequest;
use App\Http\Api\Request\User\LanguageRequest;
use App\Http\Api\Request\User\LoginOptionsRequest;
use App\Http\Api\Request\User\LoginRequest;
use App\Http\Api\Request\User\OAuthLoginRequest;
use App\Http\Api\Request\User\RegisterByEmailRequest;
use App\Http\Api\Request\User\RegisterByPhoneRequest;
use App\Http\Api\Request\User\ReportIpLocationRequest;
use App\Http\Api\Request\User\ResetPasswordRequest;
use App\Http\Api\Request\User\SendEmailCodeRequest;
use App\Http\Api\Request\User\SendSmsCodeRequest;
use App\Http\Api\Request\User\SetFundPasswordRequest;
use App\Http\Api\Request\User\VerifyCodeLoginRequest;
use App\Http\Api\Request\User\VerifyPasswordRequest;
use App\Http\Api\Service\Sms\SmsService;
use App\Http\Api\Service\User\ConcernService;
use App\Http\Api\Service\User\IntroductionService;
use App\Http\Api\Service\User\LanguageService;
use App\Http\Api\Service\User\UserService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Mine\Jwt\Traits\RequestScopedTokenTrait;

#[Controller(prefix: 'api/user')]
final class UserController extends AbstractController
{
    use RequestScopedTokenTrait;

    #[Inject]
    private readonly UserService $userService;

    #[Inject]
    private readonly ConcernService $concernService;

    #[Inject]
    private readonly SmsService $smsService;

    #[Inject]
    private readonly IntroductionService $introductionService;

    #[Inject]
    private readonly LanguageService $languageService;

    /**
     * 发送邮箱验证码
     */
    #[PostMapping('sendEmailCode')]
    public function sendEmailCode(SendEmailCodeRequest $request): Result
    {
        $this->userService->sendEmailCode($request->input('email'), $request->input('type'));
        return $this->success("发送成功");
    }

    /**
     * 发送短信验证码
     */
    #[PostMapping('sendSmsCode')]
    public function sendSmsCode(SendSmsCodeRequest $request): Result
    {
        $this->smsService->sendVerificationCode($request->input('phone'), $request->input('code'), $request->input('type'));
        return $this->success("发送成功");
    }

    /**
     * 发送邮箱验证码（需要登录）
     */
    #[PostMapping('sendEmailCodeByLogin')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function sendEmailCodeByLogin(SendEmailCodeRequest $request): Result
    {
        $this->userService->sendEmailCode($request->input('email'), $request->input('type'));
        return $this->success("发送成功");
    }

    /**
     * 发送短信验证码（需要登录）
     */
    #[PostMapping('sendSmsCodeByLogin')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function sendSmsCodeByLogin(SendSmsCodeRequest $request): Result
    {
        $this->smsService->sendVerificationCode($request->input('phone'), $request->input('code'), $request->input('type'));
        return $this->success("发送成功");
    }

    /**
     * 用户登录
     */
    #[PostMapping('login')]
    public function login(LoginRequest $request): Result
    {
        return $this->success(
            $this->userService->login(
                $request->input('username'),
                $request->input('password')
            )
        );
    }

    /**
     * 验证码登录（如果存在邮箱和手机号则都需要验证）
     */
    #[PostMapping('verifyCodeLogin')]
    public function verifyCodeLogin(VerifyCodeLoginRequest $request): Result
    {
        return $this->success(
            $this->userService->verifyCodeLogin($request->all())
        );
    }

    /**
     * 第三方登录
     */
    #[PostMapping('oauthLogin')]
    public function oauthLogin(OAuthLoginRequest $request): Result
    {
        return $this->success(
            $this->userService->oauthLogin($request->all())
        );
    }

    /**
     * 用户邮箱注册
     */
    #[PostMapping('registerByEmail')]
    public function registerByEmail(RegisterByEmailRequest $request): Result
    {
        return $this->success(
            $this->userService->registerByEmail($request->all())
        );
    }

    /**
     * 用户手机号注册
     */
    #[PostMapping('registerByPhone')]
    public function registerByPhone(RegisterByPhoneRequest $request): Result
    {
        return $this->success(
            $this->userService->registerByPhone($request->all())
        );
    }

    /**
     * 获取用户信息
     */
    #[GetMapping('info')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function userInfo(): Result
    {
        return $this->success($this->userService->getUserInfo());
    }

    /**
     * 用户退出登录
     */
    #[DeleteMapping('logout')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function logout(RequestInterface $request): Result
    {
        $this->userService->logout($this->getToken());
        return $this->success();
    }

    /**
     * 刷新令牌
     */
    #[GetMapping('refresh/token')]
    #[Middleware(middleware: RefreshTokenMiddleware::class, priority: 100)]
    public function refresh(RequestInterface $request): Result
    {
        return $this->success(
            $this->userService->refreshToken($this->getToken())
        );
    }

    /**
     * 重置密码
     */
    #[PostMapping('resetPassword')]
    public function resetPassword(ResetPasswordRequest $request): Result
    {
        return $this->success(
            $this->userService->resetPassword($request->all())
        );
    }

    /**
     * 绑定手机号
     */
    #[PostMapping('bindPhone')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function bindPhone(BindRequest $request): Result
    {
        return $this->success(
            $this->userService->bindPhone($request->all())
        );
    }

    /**
     * 绑定邮箱
     */
    #[PostMapping('bindEmail')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function bindEmail(BindRequest $request): Result
    {
        return $this->success(
            $this->userService->bindEmail($request->all())
        );
    }

    /**
     * 获取谷歌验证器密钥及二维码
     */
    #[GetMapping('google2fa')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function google2fa(BindRequest $request): Result
    {
        return $this->success(
            $this->userService->google2fa($request->all())
        );
    }

    /**
     * 绑定谷歌验证器
     */
    #[PostMapping('bindGoogle2fa')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function bindGoogle2fa(BindRequest $request): Result
    {
        return $this->success(
            $this->userService->bindGoogle2fa($request->all())
        );
    }

    /**
     * 设置资金密码
     */
    #[PutMapping('setFundPassword')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function setFundPassword(SetFundPasswordRequest $request): Result
    {
        return $this->success(
            $this->userService->setFundPassword($request->all())
        );
    }

    /**
     * 通过用户名获取进一步登录验证项
     */
    #[GetMapping('getLoginOptions')]
    public function getLoginOptions(LoginOptionsRequest $request): Result
    {
        return $this->success(
            $this->userService->getLoginOptions($request->all())
        );
    }

    /**
     * 单一密码验证接口
     */
    #[PostMapping('verifyPassword')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function verifyPassword(VerifyPasswordRequest $request): Result
    {
        $this->userService->verifyPassword($request->all());
        return $this->success();
    }

    /**
     * IP 归属地上报（为了白嫖，由客户端上报）
     */
    #[PostMapping('reportIpLocation')]
    public function reportIpLocation(ReportIpLocationRequest $request): Result
    {
        $this->userService->reportIpLocation($request->all());
        return $this->success();
    }

    /**
     * 注销账号
     */
    #[DeleteMapping('delete')]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function delete(): Result
    {
        $this->userService->delete();
        return $this->success();
    }


    /**
     * 说明：关注
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("concern")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function concern(ConcernRequest $request): Result
    {
        $result = $this->concernService->concern($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：取消关注
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("unConcern")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function unConcern(ConcernRequest $request): Result
    {
        $result = $this->concernService->unConcern($request);
        if (!$result) {
            return $this->error();
        }
        return $this->success();
    }

    /**
     * 说明：登录用户关注的人
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("concernUser")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function concernUser(ConcernRequest $request): Result
    {
        return $this->success($this->concernService->concernUser($request));
    }

    /**
     * 说明：设置简介
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("setIntroduction")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function setIntroduction(IntroductionRequest $request): Result
    {
        return $this->success($this->introductionService->setIntroduction($request));
    }

    /**
     * 说明：设置偏好语言
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("setLanguage")]
    #[Middleware(middleware: TokenMiddleware::class, priority: 100)]
    public function setLanguage(LanguageRequest $request): Result
    {
        return $this->success($this->languageService->setLanguage($request));
    }


}
