<?php

declare(strict_types=1);

/**
 * DeviceController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Controller\User;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\User\DeviceRequest;
use \App\Http\Api\Service\User\DeviceService;

#[Controller(prefix: "api/user/device")]
#[Middleware(TokenMiddleware::class)]
class DeviceController extends AbstractController
{
    #[Inject]
    protected DeviceService $deviceService;

    /**
     * 说明：
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(DeviceRequest $request): Result
    {
        $result = $this->deviceService->list($request);
        return $this->success($result);
    }
}
