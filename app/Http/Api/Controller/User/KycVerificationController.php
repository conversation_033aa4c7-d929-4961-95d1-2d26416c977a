<?php

declare(strict_types=1);

/**
 * KycVerificationController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-05
 * Website:xxx
 */

namespace App\Http\Api\Controller\User;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\User\KycVerificationRequest;
use \App\Http\Api\Service\User\KycVerificationService;
use Hyperf\HttpServer\Annotation\GetMapping;

#[Controller(prefix: "api/user/kycVerification")]
#[Middleware(TokenMiddleware::class)]
class KycVerificationController extends AbstractController
{
    #[Inject]
    protected KycVerificationService $kycVerificationService;

    /**
     * 获取 kyc 认证信息
     */
    #[GetMapping('info')]
    public function info(): Result
    {
        $result = $this->kycVerificationService->info();
        return $this->success($result);
    }

    /**
     * 提交 kyc 认证
     */
    #[PostMapping('submit')]
    public function submit(KycVerificationRequest $request): Result
    {
        $result = $this->kycVerificationService->submit($request->all());
        return $this->success($result);
    }
}
