<?php

declare(strict_types=1);

/**
 * TranslationController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Controller\Translation;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Translation\TranslationRequest;
use \App\Http\Api\Service\Translation\TranslationService;
use Hyperf\HttpServer\Annotation\PostMapping;

/**
 * 翻译
 */
#[Controller(prefix: "api/translation/translation")]
#[Middleware(TokenMiddleware::class)]
class TranslationController extends AbstractController
{
    #[Inject]
    protected TranslationService $translationService;

    /**
     * 说明：翻译
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("info")]
    public function info(TranslationRequest $request): Result
    {
        $result = $this->translationService->info($request);
        return $this->success($result);
    }
}
