<?php

declare(strict_types=1);

/**
 * AgentController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Controller\Agent;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Agent\AgentRequest;
use \App\Http\Api\Service\Agent\AgentService;

#[Controller(prefix: "api/agent")]
#[Middleware(TokenMiddleware::class)]
class AgentController extends AbstractController
{
    #[Inject]
    protected AgentService $agentService;

    /**
     * 代理商信息
     */
    #[GetMapping("info")]
    public function info(): Result
    {
        $result = $this->agentService->info();
        return $this->success($result);
    }

    /**
     * 邀请码列表
     */
    #[GetMapping("inviteCodeList")]
    public function inviteCodeList(AgentRequest $request): Result
    {
        $result = $this->agentService->inviteCodeList($request);
        return $this->success($result);
    }

    /**
     * 创建邀请码
     */
    #[PostMapping("createInviteCode")]
    public function createInviteCode(AgentRequest $request): Result
    {
        $result = $this->agentService->createInviteCode($request->all());
        return $this->success($result);
    }

    /**
     * 编辑邀请码
     */
    #[PutMapping("editInviteCode/{id}")]
    public function editInviteCode(int $id, AgentRequest $request): Result
    {
        $result = $this->agentService->editInviteCode($id, $request->all());
        return $this->success($result);
    }

    /**
     * 代理商日综合统计（日直客注册人数、日直客交易手续费、日直客返佣收益）
     */
    #[GetMapping("dailyStatistic")]
    public function dailyStatistic(): Result
    {
        $result = $this->agentService->dailyStatistic();
        return $this->success($result);
    }

    /**
     * 指定日期范围日直客注册人数统计图表
     */
    #[GetMapping("dailyRegisterCountChart")]
    public function dailyRegisterCountChart(AgentRequest $request): Result
    {
        $result = $this->agentService->dailyRegisterCountChart($request->all());
        return $this->success($result);
    }

    /**
     * 指定日期范围日下级交易手续费统计图表
     */
    #[GetMapping("dailyTransactionFeeChart")]
    public function dailyTransactionFeeChart(AgentRequest $request): Result
    {
        $result = $this->agentService->dailyTransactionFeeChart($request->all());
        return $this->success($result);
    }

    /**
     * 指定日期范围日返佣收益统计图表
     */
    #[GetMapping("dailyCommissionIncomeChart")]
    public function dailyCommissionIncomeChart(AgentRequest $request): Result
    {
        $result = $this->agentService->dailyCommissionIncomeChart($request->all());
        return $this->success($result);
    }
}
