<?php

declare(strict_types=1);

/**
 * AgentIncomeController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-11
 * Website:xxx
 */

namespace App\Http\Api\Controller\Agent;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Agent\AgentIncomeRequest;
use \App\Http\Api\Service\Agent\AgentIncomeService;

#[Controller(prefix: "api/agent/agentIncome")]
#[Middleware(TokenMiddleware::class)]
class AgentIncomeController extends AbstractController
{
    #[Inject]
    protected AgentIncomeService $agentIncomeService;

    /**
     * 说明：返佣收益
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("commission")]
    public function commission(AgentIncomeRequest $request): Result
    {
        $result = $this->agentIncomeService->commission($request);
        return $this->success($result);
    }
}
