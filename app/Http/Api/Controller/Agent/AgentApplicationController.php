<?php

declare(strict_types=1);

/**
 * AgentApplicationController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Controller\Agent;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Agent\AgentApplicationRequest;
use \App\Http\Api\Service\Agent\AgentApplicationService;

#[Controller(prefix: "api/agent")]
#[Middleware(TokenMiddleware::class)]
class AgentApplicationController extends AbstractController
{
    #[Inject]
    protected AgentApplicationService $agentApplicationService;

    /**
     * 代理商申请信息
     */
    #[GetMapping("application")]
    public function application(): Result
    {
        $result = $this->agentApplicationService->application();
        return $this->success($result);
    }

    /**
     * 代理商申请
     */
    #[PostMapping("apply")]
    public function apply(AgentApplicationRequest $request): Result
    {
        $result = $this->agentApplicationService->apply($request->all());
        return $this->success($result);
    }
}
