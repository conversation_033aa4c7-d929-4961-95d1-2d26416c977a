<?php

declare(strict_types=1);

/**
 * AgentClientController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-11
 * Website:xxx
 */

namespace App\Http\Api\Controller\Agent;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Agent\AgentClientRequest;
use \App\Http\Api\Service\Agent\AgentClientService;

#[Controller(prefix: "api/agent/agentClient")]
#[Middleware(TokenMiddleware::class)]
class AgentClientController extends AbstractController
{
    #[Inject]
    protected AgentClientService $agentClientService;

    /**
     * 说明：代理商直客列表
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(AgentClientRequest $request): Result
    {
        $result = $this->agentClientService->list($request);
        return $this->success($result);
    }

    /**
     * 修改备注
     */
    #[PutMapping("updateRemark/{id}")]
    public function updateRemark(int $id, AgentClientRequest $request): Result
    {
        $result = $this->agentClientService->updateRemark($id, $request->all());
        return $this->success($result);
    }
}
