<?php

declare(strict_types=1);

/**
 * TipoffsController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Controller\Tipoffs;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Tipoffs\TipoffsRequest;
use \App\Http\Api\Service\Tipoffs\TipoffsService;
use Hyperf\HttpServer\Annotation\PostMapping;

#[Controller(prefix: "api/tipoffs/tipoffs")]
#[Middleware(TokenMiddleware::class)]
class TipoffsController extends AbstractController
{
    #[Inject]
    protected TipoffsService $tipoffsService;

    /**
     * 说明：举报
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("create")]
    public function create(TipoffsRequest $request): Result
    {
        $result = $this->tipoffsService->create($request);
        return $this->success($result);
    }
}
