<?php

declare(strict_types=1);

/**
 * CategoryController

 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-06-24
 * Website:xxx
 */

namespace App\Http\Api\Controller\Category;

use App\Http\Common\Result;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Category\CategoryRequest;
use \App\Http\Api\Service\Category\CategoryService;
use Mine\Swagger\Attributes\ResultResponse;

/**
 * 分类api
 */
#[Controller(prefix: "api/category/category")]
class CategoryController extends AbstractController
{
    #[Inject]
    private CategoryService $categoryService;

    /**
     * 说明：分类列表，通过分类key标识查询
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping(path:"list")]
    #[ResultResponse(instance: new Result())]
    public function list(CategoryRequest $request): Result
    {
        $result = $this->categoryService->list($request);
        return $this->success($result);
    }
}
