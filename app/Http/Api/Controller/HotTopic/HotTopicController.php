<?php

declare(strict_types=1);

/**
 * HotTopicController
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-09
 * Website:xxx
 */

namespace App\Http\Api\Controller\HotTopic;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
// use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\HotTopic\HotTopicRequest;
use \App\Http\Api\Service\HotTopic\HotTopicService;

/**
 * 热门话题表控制器
 */
#[Controller(prefix: "api/hottopic/hotTopic")]
#[Middleware(TokenMiddleware::class)]
class HotTopicController extends AbstractController
{
    #[Inject]
    protected HotTopicService $hotTopicService;

    /**
     * 说明：热门话题列表
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list")]
    public function list(HotTopicRequest $request): Result
    {
        $result = $this->hotTopicService->list($request);
        return $this->success($result);
    }
}
