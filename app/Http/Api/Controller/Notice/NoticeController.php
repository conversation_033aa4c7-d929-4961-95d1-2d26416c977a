<?php

declare(strict_types=1);

/**
 * NoticeController
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-01
 * Website:xxx
 */

namespace App\Http\Api\Controller\Notice;

use App\Http\Common\Result;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Notice\NoticeRequest;
use \App\Http\Api\Service\Notice\NoticeService;
use ReflectionMethod;

/**
 * 公告控制器
 */
#[Controller(prefix: "api/notice/notice")]
class NoticeController extends AbstractController
{

    #[Inject]
    private NoticeService $noticeService;

    /**
     * 说明：公告列表，通过分类key标识查询
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("list",['scene'>'list'])]
    public function list(NoticeRequest $request): Result
    {
        $result = $this->noticeService->list($request);
        return $this->success($result);
    }

    /**
     * 获取详情
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[GetMapping("detail")]
    public function detail(NoticeRequest $request): Result
    {
        $result = $this->noticeService->detail($request->input('id'));
        return $this->success($result);
    }
}
