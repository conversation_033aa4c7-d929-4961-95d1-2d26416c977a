<?php

declare(strict_types=1);

/**
 * UploadController
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-04
 * Website:xxx
 */

namespace App\Http\Api\Controller\Upload;

use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
// use Hyperf\HttpServer\Annotation\PutMapping;
// use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use App\Http\Api\Request\Upload\UploadRequest;
use \App\Http\Api\Service\Upload\UploadService;

#[Controller(prefix: "api/upload")]
#[Middleware(TokenMiddleware::class)]
class UploadController extends AbstractController
{
    #[Inject]
    protected UploadService $uploadService;

    /**
     * 说明：上传图片
     * @return Result
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    #[PostMapping("uploadImage")]
    public function uploadImage(UploadRequest $request): Result
    {
        $result = $this->uploadService->uploadSingle($request->file('file'), [
            'file_category' => 'image',
        ]);
        return $this->success($result);
    }

    /**
     * 说明：上传多张图片
     */
    #[PostMapping("uploadImages")]
    public function uploadImages(UploadRequest $request): Result
    {
        $result = $this->uploadService->uploadMultiple($request->file('files'), [
            'file_category' => 'image',
        ]);
        return $this->success($result);
    }
}
