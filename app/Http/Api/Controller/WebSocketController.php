<?php

/**
 * WebSocketController.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/24
 * Website:algoquant.org
 */

namespace App\Http\Api\Controller;

use App\Logger\LoggerFactory;
use App\Service\WebSocket\MarketDataService;
use App\Service\WebSocket\SubscriptionManager;
use Hyperf\Contract\OnCloseInterface;
use Hyperf\Contract\OnMessageInterface;
use Hyperf\Contract\OnOpenInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\WebSocketServer\Sender;

class WebSocketController implements OnMessageInterface, OnOpenInterface, OnCloseInterface
{
    #[Inject]
    private MarketDataService $marketDataService;

    #[Inject]
    private SubscriptionManager $subscriptionManager;

    #[Inject]
    private Sender $sender;

    #[Inject]
    private LoggerFactory $loggerFactory;
    
    private array $activeConnections = [];
    private int $heartbeatTimerId = 0;
    private int $connectionCleanupTimerId = 0;

    public function onMessage($server, $frame): void
    {
        try {
            if ($frame->opcode === WEBSOCKET_OPCODE_PING) {
                $server->push($frame->fd, '', WEBSOCKET_OPCODE_PONG);
                $this->updateHeartbeat($frame->fd);
                return;
            }

            if ($frame->opcode === WEBSOCKET_OPCODE_PONG) {
                $this->updateHeartbeat($frame->fd);
                return;
            }

            $data = json_decode($frame->data, true);

            if (!$data || !isset($data['action'])) {
                if (isset($data['type']) && $data['type'] === 'ping') {
                    $this->sendPong($frame->fd);
                    $this->updateHeartbeat($frame->fd);
                    return;
                }
                $this->sendError($frame->fd, 'Invalid message format');
                return;
            }

            $this->updateHeartbeat($frame->fd);
            $response = $this->handleSubscriptionRequest($frame->fd, $data);
            $this->sendResponse($frame->fd, $response);

        } catch (\Throwable $e) {
            $this->loggerFactory->get('websocket')->error("WebSocket message error: " . $e->getMessage());
            $this->sendError($frame->fd, 'Internal server error');
        }
    }

    public function onClose($server, int $fd, int $_reactorId): void
    {
        try {
            $this->removeConnection($fd);
            $this->subscriptionManager->cleanupConnection($fd);
            $this->loggerFactory->get('websocket')->info("Connection closed and cleaned up: fd={$fd}");
        } catch (\Throwable $e) {
            $this->loggerFactory->get('websocket')->error("Error cleaning up connection {$fd}: " . $e->getMessage());
        }
    }

    public function onOpen($server, $request): void
    {
        $fd = $request->fd;

        $this->addConnection($fd, $server);
        
        // 立即设置心跳键，防止被误删
        $this->subscriptionManager->updateConnectionActivity($fd);
        
        $this->startGlobalTimers();

        $welcomeMessage = [
            'type' => 'connected',
            'message' => 'Connect has been connected',
            'timestamp' => time(),
            'heartbeat_interval' => 30
        ];

        try {
            $this->sender->push($fd, json_encode($welcomeMessage), WEBSOCKET_OPCODE_TEXT);
        } catch (\Throwable $e) {
            $this->loggerFactory->get('websocket')->error("Failed to send welcome message to fd={$fd}: " . $e->getMessage());
        }
    }

    private function sendResponse(int $fd, array $response): void
    {
        try {
            $jsonResponse = json_encode($response);
            if ($jsonResponse === false) {
                // JSON编码失败，记录错误但不断开连接
                error_log("JSON encode failed for fd={$fd}");
                return;
            }
            
            $this->sender->push($fd, $jsonResponse, WEBSOCKET_OPCODE_TEXT);
            
        } catch (\Throwable $e) {
            // 其他异常，记录但不断开连接
            var_dump("Error sending response to fd={$fd}: " . $e->getMessage());
        }
    }

    private function sendError(int $fd, string $message): void
    {
        $error = [
            'success' => false,
            'message' => $message,
            'timestamp' => time()
        ];

        try {
            $jsonError = json_encode($error);
            if ($jsonError === false) {
                error_log("JSON encode failed for error message to fd={$fd}");
                return;
            }
            
            $this->sender->push($fd, $jsonError, WEBSOCKET_OPCODE_TEXT);
            
        } catch (\Throwable $e) {
            $this->removeConnection($fd);
            // 其他异常，记录但不断开连接
            error_log("Error sending error to fd={$fd}: " . $e->getMessage());
        }
    }

    /**
     * 添加连接到活跃连接列表
     */
    private function addConnection(int $fd, $server): void
    {
        $this->activeConnections[$fd] = [
            'fd' => $fd,
            'server' => $server,
            'added_at' => time()
        ];
    }

    private function updateHeartbeat(int $fd): void
    {
        $this->subscriptionManager->updateConnectionActivity($fd);
    }

    /**
     * 从活跃连接列表移除连接
     */
    private function removeConnection(int $fd): void
    {
        unset($this->activeConnections[$fd]);
    }

    private function sendPong(int $fd): void
    {
        $pongMessage = [
            'type' => 'pong',
            'timestamp' => time()
        ];

        try {
            $this->sender->push($fd, json_encode($pongMessage), WEBSOCKET_OPCODE_TEXT);
        } catch (\Throwable $e) {
            $this->loggerFactory->get('websocket')->error("Failed to send pong to fd={$fd}: " . $e->getMessage());
        }
    }

    /**
     * 处理订阅请求（从MarketDataService移过来）
     */
    private function handleSubscriptionRequest(int $fd, array $request): array
    {
        try {
            $action = $request['action'] ?? '';
            $type = $request['type'] ?? '';
            $marketType = (int)($request['market_type'] ?? 0);
            $symbol = $request['symbol'] ?? '';

            // 处理用户认证
            if ($action === 'auth' && $type === 'login') {
                return $this->subscriptionManager->authenticateUser($fd, $request);
            }

            if (!in_array($action, ['subscribe', 'unsubscribe'])) {
                return ['success' => false, 'message' => 'Invalid action. Must be subscribe, unsubscribe, or auth'];
            }

            if (!in_array($type, ['ticker', 'depth', 'trade'])) {
                return ['success' => false, 'message' => 'Invalid type. Must be ticker, depth, or trade'];
            }

            if (!in_array($marketType, [1, 5])) {
                return ['success' => false, 'message' => 'Invalid market_type. Must be 1 (spot) or 5 (margin)'];
            }

            if (in_array($type, ['depth', 'trade']) && empty($symbol)) {
                return ['success' => false, 'message' => 'Symbol required for depth/trade'];
            }

            // 调用订阅管理器的方法
            $result = false;
            $errorMessage = '';
            
            try {
                $result = match ([$action, $type]) {
                    ['subscribe', 'ticker'] => $this->subscriptionManager->subscribeTicker($fd, $marketType),
                    ['unsubscribe', 'ticker'] => $this->subscriptionManager->unsubscribeTicker($fd, $marketType),
                    ['subscribe', 'depth'] => $this->subscriptionManager->subscribeDepth($fd, $marketType, $symbol),
                    ['unsubscribe', 'depth'] => $this->subscriptionManager->unsubscribeDepth($fd, $marketType, $symbol),
                    ['subscribe', 'trade'] => $this->subscriptionManager->subscribeTrade($fd, $marketType, $symbol),
                    ['unsubscribe', 'trade'] => $this->subscriptionManager->unsubscribeTrade($fd, $marketType, $symbol),
                    default => false
                };
                
            } catch (\Throwable $e) {
                $errorMessage = 'Subscription service error';
            }

            if ($result) {
                return [
                    'success' => true,
                    'message' => ucfirst($action) . ' ' . $type . ' successful',
                    'data' => [
                        'action' => $action,
                        'type' => $type,
                        'market_type' => $marketType,
                        'symbol' => $symbol ?: null
                    ]
                ];
            } else {
                $failureMessage = $errorMessage ?: 'Operation failed';
                return ['success' => false, 'message' => $failureMessage];
            }
            
        } catch (\Throwable $e) {
            return ['success' => false, 'message' => 'Internal error: ' . $e->getMessage()];
        }
    }

    /**
     * 启动全局定时器（心跳和连接清理）
     */
    private function startGlobalTimers(): void
    {
        // 启动心跳定时器（35秒间隔，批量发送）
        if ($this->heartbeatTimerId === 0) {
            $this->heartbeatTimerId = \Swoole\Timer::tick(35000, function () {
                $this->batchSendHeartbeat();
            });
        }

        // 启动连接清理定时器（120秒间隔，大于心跳键90秒过期时间）
        if ($this->connectionCleanupTimerId === 0) {
            $this->connectionCleanupTimerId = \Swoole\Timer::tick(120000, function () {
                try {
                    $this->subscriptionManager->cleanupInactiveConnections();
                } catch (\Throwable $e) {
                    // 忽略清理错误
                }
            });
        }
    }

    /**
     * 批量发送心跳包到所有活跃连接
     */
    private function batchSendHeartbeat(): void
    {
        if (empty($this->activeConnections)) {
            return;
        }

        $deadConnections = [];
        $sentCount = 0;

        foreach ($this->activeConnections as $fd => $connection) {
            try {
                // 检查连接是否仍然活跃
                if (!$this->subscriptionManager->isConnectionActive($fd)) {
                    $deadConnections[] = $fd;
                    continue;
                }

                // 发送心跳包
                $connection['server']->push($fd, '', WEBSOCKET_OPCODE_PING);
                $sentCount++;

            } catch (\Throwable $e) {
                // 发送失败，标记为死连接
                $deadConnections[] = $fd;
            }
        }

        // 清理死连接
        foreach ($deadConnections as $fd) {
            $this->removeConnection($fd);
            $this->subscriptionManager->cleanupConnection($fd);
        }
    }
}