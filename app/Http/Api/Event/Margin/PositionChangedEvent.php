<?php

declare(strict_types=1);

namespace App\Http\Api\Event\Margin;

use App\Model\Trade\TradeMarginPosition;

/**
 * 仓位变动事件
 */
class PositionChangedEvent
{
    public function __construct(
        public readonly TradeMarginPosition $position,
        public readonly string $changeType, // 'created', 'increased', 'decreased', 'closed', 'reversed'
        public readonly array $changeData = [] // 额外的变更数据
    ) {}

    /**
     * 获取事件描述
     */
    public function getDescription(): string
    {
        return match ($this->changeType) {
            'created' => '创建仓位',
            'increased' => '增加仓位',
            'decreased' => '减少仓位',
            'closed' => '关闭仓位',
            'reversed' => '反向开仓',
            default => '未知操作'
        };
    }

    /**
     * 获取事件数据
     */
    public function toArray(): array
    {
        return [
            'position_id' => $this->position->getId(),
            'user_id' => $this->position->getUserId(),
            'currency_id' => $this->position->getCurrencyId(),
            'change_type' => $this->changeType,
            'change_data' => $this->changeData,
            'timestamp' => time()
        ];
    }
}
