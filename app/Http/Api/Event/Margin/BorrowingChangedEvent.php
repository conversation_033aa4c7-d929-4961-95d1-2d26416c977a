<?php

declare(strict_types=1);

namespace App\Http\Api\Event\Margin;

use App\Model\User\UserMarginBorrow;

/**
 * 借贷变动事件
 */
class BorrowingChangedEvent
{
    public function __construct(
        public readonly int $userId,
        public readonly int $currencyId,
        public readonly int $marginType,
        public readonly string $changeType, // 'created', 'increased', 'decreased', 'repaid'
        public readonly float $amount,
        public readonly ?UserMarginBorrow $borrow = null
    ) {}

    /**
     * 获取事件描述
     */
    public function getDescription(): string
    {
        return match ($this->changeType) {
            'created' => '创建借贷',
            'increased' => '增加借贷',
            'decreased' => '减少借贷',
            'repaid' => '偿还借贷',
            default => '未知操作'
        };
    }

    /**
     * 获取事件数据
     */
    public function toArray(): array
    {
        return [
            'user_id' => $this->userId,
            'currency_id' => $this->currencyId,
            'margin_type' => $this->marginType,
            'change_type' => $this->changeType,
            'amount' => $this->amount,
            'borrow_id' => $this->borrow?->getId(),
            'timestamp' => time()
        ];
    }
}
