<?php

declare(strict_types=1);

namespace App\Http\Api\Event;

use App\Http\Api\Service\User\DeviceService;
use App\Model\User\User;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Di\Annotation\Inject;

/**
 * 用户注册事件
 */
final class UserLoginEvent
{
    #[Inject]
    protected DeviceService $deviceService;

    public function __construct(
        public readonly User $user,
        public readonly string $accessToken,
        public readonly RequestInterface $request,
        public readonly bool $isLogin = true,
        public readonly ?string $failureReason = null,
    ) {}

    public function getClientIp(): string
    {
        return $this->deviceService->getClientIp();
    }

    public function getDeviceName(): string
    {
        return $this->deviceService->getDeviceName();
    }

    public function getDeviceType(): string
    {
        return $this->deviceService->getDeviceType();
    }

    public function getBrowser(): string
    {
        return $this->deviceService->getBrowser();
    }

    public function getOs(): string
    {
        return $this->deviceService->getOs();
    }

    public function getAppVersion(): string
    {
        return $this->deviceService->getAppVersion();
    }

    public function getDeviceId(): string
    {
        return $this->deviceService->getDeviceId();
    }

    public function getDeviceInfo(): array
    {
        return $this->deviceService->getDeviceInfo();
    }

    public function getPushToken(): string
    {
        return $this->deviceService->getPushToken();
    }
}
