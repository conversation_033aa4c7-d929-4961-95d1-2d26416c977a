<?php

declare(strict_types=1);

namespace App\Http\Api\Event;

use App\Http\Api\Service\User\DeviceService;
use App\Model\User\User;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Di\Annotation\Inject;

/**
 * 用户注册事件
 */
final class UserRegisteredEvent
{
    #[Inject]
    protected DeviceService $deviceService;

    public function __construct(
        public readonly User $user,
        public readonly RequestInterface $request,
    ) {}

    public function getClientIp(): string
    {
        return $this->deviceService->getClientIp();
    }
}
