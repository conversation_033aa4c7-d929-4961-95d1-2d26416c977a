<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 订单修改失败事件
 */

namespace App\Http\Api\Event\Perpetual;

class OrderModifyFailedEvent
{
    /**
     * 事件数据
     */
    public array $eventData;

    /**
     * 构造函数
     */
    public function __construct(array $eventData)
    {
        $this->eventData = $eventData;
    }

    /**
     * 获取事件类型
     */
    public function getEvent(): string
    {
        return $this->eventData['event'] ?? 'order_modify_failed';
    }

    /**
     * 获取币种符号
     */
    public function getSymbol(): string
    {
        return $this->eventData['symbol'] ?? '';
    }

    /**
     * 获取时间戳
     */
    public function getTimestamp(): int
    {
        return $this->eventData['timestamp'] ?? time();
    }

    /**
     * 获取订单ID
     */
    public function getOrderId(): int
    {
        return (int)($this->eventData['order_id'] ?? 0);
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return (int)($this->eventData['user_id'] ?? 0);
    }

    /**
     * 获取失败原因
     */
    public function getReason(): string
    {
        return $this->eventData['reason'] ?? '';
    }

    /**
     * 获取请求的价格
     */
    public function getRequestedPrice(): float
    {
        return (float)($this->eventData['requested_price'] ?? 0);
    }

    /**
     * 获取请求的数量
     */
    public function getRequestedQuantity(): float
    {
        return (float)($this->eventData['requested_quantity'] ?? 0);
    }

    /**
     * 获取请求的时间有效性
     */
    public function getRequestedTimeInForce(): string
    {
        return $this->eventData['requested_time_in_force'] ?? 'gtc';
    }
} 