<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 订单修改事件
 */

namespace App\Http\Api\Event\Perpetual;

class OrderModifyEvent
{
    /**
     * 订单ID
     */
    public int $orderId;

    /**
     * 新价格
     */
    public float $newPrice;

    /**
     * 新数量
     */
    public float $newQuantity;

    /**
     * 构造函数
     */
    public function __construct(int $orderId, float $newPrice, float $newQuantity)
    {
        $this->orderId = $orderId;
        $this->newPrice = $newPrice;
        $this->newQuantity = $newQuantity;
    }
} 