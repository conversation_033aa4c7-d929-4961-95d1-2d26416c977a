<?php

declare(strict_types=1);
/**
 * ArticleService
 * Author:<PERSON>.Mr
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-01
 * Website:xxx
 */

namespace App\Http\Api\Service\Article;

use App\Http\Api\Request\Article\ArticleRequest;
use App\Repository\Article\ArticleRepository;
use Hyperf\Di\Annotation\Inject;

class ArticleService
{

    #[Inject]
    protected ArticleRepository $repository;
    /**
     * 通过分类查询公告列表
     * Summary of list
     * @return array
     */
    public function list(ArticleRequest $request)
    {
        $page = $request->input('page',1);
        $pageSize = $request->input('page_size',10);
        return $this->repository->page($request->all(),$page,$pageSize);
    }

    /**
     * 获取详情
     * Summary of detail
     * @param mixed $id
     */
    public function detail($id) {
        return $this->repository->findById($id);
    }
}
