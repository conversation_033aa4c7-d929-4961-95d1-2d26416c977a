<?php

declare(strict_types=1);
/**
 * AgentIncomeService
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-11
 * Website:xxx
 */

namespace App\Http\Api\Service\Agent;

use ApiElf\QueryBuilder\AllowedFilter;
use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Agent\AgentIncomeRequest;
use App\Model\Agent\AgentCommissionIncome;
use App\QueryBuilder\QueryBuilder;

class AgentIncomeService extends BaseService
{
    public function commission(AgentIncomeRequest $request)
    {
        $query = AgentCommissionIncome::query()
            ->with([
                'tradeUser',
                'user.currentVipLevel.vipLevel'
            ]);
        return QueryBuilder::for($query, $request)
            ->filters([
                'user.account',
                'user.username',
                'user.email',
                'user.phone',
                'user.currentVipLevel.vipLevel.name',
                AllowedFilter::exact('trade_type'),
                AllowedFilter::scope('TradeUserType'),
                AllowedFilter::scope('TradeTimeBetween'),
            ])
            ->defaultSort('-created_at')
            ->page();
    }
}
