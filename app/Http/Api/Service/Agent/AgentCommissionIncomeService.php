<?php

declare(strict_types=1);
/**
 * AgentCommissionIncomeService
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-11
 * Website:xxx
 */

namespace App\Http\Api\Service\Agent;

use App\Http\Api\Service\BaseService;
use App\Model\Agent\Agent;
use App\Model\Agent\AgentCommissionIncome;
use App\Model\Agent\Enums\TradeType;
use App\Model\User\UserFeeCommission;
use Hyperf\Di\Annotation\Inject;

class AgentCommissionIncomeService extends BaseService
{
    #[Inject]
    protected AgentService $agentService;

    /**
     * 代理商返佣收益记录
     */
    public function create(UserFeeCommission $userFeeCommission)
    {
        // 检查上级是否为代理商
        $agent = Agent::query()->where(Agent::FIELD_USER_ID, $userFeeCommission->parent_id)->first();
        if (!$agent) {
            return;
        }

        switch ($userFeeCommission->trade_type) {
            case TradeType::SPOT:
                $trade_type = TradeType::SPOT;
                // TODO: 考虑代理商是否为交易员
                $agentRate = $agent->spot_commission_rate;
                break;
            case TradeType::CONTRACT:
                $trade_type = TradeType::CONTRACT;
                // TODO: 考虑代理商是否为交易员
                $agentRate = $agent->contract_commission_rate;
                break;
            case TradeType::SPOT_LEVERAGE:
                $trade_type = TradeType::SPOT_LEVERAGE;
                // TODO: 考虑代理商是否为交易员
                $agentRate = $agent->spot_commission_rate;
                break;
        }

        $commission_income_rate = $agentRate - $userFeeCommission->commission_rate;

        $commission_income_amount = bcmul((string)$userFeeCommission->fee_amount, (string)$commission_income_rate, 8);

        $agentCommissionIncome = AgentCommissionIncome::query()->create([
            AgentCommissionIncome::FIELD_AGENT_ID => $agent->id,
            AgentCommissionIncome::FIELD_SUB_AGENT_ID => $userFeeCommission->agent_id,
            AgentCommissionIncome::FIELD_AGENT_CLIENT_ID => $userFeeCommission->agent_client_id,
            AgentCommissionIncome::FIELD_TRADE_USER_ID => $userFeeCommission->user_id,
            AgentCommissionIncome::FIELD_ORDER_ID => $userFeeCommission->order_id,
            AgentCommissionIncome::FIELD_TRADE_TYPE => $trade_type,
            AgentCommissionIncome::FIELD_TRADE_AMOUNT => $userFeeCommission->trade_amount,
            AgentCommissionIncome::FIELD_FEE_AMOUNT => $userFeeCommission->fee_amount,
            AgentCommissionIncome::FIELD_COMMISSION_INCOME_RATE => $commission_income_rate,
            AgentCommissionIncome::FIELD_COMMISSION_INCOME_AMOUNT => $commission_income_amount,
            AgentCommissionIncome::FIELD_TRADE_TIME => $userFeeCommission->trade_time,
        ]);

        // 统计代理商所有直客日返佣收益
        $this->agentService->commissionIncomeStatisticByAgentCommissionIncome($agentCommissionIncome);

        return $agentCommissionIncome;
    }
}
