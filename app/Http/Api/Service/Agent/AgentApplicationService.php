<?php

declare(strict_types=1);
/**
 * AgentApplicationService
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Service\Agent;

use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Common\ResultCode;
use App\Model\Agent\AgentApplication;
use App\Model\Agent\Enums\ApplicationStatus;
use Carbon\Carbon;

class AgentApplicationService extends BaseService
{
    public function application()
    {
        $agentApplication = AgentApplication::query()->where(AgentApplication::FIELD_USER_ID, $this->userId())->first();
        return $agentApplication;
    }

    public function apply(array $data)
    {
        // 后台审核通过后自动生成一条邀请链接 cpx_agent_invite_code 邀请码为 cpx_user 表中的邀请码

        $agentApplication = AgentApplication::query()->where(AgentApplication::FIELD_USER_ID, $this->userId())->first();

        if ($agentApplication && $agentApplication->status == ApplicationStatus::PENDING) {
            throw new BusinessException(ResultCode::FORBIDDEN, '代理商申请正在审核中，请耐心等待');
        }

        if ($agentApplication && $agentApplication->status == ApplicationStatus::APPROVED) {
            throw new BusinessException(ResultCode::FORBIDDEN, '您已通过代理商申请，请勿重复申请');
        }

        if ($agentApplication) {
            $agentApplication->update($data);
        } else {
            $data[AgentApplication::FIELD_USER_ID] = $this->userId();
            $data[AgentApplication::FIELD_SUBMITTED_AT] = Carbon::now();
            $agentApplication = AgentApplication::query()->create($data);
        }
        return $agentApplication;
    }
}
