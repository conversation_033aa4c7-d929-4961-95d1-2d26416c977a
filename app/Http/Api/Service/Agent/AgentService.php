<?php

declare(strict_types=1);
/**
 * AgentService
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Service\Agent;

use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Agent\AgentRequest;
use App\Http\Common\ResultCode;
use App\Model\Agent\Agent;
use App\Model\Agent\AgentClient;
use App\Model\Agent\AgentCommissionIncome;
use App\Model\Agent\AgentInviteCode;
use App\Model\User\Enums\KycStatus;
use App\Model\User\User;
use App\Model\User\UserFeeCommission;
use App\Model\User\UserKycVerification;
use App\QueryBuilder\QueryBuilder;
use Carbon\Carbon;
use Hyperf\Database\Model\Collection;
use Hyperf\Redis\Redis;
use Hyperf\Di\Annotation\Inject;

class AgentService extends BaseService
{
    public const DAILY_REGISTER_COUNT_STATISTIC_KEY = 'agent-client:daily-register-count:agentid-%d'; // %d 代理id
    public const DAILY_TRANSACTION_FEE_STATISTIC_KEY = 'agent-client:daily-transaction-fee:agentid-%d'; // %d 代理id
    public const DAILY_COMMISSION_INCOME_STATISTIC_KEY = 'agent-client:daily-commission-income:agentid-%d'; // %d 代理id

    #[Inject]
    protected Redis $redis;

    public function info()
    {
        $agent = Agent::query()->where('user_id', $this->userId())->first();

        if (!$agent) {
            return null;
        }

        $agent->user = $this->user();

        return $agent;
    }

    public function inviteCodeList(AgentRequest $request)
    {
        $query = AgentInviteCode::query()->where('agent_id', $this->getUser()->agent_id);

        return QueryBuilder::for($query, $request)
            ->filters(['invite_code'])
            ->defaultSort('-is_default', '-created_at')
            ->page(function ($item) {
                $statistics = [];
                // 邀请码下的客户数量
                $agentClients = AgentClient::query()->select('id', 'invite_code_id', 'user_id')->where('invite_code_id', $item->id)->get();
                $statistics['client_count'] = $agentClients->count();

                // 身份认证人数
                $statistics['kyc_count'] = UserKycVerification::query()
                    ->whereIn('user_id', $agentClients->pluck('user_id'))
                    ->where('status', KycStatus::APPROVED)
                    ->count();

                $item->statistics = $statistics;

                return $item;
            });
    }

    public function createInviteCode(array $data)
    {
        $user = $this->getUser();
        $inviteCodeCount = AgentInviteCode::query()->where('agent_id', $user->agent_id)->count();
        $data['invite_code'] = $user->invite_code . $inviteCodeCount + 1;
        $data['user_id'] = $user->id;
        $data['agent_id'] = $user->agent_id;

        $agentInviteCode = AgentInviteCode::query()->create($data);

        return $agentInviteCode;
    }

    public function editInviteCode(int $id, array $data): void
    {
        $agentInviteCode = AgentInviteCode::query()->where('id', $id)->first();
        if ($agentInviteCode->invite_code == $this->user()->invite_code) {
            throw new BusinessException(ResultCode::FORBIDDEN, '主邀请链接不可修改');
        }
        $agentInviteCode->update($data);

        return;
    }

    public function dailyStatistic()
    {
        $date = date('Y-m-d');
        $agentId = $this->getUser()->agent_id;

        // 日注册人数
        $key = sprintf(self::DAILY_REGISTER_COUNT_STATISTIC_KEY, $agentId, $date);
        if (!$this->redis->exists($key)) {
            $dailyRegisterCount = $this->registerStatistic($agentId, $date);
        } else {
            $dailyRegisterCount = (int)$this->redis->hGet($key, $date);
        }

        // 日交易手续费
        $key = sprintf(self::DAILY_TRANSACTION_FEE_STATISTIC_KEY, $agentId, $date);
        if (!$this->redis->exists($key)) {
            $dailyTransactionFee = $this->transactionFeeStatisticByAgent($agentId, $date);
        } else {
            $dailyTransactionFee = (float)$this->redis->hGet($key, $date);
        }

        // 日返佣收益
        $key = sprintf(self::DAILY_COMMISSION_INCOME_STATISTIC_KEY, $agentId, $date);
        if (!$this->redis->exists($key)) {
            $dailyCommissionIncome = $this->commissionIncomeStatisticByAgent($agentId, $date);
        } else {
            $dailyCommissionIncome = (float)$this->redis->hGet($key, $date);
        }

        return [
            'today_register_count' => $dailyRegisterCount,
            'today_transaction_fee' => $dailyTransactionFee,
            'today_commission_income' => $dailyCommissionIncome,
        ];
    }

    public function dailyRegisterCountChart(array $data)
    {
        // 如果传入天数，转化为日期范围
        if (isset($data['days'])) {
            $days = (int)$data['days'];
            // 计算开始结束日期
            $end = date('Y-m-d');
            $days = $days - 1;
            $start = date('Y-m-d', strtotime("-$days days"));
        } elseif (isset($data['date_range'])) {
            // $data['date_range'] 2025-05-01,2025-05-15 验证一下格式
            // 是否包含逗号且为日期格式
            if (!preg_match('/^(\d{4}-\d{2}-\d{2}),(\d{4}-\d{2}-\d{2})$/', $data['date_range'])) {
                throw new BusinessException(ResultCode::FORBIDDEN, '日期格式错误');
            }
            [$start, $end] = explode(',', $data['date_range']);
        } else {
            throw new BusinessException(ResultCode::FORBIDDEN, '日期范围或天数至少指定一个');
        }

        $agentId = $this->getUser()->agent_id;

        // 开始结束日期相差的天数，一天一天取数据
        $days = Carbon::parse($start)->diffInDays(Carbon::parse($end)) + 1;
        $dateTime = Carbon::parse($start);

        $xAxis = [];
        $yAxis = [];
        $agentClients = null;
        for ($i = 0; $i < $days; $i++) {
            $date = $dateTime->format('Y-m-d');
            // 获取缓存中数据，如果还没有数据则完整统计一次
            $isExists = $this->redis->hExists(sprintf(self::DAILY_REGISTER_COUNT_STATISTIC_KEY, $agentId), $date);
            if (!$isExists) {
                if (!$agentClients) {
                    // 只查询一次
                    $agentClients = AgentClient::query()
                        ->select([
                            AgentClient::FIELD_ID,
                            AgentClient::FIELD_USER_ID,
                        ])
                        ->where(AgentClient::FIELD_AGENT_ID, $agentId)
                        ->whereBetween(AgentClient::FIELD_CREATED_AT, [$start . ' 00:00:00', $end . ' 23:59:59'])
                        ->get();
                }

                // 统计当日注册人数（为了避免任务执行失败导致的数据丢失，这里完整统计一整天的而不是简单的累加）
                $this->registerStatistic($agentId, $date, $agentClients);
            }
            // 获取数据
            $xAxis[] = $date;
            $yAxis[] = floatval($this->redis->hGet(sprintf(self::DAILY_REGISTER_COUNT_STATISTIC_KEY, $agentId), $date));
            $dateTime->addDay();
        }

        return [
            'xAxis' => $xAxis,
            'yAxis' => $yAxis,
        ];
    }

    public function dailyTransactionFeeChart(array $data)
    {
        // 如果传入天数，转化为日期范围
        if (isset($data['days'])) {
            $days = (int)$data['days'];
            // 计算开始结束日期
            $end = date('Y-m-d');
            $days = $days - 1;
            $start = date('Y-m-d', strtotime("-$days days"));
        } elseif (isset($data['date_range'])) {
            // $data['date_range'] 2025-05-01,2025-05-15 验证一下格式
            // 是否包含逗号且为日期格式
            if (!preg_match('/^(\d{4}-\d{2}-\d{2}),(\d{4}-\d{2}-\d{2})$/', $data['date_range'])) {
                throw new BusinessException(ResultCode::FORBIDDEN, '日期格式错误');
            }
            [$start, $end] = explode(',', $data['date_range']);
        } else {
            throw new BusinessException(ResultCode::FORBIDDEN, '日期范围或天数至少指定一个');
        }

        $agentId = $this->getUser()->agent_id;

        // 开始结束日期相差的天数，一天一天取数据
        $days = Carbon::parse($start)->diffInDays(Carbon::parse($end)) + 1;
        $dateTime = Carbon::parse($start);

        $xAxis = [];
        $yAxis = [];
        $userFeeCommissions = null;
        for ($i = 0; $i < $days; $i++) {
            $date = $dateTime->format('Y-m-d');
            // 获取缓存中数据，如果还没有数据则完整统计一次
            $isExists = $this->redis->hExists(sprintf(self::DAILY_TRANSACTION_FEE_STATISTIC_KEY, $agentId), $date);
            if (!$isExists) {
                if (!$userFeeCommissions) {
                    // 只查询一次
                    $userFeeCommissions = UserFeeCommission::query()
                        ->select([
                            UserFeeCommission::FIELD_ID,
                            UserFeeCommission::FIELD_FEE_AMOUNT,
                        ])
                        ->where(UserFeeCommission::FIELD_AGENT_ID, $agentId)
                        ->whereBetween(UserFeeCommission::FIELD_CREATED_AT, [$start . ' 00:00:00', $end . ' 23:59:59'])
                        ->get();
                }

                // 统计当日交易手续费（为了避免任务执行失败导致的数据丢失，这里完整统计一整天的而不是简单的累加）
                $this->transactionFeeStatisticByAgent($agentId, $date, $userFeeCommissions);
            }
            // 获取数据
            $xAxis[] = $date;
            $yAxis[] = floatval($this->redis->hGet(sprintf(self::DAILY_TRANSACTION_FEE_STATISTIC_KEY, $agentId), $date));
            $dateTime->addDay();
        }

        return [
            'xAxis' => $xAxis,
            'yAxis' => $yAxis,
        ];
    }

    public function dailyCommissionIncomeChart(array $data)
    {
        // 如果传入天数，转化为日期范围
        if (isset($data['days'])) {
            $days = (int)$data['days'];
            // 计算开始结束日期
            $end = date('Y-m-d');
            $days = $days - 1;
            $start = date('Y-m-d', strtotime("-$days days"));
        } elseif (isset($data['date_range'])) {
            // $data['date_range'] 2025-05-01,2025-05-15 验证一下格式
            // 是否包含逗号且为日期格式
            if (!preg_match('/^(\d{4}-\d{2}-\d{2}),(\d{4}-\d{2}-\d{2})$/', $data['date_range'])) {
                throw new BusinessException(ResultCode::FORBIDDEN, '日期格式错误');
            }
            [$start, $end] = explode(',', $data['date_range']);
        } else {
            throw new BusinessException(ResultCode::FORBIDDEN, '日期范围或天数至少指定一个');
        }

        $agentId = $this->getUser()->agent_id;

        // 开始结束日期相差的天数，一天一天取数据
        $days = Carbon::parse($start)->diffInDays(Carbon::parse($end)) + 1;
        $dateTime = Carbon::parse($start);

        $xAxis = [];
        $yAxis = [];
        $agentCommissionIncomes = null;
        for ($i = 0; $i < $days; $i++) {
            $date = $dateTime->format('Y-m-d');
            // 获取缓存中数据，如果还没有数据则完整统计一次
            $isExists = $this->redis->hExists(sprintf(self::DAILY_COMMISSION_INCOME_STATISTIC_KEY, $agentId), $date);
            if (!$isExists) {
                if (!$agentCommissionIncomes) {
                    // 只查询一次
                    $agentCommissionIncomes = AgentCommissionIncome::query()
                        ->select([
                            AgentCommissionIncome::FIELD_ID,
                            AgentCommissionIncome::FIELD_COMMISSION_INCOME_AMOUNT,
                        ])
                        ->where(AgentCommissionIncome::FIELD_AGENT_ID, $agentId)
                        ->whereBetween(AgentCommissionIncome::FIELD_CREATED_AT, [$start . ' 00:00:00', $end . ' 23:59:59'])
                        ->get();
                }

                // 统计当日返佣收益（为了避免任务执行失败导致的数据丢失，这里完整统计一整天的而不是简单的累加）
                $this->commissionIncomeStatisticByAgent($agentId, $date, $agentCommissionIncomes);
            }
            // 获取数据
            $xAxis[] = $date;
            $yAxis[] = floatval($this->redis->hGet(sprintf(self::DAILY_COMMISSION_INCOME_STATISTIC_KEY, $agentId), $date));
            $dateTime->addDay();
        }

        return [
            'xAxis' => $xAxis,
            'yAxis' => $yAxis,
        ];
    }

    /**
     * 代理商直客日注册人数维护（存入 Redis）
     */
    public function registerIncrement(int $agentId): int
    {
        $date = date('Y-m-d');
        $key = sprintf(self::DAILY_REGISTER_COUNT_STATISTIC_KEY, $agentId);
        if (!$this->redis->exists($key)) {
            $count = $this->registerStatistic($agentId, $date);
        } else {
            $count = (int)$this->redis->hGet($key, $date);
        }

        $count++;
        $this->redis->hSet($key, $date, $count);

        return $count;
    }


    /**
     * 统计日注册人数（存入 Redis）
     * @param int $agentId cpx_agent 表 ID
     * @param string|null $date 格式：2025-07-08
     * @param Collection<AgentClient>|null $agentClients 代理商直客列表
     * @return int
     */
    public function registerStatistic(int $agentId, ?string $date = null, ?Collection $agentClients = null): int
    {
        $date = $date ?? date('Y-m-d');
        $key = sprintf(self::DAILY_REGISTER_COUNT_STATISTIC_KEY, $agentId);

        if ($agentClients) {
            $count = $agentClients
                ->where('created_at', '>=', $date . ' 00:00:00')
                ->where('created_at', '<=', $date . ' 23:59:59')
                ->count();
        } else {
            $count = AgentClient::query()
                ->where('agent_id', $agentId)
                ->whereBetween('created_at', [
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->count();
        }

        $this->redis->hSet($key, $date, $count);

        return $count;
    }

    /**
     * 根据交易用户手续费返佣记录统计用户上级代理商所有直客日交易手续费（存入 Redis）
     * @param UserFeeCommission $userFeeCommission 用户手续费返佣记录
     * @param string|null $date 格式：2025-07-08
     * @return float
     */
    public function transactionFeeStatisticByByUserFeeCommission(UserFeeCommission $userFeeCommission, ?string $date = null): float
    {
        // 上级代理商
        $parentId = $userFeeCommission->parent_id;
        $parent = null;
        if ($parentId) {
            $parent = User::query()->where(User::FIELD_ID, $parentId)->first();
        }
        if (!$parent || !$parent->agent_id) {
            // 没有上级代理商，则不统计
            return 0;
        }

        $date = $date ?? date('Y-m-d');
        $key = sprintf(self::DAILY_TRANSACTION_FEE_STATISTIC_KEY, $parent->agent_id);

        // 如果对应日期不存在，则通过查询 cpx_user_fee_commission 表，统计对应日期交易手续费
        if (!$this->redis->exists($key)) {
            $totalFeeAmount = $this->transactionFeeStatisticByAgent($parent->agent_id, $date);
        } else {
            $totalFeeAmount = (float)$this->redis->hGet($key, $date) + $userFeeCommission->fee_amount;
            $this->redis->hSet($key, $date, $totalFeeAmount);
        }

        return $totalFeeAmount;
    }

    /**
     * 完整统计指定代理商 id 的直客日交易手续费（存入 Redis）
     * @param int $agentId cpx_agent 表 ID
     * @param string|null $date 格式：2025-07-08
     * @param Collection<UserFeeCommission>|null $userFeeCommissions 用户手续费返佣记录列表
     * @return float
     */
    public function transactionFeeStatisticByAgent(int $agentId, ?string $date = null, ?Collection $userFeeCommissions = null): float
    {
        $date = $date ?? date('Y-m-d');
        $key = sprintf(self::DAILY_TRANSACTION_FEE_STATISTIC_KEY, $agentId);
        if ($userFeeCommissions) {
            $feeAmount = $userFeeCommissions
                ->where('created_at', '>=', $date . ' 00:00:00')
                ->where('created_at', '<=', $date . ' 23:59:59')
                ->sum('fee_amount');
        } else {
            $feeAmount = UserFeeCommission::query()
                ->where('agent_id', $agentId)
                ->whereBetween('created_at', [
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->sum('fee_amount');
        }

        $this->redis->hSet($key, $date, $feeAmount);

        return $feeAmount;
    }

    /**
     * 根据代理商佣金收益记录统计代理商所有直客日返佣收益（存入 Redis）
     * @param AgentCommissionIncome $agentCommissionIncome 代理商佣金收益记录
     * @param string|null $date 格式：2025-07-08
     * @return float
     */
    public function commissionIncomeStatisticByAgentCommissionIncome(AgentCommissionIncome $agentCommissionIncome, ?string $date = null): float
    {
        $date = $date ?? date('Y-m-d');
        $key = sprintf(self::DAILY_COMMISSION_INCOME_STATISTIC_KEY, $agentCommissionIncome->agent_id);

        // 如果对应日期不存在，则通过查询 cpx_agent_commission_income 表，统计对应日期返佣收益
        if (!$this->redis->exists($key)) {
            $totalCommissionIncome = $this->commissionIncomeStatisticByAgent($agentCommissionIncome->agent_id, $date);
        } else {
            $totalCommissionIncome = (float)$this->redis->hGet($key, $date) + $agentCommissionIncome->commission_income_amount;
            $this->redis->hSet($key, $date, $totalCommissionIncome);
        }

        return $totalCommissionIncome;
    }

    /**
     * 完整统计指定代理商 id 的直客日返佣收益（存入 Redis）
     * @param int $agentId cpx_agent 表 ID
     * @param string|null $date 格式：2025-07-08
     * @param Collection<AgentCommissionIncome>|null $agentCommissionIncomes 代理商佣金收益记录列表
     * @return float
     */
    public function commissionIncomeStatisticByAgent(int $agentId, ?string $date = null, ?Collection $agentCommissionIncomes = null): float
    {
        $date = $date ?? date('Y-m-d');
        $key = sprintf(self::DAILY_COMMISSION_INCOME_STATISTIC_KEY, $agentId);

        if ($agentCommissionIncomes) {
            $commissionIncomeAmount = $agentCommissionIncomes
                ->where('created_at', '>=', $date . ' 00:00:00')
                ->where('created_at', '<=', $date . ' 23:59:59')
                ->sum('commission_income_amount');
        } else {
            $commissionIncomeAmount = AgentCommissionIncome::query()
                ->where('agent_id', $agentId)
                ->whereBetween('created_at', [
                    $date . ' 00:00:00',
                    $date . ' 23:59:59',
                ])
                ->sum('commission_income_amount');
        }

        $this->redis->hSet($key, $date, $commissionIncomeAmount);

        return $commissionIncomeAmount;
    }
}
