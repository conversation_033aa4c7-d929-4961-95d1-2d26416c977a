<?php

declare(strict_types=1);
/**
 * AgentClientService
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-11
 * Website:xxx
 */

namespace App\Http\Api\Service\Agent;

use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Agent\AgentClientRequest;
use App\Http\Common\ResultCode;
use App\Model\Agent\AgentClient;
use App\Model\User\UserFeeCommission;
use App\QueryBuilder\QueryBuilder;

class AgentClientService extends BaseService
{
    public function list(AgentClientRequest $request)
    {
        // 交易时间过滤条件特殊处理
        $trade_time = null;
        if ($request->input('trade_time')) {
            $trade_time = explode(',', $request->input('trade_time'));
        }
        $query = AgentClient::query()->with(['user.currentVipLevel.vipLevel', 'user.kycVerification']);
        return QueryBuilder::for($query, $request)
            ->filters([
                'user.account',
                'user.username',
                'user.email',
                'user.phone',
                'user.currentVipLevel.vipLevel.name',
                'remark'
            ])
            ->defaultSort('-created_at')
            ->page(function ($item) use ($trade_time) {
                // TODO: 用户资产

                // TODO: 交易额

                // 手续费返佣记录
                $userFeeCommissions = UserFeeCommission::query()
                    ->select(
                        UserFeeCommission::FIELD_ID,
                        UserFeeCommission::FIELD_USER_ID,
                        UserFeeCommission::FIELD_FEE_AMOUNT,
                        UserFeeCommission::FIELD_COMMISSION_AMOUNT
                    )
                    ->where(UserFeeCommission::FIELD_USER_ID, $item->user_id)
                    ->when($trade_time, function ($query) use ($trade_time) {
                        $query->whereBetween(UserFeeCommission::FIELD_TRADE_TIME, $trade_time);
                    })
                    ->get();

                // 手续费
                $item->total_fee = $userFeeCommissions->sum(UserFeeCommission::FIELD_FEE_AMOUNT);

                // 手续费返佣
                $item->total_commission = $userFeeCommissions->sum(UserFeeCommission::FIELD_COMMISSION_AMOUNT);

                return $item;
            });
    }

    public function updateRemark(int $id, array $data)
    {
        $agentClient = AgentClient::query()->where(AgentClient::FIELD_ID, $id)->first();
        if (!$agentClient) {
            throw new BusinessException(ResultCode::NOT_FOUND, '代理商直客不存在');
        }
        $agentClient->remark = $data['remark'];
        $agentClient->save();

        return $agentClient;
    }
}
