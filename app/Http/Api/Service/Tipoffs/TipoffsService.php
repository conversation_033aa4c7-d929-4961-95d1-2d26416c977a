<?php

declare(strict_types=1);
/**
 * TipoffsService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Service\Tipoffs;

use App\Http\Api\Request\Tipoffs\TipoffsRequest;
use App\Model\Article\Dynamics;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Article\TipoffsRepository;
use Hyperf\Di\Annotation\Inject;

class TipoffsService
{
     #[Inject]
    protected TipoffsRepository $tipoffsRepository;

    public function create(TipoffsRequest $request)
    {
        $params = $request->all();
        $params['user_id'] = $request->userId();
        $params['tipoffs_user_id'] = Dynamics::query()->where('id', $params['dynamics_id'])->value('user_id');
        return $this->tipoffsRepository->create($params);
    }
}
