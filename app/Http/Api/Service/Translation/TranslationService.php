<?php

declare(strict_types=1);
/**
 * TranslationService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Service\Translation;

use App\Http\Api\Request\Translation\TranslationRequest;
use Hyperf\Di\Annotation\Inject;

class TranslationService
{
    #[Inject]
    protected \App\Service\TranslationService $translationService;

    /**
     * 翻译
     * Summary of info
     * @return string
     */
    public function info(TranslationRequest $request)
    {
      return $this->translationService->translate($request->input("content"), $request->input("from"), $request->input("to"));
    }
}
