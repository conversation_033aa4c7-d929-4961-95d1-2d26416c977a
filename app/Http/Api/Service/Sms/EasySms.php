<?php

namespace App\Http\Api\Service\Sms;

use Overtrue\EasySms\Contracts\PhoneNumberInterface;
use Overtrue\EasySms\Contracts\MessageInterface;
use Overtrue\EasySms\Exceptions\InvalidArgumentException;
use Overtrue\EasySms\Exceptions\NoGatewayAvailableException;

class EasySms extends \Overtrue\EasySms\EasySms
{
    /**
     * Constructor.
     */
    public function __construct(array $config)
    {
        parent::__construct($config);
    }

    /**
     * Send a message.
     *
     * @throws NoGatewayAvailableException
     * @throws InvalidArgumentException
     */
    public function send(array|string|PhoneNumberInterface $to, MessageInterface|array $message, array $gateways = []): array
    {
        $to = $this->formatPhoneNumber($to);
        $message = $this->formatMessage($message);
        $gateways = empty($gateways) ? $message->getGateways() : $gateways;

        if (empty($gateways)) {
            $gateways = $this->config->get('default.gateways', []);
        }

        return $this->getMessenger()->send($to, $message, $this->formatGateways($gateways));
    }
}
