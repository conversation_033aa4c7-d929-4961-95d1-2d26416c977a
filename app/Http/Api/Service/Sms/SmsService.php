<?php

declare(strict_types=1);

namespace App\Http\Api\Service\Sms;

use AlibabaCloud\SDK\Dysmsapi\V20180501\Dysmsapi;
use App\Http\Api\Service\Sms\SSY\Request;
use Overtrue\EasySms\PhoneNumber;
use Overtrue\EasySms\Strategies\OrderStrategy;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dysmsapi\V20180501\Models\SendMessageToGlobeRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\User\Enums\Status;
use App\Model\User\User;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Redis\Redis;

class SmsService
{
    protected int $vcode;

    #[Inject]
    protected RequestInterface $request;

    #[Inject]
    protected Redis $redis;

    /**
     * 阿里云短信类型
     * OPT 验证码 MKT 营销短信 NOTIFY通知短信
     * @var string
     */
    protected string $type = 'OPT';
    public function send(string|int $phone, string|int $code, string|int $msg): bool
    {
        try {
            $channel = (int)env('SMS_CHANNEL', 2);
            switch ($channel) {
                case 1:
                    $sms = [
                        'smsbao' => [
                            'user' => env('SMS_BAO_ACCOUNT', ''),
                            'password' => env('SMS_BAO_PASSWORD', ''),
                        ],
                    ];
                    $msg = env('SMS_BAO_SIGN') . $msg;
                    $config = [
                        'timeout' => 5.0,
                        'default' => [
                            'strategy' => OrderStrategy::class,
                            'gateways' => array_keys($sms),
                        ],
                        'gateways' => array_merge([
                            'errorlog' => [
                                'file' => BASE_PATH . '/runtime/logs/sms.log',
                            ],
                        ], $sms),
                    ];

                    $easySms = new EasySms($config);
                    $res = $easySms->send(new PhoneNumber((int)$phone, (string)$code), [
                        'content' => $msg,
                    ]);
                    return true;
                case 2:
                    $msg = env('ALI_SMS_SIGN') . $msg;
                    return $this->sendv2($code . $phone, $msg) ? true : false;
                case 3:
                    // $msg = env('SMS_SSY_SIGN') . $msg;
                    return (bool)$this->sendv3($phone);
            }
            return false;
        } catch (\Throwable $t) {
            throw new BusinessException(ResultCode::FAIL, $t->getMessage());
        }
    }

    /**
     * 闪速码短信发送
     * @param $code
     * @param $phone
     * @param $msg
     */
    public function sendv3($phone)
    {
        try {
            $client = new \App\Http\Api\Service\Sms\SSY\Client();
            $client->setAppId(env('SMS_SSY_APPID')); //开发者ID，在【设置】-【开发设置】中获取
            $client->setSecretKey(env('SMS_SSY_SECRET')); //开发者密钥，在【设置】-【开发设置】中获取

            $request = new Request();
            $request->setMethod('sms.message.send');
            $request->setBizContent([
                'mobile' => [$phone],                     //接受号码
                'template_id' => env('SMS_SSY_VERIFY'),    //模板id
                'type' => 0,                       //验证码类型为0
                'sign' => env('SMS_SSY_SIGN'),             //这里填写短信签名，不需要填写签名id
                'send_time' => '',
                'params' => [
                    'code' => $this->vcode
                ]
            ]);
            return $client->execute($request);
        } catch (\Throwable $t) {
            throw new BusinessException(ResultCode::FAIL, "ssy sms fail:" . $t->getMessage());
        }
    }

    /**
     * 阿里云国际短信发送
     */
    public function sendv2($phone, $msg)
    {
        $config = new Config([
            "accessKeyId" => env('ALI_SMS_KEY'),
            "accessKeySecret" => env('ALI_SMS_SEC')
        ]);
        $config->endpoint = "dysmsapi.ap-southeast-1.aliyuncs.com";
        $client = new Dysmsapi($config);
        $sendMessageToGlobeRequest = new SendMessageToGlobeRequest([
            "to" => $phone,
            "message" =>  $msg,
            "from" => env('ALI_SMS_SIGN'),
            'type' => $this->type
        ]);
        $runtime = new RuntimeOptions([]);
        try {
            $res = $client->sendMessageToGlobeWithOptions($sendMessageToGlobeRequest, $runtime);
            if ($res->statusCode === 200) {
                return true;
            }
            throw new BusinessException(ResultCode::FAIL, "Sms send fail");
        } catch (\Throwable $t) {
            throw new BusinessException(ResultCode::FAIL, "Sms send fail");
        }
    }

    /**
     * 发送验证码（注册、登录、重置密码）
     * @param string|int $phone
     * @param string|int $code
     * @param string $type 验证码类型（register、login、reset_password）
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function sendVerificationCode(string|int $phone, string|int $code, string $type): bool
    {
        $msgType = '短信验证';
        if ($type === 'register' || $type === 'bind_phone') {
            switch ($type) {
                case 'register':
                    $msgType = '账号注册';
                    break;
                case 'bind_phone':
                    $msgType = '手机号绑定';
                    break;
            }
            if (User::query()->where('phone', $phone)->where('phone_country_code', $code)->exists()) {
                throw new BusinessException(ResultCode::FORBIDDEN, "手机号已存在");
            }
        }

        if ($type === 'login' || $type === 'reset_password' || $type === 'set_fund_password') {
            switch ($type) {
                case 'login':
                    $msgType = '账号登录';
                    break;
                case 'reset_password':
                    $msgType = '密码重置';
                    break;
                case 'set_fund_password':
                    $msgType = '资金密码设置';
                    break;
            }
            if (!User::query()->where('phone', $phone)->where('phone_country_code', $code)->where('status', Status::NORMAL)->exists()) {
                throw new BusinessException(ResultCode::FORBIDDEN, "手机号不存在或账号状态异常");
            }
        }

        $ip = $this->getClientIp();
        $len = strlen((string)$phone);
        $k = 'ip:' . $ip;
        $n = (int)$this->redis->get($k);
        if ($len == 15) {
            $vcode = 111111;
        } else {
            if ($n > 4) {
                throw new BusinessException(ResultCode::FORBIDDEN, "手机注册验证码已达上限");
            }
            $vcode = random_int(100000, 999999);
        }
        $this->vcode = $vcode;
        $this->redis->set('sms:' . $type . ':' . $code . $phone, $vcode, 300);
        $str = sprintf('您正在进行%s，验证码是%s，有效期为5分钟，请尽快验证！', $msgType, (string)$vcode);
        // 固定语言
        // $str = sprintf(__('api.vcode',[],'zh_CN'),__('api.register',[],'zh_CN'), (string)$vcode);
        $this->redis->incr($k);
        $this->redis->expire($k, 3600);

        if ($len == 15) return true;
        return $this->send($phone, $code, $str);
    }

    /**
     * 短信通知
     * @param string|int $phone
     * @param string|int $code
     * @param string $msg
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function sendNotificationMsg(string|int $phone, string|int $code, string $msg): bool
    {
        return $this->send($phone, $code, $msg);
    }

    /**
     * 短信验证
     * @param string|int $phone
     * @param string|int $code
     * @param string $vcode
     * @param string $type
     * @param bool $clean
     * @param string $key
     * @return bool
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function validate(string|int $phone, string|int $code, string $vcode, string $type, bool $clean = true, string $key = ''): bool
    {
        $code = str_replace("+", "", strval($code));
        $c = $this->redis->get('sms:' . $type . ':' . trim($key) . trim($code) . $phone);
        if ($c != $vcode) {
            throw new BusinessException(ResultCode::FORBIDDEN, "验证码错误或已失效");
        }
        if ($clean) {
            $this->redis->del('sms:' . $key . $code . $phone);
        }
        return true;
    }

    /**
     * 获取客户端IP
     */
    private function getClientIp(): string
    {
        $ip = $this->request->getServerParams()['remote_addr'] ?? '0.0.0.0';

        if (isset($this->request->getServerParams()['http_x_forwarded_for'])) {
            $forwardedIps = explode(',', $this->request->getServerParams()['http_x_forwarded_for']);
            $ip = trim($forwardedIps[0]);
        }

        return $ip;
    }
}
