<?php

declare(strict_types=1);
/**
 * EmailService.php
 * Author    chenmqq (<EMAIL>)
 * Version   1.0
 * Date      2025/3/25
 * website  algoquant.org
 */

namespace App\Http\Api\Service\Sms;

use AlibabaCloud\SDK\Dm\V20151123\Dm;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dm\V20151123\Models\SingleSendMailRequest;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\User\User;

class EmailService
{
    private Dm $client;
    private string $mailAccount;

    public function __construct()
    {
        // 验证必要的环境变量
        $this->validateEnvVariables();
        try {
            $config = new Config([
                // 您的AccessKey ID
                "accessKeyId" => env('ALIYUN_ACCESS_KEY_ID'),
                // 您的AccessKey Secret
                "accessKeySecret" => env('ALIYUN_ACCESS_KEY_SECRET'),
                // 访问的域名
                "endpoint" => "dm.ap-southeast-1.aliyuncs.com"
            ]);
            $this->client = new Dm($config);
        } catch (\Throwable $e) {
            throw new BusinessException(ResultCode::FAIL, '邮件服务初始化失败：' . $e->getMessage());
        }
    }

    private function validateEnvVariables(): void
    {
        $this->mailAccount = env('ALIYUN_MAIL_ACCOUNT');
        if (empty($this->mailAccount)) {
            throw new BusinessException(ResultCode::FAIL, '发信地址未配置(ALIYUN_MAIL_ACCOUNT)');
        }

        if (empty(env('ALIYUN_ACCESS_KEY_ID'))) {
            throw new BusinessException(ResultCode::FAIL, '阿里云AccessKey未配置(ALIYUN_ACCESS_KEY_ID)');
        }

        if (empty(env('ALIYUN_ACCESS_KEY_SECRET'))) {
            throw new BusinessException(ResultCode::FAIL, '阿里云AccessSecret未配置(ALIYUN_ACCESS_KEY_SECRET)');
        }
    }

    public function sendVerificationCode(string $to, string $code): void
    {
        try {
            $sendMailRequest = new SingleSendMailRequest([
                // 管理控制台中配置的发信地址
                "accountName" => $this->mailAccount,
                // 发信地址
                "addressType" => 1,
                // 目标地址
                "toAddress" => $to,
                // 邮件主题
                "subject" => env('APP_NAME') . ' 验证码',
                // 邮件HTML正文
                "htmlBody" => $this->getEmailTemplate($code),
                // 发信人昵称
                "fromAlias" => env('ALIYUN_MAIL_ALIAS', env('APP_NAME')),
                // 是否需要回信功能
                "replyToAddress" => false,
                // 是否开启追踪功能
                "clickTrace" => "0",
            ]);

            // 复制代码运行请自行打印 API 的返回值
            $result = $this->client->singleSendMail($sendMailRequest);

            if (!isset($result->body->requestId)) {
                throw new BusinessException(ResultCode::FAIL, '发送失败，请稍后重试');
            }
        } catch (\Throwable $e) {
            throw new BusinessException(ResultCode::FAIL, '发送邮件失败：' . $e->getMessage());
        }
    }

    /**
     * 获取邮件模板
     */
    private function getEmailTemplate(string $code): string
    {
        return <<<HTML
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>验证码</title>
            </head>
            <body>
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #333;">验证码</h2>
                    <p style="color: #666; font-size: 16px;">您的验证码是：</p>
                    <div style="background-color: #f5f5f5; padding: 15px; text-align: center; margin: 20px 0;">
                        <span style="font-size: 24px; color: #333; letter-spacing: 5px;">{$code}</span>
                    </div>
                    <p style="color: #999; font-size: 14px;">验证码有效期为10分钟，请勿泄露给他人。</p>
                    <p style="color: #999; font-size: 14px;">如果这不是您的操作，请忽略此邮件。</p>
                </div>
            </body>
            </html>
            HTML;
    }

    /**
     * 发送邮件通知
     * @param string $to 收件人邮箱
     * @param string $title 邮件主题
     * @param string $content 邮件内容
     * @param User|null $user 用户信息
     * @param string|null $template 邮件模板
     */
    public function sendEmailNotification(string $to, string $title, string $content, ?User $user = null, $template = null): void
    {
        if (!$user) {
            $user = User::query()->select('id', 'username', 'display_name', 'email', 'phone', 'avatar')->where('email', $to)->first();
        }

        if (!$user) {
            throw new BusinessException(ResultCode::FAIL, '用户不存在');
        }

        if (!$template) {
            $template = $this->getEmailNotificationTemplate($user, $title, $content);
        }

        try {
            $sendMailRequest = new SingleSendMailRequest([
                // 管理控制台中配置的发信地址
                "accountName" => $this->mailAccount,
                // 发信地址
                "addressType" => 1,
                // 目标地址
                "toAddress" => $to,
                // 邮件主题
                "subject" => $title,
                // 邮件HTML正文
                "htmlBody" => $template,
                // 发信人昵称
                "fromAlias" => env('ALIYUN_MAIL_ALIAS', env('APP_NAME')),
                // 是否需要回信功能
                "replyToAddress" => false,
                // 是否开启追踪功能
                "clickTrace" => "0",
            ]);

            // 复制代码运行请自行打印 API 的返回值
            $result = $this->client->singleSendMail($sendMailRequest);

            if (!isset($result->body->requestId)) {
                throw new BusinessException(ResultCode::FAIL, '发送失败，请稍后重试');
            }
        } catch (\Throwable $e) {
            throw new BusinessException(ResultCode::FAIL, '发送邮件失败：' . $e->getMessage());
        }
    }

    /**
     * 邮箱通知模板
     */
    private function getEmailNotificationTemplate(User $user, string $title, string $content): string
    {
        return <<<HTML
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>{$title}</title>
            </head>
            <body>
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <p style="color: #666; font-size: 16px;">{$user->username}，您好：</p>
                    <h2 style="color: #333;">{$title}</h2>
                    <p style="color: #666; font-size: 16px;">{$content}</p>
                </div>
            </body>
            </html>
            HTML;
    }
}
