<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 文件上传服务
 */

namespace App\Http\Api\Service\Upload;

use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Common\ResultCode;
use Hyperf\Collection\Collection;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Filesystem\FilesystemFactory;
use Hyperf\HttpMessage\Upload\UploadedFile;
use Hyperf\Validation\ValidatorFactory;
use League\Flysystem\Filesystem;
use Ramsey\Uuid\Uuid;

/**
 * 文件上传服务类
 */
class UploadService extends BaseService
{
    #[Inject]
    protected FilesystemFactory $filesystemFactory;

    #[Inject]
    protected ValidatorFactory $validatorFactory;

    /**
     * 默认文件类型限制
     */
    private const DEFAULT_ALLOWED_TYPES = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
        'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
        'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
        'audio' => ['mp3', 'wav', 'flac', 'aac', 'ogg'],
        'archive' => ['zip', 'rar', '7z', 'tar', 'gz'],
    ];

    /**
     * 默认文件大小限制 (KB)
     */
    private const DEFAULT_SIZE_LIMITS = [
        'image' => 10240,    // 10MB
        'document' => 10240,  // 10MB
        'video' => 51200,   // 50MB
        'audio' => 20480,   // 20MB
        'archive' => 20480, // 20MB
        'default' => 2048,  // 2MB
    ];

    /**
     * 上传单个文件
     *
     * @param UploadedFile $file 上传的文件
     * @param array $options 上传配置选项
     * @return array 上传结果
     * @throws BusinessException
     */
    public function uploadSingle(UploadedFile $file, array $options = []): array
    {
        // 验证文件
        $this->validateFile($file, $options);

        // 获取文件系统
        $filesystem = $this->getFilesystem();

        // 生成文件路径和名称
        $pathInfo = $this->generateFilePath($file, $options);

        try {
            // 上传文件
            $content = file_get_contents($file->getRealPath());
            $fullPath = $pathInfo['path'] . '/' . $pathInfo['filename'];

            $filesystem->write($fullPath, $content);

            // 生成访问URL
            $url = $this->generateUrl($fullPath);

            return [
                'path' => $pathInfo['path'],
                'filename' => $pathInfo['filename'],
                'original_name' => $file->getClientFilename(),
                'mime_type' => $file->getClientMediaType(),
                'size' => $file->getSize(),
                'extension' => $file->getExtension(),
                'hash' => md5($content),
                'url' => $url,
                'full_path' => $fullPath,
            ];
        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, trans('attachment.upload_failed') . ': ' . $e->getMessage());
        }
    }

    /**
     * 上传多个文件
     *
     * @param array $files 上传的文件数组
     * @param array $options 上传配置选项
     * @return Collection 上传结果集合
     * @throws BusinessException
     */
    public function uploadMultiple(array $files, array $options = []): Collection
    {
        if (empty($files)) {
            throw new BusinessException(ResultCode::FAIL, '没有文件需要上传');
        }

        $results = new Collection();
        $errors = [];

        foreach ($files as $index => $file) {
            try {
                if (!$file instanceof UploadedFile) {
                    throw new BusinessException(ResultCode::FAIL, "文件索引 {$index} 不是有效的上传文件");
                }

                $result = $this->uploadSingle($file, $options);
                $result['index'] = $index;
                $results->push($result);
            } catch (\Exception $e) {
                $errors[] = [
                    'index' => $index,
                    'filename' => $file->getClientFilename() ?? 'unknown',
                    'error' => $e->getMessage(),
                ];
            }
        }

        // 如果有错误且没有成功上传的文件，抛出异常
        if (!empty($errors) && $results->isEmpty()) {
            throw new BusinessException(ResultCode::FAIL, '所有文件上传失败', $errors);
        }

        // 如果部分文件上传失败，在结果中包含错误信息
        if (!empty($errors)) {
            $results->put('errors', $errors);
        }

        return $results;
    }

    /**
     * 删除文件
     *
     * @param string $filePath 文件相对路径（如："2024-01-15/filename.jpg"，不包含存储根目录或存储桶前缀）
     * @return bool 删除结果
     */
    public function deleteFile(string $filePath): bool
    {
        try {
            $filesystem = $this->getFilesystem();

            if ($filesystem->fileExists($filePath)) {
                $filesystem->delete($filePath);
                return !$filesystem->fileExists($filePath);
            }

            return true; // 文件不存在，视为删除成功
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param string $filePath 文件相对路径（如："2024-01-15/filename.jpg"，不包含存储根目录或存储桶前缀）
     * @return bool 是否存在
     */
    public function fileExists(string $filePath): bool
    {
        try {
            $filesystem = $this->getFilesystem();
            return $filesystem->fileExists($filePath);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取文件内容
     *
     * @param string $filePath 文件相对路径（如："2024-01-15/filename.jpg"，不包含存储根目录或存储桶前缀）
     * @return string 文件内容
     * @throws BusinessException
     */
    public function getFileContent(string $filePath): string
    {
        try {
            $filesystem = $this->getFilesystem();

            if (!$filesystem->fileExists($filePath)) {
                throw new BusinessException(ResultCode::NOT_FOUND, '文件不存在');
            }

            return $filesystem->read($filePath);
        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '读取文件失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证上传文件
     *
     * @param UploadedFile $file 上传的文件
     * @param array $options 验证选项
     * @throws BusinessException
     */
    private function validateFile(UploadedFile $file, array $options = []): void
    {
        // 检查文件是否有效
        if (!$file->isValid()) {
            throw new BusinessException(ResultCode::FAIL, $this->getUploadErrorMessage($file->getError()));
        }

        // 构建验证规则
        $rules = $this->buildValidationRules($options);

        // 执行验证
        $validator = $this->validatorFactory->make(
            ['file' => $file],
            ['file' => $rules],
            [],
            ['file' => '文件']
        );

        if ($validator->fails()) {
            throw new BusinessException(ResultCode::UNPROCESSABLE_ENTITY, $validator->errors()->first());
        }
    }

    /**
     * 构建文件验证规则
     *
     * @param array $options 验证选项
     * @return array 验证规则
     */
    private function buildValidationRules(array $options = []): array
    {
        $rules = ['file'];

        // 处理文件类型限制
        if (isset($options['allowed_types'])) {
            $allowedTypes = is_array($options['allowed_types'])
                ? $options['allowed_types']
                : explode(',', $options['allowed_types']);
            $rules[] = 'extensions:' . implode(',', $allowedTypes);
        } elseif (isset($options['file_category'])) {
            // 使用预定义的文件类别
            $category = $options['file_category'];
            if (isset(self::DEFAULT_ALLOWED_TYPES[$category])) {
                $rules[] = 'extensions:' . implode(',', self::DEFAULT_ALLOWED_TYPES[$category]);
            }
        }

        // 处理文件大小限制
        if (isset($options['max_size'])) {
            $maxSize = $this->parseSizeToKb($options['max_size']);
            $rules[] = 'max:' . $maxSize;
        } elseif (isset($options['file_category'])) {
            // 使用预定义的大小限制
            $category = $options['file_category'];
            $maxSize = self::DEFAULT_SIZE_LIMITS[$category] ?? self::DEFAULT_SIZE_LIMITS['default'];
            $rules[] = 'max:' . $maxSize;
        } else {
            // 使用默认大小限制
            $rules[] = 'max:' . self::DEFAULT_SIZE_LIMITS['default'];
        }

        // 处理最小文件大小
        if (isset($options['min_size'])) {
            $minSize = $this->parseSizeToKb($options['min_size']);
            $rules[] = 'min:' . $minSize;
        }

        // 图片特殊处理
        if (isset($options['file_category']) && $options['file_category'] === 'image') {
            $rules[] = 'image';

            // 图片尺寸限制
            if (isset($options['dimensions'])) {
                $rules[] = $options['dimensions'];
            }
        }

        return $rules;
    }

    /**
     * 解析文件大小到KB
     *
     * @param string|int $size 文件大小（支持 1MB, 1024KB, 1024 等格式）
     * @return int KB大小
     */
    private function parseSizeToKb(string|int $size): int
    {
        if (is_int($size)) {
            return $size;
        }

        $size = strtolower(trim($size));
        $value = (float) $size;

        if (str_contains($size, 'mb')) {
            return (int) ($value * 1024);
        } elseif (str_contains($size, 'gb')) {
            return (int) ($value * 1024 * 1024);
        } elseif (str_contains($size, 'kb')) {
            return (int) $value;
        } else {
            // 默认按KB处理
            return (int) $value;
        }
    }

    /**
     * 生成文件路径和名称
     *
     * @param UploadedFile $file 上传文件
     * @param array $options 选项
     * @return array 路径信息
     */
    private function generateFilePath(UploadedFile $file, array $options = []): array
    {
        // 生成路径
        $path = $options['path'] ?? date('Y-m-d');

        // 生成文件名
        if (isset($options['filename'])) {
            $filename = $options['filename'];
        } else {
            $extension = $file->getExtension();
            if (isset($options['keep_original_name']) && $options['keep_original_name']) {
                $originalName = pathinfo($file->getClientFilename(), PATHINFO_FILENAME);
                $filename = $originalName . '_' . time() . '.' . $extension;
            } else {
                $filename = Uuid::uuid4()->toString() . '.' . $extension;
            }
        }

        return [
            'path' => $path,
            'filename' => $filename,
        ];
    }

    /**
     * 生成文件访问URL
     *
     * @param string $filePath 文件路径（相对路径，如："2024-01-15/filename.jpg"）
     * @return string URL
     * @throws BusinessException
     */
    private function generateUrl(string $filePath): string
    {
        $filesystem = $this->getFilesystem();
        $storage = config('file.default', 'local');

        try {
            // 尝试使用文件系统的publicUrl方法（云存储通常支持）
            return $filesystem->publicUrl($filePath);
        } catch (\Exception $e) {
            // 云存储失败时的处理
            if ($storage !== 'local') {
                throw new BusinessException(
                    ResultCode::FAIL,
                    "存储 {$storage} 不支持URL生成，请检查配置或适配器实现"
                );
            }

            // Local存储的URL生成
            $config = config('file.storage.local', []);
            if (isset($config['public_url'])) {
                return rtrim($config['public_url'], '/') . '/' . ltrim($filePath, '/');
            }

            // 抛出异常而不是返回相对路径
            throw new BusinessException(
                ResultCode::FAIL,
                'Local存储未配置public_url，无法生成文件访问URL'
            );
        }
    }

    /**
     * 获取文件系统实例
     *
     * @return Filesystem 文件系统实例
     * @throws BusinessException
     */
    private function getFilesystem(): Filesystem
    {
        try {
            $storage = config('file.default', 'local');
            $this->validateStorageConfig($storage);
            return $this->filesystemFactory->get($storage);
        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, "存储初始化失败: " . $e->getMessage());
        }
    }

    /**
     * 验证存储配置
     *
     * @param string $storage 存储类型
     * @throws BusinessException
     */
    private function validateStorageConfig(string $storage): void
    {
        $config = config('file.storage.' . $storage);

        if (!$config) {
            throw new BusinessException(ResultCode::FAIL, "存储配置 {$storage} 不存在");
        }

        // 验证不同存储类型的必需配置
        switch ($storage) {
            case 'local':
                if (!isset($config['root'])) {
                    throw new BusinessException(ResultCode::FAIL, 'Local存储缺少root配置');
                }
                break;

            case 'oss':
                $required = ['accessId', 'accessSecret', 'bucket', 'endpoint'];
                foreach ($required as $key) {
                    if (!isset($config[$key]) || empty($config[$key])) {
                        throw new BusinessException(ResultCode::FAIL, "OSS存储缺少 {$key} 配置");
                    }
                }
                break;

            case 'qiniu':
                $required = ['accessKey', 'secretKey', 'bucket', 'domain'];
                foreach ($required as $key) {
                    if (!isset($config[$key]) || empty($config[$key])) {
                        throw new BusinessException(ResultCode::FAIL, "七牛云存储缺少 {$key} 配置");
                    }
                }
                break;

            case 's3':
                if (!isset($config['credentials']['key']) || !isset($config['credentials']['secret'])) {
                    throw new BusinessException(ResultCode::FAIL, 'S3存储缺少credentials配置');
                }
                if (!isset($config['bucket_name']) || empty($config['bucket_name'])) {
                    throw new BusinessException(ResultCode::FAIL, 'S3存储缺少bucket_name配置');
                }
                break;

            case 'cos':
                $required = ['region', 'app_id', 'secret_id', 'secret_key', 'bucket'];
                foreach ($required as $key) {
                    if (!isset($config[$key]) || empty($config[$key])) {
                        throw new BusinessException(ResultCode::FAIL, "腾讯云COS存储缺少 {$key} 配置");
                    }
                }
                break;
        }
    }

    /**
     * 获取支持的文件类型
     *
     * @param string|null $category 文件类别
     * @return array 支持的文件类型
     */
    public function getSupportedTypes(?string $category = null): array
    {
        if ($category && isset(self::DEFAULT_ALLOWED_TYPES[$category])) {
            return self::DEFAULT_ALLOWED_TYPES[$category];
        }

        return self::DEFAULT_ALLOWED_TYPES;
    }

    /**
     * 获取文件大小限制
     *
     * @param string|null $category 文件类别
     * @return int|array 大小限制（KB）
     */
    public function getSizeLimit(?string $category = null): int|array
    {
        if ($category && isset(self::DEFAULT_SIZE_LIMITS[$category])) {
            return self::DEFAULT_SIZE_LIMITS[$category];
        }

        return self::DEFAULT_SIZE_LIMITS;
    }

    /**
     * 格式化文件大小
     *
     * @param int $bytes 字节数
     * @param int $precision 精度
     * @return string 格式化后的大小
     */
    public function formatFileSize(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes >= 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 获取文件上传错误信息
     *
     * @param int $errorCode 错误代码
     * @return string 错误信息
     */
    private function getUploadErrorMessage(int $errorCode): string
    {
        $errors = [
            UPLOAD_ERR_INI_SIZE => '文件大小超过了 php.ini 中 upload_max_filesize 选项限制的值',
            UPLOAD_ERR_FORM_SIZE => '文件大小超过了 HTML 表单中 MAX_FILE_SIZE 选项指定的值',
            UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
            UPLOAD_ERR_NO_FILE => '没有文件被上传',
            UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
            UPLOAD_ERR_CANT_WRITE => '文件写入失败',
            UPLOAD_ERR_EXTENSION => '文件上传停止由于 PHP 扩展',
        ];

        return $errors[$errorCode] ?? '文件上传失败，未知错误';
    }
}
