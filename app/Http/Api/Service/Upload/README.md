# 文件上传服务 (UploadService)

这是一个基于 Hyperf 框架的文件上传服务，支持多种存储方式，提供了灵活的文件类型和大小限制配置。

## ✨ 主要特性

- 🔄 **多存储支持**: Local、阿里云 OSS、七牛云、腾讯云 COS、AWS S3 等
- 📁 **单文件/多文件上传**: 支持单个或批量文件上传
- 🛡️ **安全验证**: 文件类型、大小、MIME 类型等多重验证
- ⚙️ **灵活配置**: 支持预设配置和调用时自定义配置
- 🎯 **类型分类**: 预定义图片、文档、视频、音频、压缩包等类型
- 🔗 **URL 生成**: 自动生成文件访问 URL
- 📊 **错误处理**: 完善的异常处理和错误信息

## 📋 系统要求

- PHP 8.0+
- Hyperf 3.1+
- League/Flysystem 包

## 🔧 配置说明

### 基础配置文件

文件上传服务完全基于 `config/autoload/file.php` 配置文件。不同的存储方式需要不同的配置参数：

#### Local 存储配置

```php
'local' => [
    'driver' => LocalAdapterFactory::class,
    'root' => BASE_PATH . '/storage/uploads',        // 必需：文件存储根目录
    'public_url' => env('APP_URL') . '/uploads',     // 必需：公共访问URL前缀
],
```

#### 阿里云 OSS 配置

```php
'oss' => [
    'driver' => AliyunOssAdapterFactory::class,
    'accessId' => env('OSS_ACCESS_ID'),              // 必需：AccessKey ID
    'accessSecret' => env('OSS_ACCESS_SECRET'),      // 必需：AccessKey Secret
    'bucket' => env('OSS_BUCKET'),                   // 必需：存储桶名称
    'endpoint' => env('OSS_ENDPOINT'),               // 必需：OSS访问域名
    'domain' => env('OSS_DOMAIN'),                   // 可选：自定义域名
    'schema' => 'https://',                          // 可选：访问协议
    'isCName' => false,                              // 可选：是否使用CNAME
],
```

#### 七牛云配置

```php
'qiniu' => [
    'driver' => QiniuAdapterFactory::class,
    'accessKey' => env('QINIU_ACCESS_KEY'),          // 必需：AccessKey
    'secretKey' => env('QINIU_SECRET_KEY'),          // 必需：SecretKey
    'bucket' => env('QINIU_BUCKET'),                 // 必需：存储空间名称
    'domain' => env('QINIU_DOMAIN'),                 // 必需：访问域名
    'schema' => 'https://',                          // 可选：访问协议
],
```

#### AWS S3 配置

```php
's3' => [
    'driver' => S3AdapterFactory::class,
    'credentials' => [
        'key' => env('S3_KEY'),                      // 必需：Access Key
        'secret' => env('S3_SECRET'),                // 必需：Secret Key
    ],
    'region' => env('S3_REGION'),                    // 必需：区域
    'version' => 'latest',                           // 必需：API版本
    'bucket_name' => env('S3_BUCKET'),               // 必需：存储桶名称
    'endpoint' => env('S3_ENDPOINT'),                // 可选：自定义端点
],
```

#### 腾讯云 COS 配置

```php
'cos' => [
    'driver' => CosAdapterFactory::class,
    'region' => env('COS_REGION'),                   // 必需：地域
    'app_id' => env('COS_APP_ID'),                   // 必需：APPID
    'secret_id' => env('COS_SECRET_ID'),             // 必需：SecretId
    'secret_key' => env('COS_SECRET_KEY'),           // 必需：SecretKey
    'bucket' => env('COS_BUCKET'),                   // 必需：存储桶名称
    'schema' => 'https://',                          // 可选：访问协议
],
```

### 默认存储设置

在配置文件中指定默认使用的存储方式：

```php
return [
    'default' => 'local',  // 可选值：local, oss, qiniu, s3, cos
    'storage' => [
        // ... 存储配置
    ],
];
```

## 🗂️ 文件路径说明

### 路径格式统一性

**重要**: 所有文件操作方法中的 `$filePath` 参数都使用**相对路径**格式，例如：

```php
// ✅ 正确的路径格式
$filePath = "2024-01-15/abc123.jpg";
$filePath = "documents/report.pdf";
$filePath = "user/avatar.png";

// ❌ 错误的路径格式（不要包含存储根目录或存储桶前缀）
$filePath = "/storage/uploads/2024-01-15/abc123.jpg";  // Local - 错误
$filePath = "my-bucket/2024-01-15/abc123.jpg";         // OSS - 错误
$filePath = "https://domain.com/2024-01-15/abc123.jpg"; // URL - 错误
```

### 不同存储方式的路径处理

#### Local 存储

- **配置根目录**: `/project/storage/uploads`
- **传入路径**: `"2024-01-15/filename.jpg"`
- **实际文件路径**: `/project/storage/uploads/2024-01-15/filename.jpg`
- **访问 URL**: `http://domain.com/uploads/2024-01-15/filename.jpg`

#### 云存储 (OSS/S3/七牛/COS 等)

- **存储桶配置**: `my-bucket`
- **传入路径**: `"2024-01-15/filename.jpg"`
- **实际存储路径**: `my-bucket/2024-01-15/filename.jpg`
- **访问 URL**: 由云存储服务自动生成 (如: `https://my-bucket.oss-region.aliyuncs.com/2024-01-15/filename.jpg`)

### 路径处理原理

Hyperf 的 Flysystem 集成会自动处理路径前缀：

1. **LocalFilesystemAdapter**: 使用 `PathPrefixer` 自动添加配置的 `root` 路径
2. **云存储适配器**: 自动处理存储桶名称和对象路径的组合
3. **路径标准化**: 所有路径都会通过 `PathNormalizer` 进行标准化处理

## 📚 使用方法

### 基本用法

```php
use App\Http\Api\Service\Upload\UploadService;

class FileController
{
    #[Inject]
    private UploadService $uploadService;

    public function upload(Request $request): JsonResponse
    {
        $file = $request->file('file');

        // 单文件上传
        $result = $this->uploadService->uploadSingle($file);

        return $this->success($result);
    }
}
```

### 单文件上传

```php
// 基础上传
$result = $this->uploadService->uploadSingle($file);

// 指定文件类型限制
$result = $this->uploadService->uploadSingle($file, [
    'file_category' => 'image',
]);

// 自定义路径和文件名
$result = $this->uploadService->uploadSingle($file, [
    'path' => 'avatars',
    'filename' => 'user_123.jpg',
]);

// 保留原始文件名
$result = $this->uploadService->uploadSingle($file, [
    'keep_original_name' => true,
]);

// 自定义验证规则
$result = $this->uploadService->uploadSingle($file, [
    'allowed_types' => ['jpg', 'png'],
    'max_size' => '5MB',
    'min_size' => '100KB',
]);

// 图片尺寸限制
$result = $this->uploadService->uploadSingle($file, [
    'file_category' => 'image',
    'dimensions' => 'min_width=100,min_height=100,max_width=2000,max_height=2000',
]);
```

### 多文件上传

```php
$files = $request->file('files'); // 数组形式的文件

// 批量上传
$results = $this->uploadService->uploadMultiple($files);

// 带配置的批量上传
$results = $this->uploadService->uploadMultiple($files, [
    'file_category' => 'document',
    'path' => 'documents/' . date('Y-m'),
]);

// 处理结果
foreach ($results as $result) {
    if (isset($result['error'])) {
        // 处理上传失败的文件
        echo "文件 {$result['filename']} 上传失败: {$result['error']}";
    } else {
        // 处理上传成功的文件
        echo "文件上传成功: {$result['url']}";
    }
}

// 检查是否有部分失败
if ($results->has('errors')) {
    $errors = $results->get('errors');
    foreach ($errors as $error) {
        echo "索引 {$error['index']} 文件上传失败: {$error['error']}";
    }
}
```

### 文件操作

```php
// 检查文件是否存在
$exists = $this->uploadService->fileExists('2024-01-15/abc123.jpg');

// 删除文件
$deleted = $this->uploadService->deleteFile('2024-01-15/abc123.jpg');

// 获取文件内容
$content = $this->uploadService->getFileContent('2024-01-15/abc123.jpg');

// 获取支持的文件类型
$types = $this->uploadService->getSupportedTypes();
$imageTypes = $this->uploadService->getSupportedTypes('image');

// 获取大小限制
$limits = $this->uploadService->getSizeLimit();
$imageLimit = $this->uploadService->getSizeLimit('image');

// 格式化文件大小
$formatted = $this->uploadService->formatFileSize(1048576); // "1.00 MB"
```

## ⚙️ 配置选项

### 文件类型配置

```php
$options = [
    'file_category' => 'image',          // 使用预定义类型限制
    'allowed_types' => ['jpg', 'png'],   // 或者自定义允许的扩展名
];
```

**预定义类型**:

- `image`: jpg, jpeg, png, gif, bmp, webp, svg
- `document`: pdf, doc, docx, xls, xlsx, ppt, pptx, txt
- `video`: mp4, avi, mov, wmv, flv, webm
- `audio`: mp3, wav, flac, aac, ogg
- `archive`: zip, rar, 7z, tar, gz

### 文件大小配置

```php
$options = [
    'max_size' => '10MB',               // 最大文件大小
    'min_size' => '100KB',              // 最小文件大小
];
```

**默认大小限制**:

- `image`: 10MB
- `document`: 10MB
- `video`: 50MB
- `audio`: 20MB
- `archive`: 20MB
- `default`: 2MB

### 路径和文件名配置

```php
$options = [
    'path' => 'custom/path',            // 自定义存储路径
    'filename' => 'custom_name.jpg',    // 自定义文件名
    'keep_original_name' => true,       // 保留原始文件名（会添加时间戳）
];
```

### 图片特殊配置

```php
$options = [
    'file_category' => 'image',
    'dimensions' => 'min_width=100,min_height=100,max_width=2000,max_height=2000',
];
```

## 📤 返回数据格式

### 单文件上传成功返回

```json
{
  "path": "2024-01-15",
  "filename": "abc123-def456-ghi789.jpg",
  "original_name": "photo.jpg",
  "mime_type": "image/jpeg",
  "size": 1048576,
  "extension": "jpg",
  "hash": "5d41402abc4b2a76b9719d911017c592",
  "url": "https://domain.com/uploads/2024-01-15/abc123-def456-ghi789.jpg",
  "full_path": "2024-01-15/abc123-def456-ghi789.jpg"
}
```

### 多文件上传成功返回

```json
{
  "0": {
    "path": "2024-01-15",
    "filename": "file1.jpg",
    "original_name": "photo1.jpg",
    "url": "https://domain.com/uploads/2024-01-15/file1.jpg",
    "index": 0
  },
  "1": {
    "path": "2024-01-15",
    "filename": "file2.jpg",
    "original_name": "photo2.jpg",
    "url": "https://domain.com/uploads/2024-01-15/file2.jpg",
    "index": 1
  },
  "errors": [
    {
      "index": 2,
      "filename": "invalid.txt",
      "error": "文件类型不被允许"
    }
  ]
}
```

## ⚠️ 注意事项

### 存储配置要求

1. **Local 存储**: 必须配置 `root` 和 `public_url`
2. **云存储**: 必须配置相应的认证信息和存储桶
3. **URL 生成**: 云存储依赖适配器的 URL 生成能力，Local 存储依赖配置

### 文件路径处理

1. **统一使用相对路径**: 所有文件操作都使用相对于存储根的路径
2. **自动路径处理**: Flysystem 会自动处理存储前缀和路径标准化
3. **跨存储一致性**: 相同的路径格式在不同存储间保持一致

### 安全考虑

1. **文件类型验证**: 基于扩展名和 MIME 类型双重验证
2. **文件大小限制**: 防止过大文件占用存储空间
3. **路径安全**: 自动处理路径遍历攻击防护
4. **随机文件名**: 默认使用 UUID 生成唯一文件名

### 错误处理

1. **配置验证**: 启动时验证存储配置的完整性
2. **上传验证**: 文件上传前进行完整的验证检查
3. **存储检查**: 确保存储服务可用性
4. **URL 生成**: 确保能够正确生成访问 URL

## 🔍 故障排除

### 常见问题

#### 1. Local 存储文件无法访问

**问题**: 文件上传成功但无法通过 URL 访问

**解决方案**:

- 检查 `config/autoload/file.php` 中的 `public_url` 配置
- 确保 Web 服务器能够访问 `root` 配置的目录
- 检查文件权限设置

#### 2. 云存储上传失败

**问题**: 云存储配置后仍然上传失败

**解决方案**:

- 检查认证信息是否正确配置
- 确认存储桶是否存在且有写入权限
- 检查网络连接和防火墙设置
- 查看具体的错误日志

#### 3. 文件类型验证失败

**问题**: 合法文件被拒绝上传

**解决方案**:

- 检查文件扩展名是否在允许列表中
- 确认 MIME 类型是否匹配
- 使用自定义 `allowed_types` 配置

#### 4. 文件大小限制问题

**问题**: 文件大小符合要求但仍被拒绝

**解决方案**:

- 检查 PHP 的 `upload_max_filesize` 和 `post_max_size` 设置
- 确认 Web 服务器的文件大小限制
- 检查自定义大小限制配置

### 调试建议

1. **启用日志**: 检查 Hyperf 的错误日志
2. **配置验证**: 使用服务的配置验证功能
3. **逐步测试**: 从 Local 存储开始测试，再切换到云存储
4. **权限检查**: 确保所有相关目录和服务有正确的权限设置

## 📝 更新日志

- **v1.0.0**: 初始版本，支持基本的文件上传功能
- **v1.1.0**: 添加多存储支持和配置验证
- **v1.2.0**: 完善路径处理和 URL 生成逻辑
- **v1.3.0**: 增强错误处理和文档完善
