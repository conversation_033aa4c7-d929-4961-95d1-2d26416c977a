<?php

declare(strict_types=1);
/**
 * HotTopicService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-09
 * Website:xxx
 */

namespace App\Http\Api\Service\HotTopic;

use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\HotTopic\HotTopicRequest;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Article\HotTopicRepository;
use Hyperf\Di\Annotation\Inject;

class HotTopicService extends BaseService
{
    #[Inject]
    protected HotTopicRepository $repository;

    public function list(HotTopicRequest $request)
    {
        $page = $request->input('page',1);
        $pageSize = $request->input('page_size',5);
        $params = $request->all();
        return $this->repository->page($params,$page,$pageSize);
    }
}
