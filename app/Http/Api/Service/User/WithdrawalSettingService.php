<?php

declare(strict_types=1);
/**
 * WithdrawalSettingService
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Service\User;

use App\Http\Api\Service\BaseService;
use App\Model\User\UserWithdrawalSetting;

class WithdrawalSettingService extends BaseService
{
    public function show()
    {
        $withdrawalSetting = UserWithdrawalSetting::query()->where(UserWithdrawalSetting::FIELD_USER_ID, $this->userId())->first();

        if (empty($withdrawalSetting)) {
            $withdrawalSetting = new UserWithdrawalSetting();
            $withdrawalSetting->user_id = $this->userId();
            $withdrawalSetting->small_withdrawal_enabled = 0;
            $withdrawalSetting->small_withdrawal_limit = null;
            $withdrawalSetting->withdrawal_whitelist_enabled = 0;
            $withdrawalSetting->withdrawal_whitelist = null;
            $withdrawalSetting->withdrawal_revoke_enabled = 0;
            $withdrawalSetting->preferred_networks = null;
            $withdrawalSetting->save();
        }

        return $withdrawalSetting;
    }

    public function update(array $data)
    {
        $withdrawalSetting = UserWithdrawalSetting::query()->where(UserWithdrawalSetting::FIELD_USER_ID, $this->userId())->first();
        $withdrawalSetting->fill($data);
        $withdrawalSetting->save();
        return $withdrawalSetting;
    }
}
