<?php

declare(strict_types=1);
/**
 * 策略平台API
 * PassKey服务类
 *
 * 实现WebAuthn通行密钥（Passkey）的完整功能，包括：
 * - 用户注册通行密钥的选项生成
 * - 用户登录时的认证选项生成
 * - 凭证的存储和检索
 * - 挑战码的验证和管理
 * - 支持生物识别、安全密钥等多种认证方式
 *
 * WebAuthn是W3C标准，提供强认证机制，替代传统密码认证
 * @see https://webauthn.guide/
 */

namespace App\Http\Api\Service\User;

use App\Http\Api\Service\BaseService;
use App\Model\User\User;
use App\Model\User\UserPasskey;
use App\Exception\BusinessException;
use App\Http\Api\Event\UserLoginEvent;
use App\Http\Common\ResultCode;
use Cose\Algorithms;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Redis\Redis;
use Hyperf\Di\Annotation\Inject;
use Psr\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Uid\Uuid;
use Webauthn\AttestationStatement\AttestationStatementSupportManager;
use Webauthn\AuthenticatorAttestationResponse;
use Webauthn\AuthenticatorAssertionResponse;
use Webauthn\AuthenticatorSelectionCriteria;
use Webauthn\Denormalizer\WebauthnSerializerFactory;
use Webauthn\PublicKeyCredential;
use Webauthn\PublicKeyCredentialCreationOptions;
use Webauthn\PublicKeyCredentialDescriptor;
use Webauthn\PublicKeyCredentialParameters;
use Webauthn\PublicKeyCredentialRequestOptions;
use Webauthn\PublicKeyCredentialRpEntity;
use Webauthn\PublicKeyCredentialSource;
use Webauthn\PublicKeyCredentialUserEntity;
use Webauthn\TrustPath\EmptyTrustPath;

/**
 * WebAuthn通行密钥服务类
 *
 * 提供完整的WebAuthn协议实现，支持：
 * - 通行密钥注册流程
 * - 通行密钥认证流程
 * - 凭证管理和存储
 * - 安全挑战码验证
 */
class PassKeyService extends BaseService
{
    /**
     * Redis实例，用于存储临时挑战码和会话数据
     */
    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected UserService $userService;

    #[Inject]
    private readonly EventDispatcherInterface $dispatcher;

    /**
     * WebAuthn序列化器，用于处理客户端响应数据
     */
    private SerializerInterface $serializer;

    public function __construct()
    {
        // 初始化WebAuthn序列化器
        $this->serializer = (new WebauthnSerializerFactory(
            AttestationStatementSupportManager::create()
        ))->create();
    }

    /**
     * 生成用户注册通行密钥的选项
     *
     * 此方法创建WebAuthn注册流程所需的配置选项，包括：
     * - 依赖方（RP）信息：网站标识和名称
     * - 用户信息：用户ID、用户名、显示名
     * - 挑战码：防重放攻击的随机数据
     * - 支持的算法：ES256（椭圆曲线）和RS256（RSA）
     * - 认证器选择标准：要求用户验证和常驻密钥
     *
     * @param User $user 要注册通行密钥的用户对象
     * @return array 包含注册选项的数组，可直接返回给前端
     * @throws BusinessException 当配置错误或Redis操作失败时抛出异常
     */
    public function generateRegistrationOptions(User $user): array
    {
        // 创建依赖方实体，代表当前网站/应用
        // rp_name: 网站显示名称，rp_id: 网站域名标识
        $rpEntity = new PublicKeyCredentialRpEntity(
            config('webauthn.rp_name'),
            config('webauthn.rp_id')
        );

        // 创建用户实体，包含用户的唯一标识信息
        // 第一个参数：用户名（用于显示）
        // 第二个参数：用户ID（必须是字符串，WebAuthn规范要求）
        // 第三个参数：显示名称（用户友好的名称）
        $userEntity = new PublicKeyCredentialUserEntity(
            $user->username,
            (string)$user->id,
            $user->username
        );

        // 生成加密安全的随机挑战码，防止重放攻击
        // 挑战码长度由配置文件定义，通常为32字节
        $challenge = random_bytes(config('webauthn.challenge_length'));

        // 将挑战码存储到Redis中，设置过期时间
        // 键名包含用户ID以便后续验证时查找
        // 值为base64编码的挑战码，便于传输
        // 过期时间为配置的超时时间（毫秒转秒）
        $this->redis->set(
            'passkey:challenge:reg:' . $user->id,
            base64_encode($challenge),
            config('webauthn.timeout') / 1000
        );

        // 定义支持的公钥算法参数
        // ES256: 椭圆曲线数字签名算法，基于P-256曲线和SHA-256
        // RS256: RSA签名算法，使用SHA-256哈希
        // 这两种算法覆盖了大部分现代认证器的支持范围
        $pubKeyCredParams = [
            PublicKeyCredentialParameters::create(
                PublicKeyCredentialDescriptor::CREDENTIAL_TYPE_PUBLIC_KEY,
                Algorithms::COSE_ALGORITHM_ES256  // -7: ECDSA P-256 with SHA-256
            ),
            PublicKeyCredentialParameters::create(
                PublicKeyCredentialDescriptor::CREDENTIAL_TYPE_PUBLIC_KEY,
                Algorithms::COSE_ALGORITHM_RS256  // -257: RSASSA-PKCS1-v1_5 with SHA-256
            ),
        ];

        // 获取用户已有的凭证，避免重复注册
        $existingCredentials = $this->getUserCredentials($user->id);

        // 配置认证器选择标准，优化移动端体验
        // authenticatorAttachment: null表示不限制认证器类型（平台或跨平台）
        // userVerification: 要求用户验证（生物识别、PIN等）
        // residentKey: 要求常驻密钥（密钥存储在认证器中，支持无用户名登录）
        $authenticatorSelection = new AuthenticatorSelectionCriteria(
            null, // 不限制认证器附着方式（platform/cross-platform）
            AuthenticatorSelectionCriteria::USER_VERIFICATION_REQUIREMENT_REQUIRED,
            AuthenticatorSelectionCriteria::RESIDENT_KEY_REQUIREMENT_REQUIRED
        );

        // 创建完整的注册选项对象
        // 包含所有必要的参数：RP信息、用户信息、挑战码、算法、认证器要求等
        $options = new PublicKeyCredentialCreationOptions(
            $rpEntity,                    // 依赖方信息
            $userEntity,                  // 用户信息
            $challenge,                   // 挑战码
            $pubKeyCredParams,            // 支持的算法
            $authenticatorSelection,      // 认证器选择标准
            null,                        // 认证声明偏好（默认none）
            $existingCredentials,        // 排除已有的凭证，避免重复注册
            config('webauthn.timeout')   // 超时时间（毫秒）
        );

        // 将选项对象序列化为数组格式，便于JSON传输
        return $this->serializeOptions($options);
    }

    /**
     * 生成用户登录认证的选项
     *
     * 此方法创建WebAuthn认证流程所需的配置选项，包括：
     * - 新的挑战码用于此次认证
     * - 允许的凭证列表（如果提供了用户名）
     * - 用户验证要求
     * - 超时设置
     *
     * @param User $user 用户
     * @return array 包含认证选项和挑战码密钥的数组
     * @throws BusinessException 当Redis操作失败时抛出异常
     */
    public function generateLoginOptions(User $user): array
    {
        // 生成新的挑战码，每次认证都使用不同的挑战码
        $challenge = random_bytes(config('webauthn.challenge_length'));

        // 获取该用户的所有已注册凭证
        // 这样可以限制认证器只能使用该用户的凭证进行认证
        $allowedCredentials = $this->getUserCredentials($user->id);

        // 创建认证请求选项
        $options = new PublicKeyCredentialRequestOptions(
            $challenge,                   // 挑战码
            config('webauthn.rp_id'),    // 依赖方ID
            $allowedCredentials,          // 允许的凭证列表
            PublicKeyCredentialRequestOptions::USER_VERIFICATION_REQUIREMENT_REQUIRED,
            config('webauthn.timeout')   // 超时时间
        );

        // 生成挑战码的唯一键名，用于后续验证
        // 使用bin2hex将二进制数据转换为十六进制字符串
        $challengeKey = 'login:' . bin2hex($challenge);

        // 存储挑战码到Redis，键名包含随机部分以避免冲突
        $this->redis->set(
            'passkey:challenge:' . $challengeKey,
            base64_encode($challenge),
            config('webauthn.timeout') / 1000
        );

        return [
            'options' => $this->serializeOptions($options),
            'challenge_key' => $challengeKey  // 返回挑战码键名，客户端需要在认证时提供
        ];
    }

    /**
     * 获取指定用户的所有凭证描述符
     *
     * 从数据库中查询用户的所有通行密钥，并转换为WebAuthn标准的凭证描述符格式
     * 这些描述符用于告诉认证器哪些凭证可以用于认证
     *
     * @param int $userId 用户ID
     * @return array PublicKeyCredentialDescriptor对象数组
     */
    private function getUserCredentials(int $userId): array
    {
        // 从数据库查询用户的所有通行密钥记录
        $passkeys = UserPasskey::where('user_id', $userId)->get();
        $descriptors = [];

        // 将每个通行密钥转换为凭证描述符
        foreach ($passkeys as $passkey) {
            $descriptors[] = new PublicKeyCredentialDescriptor(
                PublicKeyCredentialDescriptor::CREDENTIAL_TYPE_PUBLIC_KEY,  // 凭证类型
                base64_decode($passkey->credential_id),                      // 凭证ID（二进制格式）
                []  // 传输方式（空数组表示不限制传输方式）
            );
        }

        return $descriptors;
    }

    /**
     * 根据凭证ID查找对应的凭证源
     *
     * 这是WebAuthn认证流程中的关键方法，用于：
     * - 验证凭证是否存在于系统中
     * - 获取凭证的公钥用于签名验证
     * - 获取计数器用于防重放攻击
     *
     * @param string $publicKeyCredentialId 凭证ID（二进制格式）
     * @return PublicKeyCredentialSource|null 找到的凭证源对象，不存在则返回null
     */
    public function findOneByCredentialId(string $publicKeyCredentialId): ?PublicKeyCredentialSource
    {
        // 将二进制凭证ID转换为base64格式，以便在数据库中查询
        $credentialIdBase64 = base64_encode($publicKeyCredentialId);
        $passkey = UserPasskey::where('credential_id', $credentialIdBase64)->first();

        if (!$passkey) {
            return null;
        }

        // 创建并返回WebAuthn标准的凭证源对象
        // 这个对象包含了验证签名所需的所有信息
        return new PublicKeyCredentialSource(
            $publicKeyCredentialId,                                    // 凭证ID（原始二进制格式）
            PublicKeyCredentialDescriptor::CREDENTIAL_TYPE_PUBLIC_KEY, // 凭证类型
            [],                                                        // 传输方式
            $passkey->attestation_type,                                // 认证声明类型
            new EmptyTrustPath(),                                      // 信任路径（简单实现使用空路径）
            Uuid::v4(),                                                // AAGUID（认证器GUID）
            base64_decode($passkey->public_key),                       // 公钥（二进制格式）
            (string)$passkey->user_id,                                 // 用户句柄
            $passkey->counter                                          // 签名计数器
        );
    }

    /**
     * 保存新的凭证源到数据库
     *
     * 在用户成功注册通行密钥后调用此方法，将凭证信息持久化存储
     * 使用updateOrCreate确保不会重复插入相同的凭证
     *
     * @param PublicKeyCredentialSource $credentialSource 要保存的凭证源对象
     * @param string $deviceType 设备类型描述，用于用户识别不同设备
     */
    public function saveCredentialSource(PublicKeyCredentialSource $credentialSource, string $deviceType = 'Unknown')
    {
        // 使用updateOrCreate方法，基于凭证ID进行唯一性检查
        // 如果凭证已存在则更新，不存在则创建新记录
        /** @var UserPasskey $passKey */
        $passKey = UserPasskey::query()->updateOrCreate(
            ['credential_id' => base64_encode($credentialSource->publicKeyCredentialId)], // 查询条件
            [
                'user_id' => $credentialSource->userHandle,                    // 用户ID
                'public_key' => base64_encode($credentialSource->credentialPublicKey), // 公钥（base64编码）
                'counter' => $credentialSource->counter,                       // 签名计数器
                'attestation_type' => $credentialSource->attestationType,      // 认证声明类型
                'device_info' => $deviceType,                                  // 设备信息
            ]
        );

        return $passKey->except('id', 'user_id', 'credential_id', 'device_info', 'created_at', 'updated_at');
    }

    /**
     * 更新凭证的签名计数器
     *
     * WebAuthn规范要求跟踪签名计数器以防止重放攻击
     * 每次成功认证后，计数器应该递增
     * 如果计数器没有递增或减少，可能表示凭证被克隆
     *
     * @param string $credentialId 凭证ID（二进制格式）
     * @param int $counter 新的计数器值
     * @return void
     */
    public function updateCounter(string $credentialId, int $counter): void
    {
        UserPasskey::where('credential_id', base64_encode($credentialId))
            ->update(['counter' => $counter]);
    }

    /**
     * 验证挑战码的有效性
     *
     * 在认证流程中，客户端会使用挑战码进行签名
     * 服务端需要验证客户端使用的挑战码确实是服务端之前生成的
     * 验证成功后立即删除挑战码，防止重复使用
     *
     * @param string $challengeKey 挑战码的Redis键名
     * @param string $challenge 客户端提供的挑战码（二进制格式）
     * @return bool 验证是否成功
     */
    public function verifyChallenge(string $challengeKey, string $challenge): bool
    {
        // 从Redis获取之前存储的挑战码
        $storedChallenge = $this->redis->get('passkey:challenge:' . $challengeKey);

        if (!$storedChallenge) {
            // 挑战码不存在或已过期
            return false;
        }

        // 使用时间安全的比较函数，防止时序攻击
        $isValid = hash_equals($storedChallenge, base64_encode($challenge));

        // 验证成功后立即删除挑战码，实现一次性使用
        if ($isValid) {
            $this->redis->del('passkey:challenge:' . $challengeKey);
        }

        return $isValid;
    }

    /**
     * 将WebAuthn选项对象序列化为数组格式
     *
     * WebAuthn库的选项对象不能直接JSON序列化
     * 需要手动提取所需字段并转换为适合前端使用的格式
     * 特别注意二进制数据需要base64编码
     *
     * @param object $options WebAuthn选项对象（创建或请求选项）
     * @return array 序列化后的数组，可以直接JSON编码
     */
    private function serializeOptions(object $options): array
    {
        // 处理注册选项对象
        if ($options instanceof PublicKeyCredentialCreationOptions) {
            return [
                'rp' => [
                    'name' => $options->rp->name,    // 依赖方名称
                    'id' => $options->rp->id,        // 依赖方ID
                ],
                'user' => [
                    'id' => base64_encode($options->user->id),           // 用户ID（base64编码）
                    'name' => $options->user->name,                     // 用户名
                    'displayName' => $options->user->displayName,       // 显示名称
                ],
                'challenge' => base64_encode($options->challenge),      // 挑战码（base64编码）
                'pubKeyCredParams' => array_map(function ($param) {
                    return [
                        'type' => $param->type,     // 凭证类型
                        'alg' => $param->alg,       // 算法标识符
                    ];
                }, $options->pubKeyCredParams),
                'timeout' => $options->timeout,                         // 超时时间（毫秒）
                'authenticatorSelection' => $options->authenticatorSelection ? [
                    'authenticatorAttachment' => $options->authenticatorSelection->authenticatorAttachment,
                    'userVerification' => $options->authenticatorSelection->userVerification,
                    'residentKey' => $options->authenticatorSelection->residentKey,
                ] : null,
                'excludeCredentials' => array_map(function ($cred) {
                    return [
                        'type' => $cred->type,
                        'id' => base64_encode($cred->id),
                        'transports' => $cred->transports,
                    ];
                }, $options->excludeCredentials),
            ];
        }

        // 处理认证选项对象
        if ($options instanceof PublicKeyCredentialRequestOptions) {
            return [
                'challenge' => base64_encode($options->challenge),      // 挑战码（base64编码）
                'timeout' => $options->timeout,                         // 超时时间（毫秒）
                'rpId' => $options->rpId,                              // 依赖方ID
                'allowCredentials' => array_map(function ($cred) {
                    return [
                        'type' => $cred->type,                          // 凭证类型
                        'id' => base64_encode($cred->id),               // 凭证ID（base64编码）
                        'transports' => $cred->transports,             // 传输方式
                    ];
                }, $options->allowCredentials),
                'userVerification' => $options->userVerification,       // 用户验证要求
            ];
        }

        return [];
    }

    /**
     * 解析客户端响应数据
     *
     * 使用WebAuthn序列化器将前端发送的JSON数据转换为PublicKeyCredential对象
     * 这个方法封装了复杂的反序列化逻辑
     *
     * @param string $jsonData 客户端发送的JSON数据
     * @return PublicKeyCredential 解析后的凭证对象
     * @throws BusinessException 当解析失败时抛出异常
     */
    private function parseClientResponse(string $jsonData): PublicKeyCredential
    {
        try {
            // 使用WebAuthn库的序列化器解析客户端响应
            return $this->serializer->deserialize(
                $jsonData,
                PublicKeyCredential::class,
                'json'
            );
        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FORBIDDEN, '无法解析客户端响应: ' . $e->getMessage());
        }
    }

    /**
     * 根据用户句柄查找所有凭证
     *
     * 查找指定用户的所有通行密钥凭证
     *
     * @param string $userHandle 用户句柄
     * @return array PublicKeyCredentialSource对象数组
     */
    public function findAllForUserEntity(string $userHandle): array
    {
        $passkeys = UserPasskey::where('user_id', $userHandle)->get();
        $sources = [];

        foreach ($passkeys as $passkey) {
            $sources[] = new PublicKeyCredentialSource(
                base64_decode($passkey->credential_id),
                PublicKeyCredentialDescriptor::CREDENTIAL_TYPE_PUBLIC_KEY,
                [],
                $passkey->attestation_type,
                new EmptyTrustPath(),
                Uuid::v4(),
                base64_decode($passkey->public_key),
                (string)$passkey->user_id,
                $passkey->counter
            );
        }

        return $sources;
    }

    /**
     * 生成通行密钥注册选项
     *
     * 为当前登录用户生成注册新通行密钥所需的选项
     * 此接口供控制器调用，处理所有业务逻辑
     *
     * @return array 包含注册选项的数组
     * @throws BusinessException 当用户未登录或生成选项失败时抛出异常
     */
    public function registerOptions(): array
    {
        // 获取当前登录用户
        $user = $this->getUser();

        // 调用核心方法生成注册选项
        return $this->generateRegistrationOptions($user);
    }

    /**
     * 验证通行密钥注册结果
     *
     * 验证用户完成通行密钥注册后返回的认证响应
     * 处理请求解析、参数验证、设备识别等所有业务逻辑
     *
     * @param RequestInterface $request HTTP请求对象
     * @return array 验证结果
     * @throws BusinessException 当验证失败时抛出异常
     */
    public function registerVerify(RequestInterface $request)
    {
        $user = $this->getUser();
        if (!$user) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户未登录');
        }

        // 获取请求数据
        $data = $request->all();

        // 验证必要的参数
        if (!isset($data['response'])) {
            throw new BusinessException(ResultCode::FORBIDDEN, '缺少响应数据');
        }

        try {
            // 从Redis获取之前存储的挑战码
            $storedChallenge = $this->redis->get('passkey:challenge:reg:' . $user->id);
            if (!$storedChallenge) {
                throw new BusinessException(ResultCode::FORBIDDEN, '挑战码已过期或不存在');
            }

            // 解析客户端响应
            $publicKeyCredential = $this->parseClientResponse(
                json_encode($data['response'])
            );

            // 确保响应是认证响应类型
            $response = $publicKeyCredential->response;
            if (!$response instanceof AuthenticatorAttestationResponse) {
                throw new BusinessException(ResultCode::FORBIDDEN, '无效的认证响应类型');
            }

            $challenge = base64_decode($storedChallenge);

            // 简单验证挑战码匹配
            $clientDataJSON = json_decode($response->clientDataJSON->rawData, true);
            if (!hash_equals(base64_encode($challenge), $clientDataJSON['challenge'])) {
                throw new BusinessException(ResultCode::FORBIDDEN, '挑战码不匹配');
            }

            // 创建凭证源对象（简化实现）
            $attestationObject = $response->attestationObject;
            $attestedCredentialData = $attestationObject->authData->attestedCredentialData;

            if (!$attestedCredentialData) {
                throw new BusinessException(ResultCode::FORBIDDEN, '没有找到认证凭证数据');
            }

            $publicKeyCredentialSource = new PublicKeyCredentialSource(
                $attestedCredentialData->credentialId,
                PublicKeyCredentialDescriptor::CREDENTIAL_TYPE_PUBLIC_KEY,
                [],
                $attestationObject->attStmt->type,
                new EmptyTrustPath(),
                $attestedCredentialData->aaguid,
                $attestedCredentialData->credentialPublicKey ?? '',
                (string)$user->id,
                $attestationObject->authData->signCount
            );

            // 获取设备信息（从x-device-info）
            $deviceInfo = $request->getHeaderLine('x-device-info');

            // 保存凭证到数据库
            $passKey = $this->saveCredentialSource($publicKeyCredentialSource, $deviceInfo);

            // 清除已使用的挑战码
            $this->redis->del('passkey:challenge:reg:' . $user->id);

            return $passKey;
        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FORBIDDEN, '通行密钥注册失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成通行密钥登录选项
     *
     * 为用户登录生成认证选项
     * 支持指定用户名的登录和无用户名的通用登录
     *
     * @param RequestInterface $request HTTP请求对象
     * @return array 包含认证选项和挑战码密钥的数组
     */
    public function loginOptions(RequestInterface $request): array
    {
        $username = $request->input('username');
        $user = User::query()->where('username', $username)->orWhere('email', $username)->orWhere('phone', $username)->first();

        if (!$user) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户不存在');
        }

        if ($user->status == \App\Model\User\Enums\Status::DISABLED) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户已禁用');
        }

        if ($user->status == \App\Model\User\Enums\Status::DELETED) {
            throw new BusinessException(ResultCode::FORBIDDEN, '用户已注销');
        }

        return $this->generateLoginOptions($user);
    }

    /**
     * 验证通行密钥登录
     *
     * 验证用户的通行密钥认证响应
     * 处理请求解析、参数验证等所有业务逻辑
     *
     * @param RequestInterface $request HTTP请求对象
     * @return array 验证结果和用户信息
     * @throws BusinessException 当验证失败时抛出异常
     */
    public function loginVerify(RequestInterface $request): array
    {
        // 获取请求数据
        $data = $request->all();

        // 验证必要的参数
        if (!isset($data['response']) || !isset($data['challenge_key'])) {
            throw new BusinessException(ResultCode::FORBIDDEN, '缺少必要的参数');
        }

        try {
            // 验证挑战码
            $challengeKey = $data['challenge_key'];
            $storedChallenge = $this->redis->get('passkey:challenge:' . $challengeKey);
            if (!$storedChallenge) {
                throw new BusinessException(ResultCode::FORBIDDEN, '挑战码已过期或不存在');
            }

            // 解析客户端响应
            $publicKeyCredential = $this->parseClientResponse(
                json_encode($data['response'])
            );

            // 确保响应是断言响应类型
            $response = $publicKeyCredential->response;
            if (!$response instanceof AuthenticatorAssertionResponse) {
                throw new BusinessException(ResultCode::FORBIDDEN, '无效的认证响应类型');
            }

            // 根据凭证ID查找用户和凭证信息
            $credentialSource = $this->findOneByCredentialId($publicKeyCredential->rawId);
            if (!$credentialSource) {
                throw new BusinessException(ResultCode::FORBIDDEN, '凭证不存在');
            }

            // 获取用户信息
            $user = User::find($credentialSource->userHandle);
            if (!$user) {
                throw new BusinessException(ResultCode::FORBIDDEN, '用户不存在');
            }

            if ($user->status == \App\Model\User\Enums\Status::DISABLED) {
                throw new BusinessException(ResultCode::FORBIDDEN, '用户已禁用');
            }

            if ($user->status == \App\Model\User\Enums\Status::DELETED) {
                throw new BusinessException(ResultCode::FORBIDDEN, '用户已注销');
            }

            // 简化验证：检查挑战码匹配
            $clientDataJSON = json_decode($response->clientDataJSON->rawData, true);
            $challenge = base64_decode($storedChallenge);

            if (!hash_equals(base64_encode($challenge), $clientDataJSON['challenge'])) {
                throw new BusinessException(ResultCode::FORBIDDEN, '挑战码不匹配');
            }

            // 验证签名（简化实现）
            // 在生产环境中应该使用完整的WebAuthn验证器
            $authenticatorData = $response->authenticatorData;

            // 检查计数器（防重放攻击）
            $newCounter = $authenticatorData->signCount;
            if ($newCounter !== 0 && $newCounter <= $credentialSource->counter) {
                throw new BusinessException(ResultCode::FORBIDDEN, '签名计数器异常，可能存在重放攻击');
            }

            // 更新计数器
            $this->updateCounter($credentialSource->publicKeyCredentialId, $newCounter);

            // 清除已使用的挑战码
            $this->redis->del('passkey:challenge:' . $challengeKey);

            // 生成 token
            $jwt = $this->userService->getJwt();

            $access_token = $jwt->builderAccessToken((string) $user->id)->toString();

            $this->userService->setUserCache($user, $access_token);

            // 记录登录设备信息、登录日志
            $this->dispatcher->dispatch(new UserLoginEvent($user, $access_token, $request));

            return [
                'access_token' => $access_token,
                'refresh_token' => $jwt->builderRefreshToken((string) $user->id)->toString(),
                'expire_at' => time() + (int) $jwt->getConfig('ttl', 0),
                'user' => $user,
            ];
        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FORBIDDEN, '登录验证失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除通行密钥
     *
     * 删除用户指定的通行密钥凭证
     * 支持按凭证ID删除或删除用户所有凭证
     *
     * @param RequestInterface $request HTTP请求对象
     * @throws BusinessException 当删除失败时抛出异常
     */
    public function delete(RequestInterface $request): void
    {
        $user = $this->getUser();
        $data = $request->all();

        try {
            if (isset($data['credential_id'])) {
                // 删除指定的凭证
                $credentialId = $data['credential_id'];
                $deleted = UserPasskey::where('user_id', $user->id)
                    ->where('credential_id', $credentialId)
                    ->delete();

                if ($deleted === 0) {
                    throw new BusinessException(ResultCode::FORBIDDEN, '凭证不存在或无权删除');
                }

                return;
            } else {
                // 删除用户所有凭证
                $deleted = UserPasskey::where('user_id', $user->id)->delete();

                return;
            }
        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FORBIDDEN, '删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户的通行密钥列表
     *
     * 返回当前用户的所有通行密钥信息
     * 用于管理界面显示
     *
     * @return array 通行密钥列表
     */
    public function getUserPasskeys(): array
    {
        $user = $this->getUser();

        $passkeys = UserPasskey::query()
            ->select('id', 'credential_id', 'device_info', 'created_at', 'updated_at')
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return $passkeys;
    }
}
