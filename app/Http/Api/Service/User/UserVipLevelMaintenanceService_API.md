# UserVipLevelMaintenanceService API 调用文档

## 概述

`UserVipLevelMaintenanceService` 是用户 VIP 等级维护服务，负责根据用户统计数据自动维护用户的 VIP 等级，支持升级、降级和统计数据更新。

## 核心业务逻辑

### VIP 等级计算规则

- **条件判断**: 满足任意一个条件即可达到该等级（OR 逻辑）
- **等级匹配**: 从高到低检查，返回用户能达到的最高等级
- **支持升降级**: 根据计算结果自动升级或降级
- **历史记录**: 保留完整的等级变更历史，通过 `is_active` 字段管理

### 统计数据要求

用户统计数据包含以下字段：

```php
$userStats = [
    'spot_trading_volume' => float,      // 现货交易量
    'futures_trading_volume' => float,   // 合约交易量
    'total_asset' => float,              // 总资产
    'specific_asset_amount' => float,    // 特定币种资产（如BTC）
];
```

## 公共方法

### 1. maintainUserVipLevel()

**功能**: 根据用户统计数据维护单个用户的 VIP 等级

**方法签名**:

```php
public function maintainUserVipLevel(int $userId, array $userStats): array
```

**参数说明**:

- `$userId` (int): 用户 ID
- `$userStats` (array): 用户统计数据

**参数示例**:

```php
$userId = 12345;
$userStats = [
    'spot_trading_volume' => 100000.50,      // 现货交易量：10万USDT
    'futures_trading_volume' => 50000.25,    // 合约交易量：5万USDT
    'total_asset' => 15000.75,               // 总资产：1.5万USDT
    'specific_asset_amount' => 1.5,          // BTC资产：1.5个
];
```

**返回值结构**:

```php
// 升级场景
[
    'upgraded' => true,
    'downgraded' => false,
    'message' => 'VIP等级升级成功',
    'from_level' => 1,                    // 原等级
    'to_level' => 2,                      // 新等级
    'from_vip_level_name' => 'VIP1',      // 原等级名称
    'to_vip_level_name' => 'VIP2',        // 新等级名称
    'user_vip_level_id' => 123            // 新的用户VIP等级记录ID
]

// 降级场景
[
    'upgraded' => false,
    'downgraded' => true,
    'message' => 'VIP等级降级成功',
    'from_level' => 3,
    'to_level' => 1,
    'from_vip_level_name' => 'VIP3',
    'to_vip_level_name' => 'VIP1',
    'user_vip_level_id' => 124
]

// 等级维持场景
[
    'upgraded' => false,
    'downgraded' => false,
    'message' => 'VIP等级统计数据更新成功',
    'current_level' => 2,
    'vip_level_name' => 'VIP2'
]

// 首次设置场景
[
    'upgraded' => true,
    'downgraded' => false,
    'message' => 'VIP等级设置成功',
    'from_level' => null,                 // 首次设置无原等级
    'to_level' => 1,
    'vip_level_name' => 'VIP1',
    'user_vip_level_id' => 125
]
```

**调用示例**:

```php
use App\Http\Api\Service\User\UserVipLevelMaintenanceService;
use Hyperf\Di\Annotation\Inject;

class ExampleController
{
    #[Inject]
    private UserVipLevelMaintenanceService $vipLevelService;

    public function updateUserVipLevel()
    {
        $userId = 12345;
        $userStats = [
            'spot_trading_volume' => 100000.50,
            'futures_trading_volume' => 50000.25,
            'total_asset' => 15000.75,
            'specific_asset_amount' => 1.5,
        ];

        try {
            $result = $this->vipLevelService->maintainUserVipLevel($userId, $userStats);

            if ($result['upgraded']) {
                // 处理升级逻辑
                $this->handleVipUpgrade($userId, $result);
            } elseif ($result['downgraded']) {
                // 处理降级逻辑
                $this->handleVipDowngrade($userId, $result);
            } else {
                // 处理数据更新逻辑
                $this->handleVipDataUpdate($userId, $result);
            }

            return $result;
        } catch (\Exception $e) {
            // 错误处理
            throw new BusinessException(ResultCode::FAIL, '维护VIP等级失败: ' . $e->getMessage());
        }
    }
}
```

### 2. batchMaintainUserVipLevel()

**功能**: 批量维护多个用户的 VIP 等级

**方法签名**:

```php
public function batchMaintainUserVipLevel(array $userStatsArray): array
```

**参数说明**:

- `$userStatsArray` (array): 用户统计数据数组，格式为 `[user_id => user_stats]`

**参数示例**:

```php
$userStatsArray = [
    12345 => [
        'spot_trading_volume' => 100000.50,
        'futures_trading_volume' => 50000.25,
        'total_asset' => 15000.75,
        'specific_asset_amount' => 1.5,
    ],
    12346 => [
        'spot_trading_volume' => 200000.00,
        'futures_trading_volume' => 80000.00,
        'total_asset' => 25000.00,
        'specific_asset_amount' => 2.5,
    ],
    12347 => [
        'spot_trading_volume' => 50000.00,
        'futures_trading_volume' => 20000.00,
        'total_asset' => 8000.00,
        'specific_asset_amount' => 0.8,
    ],
];
```

**返回值结构**:

```php
[
    'total_count' => 3,                   // 总处理数量
    'success_count' => 2,                 // 成功数量
    'fail_count' => 1,                    // 失败数量
    'results' => [
        12345 => [
            'upgraded' => true,
            'downgraded' => false,
            'message' => 'VIP等级升级成功',
            'from_level' => 1,
            'to_level' => 2,
            // ... 其他字段
        ],
        12346 => [
            'upgraded' => false,
            'downgraded' => false,
            'message' => 'VIP等级统计数据更新成功',
            // ... 其他字段
        ],
        12347 => [
            'upgraded' => false,
            'message' => '维护失败：用户不存在',
            'error' => true
        ]
    ]
]
```

**调用示例**:

```php
public function batchUpdateVipLevels()
{
    // 构建批量数据
    $userStatsArray = [];
    $userIds = [12345, 12346, 12347];

    foreach ($userIds as $userId) {
        $userStatsArray[$userId] = [
            'spot_trading_volume' => $this->calculateSpotVolume($userId),
            'futures_trading_volume' => $this->calculateFuturesVolume($userId),
            'total_asset' => $this->calculateTotalAsset($userId),
            'specific_asset_amount' => $this->calculateBtcAmount($userId),
        ];
    }

    $result = $this->vipLevelService->batchMaintainUserVipLevel($userStatsArray);

    // 处理批量结果
    foreach ($result['results'] as $userId => $userResult) {
        if (isset($userResult['error'])) {
            Log::error("用户 {$userId} VIP等级维护失败", $userResult);
        } elseif ($userResult['upgraded']) {
            // 发送升级通知
            $this->sendUpgradeNotification($userId, $userResult);
        } elseif ($userResult['downgraded']) {
            // 发送降级通知
            $this->sendDowngradeNotification($userId, $userResult);
        }
    }

    return $result;
}
```

### 3. getUserVipLevelInfo()

**功能**: 获取用户当前 VIP 等级信息

**方法签名**:

```php
public function getUserVipLevelInfo(int $userId): ?UserVipLevel
```

**参数说明**:

- `$userId` (int): 用户 ID

**返回值**:

- 成功: 返回 `UserVipLevel` 模型对象
- 失败: 返回 `null`（用户没有 VIP 等级）

**调用示例**:

```php
public function getUserVipInfo(int $userId)
{
    $userVipLevel = $this->vipLevelService->getUserVipLevelInfo($userId);

    if (!$userVipLevel) {
        return [
            'has_vip_level' => false,
            'message' => '用户还没有VIP等级'
        ];
    }

    return [
        'has_vip_level' => true,
        'user_id' => $userVipLevel->user_id,
        'vip_level_id' => $userVipLevel->vip_level_id,
        'vip_level_name' => $userVipLevel->vipLevel->name,
        'vip_level' => $userVipLevel->vipLevel->level,
        'current_spot_trading_volume' => $userVipLevel->current_spot_trading_volume,
        'current_futures_trading_volume' => $userVipLevel->current_futures_trading_volume,
        'current_total_asset' => $userVipLevel->current_total_asset,
        'current_specific_asset_amount' => $userVipLevel->current_specific_asset_amount,
        'level_achieved_at' => $userVipLevel->level_achieved_at->toDateTimeString(),
        'is_active' => $userVipLevel->is_active === UserVipLevel::IS_ACTIVE_YES,
        'gift_received' => $userVipLevel->gift_received === UserVipLevel::GIFT_RECEIVED_YES,
        'vip_benefits' => [
            'spot_maker_fee_rate' => $userVipLevel->vipLevel->spot_maker_fee_rate,
            'spot_taker_fee_rate' => $userVipLevel->vipLevel->spot_taker_fee_rate,
            'futures_maker_fee_rate' => $userVipLevel->vipLevel->futures_maker_fee_rate,
            'futures_taker_fee_rate' => $userVipLevel->vipLevel->futures_taker_fee_rate,
            'daily_withdrawal_limit' => $userVipLevel->vipLevel->daily_withdrawal_limit,
            'vip_gift' => $userVipLevel->vipLevel->vip_gift,
            'vip_privileges' => $userVipLevel->vipLevel->vip_privileges,
        ]
    ];
}
```

### 4. markVipGiftReceived()

**功能**: 标记用户已领取 VIP 礼包

**方法签名**:

```php
public function markVipGiftReceived(int $userId): bool
```

**参数说明**:

- `$userId` (int): 用户 ID

**返回值**:

- 成功: 返回 `true`
- 失败: 抛出 `BusinessException` 异常

**异常情况**:

- 用户当前没有 VIP 等级
- 用户已经领取过 VIP 礼包

**调用示例**:

```php
public function receiveVipGift(int $userId)
{
    try {
        $result = $this->vipLevelService->markVipGiftReceived($userId);

        if ($result) {
            // 发放VIP礼包
            $this->distributeVipGift($userId);

            return [
                'success' => true,
                'message' => 'VIP礼包领取成功'
            ];
        }
    } catch (BusinessException $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

private function distributeVipGift(int $userId)
{
    // 获取用户当前VIP等级信息
    $userVipLevel = $this->vipLevelService->getUserVipLevelInfo($userId);
    $vipGift = $userVipLevel->vipLevel->vip_gift;

    if ($vipGift) {
        // 解析礼包内容并发放
        $giftData = json_decode($vipGift, true);
        foreach ($giftData as $item) {
            // 发放具体礼品
            $this->giveGiftItem($userId, $item);
        }
    }
}
```

## 定时任务使用示例

### 每日 VIP 等级维护任务

```php
use Hyperf\Crontab\Annotation\Crontab;

#[Crontab(rule: "0 2 * * *", name: "daily_vip_level_maintenance")]
class DailyVipLevelMaintenanceTask
{
    #[Inject]
    private UserVipLevelMaintenanceService $vipLevelService;

    public function execute()
    {
        // 获取需要维护的用户列表（可以分批处理）
        $batchSize = 1000;
        $offset = 0;

        do {
            $users = User::query()
                ->where('status', Status::NORMAL)
                ->offset($offset)
                ->limit($batchSize)
                ->get(['id']);

            if ($users->isEmpty()) {
                break;
            }

            $userStatsArray = [];

            foreach ($users as $user) {
                $userId = $user->id;

                // 计算用户过去30天的统计数据
                $userStatsArray[$userId] = [
                    'spot_trading_volume' => $this->calculateLast30DaysSpotVolume($userId),
                    'futures_trading_volume' => $this->calculateLast30DaysFuturesVolume($userId),
                    'total_asset' => $this->calculateCurrentTotalAsset($userId),
                    'specific_asset_amount' => $this->calculateCurrentBtcAmount($userId),
                ];
            }

            // 批量维护
            $result = $this->vipLevelService->batchMaintainUserVipLevel($userStatsArray);

            // 记录处理结果
            Log::info('VIP等级维护批次完成', [
                'offset' => $offset,
                'batch_size' => $batchSize,
                'total_count' => $result['total_count'],
                'success_count' => $result['success_count'],
                'fail_count' => $result['fail_count']
            ]);

            $offset += $batchSize;

        } while (true);
    }

    private function calculateLast30DaysSpotVolume(int $userId): float
    {
        // 计算用户过去30天现货交易量
        return SpotOrder::query()
            ->where('user_id', $userId)
            ->where('status', OrderStatus::FILLED)
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->sum('amount');
    }

    private function calculateLast30DaysFuturesVolume(int $userId): float
    {
        // 计算用户过去30天合约交易量
        return FuturesOrder::query()
            ->where('user_id', $userId)
            ->where('status', OrderStatus::FILLED)
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->sum('amount');
    }

    private function calculateCurrentTotalAsset(int $userId): float
    {
        // 计算用户当前总资产
        return UserAsset::query()
            ->where('user_id', $userId)
            ->sum('total_amount');
    }

    private function calculateCurrentBtcAmount(int $userId): float
    {
        // 计算用户当前BTC资产
        return UserAsset::query()
            ->where('user_id', $userId)
            ->where('currency_symbol', 'BTC')
            ->value('total_amount') ?? 0;
    }
}
```

## 事件监听使用示例

### 交易完成后触发 VIP 等级检查

```php
use App\Event\TradeCompletedEvent;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;

#[Listener]
class TradeCompletedListener implements ListenerInterface
{
    #[Inject]
    private UserVipLevelMaintenanceService $vipLevelService;

    public function listen(): array
    {
        return [TradeCompletedEvent::class];
    }

    public function process(object $event)
    {
        if ($event instanceof TradeCompletedEvent) {
            $userId = $event->getUserId();

            // 重新计算用户统计数据
            $userStats = [
                'spot_trading_volume' => $this->calculateUserSpotVolume($userId),
                'futures_trading_volume' => $this->calculateUserFuturesVolume($userId),
                'total_asset' => $this->calculateUserTotalAsset($userId),
                'specific_asset_amount' => $this->calculateUserBtcAmount($userId),
            ];

            try {
                $result = $this->vipLevelService->maintainUserVipLevel($userId, $userStats);

                if ($result['upgraded']) {
                    // 发送升级通知
                    Event::dispatch(new UserVipLevelUpgradedEvent($userId, $result));
                } elseif ($result['downgraded']) {
                    // 发送降级通知
                    Event::dispatch(new UserVipLevelDowngradedEvent($userId, $result));
                }
            } catch (\Exception $e) {
                Log::error('交易完成后维护VIP等级失败', [
                    'user_id' => $userId,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
```

## 注意事项

1. **性能考虑**: 批量维护时建议分批处理，避免一次处理过多用户
2. **事务安全**: 升级和降级操作都在事务中进行，确保数据一致性
3. **异常处理**: 所有公共方法都可能抛出 `BusinessException`，需要适当处理
4. **数据准确性**: 确保传入的统计数据准确，这直接影响等级计算结果
5. **并发安全**: 同一用户的 VIP 等级维护操作应该避免并发执行
6. **历史记录**: 系统会保留完整的等级变更历史，通过 `is_active` 字段管理当前等级
