<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Service\User;

use ApiElf\QueryBuilder\AllowedFilter;
use App\Http\Api\Request\User\DeviceRequest;
use App\Http\Api\Service\BaseService;
use App\Model\User\UserDevice;
use App\QueryBuilder\QueryBuilder;

class DeviceService extends BaseService
{
    /**
     * 设备列表
     */
    public function list(DeviceRequest $request)
    {
        $query = UserDevice::query()
            ->with('lastLoginLog')
            ->where(UserDevice::FIELD_USER_ID, $this->userId());
        return QueryBuilder::for($query, $request)
            ->filters([
                'device_name',
                AllowedFilter::exact('device_type'),
                AllowedFilter::exact('os'),
                AllowedFilter::exact('status'),
                AllowedFilter::exact('is_frequently_used'),
            ])
            ->defaultSort('-last_login_at')
            ->pagex();
    }

    public function getClientIp(): string
    {
        $ip = $this->request->getServerParams()['remote_addr'] ?? '0.0.0.0';

        if (isset($this->request->getServerParams()['http_x_forwarded_for'])) {
            $forwardedIps = explode(',', $this->request->getServerParams()['http_x_forwarded_for']);
            $ip = trim($forwardedIps[0]);
        }

        return $ip;
    }

    public function getDeviceName(): string
    {
        return $this->request->getHeaderLine('x-device-info') ?? 'unknown';
    }

    public function getDeviceType(string $deviceName = ''): string
    {
        $deviceName = !empty($deviceName) ? $deviceName : $this->getDeviceName();
        if (str_contains($deviceName, 'iPhone') || str_contains($deviceName, 'iPad')) {
            return 'ios';
        }

        if (str_contains($deviceName, 'Android') || str_contains($deviceName, 'android')) {
            return 'android';
        }

        if (str_contains($deviceName, 'Windows') || str_contains($deviceName, 'windows')) {
            return 'windows';
        }

        if (str_contains($deviceName, 'Mac') || str_contains($deviceName, 'mac')) {
            return 'mac';
        }

        if (str_contains($deviceName, 'web') || str_contains($deviceName, 'Web')) {
            return 'web';
        }

        return 'unknown';
    }

    public function getBrowser(): string
    {
        return $this->request->getHeaderLine('user-agent') ?? 'unknown';
    }

    public function getOs(): string
    {
        $deviceType = $this->getDeviceType();
        if ($deviceType === 'ios') {
            return 'ios';
        }

        if ($deviceType === 'android') {
            return 'android';
        }

        if ($deviceType === 'windows') {
            return 'windows';
        }

        if ($deviceType === 'mac') {
            return 'macos';
        }

        if ($deviceType === 'web') {
            return 'web';
        }

        return 'unknown';
    }

    public function getAppVersion(): string
    {
        return $this->request->getHeaderLine('x-app-version') ?? 'unknown';
    }

    public function getDeviceId(): string
    {
        // 根据设备名称和设备类型和可能存在的user_agent生成设备ID
        $deviceId = $this->getDeviceName() . '_' . $this->getDeviceType() . '_' . $this->getBrowser();
        return md5($deviceId);
    }

    public function getDeviceInfo(): array
    {
        return [
            'device_id' => $this->getDeviceId(),
            'device_name' => $this->getDeviceName(),
            'device_type' => $this->getDeviceType(),
            'browser' => $this->getBrowser(),
            'os' => $this->getOs()
        ];
    }

    public function getPushToken(): string
    {
        return $this->request->getHeaderLine('x-push-token') ?? null;
    }
}
