<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Service\User;

use App\Http\Api\Request\User\ConcernRequest;
use App\Http\Api\Service\BaseService;
use App\Model\User\User;
use App\QueryBuilder\QueryBuilder;

/**
 * 关注
 * Summary of ConcernService
 */
final class ConcernService extends BaseService
{

    /**
     * 关注
     * Summary of comment
     * @param mixed $request
     */
    public function concern(ConcernRequest $request): bool {
        $params = $request->all();
        $concern = User::where('id',$params['user_id'])->value('concern_uids');
        // 判断是否已关注 $concern 是否包含当前用户
        $concern = $concern ?? [];
        if(!$concern) {
            $concern = [$request->userId()];
        }else if (!in_array($request->userId(),$concern)) {
            $concern = array_merge($concern,[$request->userId()]);
        }
        $concern = json_encode($concern);
        User::where('id',$params['user_id'])->update(['concern_uids'=>$concern]);
        return true;
    }

    /**
     * 取消关注
     * Summary of comment
     * @param mixed $request
     */
    public function unConcern(ConcernRequest $request): bool {
        $params = $request->all();
        $concern = User::where('id',$params['user_id'])->value('concern_uids');
        // 判断是否已关注 $concern 是否包含当前用户
        $concern = $concern ?? [];
        if (in_array($request->userId(), $concern)) {
            $key = array_search($request->userId(), $concern);
            if ($key !== false) {
                unset($concern[$key]);
            }
            User::where('id', $params['user_id'])->update(['concern_uids' => json_encode(array_values($concern))]);
        }
        return true;
    }

    /**
     * 登录用户关注的人
     * Summary of concernUser
     * @param \App\Http\Api\Request\User\ConcernRequest $request
     * @return \Hyperf\Contract\LengthAwarePaginatorInterface
     */
    public function concernUser(ConcernRequest $request) {
        return QueryBuilder::for(User::class, $request)
                ->whereJsonContains('concern_uids',[$request->userId()])
                ->orderByDesc('id')
                ->allowedSorts(['id', 'created_at'])
                ->select([
                    User::FIELD_AVATAR,
                    User::FIELD_DISPLAY_NAME,
                    User::FIELD_EMAIL,
                    User::FIELD_ACCOUNT
                ])
                ->page();
    }
}
