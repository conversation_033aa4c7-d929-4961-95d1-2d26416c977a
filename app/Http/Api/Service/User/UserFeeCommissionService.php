<?php

declare(strict_types=1);
/**
 * UserFeeCommissionService
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-10
 * Website:xxx
 */

namespace App\Http\Api\Service\User;

use App\Http\Api\Service\Agent\AgentCommissionIncomeService;
use App\Http\Api\Service\BaseService;
use App\Model\Agent\AgentClient;
use App\Model\Trade\TradeMarginOrder;
use App\Model\Trade\TradePerpetualOrder;
use App\Model\Trade\TradeSpotOrder;
use App\Model\User\Enums\TradeType;
use App\Model\User\User;
use App\Model\User\UserFeeCommission;
use App\Http\Api\Service\Agent\AgentService;
use App\Model\Agent\Agent;
use Hyperf\Di\Annotation\Inject;

class UserFeeCommissionService extends BaseService
{
    #[Inject]
    protected AgentService $agentService;

    #[Inject]
    protected AgentCommissionIncomeService $agentCommissionIncomeService;

    /**
     * 用户手续费返佣记录写入
     * @param TradeSpotOrder|TradePerpetualOrder|TradeMarginOrder $order
     * @return void
     */
    public function create(TradeSpotOrder|TradePerpetualOrder|TradeMarginOrder $order)
    {
        $user = User::query()->where(User::FIELD_ID, $order->user_id)->first();
        if (!$user) {
            return;
        }

        $rate = [];
        if ($user->agent_id) {
            // 代理商开仓
            $agent = Agent::query()->where(Agent::FIELD_ID, $user->agent_id)->first();
            // TODO: 考虑代理商是否为交易员
            $rate = [
                'spot' => $agent->spot_commission_rate,
                'contract' => $agent->contract_commission_rate,
            ];
        } else if ($user->agent_client_id) {
            // 代理商直客开仓
            // 手续费返佣比例
            $agentClient = AgentClient::query()->where(AgentClient::FIELD_USER_ID, $user->id)->first();
            if (!$agentClient) {
                return;
            }
            // TODO: 考虑代理商是否为交易员
            $rate = [
                'spot' => $agentClient->spot_commission_rate,
                'contract' => $agentClient->contract_commission_rate,
            ];
        } else {
            // 普通用户开仓 后期考虑是否有默认的返佣比例设置
        }

        if (empty($rate)) {
            return;
        }

        switch ($order) {
            case $order instanceof TradeSpotOrder:
                $trade_type = TradeType::SPOT;
                $trade_amount = bcmul((string)$order->price, (string)$order->amount, 8);
                $fee_amount = $order->charge;
                $commission_rate = $rate['spot'];
                $commission_amount = bcmul((string)$fee_amount, (string)($commission_rate / 100), 8);
                break;
            case $order instanceof TradePerpetualOrder:
                $trade_type = TradeType::CONTRACT;
                $trade_amount = $order->margin_amount;
                $fee_amount = $order->actual_fee;
                $commission_rate = $rate['contract'];
                $commission_amount = bcmul((string)$fee_amount, (string)($commission_rate / 100), 8);
                break;
            case $order instanceof TradeMarginOrder:
                $trade_type = TradeType::SPOT_LEVERAGE;
                $trade_amount = $order->used_amount;
                $fee_amount = $order->charge;
                $commission_rate = $rate['spot'];
                $commission_amount = bcmul((string)$fee_amount, (string)($commission_rate / 100), 8);
                break;
        }

        $userFeeCommission = UserFeeCommission::query()->create([
            UserFeeCommission::FIELD_PARENT_ID => $user->parent_id,
            UserFeeCommission::FIELD_USER_ID => $user->id,
            UserFeeCommission::FIELD_AGENT_ID => $agentClient->agent_id,
            UserFeeCommission::FIELD_AGENT_CLIENT_ID => $agentClient->id,
            UserFeeCommission::FIELD_ORDER_ID => $order->id,
            UserFeeCommission::FIELD_TRADE_TYPE => $trade_type,
            UserFeeCommission::FIELD_TRADE_AMOUNT => $trade_amount,
            UserFeeCommission::FIELD_FEE_AMOUNT => $fee_amount,
            UserFeeCommission::FIELD_COMMISSION_RATE => $commission_rate,
            UserFeeCommission::FIELD_COMMISSION_AMOUNT => $commission_amount,
            UserFeeCommission::FIELD_TRADE_TIME => $order->created_at,
        ]);

        // 统计用户上级代理商所有直客日交易手续费
        $this->agentService->transactionFeeStatisticByByUserFeeCommission($userFeeCommission);

        // 上级代理商返佣收益记录
        $this->agentCommissionIncomeService->create($userFeeCommission);

        return $userFeeCommission;
    }
}
