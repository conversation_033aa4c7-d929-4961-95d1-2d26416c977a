<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Service\User;

use App\Http\Api\Request\User\LanguageRequest;
use App\Http\Api\Service\BaseService;
use App\Model\User\User;

/**
 * 语言偏好
 * Summary of LanguageService
 */
final class LanguageService extends BaseService
{

    /**
     * 设置简介
     * Summary of setLanguage
     * @param mixed $request
     */
    public function setLanguage(LanguageRequest $request): bool {
        User::where('id',$request->userId())->update([User::FIELD_LANGUAGE=>$request->input('language')]);
        return true;
    }

}
