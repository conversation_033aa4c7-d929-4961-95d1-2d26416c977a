<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Service\User;

use App\Http\Api\Request\User\IntroductionRequest;
use App\Http\Api\Service\BaseService;
use App\Model\User\User;

/**
 * 简介
 * Summary of IntroductionService
 */
final class IntroductionService extends BaseService
{

    /**
     * 设置简介
     * Summary of setIntroduction
     * @param mixed $request
     */
    public function setIntroduction(IntroductionRequest $request): bool {
        User::where('id',$request->userId())->update([User::FIELD_INTRODCTION=>$request->input('introduction')]);
        return true;
    }

}
