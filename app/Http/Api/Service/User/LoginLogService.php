<?php

declare(strict_types=1);
/**
 * LoginLogService
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-08
 * Website:xxx
 */

namespace App\Http\Api\Service\User;

use ApiElf\QueryBuilder\AllowedFilter;
use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\User\LoginLogRequest;
use App\Model\User\UserLoginLog;
use App\QueryBuilder\QueryBuilder;

class LoginLogService extends BaseService
{
    public function list(LoginLogRequest $request)
    {
        $query = UserLoginLog::query()
            ->where(UserLoginLog::FIELD_USER_ID, $this->userId());
        return QueryBuilder::for($query, $request)
            ->filters([
                'device_name',
                AllowedFilter::exact('device_type'),
                AllowedFilter::exact('os'),
                AllowedFilter::exact('login_result'),
                AllowedFilter::exact('device_id'),
            ])
            ->defaultSort('-created_at')
            ->page();
    }
}
