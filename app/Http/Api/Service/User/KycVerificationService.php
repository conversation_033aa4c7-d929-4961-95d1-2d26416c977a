<?php

declare(strict_types=1);
/**
 * KycVerificationService
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-05
 * Website:xxx
 */

namespace App\Http\Api\Service\User;

use App\Exception\BusinessException;
use App\Http\Api\Service\BaseService;
use App\Http\Common\ResultCode;
use App\Model\User\Enums\KycStatus;
use App\Model\User\UserKycVerification;

class KycVerificationService extends BaseService
{
    public function info(): UserKycVerification
    {
        $user = $this->getUser();
        $kycVerification = UserKycVerification::query()
            ->where('user_id', $user->id)
            ->first();
        return $kycVerification;
    }

    public function submit(array $data)
    {
        $user = $this->getUser();

        // 如果已经提交且状态为审核中，则提示审核中请等待
        $kycVerification = UserKycVerification::query()
            ->where('user_id', $user->id)
            ->where('status', KycStatus::PENDING)
            ->first();
        if ($kycVerification) {
            throw new BusinessException(ResultCode::FORBIDDEN, '审核中请勿重复提交，请耐心等待');
        }

        $data['status'] = KycStatus::PENDING;
        $kycVerification = UserKycVerification::query()
            ->updateOrCreate([
                'user_id' => $user->id,
            ], $data);
        return $kycVerification;
    }
}
