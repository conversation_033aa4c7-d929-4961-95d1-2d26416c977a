<?php

declare(strict_types=1);
/**
 * VIP等级服务
 * Author:chenmaq
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-01-15
 * Website:bbbtrade.net
 */

namespace App\Http\Api\Service\User;

use App\Http\Api\Service\BaseService;
use App\Model\User\VipLevel;
use App\Model\User\Enums\VipLevelStatus;
use App\QueryBuilder\QueryBuilder;
use Hyperf\HttpServer\Contract\RequestInterface;

class VipLevelService extends BaseService
{
    /**
     * 获取VIP等级列表
     */
    public function list(RequestInterface $request): mixed
    {
        // 构建固定查询条件
        $query = VipLevel::query()->where(VipLevel::FIELD_STATUS, VipLevelStatus::ENABLED);

        return QueryBuilder::for($query, $request)
            ->filters(['name', 'level'])
            ->allowedSorts(['id', 'level', 'sort', 'created_at'])
            ->defaultSort('sort')
            ->pagex();
    }
}
