<?php

declare(strict_types=1);
/**
 * NotificationSettingService
 * Author:dxx
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-07
 * Website:xxx
 */

namespace App\Http\Api\Service\User;

use App\Http\Api\Service\BaseService;
use App\Model\User\UserNotificationSetting;

class NotificationSettingService extends BaseService
{
    public function show(): UserNotificationSetting
    {
        $notificationSetting = UserNotificationSetting::query()
            ->where('user_id', $this->userId())
            ->first();
        if (!$notificationSetting) {
            $notificationSetting = new UserNotificationSetting();
            $notificationSetting->user_id = $this->userId();
            $notificationSetting->system_message_enabled = 1;
            $notificationSetting->trading_message_enabled = 1;
            $notificationSetting->security_message_enabled = 1;
            $notificationSetting->promotion_message_enabled = 1;
            $notificationSetting->email_login_enabled = 1;
            $notificationSetting->email_trading_enabled = 1;
            $notificationSetting->email_withdrawal_enabled = 1;
            $notificationSetting->email_security_enabled = 1;
            $notificationSetting->email_promotion_enabled = 0;
            $notificationSetting->email_news_enabled = 0;
            $notificationSetting->push_login_enabled = 1;
            $notificationSetting->push_trading_enabled = 1;
            $notificationSetting->push_price_alert_enabled = 1;
            $notificationSetting->push_security_enabled = 1;
            $notificationSetting->push_promotion_enabled = 0;
            $notificationSetting->push_news_enabled = 0;
            $notificationSetting->sms_login_enabled = 0;
            $notificationSetting->sms_trading_enabled = 0;
            $notificationSetting->sms_withdrawal_enabled = 1;
            $notificationSetting->sms_security_enabled = 1;
            $notificationSetting->save();
        }
        return $notificationSetting;
    }

    public function update(array $data)
    {
        return UserNotificationSetting::query()
            ->where('user_id', $this->userId())
            ->update($data);
    }
}
