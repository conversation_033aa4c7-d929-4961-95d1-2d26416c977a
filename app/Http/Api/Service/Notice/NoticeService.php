<?php

declare(strict_types=1);
/**
 * NoticeService

 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-01
 * Website:xxx
 */

namespace App\Http\Api\Service\Notice;

use App\Http\Api\Request\Notice\NoticeRequest;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Article\NoticeRepository;
use Hyperf\Di\Annotation\Inject;

/**
 * 公告服务
 * Summary of NoticeService
 */
class NoticeService
{
    #[Inject]
    protected NoticeRepository $repository;


    /**
     * 通过分类查询公告列表
     * Summary of list
     * @param \App\Http\Api\Request\Notice\NoticeRequest $request
     * @return array
     */
    public function list(NoticeRequest $request)
    {
        $page = $request->input('page',1);
        $pageSize = $request->input('page_size',10);
        return $this->repository->page($request->all(),$page,$pageSize);
    }

    /**
     * 获取详情
     * Summary of detail
     * @param mixed $id
     */
    public function detail($id) {
        return $this->repository->findById($id);
    }
}
