<?php

declare(strict_types=1);
/**
 * DynamicsCurrencyService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-09
 * Website:xxx
 */

namespace App\Http\Api\Service\Dynamics;

use App\Http\Api\Service\BaseService;
use App\Http\Api\Request\Dynamics\DynamicsCurrencyRequest;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Article\DynamicsCurrencyRepository;
use Hyperf\Di\Annotation\Inject;

class DynamicsCurrencyService extends BaseService
{
    #[Inject]
    protected DynamicsCurrencyRepository $repository;

    public function list(DynamicsCurrencyRequest $request)
    {
        $page = $request->input('page',1);
        $pageSize = $request->input('page_size',10);
        $params = $request->all();
        return $this->repository->page($params,$page,$pageSize);
    }
}
