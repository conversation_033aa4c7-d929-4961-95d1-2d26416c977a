<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Api\Service;

use App\Model\User\User;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;

class BaseService
{
    #[Inject]
    protected RequestInterface $request;

    /**
     * 当前登录用户ID
     */
    public function userId(): ?int
    {
        return $this->request->getAttribute('user_id');
    }

    /**
     * 缓存中的用户信息
     */
    public function user(): ?User
    {
        return $this->request->getAttribute('user');
    }

    /**
     * 数据库中的用户信息
     */
    public function getUser(): ?User
    {
        $user = null;

        if ($this->userId()) {
            $user = User::query()->where('id', $this->userId())->first();
        }

        return $user;
    }
}
