<?php

declare(strict_types=1);
/**
 * CategoryService

 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-06-24
 * Website:xxx
 */

namespace App\Http\Api\Service\Category;

use App\Http\Api\Request\Category\CategoryRequest;
use App\Repository\Article\CategoryRepository;
use Hyperf\Di\Annotation\Inject;

/**
 * 分类服务
 * Summary of CategoryService
 */
class CategoryService
{
    #[Inject]
    protected CategoryRepository $repository;

    /**
     * 获取列表 key 标识必传
     * @param CategoryRequest $request
     * @return array
     */
    public function list(CategoryRequest $request):array
    {
        return $this->repository->list($request->all())->toArray();
    }
}
