<?php

declare(strict_types=1);
/**
 * MessageService
 * Author:XTeam
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-07-03
 * Website:xxx
 */

namespace App\Http\Api\Service\Message;

use App\Http\Api\Request\Message\MessageRequest;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Message\MessageRepository;
use Hyperf\Di\Annotation\Inject;

class MessageService
{

    #[Inject]
    private MessageRepository $repository;

     /**
     * 通过分类查询消息列表
     * Summary of list
     * @return array
     */
    public function list(MessageRequest $request)
    {
        $page = $request->input('page',1);
        $pageSize = $request->input('page_size',10);
        $params = $request->all();
        $params = array_merge($params,[
            'user_ids'=> null,
        ]);
        return $this->repository->page($params,$page,$pageSize);
    }

     /**
     * 通过分类查询私人消息列表
     * Summary of list
     * @return array
     */
    public function listPrivate(MessageRequest $request)
    {
        $page = $request->input('page',1);
        $pageSize = $request->input('page_size',10);
        $params = $request->all();
        $params = array_merge($params,[
            'user_ids'=> $request->getAttribute('user_id'),
        ]);
        return $this->repository->page($params,$page,$pageSize);
    }

    /**
     * 获取详情
     * Summary of detail
     * @param mixed $id
     */
    public function detail($id) {
        return $this->repository->findById($id);
    }
}
