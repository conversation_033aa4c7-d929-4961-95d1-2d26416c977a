<?php

/**
 * MarketService.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/2
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Service\V1;

use App\Enum\Chain\ChainApis;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\MarketType;
use App\Model\Currency\Currency;
use App\Model\Currency\CurrencyCategory;
use App\Model\User\UserDiyCurrency;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

class MarketService
{
    #[Inject]
    protected Redis $redis;

    /**
     * 获取所有活跃币种及其ticker数据
     * @return array
     */
    public function getAllCurrency(): array
    {
        try {
            // 获取所有活跃币种，预加载CurrencyMate关联
            $currencies = Currency::query()
                ->where('status', 1)
                ->with('currencyMate:id,currency_id,logo,cateIds')
                ->orderBy('id', 'asc')
                ->get();

            if ($currencies->isEmpty()) {
                return [];
            }

            // 批量获取所有ticker数据
            $allTickerData = $this->batchGetTickerData($currencies);

            $result = [];

            foreach ($currencies as $currency) {
                $currencyId = $currency->getId();
                
                $currencyData = [
                    'id' => $currencyId,
                    'symbol' => $currency->getSymbol(),
                    'base_asset' => $currency->getBaseAsset(),
                    'quote_asset' => $currency->getQuoteAsset(),
                    'base_assets_precision' => $currency->getBaseAssetsPrecision(),
                    's_price_precision' => $currency->getSPricePrecision(),
                    's_quantity_precision' => $currency->getSQuantityPrecision(),
                    'm_price_precision' => $currency->getMPricePrecision(),
                    'm_quantity_precision' => $currency->getMQuantityPrecision(),
                    'is_spotTrade' => $currency->getIsSpotTrade(),
                    'is_marginTrade' => $currency->getIsMarginTrade(),
                    'market_type' => $currency->getMarketType(),
                    'trading_start' => $currency->getTradingStart(),
                    'trading_end' => $currency->getTradingEnd(),
                    'trading_timezone' => $currency->getTradingTimezone(),
                    'status' => $currency->getStatus(),
                    'logo' => $currency->getLogo(),
                    'cateIds' => $currency->getCateIds(),
                    'tickers' => []
                ];

                // 添加现货ticker数据
                if ($currency->getIsSpotTrade() === 1 && isset($allTickerData[$currencyId][MarketType::CRYPTO->value])) {
                    $currencyData['tickers']['spot'] = $allTickerData[$currencyId][MarketType::CRYPTO->value];
                }

                // 添加合约ticker数据
                if ($currency->getIsMarginTrade() === 1 && isset($allTickerData[$currencyId][MarketType::MARGIN->value])) {
                    $currencyData['tickers']['margin'] = $allTickerData[$currencyId][MarketType::MARGIN->value];
                }

                $result[] = $currencyData;
            }

            return $result;

        } catch (\Throwable $e) {
            // 记录错误日志
            error_log("获取币种数据失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 批量获取所有币种的ticker数据
     * @param \Hyperf\Database\Model\Collection $currencies
     * @return array [currency_id => [market_type => ticker_data]]
     */
    private function batchGetTickerData($currencies): array
    {
        try {
            $tickerKeys = [];
            $keyMapping = [];

            // 收集所有需要查询的Redis key
            foreach ($currencies as $currency) {
                $currencyId = $currency->getId();
                
                // 现货ticker keys
                if ($currency->getIsSpotTrade() === 1) {
                    $spotKeys = $this->generateTickerKeys($currencyId, MarketType::CRYPTO->value);
                    foreach ($spotKeys as $priority => $key) {
                        $tickerKeys[] = $key;
                        $keyMapping[$key] = ['currency_id' => $currencyId, 'market_type' => MarketType::CRYPTO->value, 'priority' => $priority];
                    }
                }

                // 合约ticker keys
                if ($currency->getIsMarginTrade() === 1) {
                    $marginKeys = $this->generateTickerKeys($currencyId, MarketType::MARGIN->value);
                    foreach ($marginKeys as $priority => $key) {
                        $tickerKeys[] = $key;
                        $keyMapping[$key] = ['currency_id' => $currencyId, 'market_type' => MarketType::MARGIN->value, 'priority' => $priority];
                    }
                }
            }

            if (empty($tickerKeys)) {
                return [];
            }

            // 批量检查key存在性并获取数据类型
            $pipeline = $this->redis->pipeline();
            foreach ($tickerKeys as $key) {
                $pipeline->exists($key);
                $pipeline->type($key);
            }
            $existsAndTypes = $pipeline->exec();

            // 根据数据类型批量获取数据
            $pipeline = $this->redis->pipeline();
            $validKeys = [];
            
            for ($i = 0; $i < count($tickerKeys); $i++) {
                $key = $tickerKeys[$i];
                $exists = $existsAndTypes[$i * 2];
                $type = $existsAndTypes[$i * 2 + 1];

                if ($exists) {
                    $validKeys[] = $key;
                    if ($type === \Redis::REDIS_HASH) {
                        $pipeline->hGetAll($key);
                    } elseif ($type === \Redis::REDIS_STRING) {
                        $pipeline->get($key);
                    }
                }
            }

            $rawResults = $pipeline->exec();

            // 组织数据
            $result = [];
            $resultIndex = 0;

            foreach ($validKeys as $key) {
                if (!isset($keyMapping[$key]) || !isset($rawResults[$resultIndex])) {
                    $resultIndex++;
                    continue;
                }

                $mapping = $keyMapping[$key];
                $rawData = $rawResults[$resultIndex];
                $resultIndex++;

                if ($rawData) {
                    // 处理不同数据类型
                    if (is_array($rawData)) {
                        // Hash数据
                        $tickerData = $this->convertTickerFieldTypes($rawData);
                    } else {
                        // String数据，尝试JSON解码
                        $decoded = json_decode($rawData, true);
                        if ($decoded && is_array($decoded)) {
                            $tickerData = $this->convertTickerFieldTypes($decoded);
                        } else {
                            continue;
                        }
                    }

                    $currencyId = $mapping['currency_id'];
                    $marketType = $mapping['market_type'];
                    $priority = $mapping['priority'];

                    // 按优先级保存数据（优先级越低越优先）
                    if (!isset($result[$currencyId][$marketType]) || $priority < $result[$currencyId][$marketType]['_priority']) {
                        $tickerData['source'] = $this->getPrioritySource($priority);
                        $tickerData['_priority'] = $priority;
                        $result[$currencyId][$marketType] = $tickerData;
                    }
                }
            }

            // 清理内部字段
            foreach ($result as $currencyId => $markets) {
                foreach ($markets as $marketType => $ticker) {
                    unset($result[$currencyId][$marketType]['_priority']);
                }
            }

            return $result;

        } catch (\Throwable $e) {
            error_log("批量获取ticker数据失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 生成指定币种和市场类型的ticker keys（按优先级排序）
     * @param int $currencyId
     * @param int $marketType
     * @return array [priority => key]
     */
    private function generateTickerKeys(int $currencyId, int $marketType): array
    {
        return [
            1 => TickerSyncKey::getInnerAggregatorTickerKey($currencyId, $marketType), // 最高优先级
            2 => TickerSyncKey::getInnerTickerKey($currencyId, $marketType),
            3 => TickerSyncKey::getOuterTickerKey($currencyId, $marketType), // 最低优先级
        ];
    }

    /**
     * 根据优先级获取数据源名称
     * @param int $priority
     * @return string
     */
    private function getPrioritySource(int $priority): string
    {
        return match ($priority) {
            1 => 'inner_aggregator',
            2 => 'inner',
            3 => 'outer',
            default => 'unknown'
        };
    }

    /**
     * 获取指定币种和市场类型的ticker数据
     * 按优先级获取：内部聚合ticker > 内部ticker > 外部ticker
     * @param int $currencyId
     * @param int $marketType
     * @return array|null
     */
    private function getTickerData(int $currencyId, int $marketType): ?array
    {
        try {
            // 优先级1：内部聚合ticker（撮合引擎产生）
            $innerAggregatorKey = TickerSyncKey::getInnerAggregatorTickerKey($currencyId, $marketType);
            $tickerData = $this->getRedisTickerData($innerAggregatorKey);
            
            if ($tickerData) {
                return array_merge($tickerData, ['source' => 'inner_aggregator']);
            }

            // 优先级2：内部ticker
            $innerKey = TickerSyncKey::getInnerTickerKey($currencyId, $marketType);
            $tickerData = $this->getRedisTickerData($innerKey);
            
            if ($tickerData) {
                return array_merge($tickerData, ['source' => 'inner']);
            }

            // 优先级3：外部ticker
            $outerKey = TickerSyncKey::getOuterTickerKey($currencyId, $marketType);
            $tickerData = $this->getRedisTickerData($outerKey);
            
            if ($tickerData) {
                return array_merge($tickerData, ['source' => 'outer']);
            }

            return null;

        } catch (\Throwable $e) {
            error_log("获取ticker数据失败，币种ID: {$currencyId}, 市场类型: {$marketType}, 错误: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 从Redis获取ticker数据（支持Hash和String两种格式）
     * @param string $key
     * @return array|null
     */
    private function getRedisTickerData(string $key): ?array
    {
        try {
            // 首先检查key是否存在
            if (!$this->redis->exists($key)) {
                return null;
            }

            // 检查数据类型
            $type = $this->redis->type($key);
            
            if ($type === \Redis::REDIS_HASH) {
                // Hash类型数据
                $hashData = $this->redis->hGetAll($key);
                if ($hashData && is_array($hashData)) {
                    // 转换数值字段类型
                    return $this->convertTickerFieldTypes($hashData);
                }
            } elseif ($type === \Redis::REDIS_STRING) {
                // String类型数据（JSON格式）
                $stringData = $this->redis->get($key);
                if ($stringData) {
                    $decoded = json_decode($stringData, true);
                    if ($decoded && is_array($decoded)) {
                        return $this->convertTickerFieldTypes($decoded);
                    }
                }
            }

            return null;

        } catch (\Throwable $e) {
            error_log("从Redis获取ticker数据失败，key: {$key}, 错误: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 转换ticker数据中的字段类型
     * @param array $data
     * @return array
     */
    private function convertTickerFieldTypes(array $data): array
    {
        // 需要转换为浮点数的字段
        $floatFields = [
            'price_change', 'price_changeP', 'pre_close_price', 'last_price',
            'last_qty', 'open_price', 'high_price', 'low_price', 'volume'
        ];

        // 需要转换为整数的字段
        $intFields = ['currency_id', 'market_type', 'timestamp', 'reset_period'];

        foreach ($floatFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = (float)$data[$field];
            }
        }

        foreach ($intFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = (int)$data[$field];
            }
        }

        return $data;
    }

    /**
     * 用户获取币种数据是否有变更，一般所有币种数据缓存在客户端
     * @return array
     */
    public function isNewCurrency(): array
    {
        try {
            // 检查是否有变更的币种
            if (!$this->redis->exists("currency:edit")) {
                return [];
            }

            $currencyIds = $this->redis->sMembers("currency:edit");
            
            if (empty($currencyIds)) {
                return [];
            }

            // 转换为整数数组
            $currencyIds = array_map('intval', $currencyIds);

            // 查询指定币种的数据
            $currencies = Currency::query()
                ->with('currencyMate:id,logo,currency_id,cateIds')
                ->whereIn('id', $currencyIds)
                ->where('status', 1)
                ->get();

            if ($currencies->isEmpty()) {
                return [];
            }

            // 批量获取ticker数据
            $allTickerData = $this->batchGetTickerData($currencies);

            $result = [];

            foreach ($currencies as $currency) {
                $currencyId = $currency->getId();
                
                $currencyData = [
                    'id' => $currencyId,
                    'symbol' => $currency->getSymbol(),
                    'base_asset' => $currency->getBaseAsset(),
                    'quote_asset' => $currency->getQuoteAsset(),
                    'base_assets_precision' => $currency->getBaseAssetsPrecision(),
                    's_price_precision' => $currency->getSPricePrecision(),
                    's_quantity_precision' => $currency->getSQuantityPrecision(),
                    'm_price_precision' => $currency->getMPricePrecision(),
                    'm_quantity_precision' => $currency->getMQuantityPrecision(),
                    'is_spotTrade' => $currency->getIsSpotTrade(),
                    'is_marginTrade' => $currency->getIsMarginTrade(),
                    'market_type' => $currency->getMarketType(),
                    'trading_start' => $currency->getTradingStart(),
                    'trading_end' => $currency->getTradingEnd(),
                    'trading_timezone' => $currency->getTradingTimezone(),
                    'status' => $currency->getStatus(),
                    'logo' => $currency->getLogo(),
                    'cateIds' => $currency->getCateIds(),
                    'tickers' => []
                ];

                // 添加现货ticker数据
                if ($currency->getIsSpotTrade() === 1 && isset($allTickerData[$currencyId][MarketType::CRYPTO->value])) {
                    $currencyData['tickers']['spot'] = $allTickerData[$currencyId][MarketType::CRYPTO->value];
                }

                // 添加合约ticker数据
                if ($currency->getIsMarginTrade() === 1 && isset($allTickerData[$currencyId][MarketType::MARGIN->value])) {
                    $currencyData['tickers']['margin'] = $allTickerData[$currencyId][MarketType::MARGIN->value];
                }

                $result[] = $currencyData;
            }

            // 删除已查询的币种ID
            if (!empty($result)) {
                $queriedCurrencyIds = array_column($result, 'id');
                foreach ($queriedCurrencyIds as $currencyId) {
                    $this->removeEditCurrency($currencyId);
                }
            }

            return $result;

        } catch (\Throwable $e) {
            error_log("获取新币种数据失败: " . $e->getMessage());
            return [];
        }
    }

    public function getCategory()
    {
        return CurrencyCategory::query()->orderBy('sort','asc')->get();
    }

    public function getDiyCate(int $user_id)
    {
        return UserDiyCurrency::query()->where('user_id',$user_id)->get();
    }

    /**
     * 设置币种为已编辑状态
     * @param int $currency_id 币种ID
     * @return int 添加成功返回1，已存在返回0
     */
    public function setEditCurrency(int $currency_id): int
    {
        try {
            // 使用Redis集合添加币种ID
            return $this->redis->sadd("currency:edit", $currency_id);
        } catch (\Throwable $e) {
            error_log("设置币种编辑状态失败，币种ID: {$currency_id}, 错误: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * 移除币种的编辑状态
     * @param int $currency_id 币种ID
     * @return int 移除成功返回1，不存在返回0
     */
    public function removeEditCurrency(int $currency_id): int
    {
        try {
            return $this->redis->srem("currency:edit", $currency_id);
        } catch (\Throwable $e) {
            error_log("移除币种编辑状态失败，币种ID: {$currency_id}, 错误: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * 清空所有币种的编辑状态
     * @return bool
     */
    public function clearEditCurrency(): bool
    {
        try {
            return $this->redis->del("currency:edit") > 0;
        } catch (\Throwable $e) {
            error_log("清空币种编辑状态失败: " . $e->getMessage());
            return false;
        }
    }


    /*************************************************************************链上交易**********************************************************************************************/

    /**
     * 获取链上交易支持的链
     * @return array|mixed
     */
    public function getChainCate()
    {
        try {
            if($this->redis->exists('chain:chain-cate')){
                return json_decode($this->redis->get('chain:chain-cate'));
            }

            $response = client()->get(ChainApis::CHAIN_CATE->value);
            $data = json_decode($response->getBody()->getContents(),true);
            $this->redis->set("chain:chain-cate",json_encode($data));
            $this->redis->expire("chain:chain-cate",300);
            return $data;
        }catch (\Throwable){
            return [];
        }
    }

    /**
     * 根据分类获取币种
     * @param string $filter
     * @return array|mixed
     */
    public function getChainCurrency(string $filter)
    {
        try {
            if($this->redis->exists("chain:currency:{$filter}")){
                return json_decode($this->redis->get("chain:currency:{$filter}"),true);
            }

            $response = client()->get(ChainApis::getFetchUrl($filter));
            $data = json_decode($response->getBody()->getContents(),true);
            $this->redis->set("chain:currency:{$filter}",json_encode($data));
            $this->redis->expire("chain:currency:{$filter}",300);
            return $data;
        }catch (\Throwable){
            return [];
        }
    }
}