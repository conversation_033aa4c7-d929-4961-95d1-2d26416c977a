<?php

declare(strict_types=1);
/**
 * TradeSpotCommissionService.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/26
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Service\V1;

use App\Exception\BusinessException;
use App\Http\Api\Event\CommissionOrder\CommissionOrderCancelledEvent;
use App\Http\Api\Event\CommissionOrder\CommissionOrderTriggeredEvent;
use App\Http\Api\Service\V1\TradeSpotService;
use App\Http\Common\ResultCode;
use App\Model\Enums\User\AccountType;
use App\Model\Trade\Enums\CommissionStatus;
use App\Model\Trade\TradeSpotCommission;
use App\Service\UserAccounts\UserAccountsAssetService;
use Carbon\Carbon;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Hyperf\Event\EventDispatcher;
use Hyperf\Paginator\Paginator;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\MarketType;
use App\Model\Trade\TradeSpotOrder;
use Hyperf\Redis\Redis;
use App\Enum\CommissionRedisKey;
use Psr\Log\LoggerInterface;

class TradeSpotCommissionService
{
    #[Inject]
    protected TradeSpotService $tradeSpotService;

    #[Inject]
    protected UserAccountsAssetService $userAccountsAssetService;

    #[Inject]
    protected EventDispatcher $eventDispatcher;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected LoggerInterface $logger;

    /**
     * 创建委托订单
     */
    public function placeOrder(
        int $userId,
        int $currencyId,
        int $side,
        int $orderType,
        int $triggerCondition,
        string $triggerPrice,
        string $amount,
        int $triggerType,
        ?string $placePrice = null
    ): TradeSpotCommission {
        // 验证用户余额是否充足（不冻结资金，只验证）
        $this->validateUserBalance($userId, $currencyId, $side, $amount, $placePrice);

        return Db::transaction(function () use ($userId, $currencyId, $side, $orderType, $triggerCondition, $triggerPrice, $amount, $triggerType, $placePrice) {
            // 创建委托订单
            $commission = new TradeSpotCommission();
            $commission->user_id = $userId;
            $commission->currency_id = $currencyId;
            $commission->side = $side;
            $commission->order_type = $orderType;
            $commission->trigger_condition = $triggerCondition;
            $commission->trigger_price = $triggerPrice;
            $commission->amount = $amount;
            $commission->trigger_type = $triggerType;
            $commission->place_price = $placePrice;
            $commission->status = CommissionStatus::PENDING->value;
            $commission->save();

            // 添加到Redis有序集合
            $this->addCommissionToRedis($commission);

            return $commission;
        });
    }

    /**
     * 修改委托订单
     */
    public function updateOrder(
        int $userId,
        int $commissionId,
        ?string $triggerPrice = null,
        ?string $amount = null,
        ?string $placePrice = null,
        ?int $triggerType = null
    ): TradeSpotCommission {
        return Db::transaction(function () use ($userId, $commissionId, $triggerPrice, $amount, $placePrice, $triggerType) {
            // 查询委托订单
            $commission = TradeSpotCommission::query()
                ->where('id', $commissionId)
                ->where('user_id', $userId)
                ->where('status', CommissionStatus::PENDING->value)
                ->lockForUpdate()
                ->first();

            if (!$commission) {
                throw new BusinessException(ResultCode::FAIL, '委托订单不存在或已处理');
            }

            // 记录原始数据用于Redis更新
            $originalData = [
                'trigger_price' => $commission->trigger_price,
                'amount' => $commission->amount,
                'place_price' => $commission->place_price,
                'trigger_type' => $commission->trigger_type
            ];

            // 更新字段
            $updated = false;
            if ($triggerPrice !== null) {
                $commission->trigger_price = $triggerPrice;
                $updated = true;
            }
            if ($amount !== null) {
                // 重新验证用户余额
                $this->validateUserBalance(
                    $userId,
                    $commission->currency_id,
                    $commission->side,
                    $amount,
                    $placePrice ?? $commission->place_price
                );
                $commission->amount = $amount;
                $updated = true;
            }
            if ($placePrice !== null) {
                $commission->place_price = $placePrice;
                $updated = true;
            }
            if ($triggerType !== null) {
                $commission->trigger_type = $triggerType;
                $updated = true;
            }

            if (!$updated) {
                throw new BusinessException(ResultCode::FAIL, '没有需要更新的字段');
            }

            // 保存更新
            $commission->save();

            // 更新Redis中的数据
            $this->updateCommissionInRedis($commission, $originalData);

            return $commission;
        });
    }

    /**
     * 取消委托订单
     */
    public function cancelOrder(int $userId, int $commissionId): bool
    {
        return Db::transaction(function () use ($userId, $commissionId) {
            $commission = TradeSpotCommission::query()
                ->where('id', $commissionId)
                ->where('user_id', $userId)
                ->first();

            if (!$commission) {
                throw new BusinessException(ResultCode::FAIL,'委托订单不存在');
            }

            if ($commission->status !== CommissionStatus::PENDING) {
                throw new BusinessException(ResultCode::FAIL,'只能取消等待执行的委托订单');
            }

            $commission->status = CommissionStatus::CANCELLED;
            $commission->trigger_reason = '用户主动取消';
            $result = $commission->save();

            if ($result) {
                // 从Redis有序集合中移除
                $this->removeCommissionFromRedis($commission);

                // 触发取消事件
                $this->eventDispatcher->dispatch(new CommissionOrderCancelledEvent(
                    $commission,
                    '用户主动取消'
                ));
            }

            return $result;
        });
    }

    /**
     * 查询委托订单列表
     */
    public function queryOrders(
        int $userId,
        ?int $currencyId = null,
        ?int $orderType = null,
        ?int $status = null,
        int $page = 1,
        int $pageSize = 20
    ): \Hyperf\Contract\LengthAwarePaginatorInterface
    {
        $query = TradeSpotCommission::query()
            ->where('user_id', $userId);

        if ($currencyId) {
            $query->where('currency_id', $currencyId);
        }

        if ($orderType) {
            $query->where('order_type', $orderType);
        }

        if ($status !== null) {
            $query->where('status', $status);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($pageSize, ['*'], 'page', $page);
    }

    /**
     * 获取委托订单详情
     */
    public function getOrderDetail(int $userId, int $commissionId): TradeSpotCommission
    {
        $commission = TradeSpotCommission::query()
            ->where('id', $commissionId)
            ->where('user_id', $userId)
            ->with(['currency', 'spotOrder'])
            ->first();

        if (!$commission) {
            throw new BusinessException(ResultCode::FAIL,'委托订单不存在');
        }

        return $commission;
    }

    /**
     * 获取等待执行的委托订单（供监控进程使用）
     */
    public function getPendingOrders(?int $currencyId = null): array
    {
        $query = TradeSpotCommission::query()
            ->where('status', CommissionStatus::PENDING->value);

        if ($currencyId) {
            $query->where('currency_id', $currencyId);
        }

        return $query->orderBy('created_at', 'asc')->get()->toArray();
    }

    /**
     * 触发委托订单（供触发服务使用）
     */
    public function triggerOrder(int $commissionId, string $currentPrice): bool
    {
        return Db::transaction(function () use ($commissionId, $currentPrice) {
            $commission = TradeSpotCommission::query()
                ->where('id', $commissionId)
                ->where('status', CommissionStatus::PENDING->value)
                ->lockForUpdate()
                ->first();

            if (!$commission) {
                return false;
            }

            try {
                // 检查触发条件
                if (!$this->checkTriggerCondition($commission, $currentPrice)) {
                    return false;
                }

                // 再次验证用户余额
                $this->validateUserBalance(
                    $commission->user_id,
                    $commission->currency_id,
                    $commission->side,
                    $commission->amount,
                    $commission->place_price
                );

                // 创建现货订单
                $spotOrder = $this->createSpotOrder($commission);

                // 更新委托订单状态
                $commission->status = CommissionStatus::TRIGGERED->value;
                $commission->order_id = $spotOrder->id;
                $commission->triggered_at = Carbon::now();
                $commission->trigger_reason = "价格触发，当前价格：{$currentPrice}";
                $commission->save();

                // 从Redis中移除已触发的委托订单
                $this->removeCommissionFromRedis($commission);

                // 触发成功事件
                $this->eventDispatcher->dispatch(new CommissionOrderTriggeredEvent(
                    $commission,
                    $currentPrice,
                    $spotOrder->id
                ));

                return true;

            } catch (\Exception $e) {
                // 触发失败，更新状态
                $commission->status = CommissionStatus::TRIGGER_FAILED->value;
                $commission->trigger_reason = '触发失败：' . $e->getMessage();
                $commission->save();

                // 从Redis中移除失败的委托订单
                $this->removeCommissionFromRedis($commission);

                return false;
            }
        });
    }

    /**
     * 验证用户余额是否充足
     */
    protected function validateUserBalance(
        int $userId,
        int $currencyId,
        int $side,
        string $amount,
        ?string $placePrice = null
    ): void {
        if ($side === 1) {
            // 买单：检查计价币种余额
            $quoteCurrencyId = $this->tradeSpotService->getQuoteCurrencyId($currencyId);
            $requiredAmount = $this->calculateRequiredAmount($amount, $placePrice, $side, $currencyId);
            
            $asset = $this->userAccountsAssetService->getUserAsset($userId, AccountType::SPOT->value, $quoteCurrencyId);
            $balance = $asset ? (string)$asset->available : '0';
            
            if (bccomp($balance, $requiredAmount, 8) < 0) {
                throw new BusinessException(ResultCode::FAIL,'余额不足，无法创建委托订单');
            }
        } else {
            // 卖单：检查基础币种余额
            $asset = $this->userAccountsAssetService->getUserAsset($userId, AccountType::SPOT->value, $currencyId);
            $balance = $asset ? (string)$asset->available : '0';
            
            if (bccomp($balance, $amount, 8) < 0) {
                throw new BusinessException(ResultCode::FAIL,'余额不足，无法创建委托订单');
            }
        }
    }

    /**
     * 计算所需金额
     */
    protected function calculateRequiredAmount(string $amount, ?string $price, int $side, int $currencyId): string
    {
        if ($side === 1) {
            // 买单
            if ($price) {
                // 限价单
                return bcmul($amount, $price, 8);
            } else {
                // 市价单，从Redis获取最新价格
                $marketPrice = $this->getMarketPrice($currencyId);
                return bcmul($amount, $marketPrice, 8);
            }
        }
        
        return $amount;
    }

    /**
     * 获取市价（从Redis获取最新价格）
     */
    protected function getMarketPrice(int $currencyId): string
    {
        try {
            // 使用TickerSyncKey::getOuterTradeKey获取Redis key
            $key = TickerSyncKey::getOuterTradeKey($currencyId, MarketType::CRYPTO->value);
            
            // 查询Redis hash的price字段
            $price = $this->redis->hGet($key, 'price');
            
            if ($price === false || $price === null) {
                throw new BusinessException(ResultCode::FAIL, '无法获取当前市价');
            }
            
            return (string)$price;
            
        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '获取市价失败：' . $e->getMessage());
        }
    }

    /**
     * 检查触发条件
     */
    protected function checkTriggerCondition(TradeSpotCommission $commission, string $currentPrice): bool
    {
        $triggerPrice = $commission->trigger_price;
        
        switch ($commission->trigger_condition) {
            case 1: // 大于等于
                return bccomp($currentPrice, strval($triggerPrice), 8) >= 0;
            case 2: // 小于等于
                return bccomp($currentPrice, strval($triggerPrice), 8) <= 0;
            default:
                return false;
        }
    }

    /**
     * 创建现货订单
     */
    protected function createSpotOrder(TradeSpotCommission $commission): TradeSpotOrder
    {
        // 构造订单数据数组
        $orderData = [
            'currency_id' => $commission->currency_id,
            'side' => $commission->side === 1 ? 'buy' : 'sell',
            'type' => $commission->trigger_type === 1 ? 'limit' : 'market',
            'quantity' => (float)$commission->amount,
            'time_in_force' => 'gtc' // 默认使用GTC
        ];

        // 如果是限价单，添加价格
        if ($commission->trigger_type === 1 && $commission->place_price) {
            $orderData['price'] = (float)$commission->place_price;
        }

        // 调用现货交易服务创建订单
        $result = $this->tradeSpotService->placeOrder($commission->user_id, $orderData);

        // 从返回结果中获取订单ID
        $orderId = $result['order_id'] ?? null;
        if (!$orderId) {
            throw new \RuntimeException('创建现货订单失败，未获取到订单ID');
        }

        // 查询并返回现货订单模型
        $spotOrder = TradeSpotOrder::query()
            ->where('id', $orderId)
            ->first();

        if (!$spotOrder) {
            throw new \RuntimeException('创建现货订单失败，未找到订单记录');
        }

        return $spotOrder;
    }

    /**
     * 添加委托订单到Redis有序集合
     */
    protected function addCommissionToRedis(TradeSpotCommission $commission): void
    {
        try {
            // 获取对应的Redis键
            $redisKey = CommissionRedisKey::getCommissionKey(
                $commission->currency_id,
                $commission->side,
                $commission->order_type->value,
                $commission->trigger_condition->value
            );

            // 添加到有序集合：score=触发价格, member=订单ID
            $this->redis->zAdd($redisKey, $commission->trigger_price, $commission->id);

            // 存储委托订单详细信息到哈希表
            $detailKey = CommissionRedisKey::getCommissionDetailKey($commission->id);
            $this->redis->hMSet($detailKey, [
                'id' => $commission->id,
                'user_id' => $commission->user_id,
                'currency_id' => $commission->currency_id,
                'side' => $commission->side,
                'order_type' => $commission->order_type->value,
                'trigger_condition' => $commission->trigger_condition->value,
                'trigger_price' => $commission->trigger_price,
                'amount' => $commission->amount,
                'trigger_type' => $commission->trigger_type->value,
                'place_price' => $commission->place_price ?? '',
                'status' => $commission->status->value,
                'created_at' => $commission->created_at->toDateTimeString()
            ]);

            // 设置详情哈希表过期时间（7天）
            $this->redis->expire($detailKey, 7 * 24 * 3600);

        } catch (\Exception $e) {
            // 记录日志但不抛出异常，避免影响主流程
            $this->logger->error('添加委托订单到Redis失败', [
                'commission_id' => $commission->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新Redis中的委托订单数据
     */
    protected function updateCommissionInRedis(TradeSpotCommission $commission, array $originalData): void
    {
        try {
            // 如果触发价格发生变化，需要更新有序集合
            if ($originalData['trigger_price'] != $commission->trigger_price) {
                $redisKey = CommissionRedisKey::getCommissionKey(
                    $commission->currency_id,
                    $commission->side,
                    $commission->order_type->value,
                    $commission->trigger_condition->value
                );

                // 删除旧的score
                $this->redis->zRem($redisKey, $commission->id);
                
                // 添加新的score
                $this->redis->zAdd($redisKey, $commission->trigger_price, $commission->id);
            }

            // 更新详情哈希表
            $detailKey = CommissionRedisKey::getCommissionDetailKey($commission->id);
            $this->redis->hMSet($detailKey, [
                'trigger_price' => $commission->trigger_price,
                'amount' => $commission->amount,
                'trigger_type' => $commission->trigger_type->value,
                'place_price' => $commission->place_price ?? '',
            ]);

        } catch (\Exception $e) {
            // 记录日志但不抛出异常
            $this->logger->error('更新Redis中的委托订单失败', [
                'commission_id' => $commission->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 从Redis有序集合中移除委托订单
     */
    protected function removeCommissionFromRedis(TradeSpotCommission $commission): void
    {
        try {
            // 获取对应的Redis键
            $redisKey = CommissionRedisKey::getCommissionKey(
                $commission->currency_id,
                $commission->side,
                $commission->order_type->value,
                $commission->trigger_condition->value
            );

            // 从有序集合中移除
            $this->redis->zRem($redisKey, $commission->id);

            // 删除详情哈希表
            $detailKey = CommissionRedisKey::getCommissionDetailKey($commission->id);
            $this->redis->del($detailKey);

        } catch (\Exception $e) {
            // 记录日志但不抛出异常
            $this->logger->error('从Redis移除委托订单失败', [
                'commission_id' => $commission->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 批量加载待处理的委托订单到Redis（供监控进程初始化使用）
     */
    public function loadPendingOrdersToRedis(): array
    {
        try {
            // 查询所有等待触发的委托订单
            $pendingOrders = TradeSpotCommission::query()
                ->where('status', CommissionStatus::PENDING->value)
                ->get();

            $loadedCount = 0;
            $currencies = [];

            foreach ($pendingOrders as $commission) {
                $this->addCommissionToRedis($commission);
                $loadedCount++;
                
                if (!in_array($commission->currency_id, $currencies)) {
                    $currencies[] = $commission->currency_id;
                }
            }

            return [
                'count' => $loadedCount,
                'currencies' => $currencies
            ];

        } catch (\Exception $e) {
            $this->logger->error('批量加载委托订单到Redis失败', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'count' => 0,
                'currencies' => []
            ];
        }
    }

    /**
     * 触发指定的委托订单（供监控进程使用）
     */
    public function triggerCommissionOrder(int $orderId, string $currentPrice): bool
    {
        try {
            return $this->triggerOrder($orderId, $currentPrice);
        } catch (\Exception $e) {
            $this->logger->error('触发委托订单失败', [
                'order_id' => $orderId,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 获取需要监控的币种列表（供监控进程使用）
     */
    public function getMonitoredCurrencies(): array
    {
        try {
            // 查询所有有等待触发委托订单的币种
            $currencies = TradeSpotCommission::query()
                ->where('status', CommissionStatus::PENDING->value)
                ->distinct()
                ->pluck('currency_id')
                ->toArray();

            return array_map('intval', $currencies);

        } catch (\Exception $e) {
            $this->logger->error('获取监控币种列表失败', [
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * 清理过期的委托订单（供监控进程使用）
     */
    public function cleanupExpiredCommissionOrders(): int
    {
        try {
            // 查询过期的委托订单（这里假设有expires_at字段，如果没有可以根据创建时间判断）
            $expiredOrders = TradeSpotCommission::query()
                ->where('status', CommissionStatus::PENDING->value)
                ->where('created_at', '<', Carbon::now()->subDays(30)) // 30天过期
                ->get();

            $cleanedCount = 0;
            
            foreach ($expiredOrders as $commission) {
                // 更新数据库状态
                $commission->status = CommissionStatus::CANCELLED->value;
                $commission->trigger_reason = '订单过期自动取消';
                $commission->save();

                // 从Redis中移除
                $this->removeCommissionFromRedis($commission);
                
                $cleanedCount++;
            }

            return $cleanedCount;

        } catch (\Exception $e) {
            $this->logger->error('清理过期委托订单失败', [
                'error' => $e->getMessage()
            ]);
            
            return 0;
        }
    }
} 