<?php

/**
 * MatchService.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/30
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Service\V1;

use App\Enum\OrderType;
use App\Enum\TradeSide;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\CpxUser;
use App\Model\Match\MatchOrder;
use App\Service\MatchEngineOrderService;
use Hyperf\Snowflake\IdGeneratorInterface;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Hyperf\Snowflake\IdGenerator;
use Psr\Container\ContainerInterface;
use TheSeer\Tokenizer\Exception;

class MatchService
{
    #[Inject]
    private Redis $redis;

    #[Inject]
    private ContainerInterface $container;

    #[Inject]
    private MatchEngineOrderService $matchEngineOrderService;

    /**
     * 添加订单到数据库并投递到撮合引擎
     * @return bool
     */
    public function createOrder(array $post): bool
    {
        /**
         * @var CpxUser $user ;
         */
        $user = context_get("user");

        try {
            Db::beginTransaction();

            $order = [
                'user_id' => $user->getId(),
                'currency_id' => $post['currency_id'],
                'order_id' => $this->container->get(IdGeneratorInterface::class)->generate(),
                'market_type' => $post['market_type'],
                'side' => TradeSide::getSideInt($post['side']),
                'quantity' => $post['quantity'],
                'fill_quantity' => 0,
                'price' => $post['price'] ?? 0,
                'order_type' => OrderType::getOrderType($post['order_type']),
                'order_force' => $post['order_force'] ?? 'gtc',
                'symbol' => $post['symbol']
            ];

            $result = MatchOrder::create($order)->save();
            if (!$result) {
                Db::rollBack();
                throw new BusinessException(ResultCode::PLACE_ORDER_FAIL, "下单失败");
            }
            $order['side'] = TradeSide::getSideString($order['side']);
            $order['time_in_force'] = $post['order_force'];
            if (!($messageId = $this->matchEngineOrderService->addOrder(intval($post['market_type']), $post['symbol'], $user->getId(), $order))) {
                Db::rollBack();
                throw new BusinessException(ResultCode::PLACE_ORDER_FAIL, "下单失败");
            }
            Db::commit();
            return $messageId;
        } catch (\Throwable $t) {
            Db::rollBack();
            var_dump($t->getMessage());
            throw new BusinessException(ResultCode::PLACE_ORDER_FAIL, $t->getMessage());
        }
    }

    public function cancelOrder(array $post): bool
    {
        try {
            /**
             * @var CpxUser $user
             */
            $user = context_get("user");

            $md = MatchOrder::where([
                'user_id' => $user->getId(),
                'order_id' => $post['order_id']
            ])->with(['currency:id,symbol'])
                ->first();
            if(!$md){
                return false;
            }

            /**
             * 撤单应等待事件回调的后才修改订单状态
             */
            return $this->matchEngineOrderService->cancelOrder(
                $md->market_type,
                $md->currency->symbol,
                $md->order_id,
                $user->getId()
            );

        } catch (\Throwable $t) {
            var_dump($t->getMessage());
            return false;
        }
    }

    /**
     * 验证现货或者合约下单交易规则限制
     * @return void
     */
    private function verifyQuantity()
    {

    }

    /**
     * 验证下单价格是否在配置范围
     * @return void
     */
    private function verifyPrice()
    {

    }
}