<?php

declare(strict_types=1);

namespace App\Http\Api\Service\V1\Margin;

use App\Model\Trade\TradeMarginPosition;
use App\Model\Enums\Trade\Margin\MarginPositionStatus;
use App\Enum\Config\MarginConfigKey;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

/**
 * 杠杆仓位缓存同步服务
 */
class MarginPositionCacheService
{
    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected LoggerInterface $logger;

    /**
     * 同步仓位到缓存（开仓/加仓/减仓时调用）
     */
    public function syncPositionToCache(TradeMarginPosition $position): void
    {
        if ($position->getStatus() !== MarginPositionStatus::HOLDING) {
            return; // 只同步持仓中的仓位
        }

        $currencyId = $position->getCurrencyId();
        $positionKey = MarginConfigKey::buildPositionKey(
            $position->getUserId(),
            $position->getSide()->value,
            $position->getMarginType()->value
        );
        
        $positionData = $this->buildPositionCacheData($position);

        // 获取同步锁
        $lockKey = MarginConfigKey::getSyncLockKey($currencyId);
        $lockAcquired = $this->redis->set($lockKey, '1', ['NX', 'EX' => 10]);

        if (!$lockAcquired) {
            $this->logger->warning('获取仓位同步锁失败', ['currency_id' => $currencyId]);
            return;
        }

        try {
            // 存储到币种仓位Hash
            $this->redis->hSet(
                MarginConfigKey::getCurrencyPositionsKey($currencyId),
                $positionKey,
                json_encode($positionData)
            );

            // 更新币种监控状态
            $this->updateCurrencyMonitorStatus($currencyId);

            $this->logger->info('仓位数据同步到缓存成功', [
                'position_id' => $position->getId(),
                'currency_id' => $currencyId,
                'user_id' => $position->getUserId(),
                'position_key' => $positionKey
            ]);

        } finally {
            // 释放锁
            $this->redis->del($lockKey);
        }
    }

    /**
     * 从缓存中移除仓位（平仓时调用）
     */
    public function removePositionFromCache(TradeMarginPosition $position): void
    {
        $currencyId = $position->getCurrencyId();
        $positionKey = MarginConfigKey::buildPositionKey(
            $position->getUserId(),
            $position->getSide()->value,
            $position->getMarginType()->value
        );

        // 获取同步锁
        $lockKey = MarginConfigKey::getSyncLockKey($currencyId);
        $lockAcquired = $this->redis->set($lockKey, '1', ['NX', 'EX' => 10]);

        if (!$lockAcquired) {
            $this->logger->warning('获取仓位移除锁失败', ['currency_id' => $currencyId]);
            return;
        }

        try {
            // 从币种仓位Hash中移除
            $this->redis->hDel(
                MarginConfigKey::getCurrencyPositionsKey($currencyId),
                $positionKey
            );

            // 从风险等级索引中移除
            $this->removeFromRiskIndexes($currencyId, $positionKey);

            // 更新币种监控状态
            $this->updateCurrencyMonitorStatus($currencyId);

            $this->logger->info('仓位数据从缓存移除成功', [
                'position_id' => $position->getId(),
                'currency_id' => $currencyId,
                'user_id' => $position->getUserId(),
                'position_key' => $positionKey
            ]);

        } finally {
            // 释放锁
            $this->redis->del($lockKey);
        }
    }

    /**
     * 构建仓位缓存数据
     */
    protected function buildPositionCacheData(TradeMarginPosition $position): array
    {
        return [
            'position_id' => $position->getId(),
            'user_id' => $position->getUserId(),
            'currency_id' => $position->getCurrencyId(),
            'side' => $position->getSide()->value,
            'margin_type' => $position->getMarginType()->value,
            'leverage' => $position->getLeverage(),
            'quantity' => $position->getQuantity(),
            'available_quantity' => $position->getAvailableQuantity(),
            'frozen_quantity' => $position->getFrozenQuantity(),
            'entry_price' => $position->getEntryPrice(),
            'margin_amount' => $position->getMarginAmount(),
            'initial_margin' => $position->getInitialMargin(),
            'maintenance_margin' => $position->getMaintenanceMargin(),
            'realized_pnl' => $position->getRealizedPnl(),
            'last_calculated_price' => $position->getEntryPrice(),
            'last_margin_ratio' => 0.0,
            'last_risk_level' => 'safe',
            'updated_at' => time(),
            'synced_at' => time()
        ];
    }

    /**
     * 构建仓位缓存数据（包含维持保证金率）
     */
    protected function buildPositionCacheDataWithMarginBracket(TradeMarginPosition $position, ?object $marginBracket): array
    {
        $baseData = $this->buildPositionCacheData($position);

        // 添加维持保证金率相关数据
        $baseData['keep_rate'] = $marginBracket?->getKeepRate() ?? 0.05; // 默认5%
        $baseData['margin_bracket_level'] = $marginBracket?->getLevel() ?? 1;
        $baseData['margin_bracket_updated_at'] = time();

        return $baseData;
    }

    /**
     * 从风险等级索引中移除仓位
     */
    protected function removeFromRiskIndexes(int $currencyId, string $positionKey): void
    {
        $riskLevels = ['warning', 'danger', 'liquidation'];
        
        foreach ($riskLevels as $level) {
            $this->redis->sRem(
                MarginConfigKey::getRiskLevelKey($level, $currencyId),
                $positionKey
            );
        }
    }

    /**
     * 检查仓位是否在缓存中
     */
    public function isPositionInCache(TradeMarginPosition $position): bool
    {
        $currencyId = $position->getCurrencyId();
        $positionKey = MarginConfigKey::buildPositionKey(
            $position->getUserId(),
            $position->getSide()->value,
            $position->getMarginType()->value
        );

        return $this->redis->hExists(
            MarginConfigKey::getCurrencyPositionsKey($currencyId),
            $positionKey
        );
    }

    /**
     * 将仓位加入监控（带原因记录）
     */
    public function addPositionToMonitoring(TradeMarginPosition $position, string $reason = ''): void
    {
        $this->syncPositionToCache($position);

        $this->logger->info('仓位加入风险监控', [
            'position_id' => $position->getId(),
            'user_id' => $position->getUserId(),
            'currency_id' => $position->getCurrencyId(),
            'reason' => $reason,
            'leverage' => $position->getLeverage()
        ]);
    }

    /**
     * 同步仓位到缓存（包含维持保证金率信息）
     */
    public function syncPositionToCacheWithMarginBracket(TradeMarginPosition $position, ?object $marginBracket): void
    {
        if ($position->getStatus() !== MarginPositionStatus::HOLDING) {
            return; // 只同步持仓中的仓位
        }

        $currencyId = $position->getCurrencyId();
        $positionKey = MarginConfigKey::buildPositionKey(
            $position->getUserId(),
            $position->getSide()->value,
            $position->getMarginType()->value
        );

        $positionData = $this->buildPositionCacheDataWithMarginBracket($position, $marginBracket);

        // 获取同步锁
        $lockKey = MarginConfigKey::getSyncLockKey($currencyId);
        $lockAcquired = $this->redis->set($lockKey, '1', ['NX', 'EX' => 10]);

        if (!$lockAcquired) {
            $this->logger->warning('获取仓位同步锁失败', ['currency_id' => $currencyId]);
            return;
        }

        try {
            // 存储到币种仓位Hash
            $this->redis->hSet(
                MarginConfigKey::getCurrencyPositionsKey($currencyId),
                $positionKey,
                json_encode($positionData)
            );

            // 更新币种监控状态
            $this->updateCurrencyMonitorStatus($currencyId);

            $this->logger->info('仓位数据同步到缓存成功（含维持保证金率）', [
                'position_id' => $position->getId(),
                'currency_id' => $currencyId,
                'user_id' => $position->getUserId(),
                'position_key' => $positionKey,
                'keep_rate' => $marginBracket?->getKeepRate(),
                'margin_bracket_level' => $marginBracket?->getLevel()
            ]);

        } finally {
            // 释放锁
            $this->redis->del($lockKey);
        }
    }

    /**
     * 更新币种监控状态
     */
    protected function updateCurrencyMonitorStatus(int $currencyId): void
    {
        $positionCount = $this->redis->hLen(
            MarginConfigKey::getCurrencyPositionsKey($currencyId)
        );

        $this->redis->hSet(
            MarginConfigKey::CURRENCY_MONITOR,
            (string)$currencyId,
            json_encode([
                'position_count' => $positionCount,
                'last_update' => time()
            ])
        );
    }
}
