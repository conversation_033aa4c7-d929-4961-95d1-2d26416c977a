<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆自动借款服务
 */

namespace App\Http\Api\Service\V1\Margin;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\Trade\Margin\PositionSide;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Currency\CurrencyMarginBorrow;
use App\Model\User\UserVipLevel;
use App\Model\User\UserAccountsAsset;
use App\Model\User\UserMarginBorrow;
use App\Model\Enums\Trade\Margin\MarginBorrowStatus;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Psr\Log\LoggerInterface;
use Hyperf\Logger\LoggerFactory;

class MarginAutoBorrowService
{
    #[Inject]
    protected UserAccountsAssetService $userAccountsAssetService;

    #[Inject]
    protected MarginRiskService $marginRiskService;

    #[Inject]
    protected MarginAssetCalculator $marginAssetCalculator;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('margin-auto-borrow');
    }

    /**
     * 计算用户可借额度
     *
     * @param int $userId 用户ID
     * @param int $currencyId 币种ID（基础币）
     * @param MarginType $marginType 保证金类型
     * @return array 返回可借额度信息
     */
    public function calculateBorrowLimit(int $userId, int $currencyId, MarginType $marginType): array
    {
        try {
            $accountType = $marginType === MarginType::CROSS ? AccountType::MARGIN : AccountType::ISOLATED;
            $quoteCurrencyId = $this->marginAssetCalculator->getQuoteCurrencyId($currencyId);

            // 1. 获取杠杆档位信息
            $bracket = $this->marginRiskService->getMarginBracket($userId, $currencyId, $marginType);
            $maxLeverage = $bracket->getMaxLever();

            // 2. 获取用户当前资产
            $baseAsset = $this->userAccountsAssetService->getUserAsset($userId, $accountType->value, $currencyId);

            $baseAvailable = $baseAsset ? $baseAsset->getAvailable() : 0.0;
            $baseBorrowed = $baseAsset ? $baseAsset->getBorrowedAmount() : 0.0;

            if ($marginType === MarginType::CROSS) {
                // 全仓：获取独立的计价币资产记录
                $quoteAsset = $this->userAccountsAssetService->getUserAsset($userId, $accountType->value, $quoteCurrencyId);
                $quoteAvailable = $quoteAsset ? $quoteAsset->getAvailable() : 0.0;
                $quoteBorrowed = $quoteAsset ? $quoteAsset->getBorrowedAmount() : 0.0;
            } else {
                // 逐仓：从基础币记录的margin_quote字段获取计价币余额
                $quoteAvailable = $baseAsset ? $baseAsset->getMarginQuote() : 0.0;
                // 逐仓：从基础币记录的margin_borrow字段获取计价币借款
                $quoteBorrowed = $baseAsset ? $baseAsset->getMarginBorrow() : 0.0;
            }

            // 3. 获取用户VIP等级借款限额
            $vipLimits = $this->getUserVipBorrowLimits($userId, $currencyId, $marginType);

            // 4. 计算理论最大可借额度
            // 基础币最大可借 = min(基础币资产 * 杠杆 - 基础币资产 * 0.9, VIP基础币限额) - 已借基础币
            $baseTheoreticalMax = $baseAvailable > 0 
                ? bcmul((string)$baseAvailable, (string)$maxLeverage, 8) 
                : '0';
            $baseBuffer = bcmul((string)$baseAvailable, '0.9', 8);
            $baseMaxBeforeBorrowed = bcsub($baseTheoreticalMax, $baseBuffer, 8);
            $baseMaxBorrow = bcmul(
                bccomp($baseMaxBeforeBorrowed, $this->formatNumberForBC($vipLimits['base_limit']), 8) <= 0
                    ? $baseMaxBeforeBorrowed
                    : $this->formatNumberForBC($vipLimits['base_limit']),
                '1', 8
            );
            $baseCurrentBorrowable = bcsub($baseMaxBorrow, (string)$baseBorrowed, 8);
            $baseCurrentBorrowable = bccomp($baseCurrentBorrowable, '0', 8) > 0 ? $baseCurrentBorrowable : '0';

            // 计价币最大可借 = min(计价币资产 * 杠杆 - 计价币资产 * 0.9, VIP计价币限额) - 已借计价币
            $quoteTheoreticalMax = $quoteAvailable > 0 
                ? bcmul((string)$quoteAvailable, (string)$maxLeverage, 8) 
                : '0';
            $quoteBuffer = bcmul((string)$quoteAvailable, '0.9', 8);
            $quoteMaxBeforeBorrowed = bcsub($quoteTheoreticalMax, $quoteBuffer, 8);
            $quoteMaxBorrow = bcmul(
                bccomp($quoteMaxBeforeBorrowed, $this->formatNumberForBC($vipLimits['quote_limit']), 8) <= 0
                    ? $quoteMaxBeforeBorrowed
                    : $this->formatNumberForBC($vipLimits['quote_limit']),
                '1', 8
            );
            $quoteCurrentBorrowable = bcsub($quoteMaxBorrow, (string)$quoteBorrowed, 8);
            $quoteCurrentBorrowable = bccomp($quoteCurrentBorrowable, '0', 8) > 0 ? $quoteCurrentBorrowable : '0';

            $result = [
                'user_id' => $userId,
                'base_currency_id' => $currencyId,
                'quote_currency_id' => $quoteCurrencyId,
                'margin_type' => $marginType->value,
                'account_type' => $accountType->value,
                'max_leverage' => $maxLeverage,
                'base' => [
                    'available' => (float)$this->formatNumberForBC($baseAvailable),
                    'borrowed' => (float)$this->formatNumberForBC($baseBorrowed),
                    'vip_limit' => $vipLimits['base_limit'],
                    'theoretical_max' => (float)$baseMaxBorrow,
                    'current_borrowable' => (float)$baseCurrentBorrowable,
                ],
                'quote' => [
                    'available' => (float)$this->formatNumberForBC($quoteAvailable),
                    'borrowed' => (float)$this->formatNumberForBC($quoteBorrowed),
                    'vip_limit' => $vipLimits['quote_limit'],
                    'theoretical_max' => (float)$quoteMaxBorrow,
                    'current_borrowable' => (float)$quoteCurrentBorrowable,
                ]
            ];

            $this->logger->info('计算可借额度成功', $result);

            return $result;

        } catch (\Exception $e) {
            $this->logger->error('计算可借额度失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'margin_type' => $marginType->value,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '计算可借额度失败：' . $e->getMessage());
        }
    }

    /**
     * 自动借款（如果需要）
     *
     * @param int $userId 用户ID
     * @param int $currencyId 币种ID（基础币）
     * @param MarginType $marginType 保证金类型
     * @param PositionSide $side 仓位方向
     * @param float $quantity 下单数量
     * @param float|null $price 下单价格
     * @param string $orderType 订单类型
     * @return array 借款结果
     */
    public function autoBorrowIfNeeded(
        int $userId,
        int $currencyId,
        MarginType $marginType,
        PositionSide $side,
        float $quantity,
        ?float $price,
        string $orderType
    ): array {
        try {
            // 1. 获取可借额度信息
            $borrowLimits = $this->calculateBorrowLimit($userId, $currencyId, $marginType);
            $accountType = AccountType::from($borrowLimits['account_type']);
            // 2. 计算需要的资金和币种
            $needBorrowData = $this->calculateBorrowNeed(
                $borrowLimits,
                $side,
                $quantity,
                $price,
                $orderType
            );

            if (!$needBorrowData['need_borrow']) {
                return [
                    'borrowed' => false,
                    'message' => '资金充足，无需借款'
                ];
            }

            // 3. 检查可借额度是否充足
            $borrowCurrencyId = $needBorrowData['borrow_currency_id'];
            $borrowAmount = $needBorrowData['borrow_amount'];
            $borrowCurrencyType = $needBorrowData['currency_type']; // 'base' or 'quote'

            $availableBorrowLimit = $borrowLimits[$borrowCurrencyType]['current_borrowable'];

            if (bccomp((string)$borrowAmount, (string)$availableBorrowLimit, 8) > 0) {
                throw new BusinessException(
                    ResultCode::FAIL, 
                    "自动借款金额超过可借限额。需要：{$borrowAmount}，可借：{$availableBorrowLimit}"
                );
            }

            // 4. 执行自动借款
            $borrowResult = $this->executeBorrow(
                $userId,
                $borrowCurrencyId,
                $marginType,
                $borrowAmount,
                $currencyId // 基础币ID用于配置查询
            );

            $this->logger->info('自动借款成功', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'side' => $side->value,
                'borrow_currency_id' => $borrowCurrencyId,
                'borrow_amount' => $borrowAmount,
                'borrow_record_id' => $borrowResult['borrow_record_id']
            ]);

            return [
                'borrowed' => true,
                'borrow_currency_id' => $borrowCurrencyId,
                'borrow_amount' => $borrowAmount,
                'borrow_record_id' => $borrowResult['borrow_record_id'],
                'daily_rate' => $borrowResult['daily_rate'],
                'message' => "自动借款成功：{$borrowAmount}"
            ];

        } catch (\Exception $e) {
            $this->logger->error('自动借款失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'margin_type' => $marginType->value,
                'side' => $side->value,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 格式化数字为BC数学库可用的字符串格式
     * 只处理科学计数法等特殊格式，避免破坏精确数字
     */
    private function formatNumberForBC($number): string
    {
        if ($number === null) {
            return '0';
        }

        $str = (string)$number;

        // 检查是否包含科学计数法（E或e）
        if (stripos($str, 'e') !== false) {
            // 只有科学计数法才使用sprintf转换
            $formatted = sprintf('%.18f', (float)$number);
            // 移除末尾的零和小数点
            $formatted = rtrim($formatted, '0');
            $formatted = rtrim($formatted, '.');
            return $formatted === '' || $formatted === '-' ? '0' : $formatted;
        }

        // 普通数字直接返回字符串形式
        return $str;
    }

    /**
     * 获取用户VIP等级借款限额
     */
    private function getUserVipBorrowLimits(int $userId, int $currencyId, MarginType $marginType): array
    {
        // 获取用户VIP等级
        $userVipLevel = UserVipLevel::getUserActiveVipLevel($userId);
        $vipLevel = $userVipLevel && $userVipLevel->vipLevel ? $userVipLevel->vipLevel->getLevel() : 0;

        // 获取借款配置
        $borrowConfig = CurrencyMarginBorrow::query()
            ->where('currency_id', $currencyId)
            ->where('is_cross', $marginType->value)
            ->first();

        if (!$borrowConfig) {
            throw new BusinessException(ResultCode::FAIL, '该币种不支持杠杆借贷');
        }

        $baseLimits = $borrowConfig->getBaseLevel();
        $quoteLimits = $borrowConfig->getQuoteLevel();

        $baseLimit = 0;
        $quoteLimit = 0;

        if ($marginType === MarginType::CROSS) {
            // 全仓：使用 vipLevel 匹配
            foreach ($baseLimits as $level) {
                if ($level['vipLevel'] == $vipLevel) {
                    $baseLimit = $level['borrowLimit'] ?? 0;
                    $quoteLimit = $level['borrowLimit'] ?? 0; // 全仓基础币和计价币使用相同限额
                    break;
                }
            }
        } else {
            // 逐仓：使用 level 匹配
            foreach ($baseLimits as $level) {
                if ($level['level'] == $vipLevel) {
                    $baseLimit = $level['maxBorrowable'] ?? 0;
                    break;
                }
            }
            foreach ($quoteLimits as $level) {
                if ($level['level'] == $vipLevel) {
                    $quoteLimit = $level['maxBorrowable'] ?? 0;
                    break;
                }
            }
        }

        return [
            'vip_level' => $vipLevel,
            'base_limit' => (float)$baseLimit,
            'quote_limit' => (float)$quoteLimit,
        ];
    }

    /**
     * 计算借款需求
     */
    private function calculateBorrowNeed(
        array $borrowLimits,
        PositionSide $side,
        float $quantity,
        ?float $price,
        string $orderType
    ): array {
        if ($side === PositionSide::LONG) {
            // 买入（做多）：需要计价币
            $requiredCurrency = 'quote';
            $requiredCurrencyId = $borrowLimits['quote_currency_id'];
            $availableAmount = $borrowLimits['quote']['available'];

            if ($orderType === 'market') {
                // 市价买单：估算所需金额（使用当前价格 + 1%缓冲）
                $estimatedPrice = $this->marginAssetCalculator->getCurrencyPrice($borrowLimits['base_currency_id']);
                $baseAmount = bcmul((string)$quantity, (string)$estimatedPrice, 8);
                $bufferAmount = bcmul($baseAmount, '0.01', 8);
                $requiredAmount = (float)bcadd($baseAmount, $bufferAmount, 8);
            } else {
                // 限价买单：数量 * 价格
                $requiredAmount = (float)bcmul((string)$quantity, (string)$price, 8);
            }
        } else {
            // 卖出（做空）：需要基础币
            $requiredCurrency = 'base';
            $requiredCurrencyId = $borrowLimits['base_currency_id'];
            $availableAmount = $borrowLimits['base']['available'];
            $requiredAmount = $quantity;
        }
        // 检查是否需要借款
        $requiredAmountStr = $this->formatNumberForBC($requiredAmount);
        $availableAmountStr = $this->formatNumberForBC($availableAmount);
        $needBorrow = bccomp($requiredAmountStr, $availableAmountStr, 8) > 0;
        //echo "requiredAmountStr:{$requiredAmountStr} - availableAmountStr:{$availableAmountStr} ";
        $borrowAmount = $needBorrow
            ? (float)bcsub($requiredAmountStr, $availableAmountStr, 8)
            : 0.0;
        return [
            'need_borrow' => $needBorrow,
            'currency_type' => $requiredCurrency,
            'borrow_currency_id' => $requiredCurrencyId,
            'required_amount' => $requiredAmount,
            'available_amount' => $availableAmount,
            'borrow_amount' => $borrowAmount,
        ];
    }

    /**
     * 执行借款操作
     */
    private function executeBorrow(
        int $userId,
        int $currencyId,
        MarginType $marginType,
        float $borrowAmount,
        int $baseCurrencyId
    ): array {
        return Db::transaction(function () use ($userId, $currencyId, $marginType, $borrowAmount, $baseCurrencyId) {
            // 1. 获取借款配置和利率
            $borrowConfig = CurrencyMarginBorrow::query()
                ->where('currency_id', $baseCurrencyId) // 使用基础币ID查询配置
                ->where('is_cross', $marginType->value)
                ->first();

            if (!$borrowConfig) {
                throw new BusinessException(ResultCode::FAIL, '借款配置不存在');
            }

            $userVipLevel = UserVipLevel::getUserActiveVipLevel($userId);
            $vipLevel = $userVipLevel && $userVipLevel->vipLevel ? $userVipLevel->vipLevel->getLevel() : 0;

            // 获取对应VIP等级的借贷利率
            $dailyRate = $this->getBorrowRate($borrowConfig, $marginType, $vipLevel, $currencyId, $baseCurrencyId);

            $accountType = $marginType === MarginType::CROSS ? AccountType::MARGIN : AccountType::ISOLATED;

            // 2. 创建借款记录
            $borrowRecord = new UserMarginBorrow();
            $borrowRecord->setUserId($userId);
            $borrowRecord->setCurrencyId($currencyId); // 实际借款的币种ID
            $borrowRecord->setAccountType($accountType->value);
            $borrowRecord->setBorrowAmount($borrowAmount);
            $borrowRecord->setRepaidAmount(0);
            $borrowRecord->setDailyRate($dailyRate);
            $borrowRecord->setBorrowSource(1); // 1-自动借款
            $borrowRecord->setBorrowTime(\Carbon\Carbon::now());
            $borrowRecord->setLastInterestTime(\Carbon\Carbon::now());
            $borrowRecord->setStatus(MarginBorrowStatus::ACTIVE);
            $borrowRecord->save();

            // 3. 更新用户资产

            // 更新用户资产
            if($accountType === AccountType::MARGIN){
                $result = $this->userAccountsAssetService->addBorrowedAmount(
                    $userId,
                    $accountType->value,
                    $currencyId, // 实际借款的币种ID
                    $borrowAmount,
                    FlowsType::MARGIN_BORROW->value,
                    $borrowRecord->getId(), // 关联借款记录ID
                    UserAccountsAsset::FIELD_BORROWED_AMOUNT ,
                    UserAccountsAsset::FIELD_AVAILABLE
                );
            }else{
                $borrowAssetsField = $baseCurrencyId == $currencyId ? UserAccountsAsset::FIELD_BORROWED_AMOUNT : UserAccountsAsset::FIELD_MARGIN_BORROW;
                $availableAssetsField = $baseCurrencyId == $currencyId ? UserAccountsAsset::FIELD_AVAILABLE : UserAccountsAsset::FIELD_MARGIN_QUOTE;
                $result = $this->userAccountsAssetService->addBorrowedAmount(
                    $userId,
                    $accountType->value,
                    $baseCurrencyId, // 实际借款的币种ID
                    $borrowAmount,
                    FlowsType::MARGIN_BORROW->value,
                    $borrowRecord->getId(), // 关联借款记录ID
                    $borrowAssetsField,
                    $availableAssetsField
                );
            }

//            $result = $this->userAccountsAssetService->addBorrowedAmount(
//                $userId,
//                $accountType->value,
//                $currencyId,
//                $borrowAmount,
//                FlowsType::MARGIN_AUTO_BORROW->value,
//                $borrowRecord->getId()
//            );

            if (!$result) {
                throw new BusinessException(ResultCode::FAIL, '自动借款操作失败');
            }

            $this->logger->info('自动借款执行成功', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'base_currency_id' => $baseCurrencyId,
                'margin_type' => $marginType->value,
                'borrow_amount' => $borrowAmount,
                'borrow_record_id' => $borrowRecord->getId(),
                'daily_rate' => $dailyRate,
            ]);

            return [
                'borrow_record_id' => $borrowRecord->getId(),
                'daily_rate' => $dailyRate,
                'borrow_time' => $borrowRecord->getBorrowTime(),
            ];
        });
    }

    /**
     * 获取借款利率
     */
    private function getBorrowRate(
        CurrencyMarginBorrow $borrowConfig,
        MarginType $marginType,
        int $vipLevel,
        int $borrowCurrencyId,
        int $baseCurrencyId
    ): float {
        $dailyRate = 0;

        if ($marginType === MarginType::CROSS) {
            // 全仓
            $baseLevel = $borrowConfig->getBaseLevel();
            foreach ($baseLevel as $level) {
                if ($level['vipLevel'] == $vipLevel) {
                    $dailyRate = $level['dailyInterestRate'];
                    break;
                }
            }
        } else {
            // 逐仓：根据借款币种类型选择配置
            if ($borrowConfig->getIsBaseBorrow() && $borrowCurrencyId === $baseCurrencyId) {
                // 借基础币
                $baseLevel = $borrowConfig->getBaseLevel();
                foreach ($baseLevel as $level) {
                    if ($level['level'] == $vipLevel) {
                        $dailyRate = $level['interestRate'];
                        break;
                    }
                }
            } elseif ($borrowConfig->getIsQuoteBorrow()) {
                // 借计价币
                $quoteLevel = $borrowConfig->getQuoteLevel();
                foreach ($quoteLevel as $level) {
                    if ($level['level'] == $vipLevel) {
                        $dailyRate = $level['interestRate'];
                        break;
                    }
                }
            }
        }

        if ($dailyRate <= 0) {
            throw new BusinessException(ResultCode::FAIL, '未找到适用的借贷利率配置');
        }

        return (float)$dailyRate;
    }
} 