<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆风险控制服务
 */

namespace App\Http\Api\Service\V1\Margin;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\Trade\TradeMarginBracket;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\Trade\Margin\PositionSide;
use App\Model\Enums\User\AccountType;
use App\Service\UserAccounts\UserAccountsAssetService;

class MarginRiskService
{
    private UserAccountsAssetService $userAccountsAssetService;
    private MarginAssetCalculator $marginAssetCalculator;

    public function __construct(
        UserAccountsAssetService $userAccountsAssetService,
        MarginAssetCalculator $marginAssetCalculator
    ) {
        $this->userAccountsAssetService = $userAccountsAssetService;
        $this->marginAssetCalculator = $marginAssetCalculator;
    }

    /**
     * 验证订单参数并返回档位数据
     *
     * @param int $userId 用户ID
     * @param int $currencyId 币种ID
     * @param PositionSide $side 仓位方向
     * @param MarginType $marginType 杠杆类型
     * @param float $leverage 杠杆倍数
     * @param float $quantity 数量
     * @param float $price 价格
     * @return TradeMarginBracket 档位数据
     * @throws BusinessException
     */
    public function validateOrderParams(int $userId, int $currencyId, PositionSide $side, MarginType $marginType, float $leverage, float $quantity, float $price): TradeMarginBracket
    {
        // 1. 优先验证用户基础资产余额
        //$this->validateUserAssetBalance($userId, $currencyId, $side, $marginType, $quantity, $price, $leverage);
        
        // 2. 获取用户对应的档位数据
        $bracket = $this->getMarginBracket($userId, $currencyId, $marginType);
        
        // 3. 验证杠杆倍数
        $this->validateLeverageWithBracket($userId, $currencyId, $marginType, $leverage, $quantity, $price, $bracket);
        
        return $bracket;
    }

    /**
     * 验证用户基础资产余额
     */
    private function validateUserAssetBalance(int $userId, int $currencyId, PositionSide $side, MarginType $marginType, float $quantity, float $price, float $leverage): void
    {
        $accountType = $marginType === MarginType::CROSS ? AccountType::MARGIN : AccountType::ISOLATED;

        // 杠杆交易中，不管做多做空，都需要计价币作为保证金
        $quoteCurrencyId = $this->marginAssetCalculator->getQuoteCurrencyId($currencyId);
        $assetName = '计价币';

        // 获取计价币可用余额
        $availableAmount = 0.0;

        if ($marginType === MarginType::CROSS) {
            // 全仓：获取独立的计价币资产记录
            $userAsset = $this->userAccountsAssetService->getUserAsset($userId, $accountType->value, $quoteCurrencyId);
            if (!$userAsset) {
                throw new BusinessException(ResultCode::FAIL, "请先划转{$assetName}资金到杠杆账户");
            }
            $availableAmount = $userAsset->getAvailable();
        } else {
            // 逐仓：从基础币记录的margin_quote字段获取计价币余额
            $baseAsset = $this->userAccountsAssetService->getUserAsset($userId, $accountType->value, $currencyId);
            if (!$baseAsset) {
                throw new BusinessException(ResultCode::FAIL, "请先划转资金到杠杆账户");
            }
            $availableAmount = $baseAsset->getMarginQuote();
        }

        if (bccomp((string)$availableAmount, '0', 18) <= 0) {
            throw new BusinessException(ResultCode::FAIL, "杠杆账户{$assetName}余额不足，请先划转资金");
        }

        // 计算开仓所需的最小保证金（不考虑风险档位，使用基础计算）
        $positionValue = bcmul((string)$quantity, (string)$price, 18); // 仓位价值
        $baseMargin = bcdiv($positionValue, (string)$leverage, 18); // 基础保证金

        // 检查用户是否有足够的可用余额作为基础保证金
        if (bccomp((string)$availableAmount, $baseMargin, 18) < 0) {
            throw new BusinessException(
                ResultCode::FAIL,
                "杠杆账户{$assetName}余额不足，当前可用: {$availableAmount}，开仓至少需要: " . (float)$baseMargin
            );
        }
    }

    /**
     * 使用已获取的档位数据验证杠杆倍数
     */
    private function validateLeverageWithBracket(int $userId, int $currencyId, MarginType $marginType, float $leverage, float $quantity, float $price, TradeMarginBracket $bracket): void
    {
        if ($leverage <= 0) {
            throw new BusinessException(ResultCode::FAIL, '杠杆倍数必须大于0');
        }

        // 使用档位数据中的最大杠杆倍数
        $maxLeverage = $bracket->getMaxLever();
        
        if ($leverage > $maxLeverage) {
            throw new BusinessException(ResultCode::FAIL, "杠杆倍数不能超过{$maxLeverage}倍");
        }

        // 根据用户资金情况验证可开的杠杆倍数
        $this->validateLeverageByUserAssetsWithBracket($userId, $currencyId, $marginType, $leverage, $quantity, $price, $bracket);
    }

    /**
     * 使用已获取的档位数据验证用户资金情况
     */
    private function validateLeverageByUserAssetsWithBracket(int $userId, int $currencyId, MarginType $marginType, float $leverage, float $quantity, float $price, TradeMarginBracket $bracket): void
    {
        // 计算开仓所需的初始保证金
        $positionValue = $quantity * $price;
        $requiredMargin = $positionValue / $leverage;
        
        // 使用传入的档位数据
        $initMarginRate = $bracket->getInitRate();
        $totalRequiredMargin = $requiredMargin + ($requiredMargin * $initMarginRate);
        
        // 获取用户可用资金
        $availableFunds = $this->marginAssetCalculator->getUserAvailableFunds($userId, $currencyId, $marginType);
        
        if (bccomp((string)$availableFunds, (string)$totalRequiredMargin, 18) < 0) {
            $maxAffordableLeverage = $this->calculateMaxAffordableLeverage($availableFunds, $quantity, $price, $initMarginRate);
            throw new BusinessException(ResultCode::FAIL, "资金不足，当前最大可开杠杆倍数为{$maxAffordableLeverage}倍");
        }
    }

    /**
     * 计算最大可承受的杠杆倍数
     */
    private function calculateMaxAffordableLeverage(float $availableFunds, float $quantity, float $price, float $initMarginRate): float
    {
        $positionValue = $quantity * $price;
        
        // 计算公式：可用资金 = (仓位价值 / 杠杆) * (1 + 初始保证金比例)
        // 推导出：杠杆 = 仓位价值 / (可用资金 / (1 + 初始保证金比例))
        $denominator = bcdiv((string)$availableFunds, (string)(1 + $initMarginRate), 18);
        $maxLeverage = bcdiv((string)$positionValue, $denominator, 2);
        
        return (float)$maxLeverage;
    }

    public function calculateMaintenanceMargin(int $userId, int $currencyId, float $quantity, float $price, MarginType $marginType): float
    {
        $bracket = $this->getMarginBracket($userId, $currencyId, $marginType);
        return ($quantity * $price) * $bracket->keep_rate;
    }

    public function calculateLiquidationPrice(float $entryPrice, float $leverage, PositionSide $side, float $maintenanceMarginRate): float
    {
        if ($side === PositionSide::LONG) {
            return $entryPrice * (1 - (1 / $leverage) + $maintenanceMarginRate);
        } else {
            return $entryPrice * (1 + (1 / $leverage) - $maintenanceMarginRate);
        }
    }

    protected function getMaxLeverage(int $currencyId, MarginType $marginType): float
    {
        // 获取该币种的最大杠杆配置
        $isCross = $marginType === MarginType::CROSS ? 1 : 2;
        
        $bracket = TradeMarginBracket::where('currency_id', $currencyId)
            ->where('is_cross', $isCross)
            ->orderBy('level', 'desc')
            ->first();
        
        if (!$bracket) {
            throw new BusinessException(ResultCode::FAIL, '该币种不支持杠杆交易');
        }

        return (float)$bracket->max_lever;
    }

    /**
     * 根据用户杠杆资产获取对应的档位配置
     * 
     * @param int $userId 用户ID
     * @param int $currencyId 币种ID
     * @param MarginType $marginType 杠杆类型
     * @return TradeMarginBracket
     * @throws BusinessException
     */
    public function getMarginBracket(int $userId, int $currencyId, MarginType $marginType): TradeMarginBracket
    {
        $isCross = $marginType === MarginType::CROSS ? 1 : 2;
        
        // 获取用户杠杆账户的总资产价值
        $userAssetValue = $this->marginAssetCalculator->getUserMarginAssetValue($userId, $currencyId, $marginType);
        if($marginType === MarginType::CROSS){
            $bracket = TradeMarginBracket::query()->where('currency_id',$currencyId)
                ->where('is_cross',$isCross)
                ->where('hold_start','<=',$userAssetValue)
                ->where('hold_end','>=',$userAssetValue)
                ->orderByDesc('level')
                ->first();
            if($bracket){
                return $bracket;
            }
        }
        $bracket = TradeMarginBracket::query()->where('currency_id',$currencyId)
            ->where('is_cross',$isCross)
            ->where('hold_start','<=',$userAssetValue)
            ->where('hold_end','>=',$userAssetValue)
            ->orderByDesc('level')
            ->first();

        // 如果没有找到对应档位，使用默认的 level = 1 档位
        if (!$bracket) {
            $bracket = TradeMarginBracket::where('currency_id', $currencyId)
                ->where('is_cross', $isCross)
                ->where('level', 1)
                ->first();
        }

        if (!$bracket) {
            throw new BusinessException(ResultCode::FAIL, '未找到对应的风险档位配置');
        }

        return $bracket;
    }
} 