<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆交易核心服务
 */

namespace App\Http\Api\Service\V1\Margin;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\Trade\TradeMarginOrder;
use App\Model\Trade\TradeMarginPosition;
use App\Model\Match\MatchOrder;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\Trade\Margin\PositionSide;
use App\Model\Enums\Trade\Margin\ReduceOnly;
use App\Service\MatchEngineOrderService;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use App\Enum\Config\MarginConfigKey;
use App\Model\Currency\CurrencyMarginBorrow;
use App\Model\User\UserVipLevel;
use App\Model\User\UserAccountsAsset;
use App\Model\User\UserMarginBorrow;
use App\Model\Enums\User\AccountType;
use App\Http\Api\Service\V1\Margin\MarginPositionCacheService;
use App\Job\Margin\PositionMonitoringJob;
use Hyperf\AsyncQueue\Driver\DriverFactory;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use App\Model\Enums\User\FlowsType;
use App\Model\Trade\TradeMarginBracket;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Logger\LoggerFactory;
use App\Model\Enums\Trade\Margin\MarginBorrowStatus;
use App\Model\User\UserMarginInterest;
use App\Enum\MarketType;
use App\Enum\CurrencyConfigKey;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\OrderType;
use App\Enum\TradeSide;
use App\Http\Api\Service\V1\Margin\MarginAssetCalculator;

class TradeMarginService
{
    #[Inject]
    protected MarginRiskService $marginRiskService;

    protected MatchEngineOrderService $matchEngineService;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected LoggerInterface $logger;

    #[Inject]
    protected UserAccountsAssetService $userAccountsAssetService;

    #[Inject]
    protected \Hyperf\Snowflake\IdGeneratorInterface $idGenerator;

    #[Inject]
    protected MarginAssetCalculator $marginAssetCalculator;

    #[Inject]
    protected MarginAutoBorrowService $marginAutoBorrowService;

    #[Inject]
    protected MarginPositionCacheService $positionCacheService;

    #[Inject]
    protected DriverFactory $driverFactory;

    public function __construct(LoggerFactory $loggerFactory,ContainerInterface $container)
    {
        $this->matchEngineService = $container->get(MatchEngineOrderService::class);
        $this->logger = $loggerFactory->get('trade-margin');
    }

    public function createMarginOrder(array $params): TradeMarginOrder
    {
        // 严格类型转换
        $userId = (int)$params['user_id'];
        $currencyId = (int)$params['currency_id'];
        $side = PositionSide::from((int)$params['side']);
        $marginType = MarginType::from((int)$params['margin_type']);
        $leverage = (float)$params['leverage'];
        $quantity = (float)$params['quantity'];
        $price = isset($params['price']) ? (float)$params['price'] : null;
        $orderType = (string)$params['order_type'];

        return Db::transaction(function () use ($userId, $currencyId, $side, $marginType, $leverage, $quantity, $price, $orderType) {
            // 1. 基础验证和风险控制，获取档位数据
            $bracket = $this->marginRiskService->validateOrderParams(
                $userId, 
                $currencyId, 
                $side, 
                $marginType, 
                $leverage, 
                $quantity, 
                $price ?? 0.0
            );

            // 2. 计算所需保证金，使用已获取的档位数据
            $marginAmount = $this->calculateMarginAmountWithBracket(
                $quantity, 
                $price ?? 0.0, 
                $leverage, 
                $bracket
            );

            // 3. 检查资金充足性并自动借款（如果需要）
            $borrowResult = $this->marginAutoBorrowService->autoBorrowIfNeeded(
                $userId,
                $currencyId,
                $marginType,
                $side,
                $quantity,
                $price,
                $orderType
            );

            // 4. 计算和冻结资金（包含借款后的资金）
            $freezeData = $this->calculateAndFreezeMarginAssets(
                $userId, 
                $currencyId, 
                $side, 
                $marginType, 
                $quantity, 
                $price, 
                $orderType
            );

            // 5. 创建撮合引擎订单
            $matchOrder = $this->createMatchOrder(
                $userId, 
                $currencyId, 
                $side->value, 
                $quantity, 
                $price, 
                $orderType
            );

            // 6. 创建杠杆订单记录
            $marginOrder = new TradeMarginOrder();
            $marginOrder->setUserId($userId);
            $marginOrder->setCurrencyId($currencyId);
            $marginOrder->setMatchOrderId($matchOrder->getId());
            $marginOrder->setMarginType($marginType->value);
            $marginOrder->setLeverage($leverage);
            $marginOrder->setPositionSide($side->value);
            $marginOrder->setMarginAmount($marginAmount);
            $marginOrder->setReduceOnly(ReduceOnly::FALSE->value);
            $marginOrder->setFrozenAmount($freezeData['freeze_amount']);
            $marginOrder->setUsedAmount(0.0);
            $marginOrder->save();

            // 7. 提交到撮合引擎
            $this->submitOrderToMatchEngine($matchOrder, $currencyId);

            $logData = [
                'user_id' => $userId,
                'margin_order_id' => $marginOrder->getId(),
                'match_order_id' => $matchOrder->getId(),
                'freeze_data' => $freezeData,
            ];

            // 如果有自动借款，添加到日志中
            if ($borrowResult['borrowed']) {
                $logData['auto_borrow'] = [
                    'currency_id' => $borrowResult['borrow_currency_id'],
                    'amount' => $borrowResult['borrow_amount'],
                    'record_id' => $borrowResult['borrow_record_id'],
                    'daily_rate' => $borrowResult['daily_rate'],
                ];
            }

            $this->logger->info('杠杆订单创建成功', $logData);

            return $marginOrder;
        });
    }

    public function cancelMarginOrder(int $userId, int $orderId): bool
    {
        $marginOrder = TradeMarginOrder::where('user_id', $userId)
            ->where('id', $orderId)
            ->first();

        if (!$marginOrder) {
            throw new BusinessException(ResultCode::FAIL, '杠杆订单不存在');
        }

        return Db::transaction(function () use ($marginOrder) {
            // 1. 查找撮合引擎订单
            $matchOrder = MatchOrder::find($marginOrder->getMatchOrderId());
            if (!$matchOrder) {
                throw new BusinessException(ResultCode::FAIL, '撮合引擎订单不存在');
            }

            // 2. 检查订单状态是否可以撤单
            if (!in_array($matchOrder->status, [\App\Enum\OrderStatus::CREATED->value, \App\Enum\OrderStatus::PENDING->value])) {
                throw new BusinessException(ResultCode::FAIL, '订单状态不允许撤单，当前状态：' . $matchOrder->status);
            }

            // 3. 提交撤单到撮合引擎
            $symbol = $this->getCurrencySymbol($marginOrder->getCurrencyId());
            $success = $this->matchEngineService->cancelOrder(
                \App\Enum\MarketType::CRYPTO->value,
                $symbol,
                $matchOrder->order_id,
                $marginOrder->getUserId()
            );

            if (!$success) {
                throw new BusinessException(ResultCode::FAIL, '提交撤单请求失败');
            }

            $this->logger->info('杠杆撤单请求已提交', [
                'user_id' => $marginOrder->getUserId(),
                'margin_order_id' => $marginOrder->getId(),
                'match_order_id' => $matchOrder->order_id,
            ]);

            return true;
        });
    }

    /**
     * 修改杠杆订单
     */
    public function modifyMarginOrder(int $userId, int $orderId, array $modifyData): TradeMarginOrder
    {
        $marginOrder = TradeMarginOrder::where('user_id', $userId)
            ->where('id', $orderId)
            ->first();

        if (!$marginOrder) {
            throw new BusinessException(ResultCode::FAIL, '杠杆订单不存在');
        }

        return Db::transaction(function () use ($marginOrder, $modifyData) {
            // 1. 查找撮合引擎订单
            $matchOrder = MatchOrder::find($marginOrder->getMatchOrderId());
            if (!$matchOrder) {
                throw new BusinessException(ResultCode::FAIL, '撮合引擎订单不存在');
            }

            // 2. 检查订单状态是否可以修改
            if (!in_array($matchOrder->status, [\App\Enum\OrderStatus::CREATED->value, \App\Enum\OrderStatus::PENDING->value])) {
                throw new BusinessException(ResultCode::FAIL, '订单状态不允许修改，当前状态：' . $matchOrder->status);
            }

            // 3. 检查是否有部分成交
            if (bccomp($matchOrder->fill_quantity, '0', 18) > 0) {
                throw new BusinessException(ResultCode::FAIL, '订单已有部分成交，不允许修改');
            }

            // 4. 获取修改参数
            $newQuantity = isset($modifyData['quantity']) ? (float)$modifyData['quantity'] : $matchOrder->getQuantity();
            $newPrice = isset($modifyData['price']) ? (float)$modifyData['price'] : $matchOrder->getPrice();
            $newLeverage = isset($modifyData['leverage']) ? (float)$modifyData['leverage'] : $marginOrder->getLeverage();

            // 5. 检查是否有实际修改
            $quantityChanged = bccomp((string)$newQuantity, (string)$matchOrder->getQuantity(), 18) !== 0;
            $priceChanged = bccomp((string)$newPrice, (string)$matchOrder->getPrice(), 18) !== 0;
            $leverageChanged = bccomp((string)$newLeverage, (string)$marginOrder->getLeverage(), 18) !== 0;
            
            if (!$quantityChanged && !$priceChanged && !$leverageChanged) {
                throw new BusinessException(ResultCode::FAIL, '没有检测到任何修改');
            }

            // 6. 如果数量或杠杆发生变化，需要重新验证风险和资金
            if ($quantityChanged || $leverageChanged) {
                // 先释放原来冻结的资金
                $this->releaseFrozenAssets($marginOrder, $matchOrder);

                // 重新验证风险控制
                $bracket = $this->marginRiskService->validateOrderParams(
                    $marginOrder->getUserId(),
                    $marginOrder->getCurrencyId(),
                    $marginOrder->getPositionSide(),
                    $marginOrder->getMarginType(),
                    $newLeverage,
                    $newQuantity,
                    $newPrice
                );

                // 重新计算保证金
                $newMarginAmount = $this->calculateMarginAmountWithBracket(
                    $newQuantity,
                    $newPrice,
                    $newLeverage,
                    $bracket
                );

                // 检查资金充足性并自动借款（如果需要）
                $borrowResult = $this->marginAutoBorrowService->autoBorrowIfNeeded(
                    $marginOrder->getUserId(),
                    $marginOrder->getCurrencyId(),
                    $marginOrder->getMarginType(),
                    $marginOrder->getPositionSide(),
                    $newQuantity,
                    $newPrice,
                    $matchOrder->getOrderType() === 1 ? 'market' : 'limit'
                );

                // 重新冻结资金
                $newFreezeData = $this->calculateAndFreezeMarginAssets(
                    $marginOrder->getUserId(),
                    $marginOrder->getCurrencyId(),
                    $marginOrder->getPositionSide(),
                    $marginOrder->getMarginType(),
                    $newQuantity,
                    $newPrice,
                    $matchOrder->getOrderType() === 1 ? 'market' : 'limit'
                );

                // 更新杠杆订单记录
                $marginOrder->setLeverage($newLeverage);
                $marginOrder->setMarginAmount($newMarginAmount);
                $marginOrder->setFrozenAmount($newFreezeData['freeze_amount']);
                $marginOrder->save();
            }

            // 7. 更新撮合引擎订单
            $matchOrder->setQuantity($newQuantity);
            $matchOrder->setPrice($newPrice);
            $matchOrder->save();

            // 8. 提交修改到撮合引擎
            $symbol = $this->getCurrencySymbol($marginOrder->getCurrencyId());
            $success = $this->matchEngineService->modifyOrder(
                $marginOrder->getUserId(),
                \App\Enum\MarketType::CRYPTO->value,
                $symbol,
                $matchOrder->order_id,
                $newPrice,
                $newQuantity,
                'gtc'
            );

            if (!$success) {
                throw new BusinessException(ResultCode::FAIL, '提交修改请求失败');
            }

            $logData = [
                'user_id' => $marginOrder->getUserId(),
                'margin_order_id' => $marginOrder->getId(),
                'match_order_id' => $matchOrder->getId(),
                'changes' => [
                    'quantity' => ['old' => $matchOrder->getQuantity(), 'new' => $newQuantity],
                    'price' => ['old' => $matchOrder->getPrice(), 'new' => $newPrice],
                    'leverage' => ['old' => $marginOrder->getLeverage(), 'new' => $newLeverage],
                ],
            ];

            // 如果有自动借款，添加到日志中
            if (isset($borrowResult) && $borrowResult['borrowed']) {
                $logData['auto_borrow'] = [
                    'currency_id' => $borrowResult['borrow_currency_id'],
                    'amount' => $borrowResult['borrow_amount'],
                    'record_id' => $borrowResult['borrow_record_id'],
                    'daily_rate' => $borrowResult['daily_rate'],
                ];
            }

            $this->logger->info('杠杆订单修改成功', $logData);

            return $marginOrder;
        });
    }

    /**
     * 使用已获取的档位数据计算保证金
     */
    private function calculateMarginAmountWithBracket(float $quantity, float $price, float $leverage, TradeMarginBracket $bracket): float
    {
        // 计算名义价值
        $notionalValue = bcmul((string)$quantity, (string)$price, 18);
        
        // 基础保证金（名义价值 / 杠杆倍数）
        $baseMargin = bcdiv($notionalValue, (string)$leverage, 18);
        
        // 使用传入的档位数据获取初始保证金比例
        $initMarginRate = $bracket->getInitRate();
        
        // 计算风险保证金增量：基础保证金 * 初始保证金比例
        $riskMarginIncrement = bcmul($baseMargin, (string)$initMarginRate, 18);
        
        // 最终保证金 = 基础保证金 + 风险保证金增量
        $finalMargin = bcadd($baseMargin, $riskMarginIncrement, 18);
        
        return (float)$finalMargin;
    }

    public function calculateMarginAmount(int $currencyId, float $quantity, float $price, float $leverage, int $marginType): float
    {
        // 计算名义价值
        $notionalValue = bcmul((string)$quantity, (string)$price, 18);
        
        // 基础保证金（名义价值 / 杠杆倍数）
        $baseMargin = bcdiv($notionalValue, (string)$leverage, 18);
        
        // 获取风险档位的初始保证金比例
        $initMarginRate = $this->getInitMarginRate($currencyId, $notionalValue, $marginType);
        
        // 计算风险保证金增量：基础保证金 * 初始保证金比例
        $riskMarginIncrement = bcmul($baseMargin, (string)$initMarginRate, 18);
        
        // 最终保证金 = 基础保证金 + 风险保证金增量
        $finalMargin = bcadd($baseMargin, $riskMarginIncrement, 18);
        
        return (float)$finalMargin;
    }

    /**
     * 获取风险档位的初始保证金比例
     */
    private function getInitMarginRate(int $currencyId, string $notionalValue, int $marginType): float
    {
        // 查找适用的风险档位
        $bracket = TradeMarginBracket::query()
            ->where('currency_id', $currencyId)
            ->where('is_cross', $marginType)
            ->where('hold_start', '<=', (float)$notionalValue)
            ->where('hold_end', '>=', (float)$notionalValue)
            ->first();

        if (!$bracket) {
            // 如果没有找到对应档位，查找最大档位
            $bracket = TradeMarginBracket::query()
                ->where('currency_id', $currencyId)
                ->where('is_cross', $marginType)
                ->orderBy('hold_end', 'desc')
                ->first();
        }

        if (!$bracket) {
            // 如果完全没有配置，使用默认初始保证金比例 5%
            $this->logger->warning('未找到风险档位配置，使用默认初始保证金比例', [
                'currency_id' => $currencyId,
                'margin_type' => $marginType,
                'notional_value' => $notionalValue
            ]);
            return 0.05; // 5%
        }

        return $bracket->getInitRate();
    }

    public function processOrderFilled(int $matchOrderId, float $filledQuantity, float $filledPrice): void
    {
        $marginOrder = TradeMarginOrder::where('match_order_id', $matchOrderId)->first();
        if (!$marginOrder) {
            return;
        }

        // 1. 处理资产变动
        $this->processAssetChange($marginOrder, $filledQuantity, $filledPrice);
        
        // 2. 更新仓位
        $this->updateOrCreatePosition($marginOrder, $filledQuantity, $filledPrice);
    }

    /**
     * 处理杠杆订单成交的资产变动
     */
    private function processAssetChange(TradeMarginOrder $marginOrder, float $filledQuantity, float $filledPrice): void
    {
        $userId = $marginOrder->getUserId();
        $currencyId = $marginOrder->getCurrencyId();
        $side = $marginOrder->getPositionSide();
        $marginType = $marginOrder->getMarginType();
        $accountType = $marginType === MarginType::CROSS ? AccountType::MARGIN : AccountType::ISOLATED;

        $quoteCurrencyId = $this->marginAssetCalculator->getQuoteCurrencyId($currencyId);
        $filledAmount = bcmul((string)$filledQuantity, (string)$filledPrice, 18);

        if ($side === PositionSide::LONG) {
            // 买入（做多）：扣除锁定的计价币，增加基础币余额
            $usedAmount = (float)$filledAmount;

            if ($marginType === MarginType::CROSS) {
                // 全仓：扣除独立的计价币资产记录的frozen字段
                $this->userAccountsAssetService->deductFrozenAsset(
                    $userId,
                    $accountType->value,
                    $quoteCurrencyId,
                    $usedAmount,
                    FlowsType::MARGIN_TRADE->value,
                    0,
                    'frozen'
                );
            } else {
                // 逐仓：扣除基础币记录的locked字段（计价币冻结资产）
                $this->userAccountsAssetService->deductFrozenAsset(
                    $userId,
                    $accountType->value,
                    $currencyId,
                    $usedAmount,
                    FlowsType::MARGIN_TRADE->value,
                    0,
                    'locked'
                );
            }

            // 增加基础币余额（全仓和逐仓都一样）
            $this->userAccountsAssetService->addAvailableAsset(
                $userId,
                $accountType->value,
                $currencyId,
                $filledQuantity,
                FlowsType::MARGIN_TRADE->value
            );

        } else {
            // 卖出（做空）：扣除锁定的基础币，增加计价币余额
            $usedAmount = $filledQuantity;

            // 扣除基础币frozen字段（全仓和逐仓都一样）
            $this->userAccountsAssetService->deductFrozenAsset(
                $userId,
                $accountType->value,
                $currencyId,
                $filledQuantity,
                FlowsType::MARGIN_TRADE->value,
                0,
                'frozen'
            );

            if ($marginType === MarginType::CROSS) {
                // 全仓：增加独立的计价币资产记录
                $this->userAccountsAssetService->addAvailableAsset(
                    $userId,
                    $accountType->value,
                    $quoteCurrencyId,
                    (float)$filledAmount,
                    FlowsType::MARGIN_TRADE->value
                );
            } else {
                // 逐仓：增加基础币记录的margin_quote字段
                $this->userAccountsAssetService->addAvailableAsset(
                    $userId,
                    $accountType->value,
                    $currencyId,
                    (float)$filledAmount,
                    FlowsType::MARGIN_TRADE->value,
                    0,
                    'margin_quote'
                );
            }
        }

        // 更新杠杆订单的使用资金
        $currentUsedAmount = $marginOrder->getUsedAmount();
        $newUsedAmount = bcadd((string)$currentUsedAmount, (string)$usedAmount, 18);
        $marginOrder->setUsedAmount((float)$newUsedAmount);
        $marginOrder->save();

        $this->logger->info('杠杆订单成交资产变动', [
            'user_id' => $userId,
            'currency_id' => $currencyId,
            'quote_currency_id' => $quoteCurrencyId,
            'side' => $side->value,
            'margin_type' => $marginType->value,
            'used_amount' => $usedAmount,
            'total_used_amount' => (float)$newUsedAmount,
            'frozen_amount' => $marginOrder->getFrozenAmount(),
            'filled_price' => $filledPrice,
            'filled_quantity' => $filledQuantity
        ]);
    }

    protected function updateOrCreatePosition(TradeMarginOrder $marginOrder, float $quantity, float $price): void
    {
        $userId = $marginOrder->getUserId();
        $currencyId = $marginOrder->getCurrencyId();
        $orderSide = $marginOrder->getPositionSide();
        $marginType = $marginOrder->getMarginType();

        // 查找现有仓位（不区分方向，因为一个币种只能有一个仓位）
        $position = TradeMarginPosition::where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_type', $marginType)
            ->where('status', \App\Model\Enums\Trade\Margin\MarginPositionStatus::HOLDING)
            ->lockForUpdate()
            ->first();

        if (!$position) {
            // 没有现有仓位，创建新仓位（开仓）
            $this->createNewPosition($marginOrder, $quantity, $price);
        } else {
            // 有现有仓位，判断是加仓还是减仓
            $currentSide = $position->getSide();
            
            if ($currentSide == $orderSide) {
                // 同方向：加仓
                $this->increasePosition($position, $marginOrder, $quantity, $price);
            } else {
                // 反方向：减仓或反向开仓
                $this->decreaseOrReversePosition($position, $marginOrder, $quantity, $price);
            }
        }
    }

    /**
     * 创建新仓位
     */
    private function createNewPosition(TradeMarginOrder $marginOrder, float $quantity, float $price): void
    {
        $position = new TradeMarginPosition();
        $position->setUserId($marginOrder->getUserId());
        $position->setCurrencyId($marginOrder->getCurrencyId());
        $position->setSide($marginOrder->getPositionSide());
        $position->setMarginType($marginOrder->getMarginType());
        $position->setLeverage($marginOrder->getLeverage());
        $position->setQuantity($quantity);
        $position->setAvailableQuantity($quantity);
        $position->setFrozenQuantity(0);
        $position->setEntryPrice($price);
        $position->setMarginAmount($marginOrder->getMarginAmount());
        $position->setInitialMargin($marginOrder->getMarginAmount());
        $position->setMaintenanceMargin($this->marginRiskService->calculateMaintenanceMargin(
            $marginOrder->getUserId(),
            $marginOrder->getCurrencyId(), 
            $quantity, 
            $price, 
            $marginOrder->getMarginType()
        ));
        $position->setRealizedPnl(0);
        $position->setStatus(\App\Model\Enums\Trade\Margin\MarginPositionStatus::HOLDING);
        $position->save();

        // 触发仓位监控（延时异步任务）
        $this->triggerPositionMonitoring($position, 'created', [
            'quantity' => $quantity,
            'price' => $price,
            'order_id' => $marginOrder->getId()
        ]);

        $this->logger->info('创建新杠杆仓位', [
            'user_id' => $marginOrder->getUserId(),
            'currency_id' => $marginOrder->getCurrencyId(),
            'side' => $marginOrder->getPositionSide(),
            'quantity' => $quantity,
            'entry_price' => $price,
        ]);
    }

    /**
     * 增加仓位（加仓）
     */
    private function increasePosition(TradeMarginPosition $position, TradeMarginOrder $marginOrder, float $quantity, float $price): void
    {
        $oldQuantity = $position->getQuantity();
        $oldEntryPrice = $position->getEntryPrice();
        
        // 计算新的总数量
        $totalQuantity = bcadd((string)$oldQuantity, (string)$quantity, 18);
        
        // 计算新的加权平均开仓价格
        $oldNotional = bcmul((string)$oldQuantity, (string)$oldEntryPrice, 18);
        $newNotional = bcmul((string)$quantity, (string)$price, 18);
        $totalNotional = bcadd($oldNotional, $newNotional, 18);
        $newEntryPrice = bccomp($totalQuantity, '0', 18) > 0 
            ? bcdiv($totalNotional, $totalQuantity, 18) 
            : '0';
        
        // 更新仓位信息
        $position->setQuantity((float)$totalQuantity);
        $position->setAvailableQuantity(
            (float)bcadd((string)$position->getAvailableQuantity(), (string)$quantity, 18)
        );
        $position->setEntryPrice((float)$newEntryPrice);
        $position->setMarginAmount(
            (float)bcadd((string)$position->getMarginAmount(), (string)$marginOrder->getMarginAmount(), 18)
        );
        
        // 重新计算维持保证金
        $position->setMaintenanceMargin($this->marginRiskService->calculateMaintenanceMargin(
            $marginOrder->getUserId(),
            $marginOrder->getCurrencyId(), 
            (float)$totalQuantity, 
            (float)$newEntryPrice, 
            $marginOrder->getMarginType()
        ));

        $position->save();

        // 触发仓位监控（延时异步任务）
        $this->triggerPositionMonitoring($position, 'increased', [
            'added_quantity' => $quantity,
            'price' => $price,
            'old_quantity' => $oldQuantity,
            'new_quantity' => (float)$totalQuantity,
            'old_entry_price' => $oldEntryPrice,
            'new_entry_price' => (float)$newEntryPrice
        ]);

        $this->logger->info('增加杠杆仓位', [
            'user_id' => $marginOrder->getUserId(),
            'currency_id' => $marginOrder->getCurrencyId(),
            'old_quantity' => $oldQuantity,
            'add_quantity' => $quantity,
            'new_quantity' => (float)$totalQuantity,
            'old_entry_price' => $oldEntryPrice,
            'new_entry_price' => (float)$newEntryPrice,
        ]);
    }

    /**
     * 减仓或反向开仓
     */
    private function decreaseOrReversePosition(TradeMarginPosition $position, TradeMarginOrder $marginOrder, float $quantity, float $price): void
    {
        $currentQuantity = $position->getQuantity();
        $entryPrice = $position->getEntryPrice();
        
        if (bccomp((string)$quantity, (string)$currentQuantity, 18) < 0) {
            // 部分减仓
            $remainingQuantity = bcsub((string)$currentQuantity, (string)$quantity, 18);
            
            // 计算已实现盈亏
            $realizedPnl = $this->calculateRealizedPnl($position, $quantity, $price);
            
            // 按比例减少保证金
            $reductionRatio = bcdiv((string)$quantity, (string)$currentQuantity, 18);
            $marginReduction = bcmul((string)$position->getMarginAmount(), $reductionRatio, 18);
            
            $position->setQuantity((float)$remainingQuantity);
            $position->setAvailableQuantity(
                (float)bcsub((string)$position->getAvailableQuantity(), (string)$quantity, 18)
            );
            $position->setMarginAmount(
                (float)bcsub((string)$position->getMarginAmount(), $marginReduction, 18)
            );
            $position->setRealizedPnl(
                (float)bcadd((string)$position->getRealizedPnl(), (string)$realizedPnl, 18)
            );
            $position->save();

            // 触发仓位监控（延时异步任务）
            $this->triggerPositionMonitoring($position, 'decreased', [
                'reduced_quantity' => $quantity,
                'remaining_quantity' => (float)$remainingQuantity,
                'realized_pnl' => $realizedPnl
            ]);

            $this->logger->info('部分减仓', [
                'user_id' => $marginOrder->getUserId(),
                'currency_id' => $marginOrder->getCurrencyId(),
                'reduce_quantity' => $quantity,
                'remaining_quantity' => (float)$remainingQuantity,
                'realized_pnl' => $realizedPnl,
            ]);
            
        } elseif (bccomp((string)$quantity, (string)$currentQuantity, 18) == 0) {
            // 完全平仓
            $realizedPnl = $this->calculateRealizedPnl($position, $quantity, $price);
            
            $position->setQuantity(0);
            $position->setAvailableQuantity(0);
            $position->setMarginAmount(0);
            $position->setRealizedPnl(
                (float)bcadd((string)$position->getRealizedPnl(), (string)$realizedPnl, 18)
            );
            $position->setStatus(\App\Model\Enums\Trade\Margin\MarginPositionStatus::CLOSED);
            $position->save();

            // 触发仓位监控（延时异步任务）
            $this->triggerPositionMonitoring($position, 'closed', [
                'final_quantity' => $quantity,
                'realized_pnl' => $realizedPnl
            ]);

            $this->logger->info('完全平仓', [
                'user_id' => $marginOrder->getUserId(),
                'currency_id' => $marginOrder->getCurrencyId(),
                'close_quantity' => $quantity,
                'realized_pnl' => $realizedPnl,
            ]);
            
        } else {
            // 平仓 + 反向开仓
            $closeQuantity = $currentQuantity;
            $reverseQuantity = bcsub((string)$quantity, (string)$currentQuantity, 18);
            
            // 计算平仓的已实现盈亏
            $realizedPnl = $this->calculateRealizedPnl($position, $closeQuantity, $price);
            
            // 更新为反向仓位
            $position->setSide($marginOrder->getPositionSide());
            $position->setQuantity((float)$reverseQuantity);
            $position->setAvailableQuantity((float)$reverseQuantity);
            $position->setEntryPrice($price);
            $position->setLeverage($marginOrder->getLeverage());
            $position->setMarginAmount($marginOrder->getMarginAmount());
            $position->setInitialMargin($marginOrder->getMarginAmount());
            $position->setMaintenanceMargin($this->marginRiskService->calculateMaintenanceMargin(
                $marginOrder->getUserId(),
                $marginOrder->getCurrencyId(), 
                (float)$reverseQuantity, 
                $price, 
                $marginOrder->getMarginType()
            ));
            $position->setRealizedPnl(
                (float)bcadd((string)$position->getRealizedPnl(), (string)$realizedPnl, 18)
            );
            $position->save();

            // 触发仓位监控（延时异步任务）
            $this->triggerPositionMonitoring($position, 'reversed', [
                'close_quantity' => $closeQuantity,
                'reverse_quantity' => (float)$reverseQuantity,
                'new_side' => $marginOrder->getPositionSide(),
                'realized_pnl' => $realizedPnl,
                'new_entry_price' => $price
            ]);

            $this->logger->info('平仓并反向开仓', [
                'user_id' => $marginOrder->getUserId(),
                'currency_id' => $marginOrder->getCurrencyId(),
                'close_quantity' => $closeQuantity,
                'reverse_quantity' => (float)$reverseQuantity,
                'new_side' => $marginOrder->getPositionSide(),
                'realized_pnl' => $realizedPnl,
            ]);
        }
    }

    /**
     * 计算已实现盈亏
     */
    private function calculateRealizedPnl(TradeMarginPosition $position, float $quantity, float $exitPrice): float
    {
        $entryPrice = $position->getEntryPrice();
        $side = $position->getSide();
        
        if ($side == PositionSide::LONG) {
            // 多头：(卖出价 - 开仓价) * 数量
            $pnlPerUnit = bcsub((string)$exitPrice, (string)$entryPrice, 18);
        } else {
            // 空头：(开仓价 - 买入价) * 数量
            $pnlPerUnit = bcsub((string)$entryPrice, (string)$exitPrice, 18);
        }
        
        $totalPnl = bcmul($pnlPerUnit, (string)$quantity, 18);
        
        return (float)$totalPnl;
    }

    /**
     * 获取借款配置
     */
    public function getBorrowConfig(int $userId, int $currencyId, int $marginType): array
    {
        try {
            $userVipLevel = UserVipLevel::getUserActiveVipLevel($userId);
            if (!$userVipLevel || !$userVipLevel->vipLevel) {
                throw new \RuntimeException('用户VIP等级不存在');
            }

            $vipLevel = $userVipLevel->vipLevel->getLevel();

            $marginBorrowConfig = CurrencyMarginBorrow::query()
                ->where('currency_id', $currencyId)
                ->where('is_cross', $marginType)
                ->first();

            if (!$marginBorrowConfig) {
                throw new \RuntimeException('该币种不支持杠杆交易');
            }

            $accountType = $marginType === MarginType::CROSS->value ? AccountType::MARGIN : AccountType::ISOLATED;
            
            $userAssets = UserAccountsAsset::query()
                ->where('user_id', $userId)
                ->where('account_type', $accountType->value)
                ->where('currency_id', $currencyId)
                ->first();

            $borrowedAmount = $userAssets ? $userAssets->getBorrowedAmount() : 0;

            $result = [
                'currency_id' => $currencyId,
                'margin_type' => $marginType,
                'user_vip_level' => $vipLevel,
                'is_base_borrow' => $marginBorrowConfig->getIsBaseBorrow(),
                'is_quote_borrow' => $marginBorrowConfig->getIsQuoteBorrow(),
                'borrowed_amount' => $borrowedAmount,
            ];

            if ($marginType === MarginType::CROSS->value) {
                // 全仓杠杆
                $baseLevels = $marginBorrowConfig->getBaseLevel();
                
                $result['base_level'] = array_filter($baseLevels, function($level) use ($vipLevel) {
                    return $level['vipLevel'] == $vipLevel;
                });
            } else {
                // 逐仓杠杆
                $baseLevels = $marginBorrowConfig->getBaseLevel();
                $quoteLevels = $marginBorrowConfig->getQuoteLevel();
                
                $result['base_level'] = array_filter($baseLevels, function($level) use ($vipLevel) {
                    return $level['level'] == $vipLevel;
                });
                
                $result['quote_level'] = array_filter($quoteLevels, function($level) use ($vipLevel) {
                    return $level['level'] == $vipLevel;
                });
            }

            return $result;

        } catch (\Exception $e) {
            $this->logger->error('获取借款配置失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'margin_type' => $marginType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取杠杆配置
     */
    public function getMarginLeverage(int $currencyId, int $marginType, ?int $userId = null): array
    {
        try {
            $marginType = MarginType::from($marginType);
            $accountType = $marginType === MarginType::CROSS ? AccountType::MARGIN : AccountType::ISOLATED;

            // 如果没有传入用户ID，返回level=1的基础档位
            if (!$userId) {
                $marginBrackets = TradeMarginBracket::query()
                    ->where('currency_id', $currencyId)
                    ->where('is_cross', $marginType->value)
                    ->where('level', 1)
                    ->get();

                if ($marginBrackets->isEmpty()) {
                    throw new \RuntimeException('该币种不支持杠杆交易');
                }

                $result = [
                    'currency_id' => $currencyId,
                    'margin_type' => $marginType->value,
                    'user_asset_value' => 0,
                    'applicable_level' => 1,
                    'brackets' => []
                ];

                foreach ($marginBrackets as $bracket) {
                    $result['brackets'] = [
                        'id' => $bracket->getId(),
                        'min_amount' => $bracket->getHoldStart(),
                        'max_amount' => $bracket->getHoldEnd(),
                        'max_lever' => $bracket->getMaxLever(),
                        'maintenance_margin_rate' => $bracket->getKeepRate(),
                        'level' => $bracket->getLevel(),
                        'init_rate' => $bracket->getInitRate(),
                    ];
                }

                return $result;
            }
            
            $result = [
                'currency_id' => $currencyId,
                'margin_type' => $marginType->value,
                'brackets' => []
            ];

            $bracket = $this->marginRiskService->getMarginBracket($userId, $currencyId, $marginType);

            $result['brackets'] = [
                'id' => $bracket->getId(),
                'min_amount' => $bracket->getHoldStart(),
                'max_amount' => $bracket->getHoldEnd(),
                'max_lever' => $bracket->getMaxLever(),
                'maintenance_margin_rate' => $bracket->getKeepRate(),
                'level' => $bracket->getLevel(),
                'init_rate' => $bracket->getInitRate(),
            ];

            return $result;

        } catch (\Exception $e) {
            $this->logger->error('获取杠杆配置失败', [
                'currency_id' => $currencyId,
                'margin_type' => $marginType,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 计算用户杠杆资产价值
     */
    private function getUserMarginAssetValue(int $userId, int $currencyId, MarginType $marginType): float
    {
        try {
            $accountType = $marginType === MarginType::CROSS ? AccountType::MARGIN : AccountType::ISOLATED;

            if ($marginType === MarginType::CROSS) {
                // 全仓：获取用户杠杆账户下的所有资产总价值
                return $this->getAllMarginAssetValue($userId, $accountType);
            } else {
                // 逐仓：获取基础币+计价币资产价值
                return $this->getIsolatedMarginAssetValue($userId, $currencyId, $accountType);
            }

        } catch (\Exception $e) {
            $this->logger->error('计算用户杠杆资产价值失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'margin_type' => $marginType->value,
                'error' => $e->getMessage()
            ]);
            return 0.0;
        }
    }

    /**
     * 获取全仓杠杆账户所有资产总价值
     */
    private function getAllMarginAssetValue(int $userId, AccountType $accountType): float
    {
        // 获取用户在杠杆账户下的所有资产
        $userAssets = UserAccountsAsset::query()
            ->where('user_id', $userId)
            ->where('account_type', $accountType->value)
            ->where('available', '>', 0)
            ->get();

        if ($userAssets->isEmpty()) {
            return 0.0;
        }

        $totalValue = 0.0;

        foreach ($userAssets as $asset) {
            $currencyId = $asset->getCurrencyId();
            $availableAmount = $asset->getAvailable();

            // 获取币种价格并计算价值
            $price = $this->getCurrencyPrice($currencyId);
            $assetValue = bcmul((string)$availableAmount, (string)$price, 18);
            $totalValue = bcadd((string)$totalValue, $assetValue, 18);
        }

        return (float)$totalValue;
    }

    /**
     * 获取逐仓杠杆指定交易对资产价值
     */
    private function getIsolatedMarginAssetValue(int $userId, int $currencyId, AccountType $accountType): float
    {
        // 获取基础币资产记录（逐仓模式下计价币资产存储在同一条记录的margin_quote字段中）
        $baseAsset = UserAccountsAsset::query()
            ->where('user_id', $userId)
            ->where('account_type', $accountType->value)
            ->where('currency_id', $currencyId)
            ->first();

        $totalValue = 0.0;

        if ($baseAsset) {
            // 计算基础币价值（available + frozen + locked - borrowed_amount）
            $baseNetAmount = $baseAsset->getAvailable() + $baseAsset->getFrozen() + $baseAsset->getLocked() - $baseAsset->getBorrowedAmount();
            if ($baseNetAmount > 0) {
                $basePrice = $this->getCurrencyPrice($currencyId);
                $baseValue = bcmul((string)$baseNetAmount, (string)$basePrice, 18);
                $totalValue = bcadd((string)$totalValue, $baseValue, 18);
            }

            // 计算计价币价值（从margin_quote字段获取）
            $quoteAmount = $baseAsset->getMarginQuote();
            if ($quoteAmount > 0) {
                $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);
                $quotePrice = $this->getCurrencyPrice($quoteCurrencyId);
                $quoteValue = bcmul((string)$quoteAmount, (string)$quotePrice, 18);
                $totalValue = bcadd((string)$totalValue, $quoteValue, 18);
            }
        }

        return (float)$totalValue;
    }

    /**
     * 获取币种价格
     */
    private function getCurrencyPrice(int $currencyId): float
    {
        try {
            // 尝试从ticker获取价格
            $tradeKey = TickerSyncKey::getOuterTradeKey($currencyId, MarketType::CRYPTO->value);
            if ($this->redis->exists($tradeKey)) {
                $price = $this->redis->hGet($tradeKey, 'price');
                if ($price !== false && $price !== null && $price > 0) {
                    return (float)$price;
                }
            }

            // 如果没有价格数据，记录警告并返回默认价格
            $this->logger->warning('无法获取币种价格，使用默认价格1.0', [
                'currency_id' => $currencyId
            ]);
            return 1.0;

        } catch (\Exception $e) {
            $this->logger->error('获取币种价格失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return 1.0; // 默认价格
        }
    }

    /**
     * 根据用户资产价值确定适用的档位
     */
    private function determineUserMarginLevel(float $userAssetValue, $allBrackets): int
    {
        // 获取所有可用的档位等级（去重）
        $availableLevels = $allBrackets->pluck('level')->unique()->sort()->values();

        if ($availableLevels->isEmpty()) {
            return 1; // 默认档位
        }

        // 从最高档位开始，找到用户资产价值满足的档位
        foreach ($availableLevels->reverse() as $level) {
            $levelBrackets = $allBrackets->where('level', $level);
            
            // 检查该档位的最小要求（取该档位最小的hold_start值）
            $minRequirement = $levelBrackets->min('hold_start');
            
            if ($userAssetValue >= $minRequirement) {
                return $level;
            }
        }

        // 如果资产不满足任何档位要求，返回最低档位
        return $availableLevels->first();
    }

    /**
     * 获取杠杆利率数据
     */
    public function getMarginRates(int $marginType): array
    {
        try {
            $rates = Db::table('currency_margin_borrow as cmb')
                ->leftJoin('currency as c', 'cmb.currency_id', '=', 'c.id')
                ->leftJoin('currency_mate as cm', 'c.id', '=', 'cm.currency_id')
                ->select([
                    'cmb.id',
                    'cmb.currency_id',
                    'cmb.is_cross',
                    'cmb.is_base_borrow',
                    'cmb.is_quote_borrow',
                    'cmb.base_level',
                    'cmb.quote_level',
                    'c.symbol as currency_symbol',
                    'c.base_asset',
                    'cm.logo'
                ])
                ->where('cmb.is_cross', $marginType)
                ->where('c.status', 1)
                ->get()
                ->toArray();

            // 处理JSON字段解码
            foreach ($rates as &$rate) {
                $rate = (array)$rate;
                $rate['base_level'] = json_decode($rate['base_level'], true) ?: [];
                $rate['quote_level'] = json_decode($rate['quote_level'], true) ?: [];
            }

            return $rates;

        } catch (\Exception $e) {
            $this->logger->error('获取杠杆利率数据失败', [
                'margin_type' => $marginType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 杠杆借款
     */
    public function borrowMargin(int $userId, int $currencyId, int $marginType, float $borrowAmount, ?int $baseCurrencyId = null): array
    {
        try {
            $this->validateBorrowLimit($userId, $currencyId, $marginType, $borrowAmount, $baseCurrencyId);

            // 确定查询借贷配置的币种ID
            // 全仓：使用传入的 currencyId（计价币种）
            // 逐仓：使用 baseCurrencyId（基础币种），如果没有传入则使用 currencyId
            $configCurrencyId = $currencyId;
            if ($marginType === MarginType::ISOLATED->value) {
                $configCurrencyId = $baseCurrencyId ?: $currencyId;
            }

            $borrowConfig = CurrencyMarginBorrow::query()
                ->where('currency_id', $configCurrencyId)
                ->where('is_cross', $marginType)
                ->first();

            if (!$borrowConfig) {
                throw new \RuntimeException('该币种不支持杠杆借贷');
            }

            $userVipLevel = UserVipLevel::getUserActiveVipLevel($userId);
            $vipLevel = $userVipLevel && $userVipLevel->vipLevel ? $userVipLevel->vipLevel->getLevel() : 0;

            // 获取对应VIP等级的借贷利率
            $dailyRate = 0;
            if ($marginType === MarginType::CROSS->value) {
                // 全仓
                $baseLevel = $borrowConfig->getBaseLevel();
                foreach ($baseLevel as $level) {
                    if ($level['vipLevel'] == $vipLevel) {
                        $dailyRate = $level['dailyInterestRate'];
                        break;
                    }
                }
            } else {
                // 逐仓需要根据借款币种类型选择配置
                if ($borrowConfig->getIsBaseBorrow() && $configCurrencyId === $baseCurrencyId) {
                    // 借基础币种
                    $baseLevel = $borrowConfig->getBaseLevel();
                    foreach ($baseLevel as $level) {
                        if ($level['level'] == $vipLevel) {
                            $dailyRate = $level['interestRate'];
                            break;
                        }
                    }
                } elseif ($borrowConfig->getIsQuoteBorrow()) {
                    // 借计价币种
                    $quoteLevel = $borrowConfig->getQuoteLevel();
                    foreach ($quoteLevel as $level) {
                        if ($level['level'] == $vipLevel) {
                            $dailyRate = $level['interestRate'];
                            break;
                        }
                    }
                }
            }

            if ($dailyRate <= 0) {
                throw new \RuntimeException('未找到适用的借贷利率配置');
            }

            $accountType = $marginType === MarginType::CROSS->value ? AccountType::MARGIN : AccountType::ISOLATED;

            return Db::transaction(function () use ($userId, $currencyId, $marginType, $borrowAmount, $accountType, $dailyRate, $configCurrencyId, $baseCurrencyId) {
                // 创建借款记录
                $borrowRecord = new UserMarginBorrow();
                $borrowRecord->setUserId($userId);
                $borrowRecord->setCurrencyId($currencyId); // 实际借款的币种ID
                $borrowRecord->setAccountType($accountType->value);
                $borrowRecord->setBorrowAmount($borrowAmount);
                $borrowRecord->setRepaidAmount(0);
                $borrowRecord->setDailyRate($dailyRate);
                $borrowRecord->setBorrowSource(value: 2); // 2-手动借款
                $borrowRecord->setBorrowTime(\Carbon\Carbon::now());
                $borrowRecord->setLastInterestTime(\Carbon\Carbon::now());
                $borrowRecord->setStatus(MarginBorrowStatus::ACTIVE);
                $borrowRecord->save();

                // 更新用户资产
                if($accountType === AccountType::MARGIN){
                    $result = $this->userAccountsAssetService->addBorrowedAmount(
                        $userId,
                        $accountType->value,
                        $currencyId, // 实际借款的币种ID
                        $borrowAmount,
                        FlowsType::MARGIN_BORROW->value,
                        $borrowRecord->getId(), // 关联借款记录ID
                        UserAccountsAsset::FIELD_BORROWED_AMOUNT ,
                        UserAccountsAsset::FIELD_AVAILABLE
                    );
                }else{
                    $borrowAssetsField = $baseCurrencyId == $currencyId ? UserAccountsAsset::FIELD_BORROWED_AMOUNT : UserAccountsAsset::FIELD_MARGIN_BORROW;
                    $availableAssetsField = $baseCurrencyId == $currencyId ? UserAccountsAsset::FIELD_AVAILABLE : UserAccountsAsset::FIELD_MARGIN_QUOTE;
                    $result = $this->userAccountsAssetService->addBorrowedAmount(
                        $userId,
                        $accountType->value,
                        $baseCurrencyId, // 实际借款的币种ID
                        $borrowAmount,
                        FlowsType::MARGIN_BORROW->value,
                        $borrowRecord->getId(), // 关联借款记录ID
                        $borrowAssetsField,
                        $availableAssetsField
                    );
                }

                if (!$result) {
                    throw new \RuntimeException('借款操作失败');
                }

                $this->logger->info('杠杆借款成功', [
                    'user_id' => $userId,
                    'currency_id' => $currencyId,
                    'config_currency_id' => $configCurrencyId,
                    'margin_type' => $marginType,
                    'borrow_amount' => $borrowAmount,
                    'account_type' => $accountType->value,
                    'borrow_record_id' => $borrowRecord->getId(),
                    'daily_rate' => $dailyRate,
                ]);

                return [
                    'user_id' => $userId,
                    'currency_id' => $currencyId,
                    'config_currency_id' => $configCurrencyId,
                    'margin_type' => $marginType,
                    'borrow_amount' => $borrowAmount,
                    'account_type' => $accountType->value,
                    'borrow_record_id' => $borrowRecord->getId(),
                    'daily_rate' => $dailyRate,
                    'borrow_time' => $borrowRecord->getBorrowTime(),
                ];
            });

        } catch (\Exception $e) {
            $this->logger->error('杠杆借款失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'margin_type' => $marginType,
                'borrow_amount' => $borrowAmount,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 验证借款限制
     */
    private function validateBorrowLimit(int $userId, int $currencyId, int $marginType, float $borrowAmount, ?int $baseCurrencyId = null): void
    {
        // 确定杠杆配置查询的币种ID
        // 全仓：使用 currencyId（计价币种）
        // 逐仓：使用 baseCurrencyId（基础币种），如果没有传入则使用 currencyId
        $leverageCurrencyId = $currencyId;
        if ($marginType === MarginType::ISOLATED->value) {
            $leverageCurrencyId = $baseCurrencyId ?: $currencyId;
        }

        $leverageConfig = $this->getMarginLeverage($leverageCurrencyId, $marginType, $userId);

        if (empty($leverageConfig['brackets'])) {
            throw new \RuntimeException('未找到杠杆配置');
        }

        $accountType = $marginType === MarginType::CROSS->value ? AccountType::MARGIN : AccountType::ISOLATED;
        $userAsset = $this->userAccountsAssetService->getUserAsset(
            $userId,
             $accountType->value, 
             $marginType === MarginType::CROSS->value ? $currencyId : $baseCurrencyId,
            );
        
        $currentAvailable = $userAsset ? $marginType === MarginType::CROSS->value ? $userAsset->getAvailable() : $userAsset->getMarginQuote() : 0;
        $currentBorrowed = $userAsset ? $marginType === MarginType::CROSS->value ? $userAsset->getBorrowedAmount() :$userAsset->getMarginBorrow() : 0;

        if(floatval($currentAvailable) <= 0){
            throw new \RuntimeException("请先划转资金到杠杆账户");
        }

        $maxLever = (float)$leverageConfig['brackets']['max_lever'];

        if ($maxLever <= 0) {
            throw new \RuntimeException('未找到适用的杠杆档位');
        }

        // 计算最大可借金额 = 用户可用余额 × 最大杠杆倍数 - 用户已借金额
        $maxBorrowAmount = bcmul((string)$currentAvailable, (string)$maxLever, 8);
        $maxBorrowAmount = bcsub($maxBorrowAmount, (string)$currentBorrowed, 8);

        if (bccomp((string)$borrowAmount, $maxBorrowAmount, 8) > 0) {
            throw new \RuntimeException("借款金额超过限制，最大可借金额为: {$maxBorrowAmount}");
        }

        if (bccomp((string)$borrowAmount, '0', 8) <= 0) {
            throw new \RuntimeException('借款金额必须大于0');
        }
    }

    /**
     * 获取用户借款汇总
     */
    public function getBorrowSummary(int $userId, int $currencyId, int $marginType): array
    {
        try {
            $accountType = $marginType === MarginType::CROSS->value ? AccountType::MARGIN : AccountType::ISOLATED;

            if ($marginType === MarginType::CROSS->value) {
                // 全仓：只返回传入币种的数据
                return $this->getCurrencyBorrowSummary($userId, $currencyId, $accountType);
            } else {
                // 逐仓：返回基础币和计价币的数据
                // 获取计价币ID
                $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);
                
                $baseSummary = $this->getCurrencyBorrowSummary($userId, $currencyId, $accountType);
                $quoteSummary = $this->getCurrencyBorrowSummary($userId, $quoteCurrencyId, $accountType);

                return [
                    'base_currency' => array_merge(['currency_id' => $currencyId], $baseSummary),
                    'quote_currency' => array_merge(['currency_id' => $quoteCurrencyId], $quoteSummary),
                ];
            }

        } catch (\Exception $e) {
            $this->logger->error('获取借款汇总失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'margin_type' => $marginType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取指定币种的借款汇总
     */
    private function getCurrencyBorrowSummary(int $userId, int $currencyId, AccountType $accountType): array
    {
        // 获取所有未还清的借款记录
        $borrowRecords = UserMarginBorrow::query()
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('account_type', $accountType->value)
            ->where('status', MarginBorrowStatus::ACTIVE)
            ->get();

        if ($borrowRecords->isEmpty()) {
            return [
                'total_borrowed' => 0,
                'total_repaid' => 0,
                'total_principal' => 0,
                'total_interest' => 0,
                'total_debt' => 0,
                'records_count' => 0,
            ];
        }

        // 计算本金汇总
        $totalBorrowed = $borrowRecords->sum('borrow_amount');
        $totalRepaid = $borrowRecords->sum('repaid_amount');
        $totalPrincipal = $totalBorrowed - $totalRepaid;

        // 查询累计待付利息
        $borrowIds = $borrowRecords->pluck('id')->toArray();
        $totalInterest = UserMarginInterest::query()
            ->whereIn('borrow_id', $borrowIds)
            ->where('status', \App\Model\Enums\Trade\Margin\MarginInterestStatus::PENDING)
            ->sum('interest_amount');

        $totalDebt = $totalPrincipal + $totalInterest;

        return [
            'total_borrowed' => (float)$totalBorrowed,
            'total_repaid' => (float)$totalRepaid,
            'total_principal' => (float)$totalPrincipal,
            'total_interest' => (float)$totalInterest,
            'total_debt' => (float)$totalDebt,
            'records_count' => $borrowRecords->count(),
        ];
    }

    /**
     * 获取计价币种ID
     */
    private function getQuoteCurrencyId(int $currencyId): int
    {
        try {
            $redisKey = \App\Enum\CurrencyConfigKey::getCurrencyKey($currencyId);
            
            if (!$this->redis->exists($redisKey)) {
                throw new \RuntimeException('币种配置不存在');
            }

            $quoteAssetsId = $this->redis->hGet($redisKey, 'quote_assets_id');
            
            if (!$quoteAssetsId) {
                throw new \RuntimeException('计价币种ID不存在');
            }

            return (int)$quoteAssetsId;

        } catch (\Exception $e) {
            $this->logger->error('获取计价币种ID失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            throw new \RuntimeException('获取计价币种失败：' . $e->getMessage());
        }
    }

    /**
     * 杠杆还款 - 自动分配到多笔借款
     */
    public function repayMargin(int $userId, int $currencyId, int $marginType, float $repayAmount, ?int $repayCurrencyId = null): array
    {
        try {
            return Db::transaction(function () use ($userId, $currencyId, $marginType, $repayAmount, $repayCurrencyId) {
                if (bccomp((string)$repayAmount, '0', 8) <= 0) {
                    throw new \RuntimeException('还款金额必须大于0');
                }

                // 确定账户类型
                $accountType = $marginType === MarginType::CROSS->value ? AccountType::MARGIN : AccountType::ISOLATED;

                // 确定实际还款的币种
                $actualRepayCurrencyId = $currencyId;
                if ($marginType === MarginType::ISOLATED->value) {
                    if (!$repayCurrencyId) {
                        throw new \RuntimeException('逐仓还款必须指定还款币种');
                    }
                    
                    // 验证还款币种是否为基础币或计价币
                    $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);
                    if ($repayCurrencyId !== $currencyId && $repayCurrencyId !== $quoteCurrencyId) {
                        throw new \RuntimeException('还款币种必须是基础币或计价币');
                    }
                    
                    $actualRepayCurrencyId = $repayCurrencyId;
                }

                // 检查用户可用余额
                $availableAmount = 0.0;

                if ($marginType === MarginType::CROSS->value) {
                    // 全仓：直接查询对应币种的资产记录
                    $userAsset = $this->userAccountsAssetService->getUserAsset(
                        $userId,
                        $accountType->value,
                        $actualRepayCurrencyId
                    );
                    if (!$userAsset) {
                        throw new \RuntimeException('用户资产不存在');
                    }
                    $availableAmount = $userAsset->getAvailable();
                } else {
                    // 逐仓：需要区分是基础币还是计价币
                    $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);

                    if ($actualRepayCurrencyId === $currencyId) {
                        // 还基础币：从基础币记录的available字段获取
                        $userAsset = $this->userAccountsAssetService->getUserAsset(
                            $userId,
                            $accountType->value,
                            $currencyId
                        );
                        if (!$userAsset) {
                            throw new \RuntimeException('用户资产不存在');
                        }
                        $availableAmount = $userAsset->getAvailable();
                    } else {
                        // 还计价币：从基础币记录的margin_quote字段获取
                        $userAsset = $this->userAccountsAssetService->getUserAsset(
                            $userId,
                            $accountType->value,
                            $currencyId
                        );
                        if (!$userAsset) {
                            throw new \RuntimeException('用户资产不存在');
                        }
                        $availableAmount = $userAsset->getMarginQuote();
                    }
                }

                if (bccomp((string)$availableAmount, (string)$repayAmount, 8) < 0) {
                    throw new \RuntimeException('可用余额不足');
                }

                // 获取该币种下所有未还清的借款记录（按借款时间排序）
                $borrowRecords = UserMarginBorrow::query()
                    ->where('user_id', $userId)
                    ->where('currency_id', $actualRepayCurrencyId)
                    ->where('account_type', $accountType->value)
                    ->where('status', MarginBorrowStatus::ACTIVE)
                    ->orderBy('borrow_time', 'asc') // 先进先出
                    ->lockForUpdate()
                    ->get();

                if ($borrowRecords->isEmpty()) {
                    throw new \RuntimeException('没有找到可还款的借款记录');
                }

                // 计算总的未还本金和利息
                $totalPrincipal = '0';
                $borrowIds = $borrowRecords->pluck('id')->toArray();
                
                foreach ($borrowRecords as $record) {
                    $remaining = bcsub(
                        (string)$record->getBorrowAmount(),
                        (string)$record->getRepaidAmount(),
                        8
                    );
                    $totalPrincipal = bcadd($totalPrincipal, $remaining, 8);
                }

                $totalInterest = UserMarginInterest::query()
                    ->whereIn('borrow_id', $borrowIds)
                    ->where('status', \App\Model\Enums\Trade\Margin\MarginInterestStatus::PENDING)
                    ->sum('interest_amount');

                $totalDebt = bcadd($totalPrincipal, (string)$totalInterest, 8);

                if (bccomp($totalDebt, '0', 8) <= 0) {
                    throw new \RuntimeException('所有借款已还清');
                }

                // 如果还款金额大于总负债，调整为总负债金额
                if (bccomp((string)$repayAmount, $totalDebt, 8) > 0) {
                    $repayAmount = (float)$totalDebt;
                }

                // 先从用户可用余额扣除还款金额
                if ($marginType === MarginType::CROSS->value) {
                    // 全仓：直接扣减对应币种的available字段
                    $result = $this->userAccountsAssetService->deductAvailableAsset(
                        $userId,
                        $accountType->value,
                        $actualRepayCurrencyId,
                        $repayAmount,
                        FlowsType::MARGIN_REPAY->value,
                        0 // 暂时传0，涉及多笔借款
                    );
                } else {
                    $result = $this->userAccountsAssetService->deductAvailableAsset(
                        $userId,
                        $accountType->value,
                        $currencyId,
                        $repayAmount,
                        FlowsType::MARGIN_REPAY->value,
                        0,
                        $currencyId == $repayCurrencyId ? UserAccountsAsset::FIELD_AVAILABLE : UserAccountsAsset::FIELD_MARGIN_QUOTE
                    );
                }

                if (!$result) {
                    throw new \RuntimeException('还款操作失败');
                }

                // 按顺序分配还款金额
                $remainingRepayAmount = (string)$repayAmount;
                $totalInterestRepaid = 0;
                $totalPrincipalRepaid = 0;
                $repaidRecordsCount = 0;
                $fullyRepaidCount = 0;

                foreach ($borrowRecords as $record) {
                    if (bccomp($remainingRepayAmount, '0', 8) <= 0) {
                        break; // 还款金额已分配完
                    }

                    // 1. 先扣除该笔借款的累计利息
                    $pendingInterest = UserMarginInterest::query()
                        ->where('borrow_id', $record->getId())
                        ->where('status', \App\Model\Enums\Trade\Margin\MarginInterestStatus::PENDING)
                        ->sum('interest_amount');

                    if (bccomp((string)$pendingInterest, '0', 8) > 0 && bccomp($remainingRepayAmount, '0', 8) > 0) {
                        $interestToDeduct = bccomp($remainingRepayAmount, (string)$pendingInterest, 8) >= 0 
                            ? (string)$pendingInterest 
                            : $remainingRepayAmount;

                        // 更新利息记录状态为已扣除
                        UserMarginInterest::query()
                            ->where('borrow_id', $record->getId())
                            ->where('status', \App\Model\Enums\Trade\Margin\MarginInterestStatus::PENDING)
                            ->update([
                                'status' => \App\Model\Enums\Trade\Margin\MarginInterestStatus::DEDUCTED,
                                'deduct_time' => \Carbon\Carbon::now()
                            ]);

                        $remainingRepayAmount = bcsub($remainingRepayAmount, $interestToDeduct, 8);
                        $totalInterestRepaid = bcadd((string)$totalInterestRepaid, $interestToDeduct, 8);
                    }

                    // 2. 再还本金
                    if (bccomp($remainingRepayAmount, '0', 8) > 0) {
                        $principalRemaining = bcsub(
                            (string)$record->getBorrowAmount(),
                            (string)$record->getRepaidAmount(),
                            8
                        );

                        if (bccomp($principalRemaining, '0', 8) > 0) {
                            $principalToRepay = bccomp($remainingRepayAmount, $principalRemaining, 8) >= 0 
                                ? $principalRemaining 
                                : $remainingRepayAmount;

                            // 更新借款记录的本金还款金额
                            $newRepaidAmount = bcadd(
                                (string)$record->getRepaidAmount(),
                                $principalToRepay,
                                8
                            );
                            $record->setRepaidAmount((float)$newRepaidAmount);

                            // 检查是否完全还清
                            if (bccomp($newRepaidAmount, (string)$record->getBorrowAmount(), 8) >= 0) {
                                $record->setStatus(MarginBorrowStatus::REPAID);
                                $record->setRepayTime(\Carbon\Carbon::now());
                                $fullyRepaidCount++;
                            }

                            $record->save();

                            $remainingRepayAmount = bcsub($remainingRepayAmount, $principalToRepay, 8);
                            $totalPrincipalRepaid = bcadd((string)$totalPrincipalRepaid, $principalToRepay, 8);
                        }
                    }

                    $repaidRecordsCount++;
                }

                // 单独更新用户资产表的borrowed_amount字段（只减少实际还的本金部分）
                if (bccomp((string)$totalPrincipalRepaid, '0', 8) > 0) {
                    $userAsset = UserAccountsAsset::query()
                        ->where('user_id', $userId)
                        ->where('account_type', $accountType->value)
                        ->where('currency_id', $accountType === AccountType::ISOLATED ? $currencyId : $actualRepayCurrencyId)
                        ->lockForUpdate()
                        ->first();

                    if ($userAsset) {
                        if($accountType === AccountType::ISOLATED && $currencyId != $repayCurrencyId){
                            $beforeBorrowed = $userAsset->getMarginBorrow();
                            $newBorrowedAmount = bcsub((string)$beforeBorrowed, (string)$totalPrincipalRepaid, 8);
                            $userAsset->setMarginBorrow((float)$newBorrowedAmount);
                            $userAsset->save();
                        }else{
                            $beforeBorrowed = $userAsset->getBorrowedAmount();
                            $newBorrowedAmount = bcsub((string)$beforeBorrowed, (string)$totalPrincipalRepaid, 8);
                            $userAsset->setBorrowedAmount((float)$newBorrowedAmount);
                            $userAsset->save();
                        }

                        $this->logger->info('更新用户借款金额', [
                            'user_id' => $userId,
                            'currency_id' => $actualRepayCurrencyId,
                            'before_borrowed' => $beforeBorrowed,
                            'after_borrowed' => $newBorrowedAmount,
                            'principal_repaid' => (float)$totalPrincipalRepaid,
                        ]);
                    }
                }

                // 记录日志
                $this->logger->info('杠杆还款成功', [
                    'user_id' => $userId,
                    'currency_id' => $currencyId,
                    'margin_type' => $marginType,
                    'repay_currency_id' => $actualRepayCurrencyId,
                    'total_repay_amount' => $repayAmount,
                    'principal_repaid' => (float)$totalPrincipalRepaid,
                    'interest_repaid' => (float)$totalInterestRepaid,
                    'repaid_records_count' => $repaidRecordsCount,
                    'fully_repaid_count' => $fullyRepaidCount,
                ]);

                return [
                    'total_repay_amount' => $repayAmount,
                    'repay_currency_id' => $actualRepayCurrencyId,
                    'principal_repaid' => (float)$totalPrincipalRepaid,
                    'interest_repaid' => (float)$totalInterestRepaid,
                    'repaid_records_count' => $repaidRecordsCount,
                    'fully_repaid_count' => $fullyRepaidCount,
                    'repay_time' => \Carbon\Carbon::now(),
                ];
            });

        } catch (\Exception $e) {
            $this->logger->error('杠杆还款失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'margin_type' => $marginType,
                'repay_currency_id' => $repayCurrencyId,
                'repay_amount' => $repayAmount,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 计算并冻结杠杆交易所需资金
     */
    private function calculateAndFreezeMarginAssets(
        int $userId,
        int $currencyId,
        PositionSide $side,
        MarginType $marginType,
        float $quantity,
        ?float $price,
        string $orderType
    ): array {
        $accountType = $marginType === MarginType::CROSS ? AccountType::MARGIN : AccountType::ISOLATED;

        if ($side === PositionSide::LONG) {
            // 买入订单：冻结计价币
            if ($marginType === MarginType::CROSS) {
                // 全仓：冻结独立的计价币资产记录
                $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);
                $freezeCurrencyId = $quoteCurrencyId;
                $assetField = 'available';
                $frozenField = 'frozen';
            } else {
                // 逐仓：冻结基础币记录的margin_quote字段到locked字段
                $freezeCurrencyId = $currencyId;
                $assetField = 'margin_quote';
                $frozenField = 'locked';
            }

            if ($orderType === 'market') {
                // 市价买单：估算价格并增加1%缓冲
                $estimatedPrice = $this->getEstimatedPrice($currencyId);
                $baseAmount = bcmul((string)$quantity, (string)$estimatedPrice, 18);
                $bufferAmount = bcmul($baseAmount, '0.01', 18); // 1%缓冲
                $freezeAmount = (float)bcadd($baseAmount, $bufferAmount, 18);
            } else {
                // 限价买单：数量 * 价格
                $freezeAmount = (float)bcmul((string)$quantity, (string)$price, 18);
            }
        } else {
            // 卖出订单：冻结基础币（全仓和逐仓都一样）
            $freezeCurrencyId = $currencyId;
            $freezeAmount = $quantity; // 卖出冻结基础币数量
            $assetField = 'available';
            $frozenField = 'frozen';
        }

        echo "assetField = {$assetField},frozenField = {$frozenField} ,freezeCurrencyId = {$freezeCurrencyId} amount = {$freezeAmount}\n";

        // 执行资金冻结
        $this->userAccountsAssetService->freezeAsset(
            $userId,
            $accountType->value,
            $freezeCurrencyId,
            $freezeAmount,
            FlowsType::MARGIN_TRADE->value,
            0,
            $assetField,
            $frozenField
        );

        return [
            'freeze_currency_id' => $freezeCurrencyId,
            'freeze_amount' => $freezeAmount,
            'account_type' => $accountType->value,
            'asset_field' => $assetField,
            'frozen_field' => $frozenField,
        ];
    }

    /**
     * 创建撮合引擎订单
     *
     * @param int $userId 用户ID
     * @param int $currencyId 币种ID  
     * @param int $side 杠杆方向(1=做多,2=做空)
     * @param float $quantity 数量
     * @param float|null $price 价格
     * @param string $orderType 订单类型
     * @return MatchOrder
     * @throws BusinessException
     */
    private function createMatchOrder(int $userId, int $currencyId, int $side, float $quantity, ?float $price, string $orderType): MatchOrder
    {
        try {
            // 杠杆Side映射到撮合引擎Side：1(做多/买) -> 1(买), 2(做空/卖) -> -1(卖)
            $engineSide = $side === 1 ? 1 : -1;
            
            $matchOrder = new MatchOrder();
            $matchOrder->setUserId($userId);
            $matchOrder->setCurrencyId($currencyId);
            $matchOrder->setOrderId($this->idGenerator->generate());
            // 创建 MatchOrder 记录时使用杠杆的 market_type
            $matchOrder->setMarketType(MarketType::LEVERAGE->value);
            $matchOrder->setSide($engineSide);
            $matchOrder->setQuantity($quantity);
            $matchOrder->setFillQuantity('0.000000000000000000');
            $matchOrder->setPrice($price ?? 0.0);
            $matchOrder->setAvgPrice(0.0);
            $matchOrder->setOrderType(\App\Enum\OrderType::getOrderType($orderType));
            $matchOrder->setTradeType(0); // 待成交
            $matchOrder->setOrderForce('gtc'); // 默认GTC
            $matchOrder->setFillTime(0);
            $matchOrder->setStatus(\App\Enum\OrderStatus::getOrderStatus('created'));
            $matchOrder->setReason('');

            if (!$matchOrder->save()) {
                throw new BusinessException(ResultCode::FAIL, '创建撮合引擎订单失败');
            }

            $this->logger->info('杠杆订单撮合引擎记录创建成功', [
                'user_id' => $userId,
                'match_order_id' => $matchOrder->getId(),
                'margin_side' => $side,
                'engine_side' => $engineSide,
                'market_type' => MarketType::LEVERAGE->value,
            ]);

            return $matchOrder;

        } catch (\Exception $e) {
            $this->logger->error('创建撮合引擎订单失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new BusinessException(ResultCode::FAIL, '创建撮合引擎订单失败');
        }
    }

    /**
     * 提交订单到撮合引擎
     *
     * @param MatchOrder $matchOrder 撮合引擎订单
     * @param int $currencyId 币种ID
     * @return bool
     * @throws BusinessException
     */
    private function submitOrderToMatchEngine(MatchOrder $matchOrder, int $currencyId): bool
    {
        try {
            $symbol = $this->getCurrencySymbol($currencyId);
            
            // 提交到撮合引擎时使用现货的 market_type，并构建正确的数据格式
            $engineData = $this->buildEngineOrderData($matchOrder, $symbol);

            // 提交到撮合引擎
            $messageId = $this->matchEngineService->addOrder(
                MarketType::CRYPTO->value,
                $symbol,
                $matchOrder->getUserId(),
                $engineData
            );

            if (!$messageId) {
                throw new BusinessException(ResultCode::FAIL, '提交订单到撮合引擎失败');
            }

            $this->logger->info('杠杆订单已提交到撮合引擎', [
                'match_order_id' => $matchOrder->getId(),
                'engine_market_type' => MarketType::CRYPTO->value,
                'message_id' => $messageId,
                'engine_data' => $engineData,
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('提交订单到撮合引擎失败', [
                'match_order_id' => $matchOrder->getId(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new BusinessException(ResultCode::FAIL, '提交订单到撮合引擎失败');
        }
    }

    /**
     * 构建撮合引擎订单数据
     *
     * @param MatchOrder $matchOrder 撮合引擎订单
     * @param string $symbol 交易对符号
     * @return array
     */
    private function buildEngineOrderData(MatchOrder $matchOrder, string $symbol): array
    {
        // 转换Side：撮合引擎期望字符串格式
        $sideString = $matchOrder->getSide() === 1 ? TradeSide::BUY_STRING->value : TradeSide::SELL_STRING->value;
        
        // 转换OrderType：撮合引擎期望字符串格式
        $orderTypeString = $matchOrder->getOrderType() === 1 ? OrderType::MARKET_STRING->value : OrderType::LIMIT_STRING->value;

        $engineData = [
            'order_id' => $matchOrder->getOrderId(),
            'user_id' => (string)$matchOrder->getUserId(),
            'side' => $sideString,
            'type' => $orderTypeString,
            'quantity' => $matchOrder->getQuantity(),
            'time_in_force' => $matchOrder->getOrderForce(),
            'leverage' => 1.0, // 杠杆订单暂时传1.0，实际杠杆在业务层处理
            'market_type' => MarketType::getMarketString(MarketType::CRYPTO->value),
            'symbol' => $symbol,
            'is_bot' => 0
        ];

        // 如果是限价单，添加价格
        if ($matchOrder->getOrderType() === 2) { // 2=限价单
            $engineData['price'] = $matchOrder->getPrice();
        }

        return $engineData;
    }

    /**
     * 获取估算价格（用于市价单）
     */
    private function getEstimatedPrice(int $currencyId): float
    {
        try {
            // 如果ticker没有价格，尝试从trade数据获取
            $tradeKey = TickerSyncKey::getOuterTradeKey($currencyId, MarketType::CRYPTO->value);
            if ($this->redis->exists($tradeKey)) {
                $tradePrice = $this->redis->hGet($tradeKey, 'price');
                if ($tradePrice !== false && $tradePrice !== null) {
                    return (float)$tradePrice;
                }
            }
            
            throw new BusinessException(ResultCode::FAIL, '无法获取当前市价');
            
        } catch (\Exception $e) {
            $this->logger->error('获取估算价格失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '获取市价失败：' . $e->getMessage());
        }
    }

    /**
     * 获取币种交易对符号
     */
    private function getCurrencySymbol(int $currencyId): string
    {
        try {
            // 从Redis获取币种配置
            $currencyKey = CurrencyConfigKey::getCurrencyKey($currencyId);
            
            if (!$this->redis->exists($currencyKey)) {
                throw new BusinessException(ResultCode::FAIL, '币种配置不存在');
            }
            
            $symbol = $this->redis->hGet($currencyKey, 'symbol');
            
            if ($symbol === false || $symbol === null) {
                throw new BusinessException(ResultCode::FAIL, '币种符号不存在');
            }
            
            return $symbol;
            
        } catch (\Exception $e) {
            $this->logger->error('获取币种符号失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '获取币种符号失败：' . $e->getMessage());
        }
    }

    /**
     * 获取杠杆订单列表
     *
     * @param int $userId 用户ID
     * @param int|null $currencyId 币种ID
     * @param int|null $marginType 保证金类型
     * @param int|null $side 仓位方向
     * @param int|null $status 订单状态
     * @param int $perPage 每页数量
     * @param int $page 页码
     * @return array
     */
    public function getOrderList(
        int $userId, 
        ?int $currencyId = null, 
        ?int $marginType = null, 
        ?int $side = null, 
        ?int $status = null, 
        int $perPage = 20, 
        int $page = 1
    ): array {
        $query = TradeMarginOrder::query()
            ->select([
                'trade_margin_order.*',
                'match_orders.order_id',
                'match_orders.side as match_side',
                'match_orders.quantity as order_quantity',
                'match_orders.fill_quantity',
                'match_orders.price as order_price',
                'match_orders.avg_price',
                'match_orders.order_type',
                'match_orders.status as order_status',
                'match_orders.created_at as order_created_at',
                'match_orders.updated_at as order_updated_at',
                'currency.symbol as currency_symbol'
            ])
            ->leftJoin('match_orders', 'trade_margin_order.match_order_id', '=', 'match_orders.id')
            ->leftJoin('currency', 'trade_margin_order.currency_id', '=', 'currency.id')
            ->where('trade_margin_order.user_id', $userId);

        // 按币种筛选
        if ($currencyId !== null) {
            $query->where('trade_margin_order.currency_id', $currencyId);
        }

        // 按保证金类型筛选
        if ($marginType !== null) {
            $query->where('trade_margin_order.margin_type', $marginType);
        }

        // 按仓位方向筛选
        if ($side !== null) {
            $query->where('trade_margin_order.position_side', $side);
        }

        // 按订单状态筛选
        if ($status !== null) {
            $query->where('match_orders.status', $status);
        }

        // 按创建时间降序排列
        $query->orderBy('trade_margin_order.created_at', 'desc');

        // 分页查询
        $total = $query->count();
        $orders = $query->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->get();

        return [
            'data' => $orders->toArray(),
            'pagination' => [
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($total / $perPage),
                'from' => ($page - 1) * $perPage + 1,
                'to' => min($page * $perPage, $total),
            ]
        ];
    }

    /**
     * 获取杠杆仓位列表
     *
     * @param int $userId 用户ID
     * @param int|null $currencyId 币种ID
     * @param int|null $marginType 保证金类型
     * @param int|null $side 仓位方向
     * @param int $perPage 每页数量
     * @param int $page 页码
     * @return array
     */
    public function getPositionList(
        int $userId, 
        ?int $currencyId = null, 
        ?int $marginType = null, 
        ?int $side = null, 
        int $perPage = 20, 
        int $page = 1
    ): array {
        $query = TradeMarginPosition::query()
            ->select([
                'trade_margin_position.*',
                'currency.symbol as currency_symbol'
            ])
            ->leftJoin('currency', 'trade_margin_position.currency_id', '=', 'currency.id')
            ->where('trade_margin_position.user_id', $userId);

        // 按币种筛选
        if ($currencyId !== null) {
            $query->where('trade_margin_position.currency_id', $currencyId);
        }

        // 按保证金类型筛选
        if ($marginType !== null) {
            $query->where('trade_margin_position.margin_type', $marginType);
        }

        // 按仓位方向筛选
        if ($side !== null) {
            $query->where('trade_margin_position.side', $side);
        }

        // 只查询持仓中的仓位（排除已平仓）
        $query->where('trade_margin_position.status', \App\Model\Enums\Trade\Margin\MarginPositionStatus::HOLDING->value);

        // 按创建时间降序排列
        $query->orderBy('trade_margin_position.created_at', 'desc');

        // 分页查询
        $total = $query->count();
        $positions = $query->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->get();

        return [
            'data' => $positions->toArray(),
            'pagination' => [
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($total / $perPage),
                'from' => ($page - 1) * $perPage + 1,
                'to' => min($page * $perPage, $total),
            ]
        ];
    }

    /**
     * 获取杠杆账户资产
     *
     * @param int $userId 用户ID
     * @param int|null $marginType 保证金类型
     * @param int|null $currencyId 币种ID
     * @return array
     */
    public function getAccountAssets(int $userId, ?int $marginType = null, ?int $currencyId = null): array
    {
        try {
            // 如果指定了保证金类型，返回对应账户类型的资产
            if ($marginType !== null) {
                $accountType = $marginType === MarginType::CROSS->value 
                    ? AccountType::MARGIN 
                    : AccountType::ISOLATED;
                
                return $this->getAssetsByAccountType($userId, $accountType, $currencyId);
            }
            
            // 未指定保证金类型，返回全仓和逐仓的资产
            $result = [
                'cross_margin' => $this->getAssetsByAccountType($userId, AccountType::MARGIN, $currencyId),
                'isolated_margin' => $this->getAssetsByAccountType($userId, AccountType::ISOLATED, $currencyId)
            ];
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error('获取杠杆账户资产失败', [
                'user_id' => $userId,
                'margin_type' => $marginType,
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '获取账户资产失败：' . $e->getMessage());
        }
    }

    /**
     * 根据账户类型获取用户资产
     *
     * @param int $userId 用户ID
     * @param AccountType $accountType 账户类型
     * @param int|null $currencyId 币种ID
     * @return array
     */
    private function getAssetsByAccountType(int $userId, AccountType $accountType, ?int $currencyId = null): array
    {
        $query = UserAccountsAsset::query()
            ->select([
                'user_accounts_assets.*',
                'currency.base_asset as currency_symbol'
            ])
            ->leftJoin('currency', 'user_accounts_assets.currency_id', '=', 'currency.id')
            ->where('user_accounts_assets.user_id', $userId)
            ->where('user_accounts_assets.account_type', $accountType->value)
            ->where('user_accounts_assets.status', 1);

        // 如果指定了币种ID，进行筛选
        if ($currencyId !== null) {
            if ($accountType === AccountType::ISOLATED) {
                // 逐仓：只查询基础币记录
                $query->where('user_accounts_assets.currency_id', $currencyId);
            } else {
                // 全仓：查询基础币和计价币记录
                $baseCurrencyId = $currencyId;
                $quoteCurrencyId = $this->marginAssetCalculator->getQuoteCurrencyId($currencyId);
                $query->whereIn('user_accounts_assets.currency_id', [$baseCurrencyId, $quoteCurrencyId]);
            }
        }

        // 只返回有余额的资产（可用+冻结+锁定+计价币+借款+利息 > 0）
        $query->where(function ($q) {
            $q->where('user_accounts_assets.available', '>', 0)
              ->orWhere('user_accounts_assets.frozen', '>', 0)
              ->orWhere('user_accounts_assets.locked', '>', 0)
              ->orWhere('user_accounts_assets.margin_quote', '>', 0)
              ->orWhere('user_accounts_assets.borrowed_amount', '>', 0)
              ->orWhere('user_accounts_assets.interest_amount', '>', 0);
        });

        $assets = $query->orderBy('user_accounts_assets.currency_id', 'asc')->get();
        $result = $assets->toArray();

        // 如果是逐仓模式且指定了币种ID，需要构造虚拟的计价币资产数据
        if ($accountType === AccountType::ISOLATED && $currencyId !== null && !empty($result)) {
            $baseAsset = $result[0];
            $marginQuoteAmount = $baseAsset['margin_quote'] ?? 0;

            // 如果有计价币余额，构造虚拟的计价币资产记录
            if ($marginQuoteAmount > 0) {
                $quoteCurrencyId = $this->marginAssetCalculator->getQuoteCurrencyId($currencyId);

                // 获取计价币符号
                $quoteCurrency = \App\Model\Currency\Currency::find($quoteCurrencyId);
                $quoteCurrencySymbol = $quoteCurrency ? $quoteCurrency->base_asset : 'UNKNOWN';

                // 构造虚拟的计价币资产记录
                $virtualQuoteAsset = [
                    'id' => 0, // 虚拟记录
                    'user_id' => $userId,
                    'account_type' => $accountType->value,
                    'currency_id' => $quoteCurrencyId,
                    'available' => $marginQuoteAmount,
                    'frozen' => 0,
                    'locked' => 0,
                    'margin_quote' => 0,
                    'borrowed_amount' => 0,
                    'interest_amount' => 0,
                    'status' => 1,
                    'currency_symbol' => $quoteCurrencySymbol,
                    'created_at' => $baseAsset['created_at'],
                    'updated_at' => $baseAsset['updated_at'],
                ];

                $result[] = $virtualQuoteAsset;
            }
        }

        return $result;
    }

    /**
     * 释放冻结资金
     */
    private function releaseFrozenAssets(TradeMarginOrder $marginOrder, MatchOrder $matchOrder): void
    {
        $userId = $marginOrder->getUserId();
        $currencyId = $marginOrder->getCurrencyId();
        $side = $marginOrder->getPositionSide();
        $marginType = $marginOrder->getMarginType();
        $accountType = $marginType === MarginType::CROSS ? AccountType::MARGIN : AccountType::ISOLATED;

        // 确定释放的币种和字段
        if ($side === PositionSide::LONG) {
            // 买入订单：释放计价币
            if ($marginType === MarginType::CROSS) {
                // 全仓：释放独立的计价币资产记录
                $quoteCurrencyId = $this->marginAssetCalculator->getQuoteCurrencyId($currencyId);
                $releaseCurrencyId = $quoteCurrencyId;
                $frozenField = 'frozen';
                $targetField = 'available';
            } else {
                // 逐仓：从基础币记录的locked字段释放到margin_quote字段
                $releaseCurrencyId = $currencyId;
                $frozenField = 'locked';
                $targetField = 'margin_quote';
            }
        } else {
            // 卖出订单：释放基础币（全仓和逐仓都一样）
            $releaseCurrencyId = $currencyId;
            $frozenField = 'frozen';
            $targetField = 'available';
        }

        // 释放冻结金额
        $frozenAmount = $marginOrder->getFrozenAmount();
        if ($frozenAmount > 0) {
            $this->userAccountsAssetService->unfreezeAsset(
                $userId,
                $accountType->value,
                $releaseCurrencyId,
                $frozenAmount,
                FlowsType::MARGIN_TRADE->value,
                0,
                $frozenField,
                $targetField
            );

            $this->logger->info('释放杠杆订单冻结资金', [
                'user_id' => $userId,
                'margin_order_id' => $marginOrder->getId(),
                'currency_id' => $releaseCurrencyId,
                'frozen_amount' => $frozenAmount,
                'frozen_field' => $frozenField,
                'target_field' => $targetField,
            ]);
        }
    }

    /**
     * 触发仓位监控（延时异步任务）
     */
    private function triggerPositionMonitoring(TradeMarginPosition $position, string $changeType, array $changeData = []): void
    {
        try {
            $queue = $this->driverFactory->get('position_monitoring');
            $queue->push(new PositionMonitoringJob(
                $position->getId(),
                $changeType,
                $changeData
            ),5);

            $this->logger->debug('仓位监控任务已推送', [
                'position_id' => $position->getId(),
                'change_type' => $changeType,
                'delay' => 5
            ]);

        } catch (\Exception $e) {
            $this->logger->error('推送仓位监控任务失败', [
                'position_id' => $position->getId(),
                'change_type' => $changeType,
                'error' => $e->getMessage()
            ]);
        }
    }
}