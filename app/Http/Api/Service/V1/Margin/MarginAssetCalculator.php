<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆资产计算公共服务
 */

namespace App\Http\Api\Service\V1\Margin;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\User\AccountType;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Context\ApplicationContext;
use Hyperf\Redis\Redis;
use App\Enum\MarketData\TickerSyncKey;
use App\Enum\MarketType;
use App\Enum\CurrencyConfigKey;
use Psr\Log\LoggerInterface;

class MarginAssetCalculator
{
    private UserAccountsAssetService $userAccountsAssetService;
    private Redis $redis;
    private LoggerInterface $logger;

    public function __construct(
        UserAccountsAssetService $userAccountsAssetService,
        Redis $redis,
        LoggerInterface $logger
    ) {
        $this->userAccountsAssetService = $userAccountsAssetService;
        $this->redis = $redis;
        $this->logger = $logger;
    }

    /**
     * 获取币种价格
     */
    public function getCurrencyPrice(int $currencyId): float
    {
        try {
            // 尝试从ticker获取价格
            $tradeKey = TickerSyncKey::getOuterTradeKey($currencyId, MarketType::CRYPTO->value);
            if ($this->redis->exists($tradeKey)) {
                $price = $this->redis->hGet($tradeKey, 'price');
                if ($price !== false && $price !== null && $price > 0) {
                    return (float)$price;
                }
            }

            // 如果没有价格数据，记录警告并返回默认价格
            $this->logger->warning('无法获取币种价格，使用默认价格1.0', [
                'currency_id' => $currencyId
            ]);
            return 1.0;

        } catch (\Exception $e) {
            $this->logger->error('获取币种价格失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return 1.0; // 默认价格
        }
    }

    /**
     * 获取计价币种ID
     */
    public function getQuoteCurrencyId(int $currencyId): int
    {
        try {
            $redisKey = CurrencyConfigKey::getCurrencyKey($currencyId);
            
            if (!$this->redis->exists($redisKey)) {
                throw new \RuntimeException('币种配置不存在');
            }

            $quoteAssetsId = $this->redis->hGet($redisKey, 'quote_assets_id');
            
            if (!$quoteAssetsId) {
                throw new \RuntimeException('计价币种ID不存在');
            }

            return (int)$quoteAssetsId;

        } catch (\Exception $e) {
            $this->logger->error('获取计价币种ID失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            throw new \RuntimeException('获取计价币种失败：' . $e->getMessage());
        }
    }

    /**
     * 计算用户杠杆资产价值
     */
    public function getUserMarginAssetValue(int $userId, int $currencyId, MarginType $marginType): float
    {
        try {
            $accountType = $marginType === MarginType::CROSS ? AccountType::MARGIN : AccountType::ISOLATED;

            if ($marginType === MarginType::CROSS) {
                // 全仓：获取用户杠杆账户下的所有资产总价值
                return $this->getAllMarginAssetValue($userId, $accountType);
            } else {
                // 逐仓：获取基础币+计价币资产价值
                return $this->getIsolatedMarginAssetValue($userId, $currencyId, $accountType);
            }

        } catch (\Exception $e) {
            $this->logger->error('计算用户杠杆资产价值失败', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'margin_type' => $marginType->value,
                'error' => $e->getMessage()
            ]);
            return 0.0;
        }
    }

    /**
     * 获取全仓杠杆账户所有资产总价值
     */
    public function getAllMarginAssetValue(int $userId, AccountType $accountType): float
    {
        // 获取用户该账户类型下的所有资产
        $assets = $this->userAccountsAssetService->getUserAssets($userId, $accountType->value);
        
        $totalValue = 0.0;
        
        foreach ($assets as $asset) {
            // 跳过余额为0的资产
            $netAmount = $asset['available'] + $asset['frozen'] - ($asset['borrowed_amount'] ?? 0);
            if (bccomp((string)$netAmount, '0', 18) <= 0) {
                continue;
            }
            
            // 获取该币种的当前价格
            $price = $this->getCurrencyPrice($asset['currency_id']);
            
            // 计算该币种的价值：(可用 + 冻结 - 借款) × 价格
            $assetValue = bcmul((string)$netAmount, (string)$price, 18);
            $totalValue = bcadd((string)$totalValue, $assetValue, 18);
        }
        
        return (float)$totalValue;
    }

    /**
     * 格式化数字为BC数学库可用的字符串格式
     * 只处理科学计数法等特殊格式，避免破坏精确数字
     */
    private function formatNumberForBC($number): string
    {
        if ($number === null) {
            return '0';
        }

        $str = (string)$number;

        // 检查是否包含科学计数法（E或e）
        if (stripos($str, 'e') !== false) {
            // 只有科学计数法才使用sprintf转换
            $formatted = sprintf('%.18f', (float)$number);
            // 移除末尾的零和小数点
            $formatted = rtrim($formatted, '0');
            $formatted = rtrim($formatted, '.');
            return $formatted === '' || $formatted === '-' ? '0' : $formatted;
        }

        // 普通数字直接返回字符串形式
        return $str;
    }

    /**
     * 获取逐仓杠杆指定交易对资产价值（基础币+计价币）
     */
    public function getIsolatedMarginAssetValue(int $userId, int $currencyId, AccountType $accountType): float
    {
        try {
            // 获取基础币资产记录（逐仓模式下计价币资产存储在同一条记录的margin_quote字段中）
            $baseAssetId = $currencyId;
            $quoteAssetId = $this->getQuoteCurrencyId($currencyId);

            $totalValue = 0.0;

            // 获取基础币资产记录
            $baseAsset = $this->userAccountsAssetService->getUserAsset($userId, $accountType->value, $baseAssetId);
            if ($baseAsset) {
                // 计算基础币资产价值（available + frozen - borrowed_amount）
                $baseNetAmount = $baseAsset->available + $baseAsset->frozen - ($baseAsset->borrowed_amount ?? 0);
                $baseNetAmountStr = $this->formatNumberForBC($baseNetAmount);
                if (bccomp($baseNetAmountStr, '0', 18) > 0) {
                    $basePrice = $this->getCurrencyPrice($baseAssetId);
                    $baseValue = bcmul($baseNetAmountStr, (string)$basePrice, 18);
                    $totalValue = bcadd((string)$totalValue, $baseValue, 18);
                }

                // 计算计价币资产价值（从margin_quote字段获取）
                $quoteNetAmount = $baseAsset->margin_quote ?? 0;
                $quoteNetAmountStr = $this->formatNumberForBC($quoteNetAmount);
                if (bccomp($quoteNetAmountStr, '0', 18) > 0) {
                    $quotePrice = $this->getCurrencyPrice($quoteAssetId);
                    $quoteValue = bcmul($quoteNetAmountStr, (string)$quotePrice, 18);
                    $totalValue = bcadd((string)$totalValue, $quoteValue, 18);
                }
            }

            return (float)$totalValue;

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '获取逐仓资产价值失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户可用资金
     */
    public function getUserAvailableFunds(int $userId, int $currencyId, MarginType $marginType): float
    {
        $accountType = $marginType === MarginType::CROSS 
            ? AccountType::MARGIN 
            : AccountType::ISOLATED;
            
        if ($marginType === MarginType::CROSS) {
            // 全仓：获取所有可用资产总价值
            return $this->getAllAvailableAssetValue($userId, $accountType);
        } else {
            // 逐仓：获取对应交易对的可用资产价值
            return $this->getIsolatedAvailableAssetValue($userId, $currencyId, $accountType);
        }
    }

    /**
     * 获取用户全仓可用资产总价值
     */
    private function getAllAvailableAssetValue(int $userId, AccountType $accountType): float
    {
        $assets = $this->userAccountsAssetService->getUserAssets($userId, $accountType->value);
        
        $totalValue = 0.0;
        
        foreach ($assets as $asset) {
            // 只计算可用余额（不包括冻结和借款）
            $availableAmount = $asset['available'];
            if (bccomp((string)$availableAmount, '0', 18) > 0) {
                $price = $this->getCurrencyPrice($asset['currency_id']);
                $assetValue = bcmul((string)$availableAmount, (string)$price, 18);
                $totalValue = bcadd((string)$totalValue, $assetValue, 18);
            }
        }
        
        return (float)$totalValue;
    }

    /**
     * 获取用户逐仓可用资产价值
     */
    private function getIsolatedAvailableAssetValue(int $userId, int $currencyId, AccountType $accountType): float
    {
        try {
            $quoteAssetId = $this->getQuoteCurrencyId($currencyId);
            $baseAssetId = $currencyId;

            $totalValue = 0.0;

            // 获取基础币资产记录（逐仓模式下计价币资产存储在同一条记录的margin_quote字段中）
            $baseAsset = $this->userAccountsAssetService->getUserAsset($userId, $accountType->value, $baseAssetId);
            if ($baseAsset) {
                // 计算基础币可用资产价值
                $baseAvailableStr = $this->formatNumberForBC($baseAsset->available);
                if (bccomp($baseAvailableStr, '0', 18) > 0) {
                    $basePrice = $this->getCurrencyPrice($baseAssetId);
                    $baseValue = bcmul($baseAvailableStr, (string)$basePrice, 18);
                    $totalValue = bcadd((string)$totalValue, $baseValue, 18);
                }

                // 计算计价币可用资产价值（从margin_quote字段获取）
                $quoteAvailable = $baseAsset->margin_quote ?? 0;
                $quoteAvailableStr = $this->formatNumberForBC($quoteAvailable);
                if (bccomp($quoteAvailableStr, '0', 18) > 0) {
                    $quotePrice = $this->getCurrencyPrice($quoteAssetId);
                    $quoteValue = bcmul($quoteAvailableStr, (string)$quotePrice, 18);
                    $totalValue = bcadd((string)$totalValue, $quoteValue, 18);
                }
            }

            return (float)$totalValue;

        } catch (\Exception $e) {
            throw new BusinessException(ResultCode::FAIL, '获取逐仓可用资产失败：' . $e->getMessage());
        }
    }
} 