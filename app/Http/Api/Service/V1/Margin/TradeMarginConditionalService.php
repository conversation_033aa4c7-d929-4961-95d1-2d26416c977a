<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆委托订单核心服务
 */

namespace App\Http\Api\Service\V1\Margin;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\Trade\TradeMarginConditionalOrder;
use App\Model\Trade\TradeMarginPosition;
use App\Model\Enums\Trade\Margin\ConditionalOrderType;
use App\Model\Enums\Trade\Margin\ConditionalOrderStatus;
use App\Model\Enums\Trade\Margin\TriggerType;
use App\Model\Enums\Trade\Margin\ExecutionType;
use App\Model\Enums\Trade\Margin\PositionSide;
use App\Enum\MarginConditionalOrderRedisKey;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;
use Hyperf\Logger\LoggerFactory;
use ApiElf\QueryBuilder\QueryBuilder;

class TradeMarginConditionalService
{
    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('margin_conditional', 'default');
    }

    /**
     * 创建委托订单
     */
    public function createConditionalOrder(int $userId, array $params): TradeMarginConditionalOrder
    {
        $currencyId = (int)$params['currency_id'];
        $marginType = (int)$params['margin_type'];
        $positionSide = (int)$params['position_side'];
        $orderType = ConditionalOrderType::from((int)$params['order_type']);
        $triggerPrice = (float)$params['trigger_price'];
        $closeQuantity = (float)$params['close_quantity'];
        $triggerType = TriggerType::from((int)$params['trigger_type']);
        $executionType = ExecutionType::from((int)$params['execution_type']);
        $executionPrice = isset($params['execution_price']) ? (float)$params['execution_price'] : null;

        return Db::transaction(function () use ($userId, $currencyId, $marginType, $positionSide, $orderType, $triggerPrice, $closeQuantity, $triggerType, $executionType, $executionPrice) {
            // 1. 验证用户在该币种和保证金类型下是否有对应方向的仓位
            $position = TradeMarginPosition::where('user_id', $userId)
                ->where('currency_id', $currencyId)
                ->where('margin_type', $marginType)
                ->where('side', $positionSide)
                ->where('status', 1) // 仓位必须是持仓中状态
                ->first();

            if (!$position) {
                $positionSideName = $positionSide === 1 ? '做多' : '做空';
                throw new BusinessException(ResultCode::FAIL, "用户在该币种下不存在{$positionSideName}仓位");
            }

            // 2. 验证委托数量不超过仓位数量
            if (bccomp((string)$closeQuantity, (string)$position->getQuantity(), 18) > 0) {
                throw new BusinessException(ResultCode::FAIL, '委托平仓数量不能超过持仓数量');
            }

            // 3. 验证触发价格合理性
            $this->validateTriggerPrice($position, $orderType, $triggerPrice);

            // 4. 验证执行参数
            $this->validateExecutionParams($executionType, $executionPrice);

            // 5. 检查是否已存在相同类型的委托订单
            $existingOrder = TradeMarginConditionalOrder::where('user_id', $userId)
                ->where('currency_id', $currencyId)
                ->where('margin_type', $marginType)
                ->where('position_side', $positionSide)
                ->where('order_type', $orderType->value)
                ->where('status', ConditionalOrderStatus::WAITING->value)
                ->first();

            if ($existingOrder) {
                throw new BusinessException(ResultCode::FAIL, '该币种该方向已存在相同类型的委托订单，请先撤销后再创建');
            }

            // 6. 创建委托订单记录
            $conditionalOrder = new TradeMarginConditionalOrder();
            $conditionalOrder->setUserId($userId);
            $conditionalOrder->setCurrencyId($currencyId);
            $conditionalOrder->setMarginType($marginType);
            $conditionalOrder->setPositionSide($positionSide);
            $conditionalOrder->setOrderType($orderType->value);
            $conditionalOrder->setTriggerPrice($triggerPrice);
            $conditionalOrder->setCloseQuantity($closeQuantity);
            $conditionalOrder->setTriggerType($triggerType->value);
            $conditionalOrder->setExecutionType($executionType->value);
            $conditionalOrder->setExecutionPrice($executionPrice);
            $conditionalOrder->setStatus(ConditionalOrderStatus::WAITING->value);

            if (!$conditionalOrder->save()) {
                throw new BusinessException(ResultCode::FAIL, '创建委托订单失败');
            }

            // 6. 添加到Redis监控队列
            $this->addToRedisMonitor($conditionalOrder);

            $this->logger->info('杠杆委托订单创建成功', [
                'user_id' => $userId,
                'conditional_order_id' => $conditionalOrder->getId(),
                'currency_id' => $currencyId,
                'margin_type' => $marginType,
                'position_side' => $positionSide,
                'order_type' => $orderType->value,
                'trigger_price' => $triggerPrice,
                'close_quantity' => $closeQuantity,
            ]);

            return $conditionalOrder;
        });
    }

    /**
     * 修改委托订单
     */
    public function updateConditionalOrder(int $userId, int $orderId, array $params): TradeMarginConditionalOrder
    {
        return Db::transaction(function () use ($userId, $orderId, $params) {
            // 1. 查找委托订单
            $conditionalOrder = TradeMarginConditionalOrder::where('id', $orderId)
                ->where('user_id', $userId)
                ->where('status', ConditionalOrderStatus::WAITING->value)
                ->first();

            if (!$conditionalOrder) {
                throw new BusinessException(ResultCode::FAIL, '委托订单不存在或状态不允许修改');
            }

            // 2. 获取关联仓位
            $position = TradeMarginPosition::where('user_id', $userId)
                ->where('currency_id', $conditionalOrder->getCurrencyId())
                ->where('margin_type', $conditionalOrder->getMarginType())
                ->where('side', $conditionalOrder->getPositionSide())
                ->where('status', 1)
                ->first();
                
            if (!$position) {
                throw new BusinessException(ResultCode::FAIL, '关联仓位不存在或已平仓');
            }

            $hasChanges = false;
            
            // 只允许修改以下4个字段：委托数量、触发价格、执行价格、执行方式
            $allowedFields = ['close_quantity', 'trigger_price', 'execution_price', 'execution_type'];
            
            // 过滤掉不允许修改的字段
            $filteredParams = array_intersect_key($params, array_flip($allowedFields));

            // 3. 更新委托数量
            if (isset($filteredParams['close_quantity'])) {
                $newCloseQuantity = (float)$filteredParams['close_quantity'];
                
                // 验证新的委托数量
                if (bccomp((string)$newCloseQuantity, (string)$position->getQuantity(), 18) > 0) {
                    throw new BusinessException(ResultCode::FAIL, '委托平仓数量不能超过持仓数量');
                }
                
                if (bccomp((string)$newCloseQuantity, (string)$conditionalOrder->getCloseQuantity(), 18) != 0) {
                    $conditionalOrder->setCloseQuantity($newCloseQuantity);
                    $hasChanges = true;
                }
            }

            // 4. 更新触发价格
            if (isset($filteredParams['trigger_price'])) {
                $newTriggerPrice = (float)$filteredParams['trigger_price'];
                $orderType = ConditionalOrderType::from($conditionalOrder->getOrderType());
                
                // 验证新的触发价格
                $this->validateTriggerPrice($position, $orderType, $newTriggerPrice);
                
                if (bccomp((string)$newTriggerPrice, (string)$conditionalOrder->getTriggerPrice(), 18) != 0) {
                    $conditionalOrder->setTriggerPrice($newTriggerPrice);
                    $hasChanges = true;
                }
            }

            // 5. 更新执行方式
            if (isset($filteredParams['execution_type'])) {
                $newExecutionType = ExecutionType::from((int)$filteredParams['execution_type']);
                if ($newExecutionType->value !== $conditionalOrder->getExecutionType()) {
                    $conditionalOrder->setExecutionType($newExecutionType->value);
                    $hasChanges = true;
                }
            }

            // 6. 更新执行价格
            if (isset($filteredParams['execution_price'])) {
                $newExecutionPrice = $filteredParams['execution_price'] ? (float)$filteredParams['execution_price'] : null;
                if (bccomp((string)($newExecutionPrice ?? 0), (string)($conditionalOrder->getExecutionPrice() ?? 0), 18) != 0) {
                    $conditionalOrder->setExecutionPrice($newExecutionPrice);
                    $hasChanges = true;
                }
            }

            // 7. 验证修改后的执行参数
            if (isset($filteredParams['execution_type']) || isset($filteredParams['execution_price'])) {
                $executionType = ExecutionType::from($conditionalOrder->getExecutionType());
                $executionPrice = $conditionalOrder->getExecutionPrice();
                $this->validateExecutionParams($executionType, $executionPrice);
            }

            if (!$hasChanges) {
                throw new BusinessException(ResultCode::FAIL, '没有检测到任何修改');
            }

            // 8. 保存修改
            if (!$conditionalOrder->save()) {
                throw new BusinessException(ResultCode::FAIL, '修改委托订单失败');
            }

            // 9. 更新Redis监控队列
            $this->updateRedisMonitor($conditionalOrder);

            $this->logger->info('杠杆委托订单修改成功', [
                'user_id' => $userId,
                'conditional_order_id' => $orderId,
                'changes' => $filteredParams,
                'allowed_fields' => $allowedFields,
            ]);

            return $conditionalOrder;
        });
    }

    /**
     * 撤销委托订单
     */
    public function cancelConditionalOrder(int $userId, int $orderId): bool
    {
        return Db::transaction(function () use ($userId, $orderId) {
            // 1. 查找委托订单
            $conditionalOrder = TradeMarginConditionalOrder::where('id', $orderId)
                ->where('user_id', $userId)
                ->where('status', ConditionalOrderStatus::WAITING->value)
                ->first();

            if (!$conditionalOrder) {
                throw new BusinessException(ResultCode::FAIL, '委托订单不存在或状态不允许撤销');
            }

            // 2. 更新状态为已撤销
            $conditionalOrder->setStatus(ConditionalOrderStatus::CANCELLED->value);
            
            if (!$conditionalOrder->save()) {
                throw new BusinessException(ResultCode::FAIL, '撤销委托订单失败');
            }

            // 3. 从Redis监控队列中移除
            $this->removeFromRedisMonitor($conditionalOrder);

            $this->logger->info('杠杆委托订单撤销成功', [
                'user_id' => $userId,
                'conditional_order_id' => $orderId,
            ]);

            return true;
        });
    }

    /**
     * 查询委托订单列表
     */
    public function getConditionalOrderList(int $userId, ?int $currencyId = null, ?int $orderType = null, ?int $status = null, int $perPage = 20, int $page = 1): array
    {
        $query = QueryBuilder::for(TradeMarginConditionalOrder::class)
            ->allowedFilters([
                'currency_id',
                'order_type', 
                'status'
            ])
            ->where('user_id', $userId);

        if ($currencyId) {
            $query = $query->where('currency_id', $currencyId);
        }

        if ($orderType) {
            $query = $query->where('order_type', $orderType);
        }

        if ($status) {
            $query = $query->where('status', $status);
        }

        $result = $query->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        return [
            'data' => $result->items(),
            'pagination' => [
                'current_page' => $result->currentPage(),
                'last_page' => $result->lastPage(),
                'per_page' => $result->perPage(),
                'total' => $result->total(),
            ]
        ];
    }

    /**
     * 查询委托订单详情
     */
    public function getConditionalOrderDetail(int $userId, int $orderId): TradeMarginConditionalOrder
    {
        $conditionalOrder = TradeMarginConditionalOrder::where('id', $orderId)
            ->where('user_id', $userId)
            ->first();

        if (!$conditionalOrder) {
            throw new BusinessException(ResultCode::FAIL, '委托订单不存在');
        }

        return $conditionalOrder;
    }

    /**
     * 验证执行参数
     */
    private function validateExecutionParams(ExecutionType $executionType, ?float $executionPrice): void
    {
        // 限价执行时必须有执行价格
        if ($executionType === ExecutionType::LIMIT && ($executionPrice === null || $executionPrice <= 0)) {
            throw new BusinessException(ResultCode::FAIL, '限价执行时必须设置有效的执行价格');
        }

        // 市价执行时不应该有执行价格
        if ($executionType === ExecutionType::MARKET && $executionPrice !== null) {
            throw new BusinessException(ResultCode::FAIL, '市价执行时不应设置执行价格');
        }
    }

    /**
     * 验证触发价格合理性
     */
    private function validateTriggerPrice(TradeMarginPosition $position, ConditionalOrderType $orderType, float $triggerPrice): void
    {
        $positionSide = $position->getSide();
        $entryPrice = $position->getEntryPrice();

        // 做多仓位
        if ($positionSide === PositionSide::LONG) {
            if ($orderType === ConditionalOrderType::TAKE_PROFIT) {
                // 做多止盈：触发价格应该高于开仓价
                if (bccomp((string)$triggerPrice, (string)$entryPrice, 18) <= 0) {
                    throw new BusinessException(ResultCode::FAIL, '做多仓位止盈价格必须高于开仓价格');
                }
            } else {
                // 做多止损：触发价格应该低于开仓价
                if (bccomp((string)$triggerPrice, (string)$entryPrice, 18) >= 0) {
                    throw new BusinessException(ResultCode::FAIL, '做多仓位止损价格必须低于开仓价格');
                }
            }
        } else {
            // 做空仓位
            if ($orderType === ConditionalOrderType::TAKE_PROFIT) {
                // 做空止盈：触发价格应该低于开仓价
                if (bccomp((string)$triggerPrice, (string)$entryPrice, 18) >= 0) {
                    throw new BusinessException(ResultCode::FAIL, '做空仓位止盈价格必须低于开仓价格');
                }
            } else {
                // 做空止损：触发价格应该高于开仓价
                if (bccomp((string)$triggerPrice, (string)$entryPrice, 18) <= 0) {
                    throw new BusinessException(ResultCode::FAIL, '做空仓位止损价格必须高于开仓价格');
                }
            }
        }
    }

    /**
     * 添加到Redis监控队列
     */
    private function addToRedisMonitor(TradeMarginConditionalOrder $conditionalOrder): void
    {
        $currencyId = $conditionalOrder->getCurrencyId();
        $orderId = $conditionalOrder->getId();
        $triggerPrice = $conditionalOrder->getTriggerPrice();

        // 根据委托类型和仓位方向确定触发条件
        $condition = $this->getTriggerCondition($conditionalOrder);
        
        // 添加到对应的有序集合
        $redisKey = MarginConditionalOrderRedisKey::getConditionalKey($currencyId, $condition);
        $this->redis->zAdd($redisKey, $triggerPrice, $orderId);

        // 存储委托订单详情
        $detailKey = MarginConditionalOrderRedisKey::getDetailKey($orderId);
        $this->redis->hMSet($detailKey, [
            'id' => $orderId,
            'user_id' => $conditionalOrder->getUserId(),
            'currency_id' => $currencyId,
            'margin_type' => $conditionalOrder->getMarginType(),
            'position_side' => $conditionalOrder->getPositionSide(),
            'order_type' => $conditionalOrder->getOrderType(),
            'trigger_price' => $triggerPrice,
            'close_quantity' => $conditionalOrder->getCloseQuantity(),
            'trigger_type' => $conditionalOrder->getTriggerType(),
            'execution_type' => $conditionalOrder->getExecutionType(),
            'execution_price' => $conditionalOrder->getExecutionPrice() ?? '',
            'condition' => $condition,
        ]);

        // 设置过期时间（30天）
        $this->redis->expire($detailKey, 30 * 24 * 3600);
    }

    /**
     * 更新Redis监控队列
     */
    private function updateRedisMonitor(TradeMarginConditionalOrder $conditionalOrder): void
    {
        // 先删除旧的记录
        $this->removeFromRedisMonitor($conditionalOrder);
        // 再添加新的记录
        $this->addToRedisMonitor($conditionalOrder);
    }

    /**
     * 从Redis监控队列中移除
     */
    private function removeFromRedisMonitor(TradeMarginConditionalOrder $conditionalOrder): void
    {
        $currencyId = $conditionalOrder->getCurrencyId();
        $orderId = $conditionalOrder->getId();

        // 从两个集合中都尝试删除
        $gteKey = MarginConditionalOrderRedisKey::getGteKey($currencyId);
        $lteKey = MarginConditionalOrderRedisKey::getLteKey($currencyId);
        
        $this->redis->zRem($gteKey, $orderId);
        $this->redis->zRem($lteKey, $orderId);

        // 删除详情记录
        $detailKey = MarginConditionalOrderRedisKey::getDetailKey($orderId);
        $this->redis->del($detailKey);
    }

    /**
     * 根据委托订单类型和仓位方向确定触发条件
     */
    private function getTriggerCondition(TradeMarginConditionalOrder $conditionalOrder): string
    {
        $orderType = ConditionalOrderType::from($conditionalOrder->getOrderType());
        $positionSide = PositionSide::from($conditionalOrder->getPositionSide());

        // 做多仓位：止盈用gte（价格上涨触发），止损用lte（价格下跌触发）
        // 做空仓位：止盈用lte（价格下跌触发），止损用gte（价格上涨触发）
        if ($positionSide === PositionSide::LONG) {
            return $orderType === ConditionalOrderType::TAKE_PROFIT ? 'gte' : 'lte';
        } else {
            return $orderType === ConditionalOrderType::TAKE_PROFIT ? 'lte' : 'gte';
        }
    }
} 