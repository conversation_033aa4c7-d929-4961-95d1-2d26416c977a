<?php

/**
 * KlineService.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/26
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Service\V1;

use App\Enum\CurrencyConfigKey;
use App\Enum\MarketData\KlineIndex;
use App\Model\Currency\Currency;
use Elasticsearch\Client;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use MessagePack\MessagePack;

class KlineService
{
    #[Inject]
    private Redis $redis;

    private ?Client $elasticsearch = null;

    private function getElasticsearch(): Client
    {
        if ($this->elasticsearch === null) {
            $this->elasticsearch = elasticsearch();
        }
        return $this->elasticsearch;
    }

    /**
     * 获取历史K线数据
     * @param int $currencyId 币种ID
     * @param int $marketType 市场类型 1=现货 5=合约
     * @param string $period 时间周期
     * @param int|null $startTime 开始时间戳(毫秒)，为null时获取最近数据
     * @param int|null $endTime 结束时间戳(毫秒)，为null时获取最近数据
     * @param int $limit 限制条数
     * @return array|string
     */
    public function getHistoryKline(int $currencyId, int $marketType, string $period, ?int $startTime, ?int $endTime, int $limit = 1000)
    {
        // 根据currency_id获取symbol
        if(!$this->redis->exists(CurrencyConfigKey::getCurrencyKey($currencyId))){
            return [];
        }

        $symbol = $this->redis->hGet(CurrencyConfigKey::getCurrencyKey($currencyId),'symbol');

        $indexName = KlineIndex::getIndexName($symbol, $marketType);

        try {
            // 构建查询条件
            $mustConditions = [
                ['term' => ['period' => $period]]
            ];

            // 如果提供了时间范围，添加时间过滤条件
            if ($startTime !== null && $endTime !== null) {
                $mustConditions[] = ['range' => [
                    'open_time' => [
                        'gte' => $startTime,
                        'lte' => $endTime
                    ]
                ]];
                $sortOrder = 'asc'; // 有时间范围时按时间升序
            } else {
                $sortOrder = 'desc'; // 没有时间范围时按时间降序获取最新数据
            }

            $params = [
                'index' => $indexName,
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => $mustConditions
                        ]
                    ],
                    'sort' => [
                        ['open_time' => ['order' => $sortOrder]]
                    ],
                    'size' => $limit
                ]
            ];

            $response = $this->getElasticsearch()->search($params);

            if (!isset($response['hits']['hits'])) {
                return [];
            }

            // 转换数据格式为二维数组
            $result = [];
            foreach ($response['hits']['hits'] as $hit) {
                $source = $hit['_source'];
                $ohlcv = $source['ohlcv'];

                // 格式: [open_time, close_time, open, high, low, close, volume, quote_volume, trades]
                $result[] = [
                    $source['open_time'],
                    $source['close_time'],
                    $ohlcv[0], // open
                    $ohlcv[1], // high
                    $ohlcv[2], // low
                    $ohlcv[3], // close
                    $ohlcv[4], // volume
                    $ohlcv[5], // quote_volume
                    $ohlcv[6]  // trades
                ];
            }

            // 如果是获取最新数据（没有时间范围），需要将结果按时间升序重新排列
            if ($startTime === null && $endTime === null && !empty($result)) {
                usort($result, function($a, $b) {
                    return $a[0] <=> $b[0]; // 按open_time升序排列
                });
            }

            return $result;

        } catch (\Throwable $e) {
            var_dump($e->getMessage());
            return [];
        }
    }
}