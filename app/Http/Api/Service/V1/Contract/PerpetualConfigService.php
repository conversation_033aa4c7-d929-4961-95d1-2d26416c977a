<?php

declare(strict_types=1);
/**
 * 永续合约配置服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Model\Trade\TradeConfig;
use App\Model\Trade\TradeMarginLevel;
use App\Model\User\VipLevel;
use App\Enum\MarketType;
use Hyperf\Di\Annotation\Inject;

class PerpetualConfigService
{
    #[Inject]
    protected TradeConfig $tradeConfig;

    #[Inject]
    protected TradeMarginLevel $tradeMarginLevel;

    #[Inject]
    protected VipLevel $vipLevel;

    /**
     * 获取合约交易规则
     */
    public function getExchangeInfo(?int $currencyId = null): array
    {
        $query = $this->tradeConfig->newQuery()
            ->where('market_type', MarketType::MARGIN->value)
            ->with(['currency']);

        if ($currencyId) {
            $query->where('currency_id', $currencyId);
        }

        $configs = $query->get();

        $result = [];
        foreach ($configs as $config) {
            $result[] = [
                'currency_id' => $config->currency_id,
                'symbol' => $config->currency->symbol ?? '',
                'min_trade_num' => $config->min_trade_num,
                'max_trade_num' => $config->max_trade_num,
                'min_trade_price' => $config->min_trade_price,
                'max_trade_price' => $config->max_trade_price,
                'limit_price_rate' => $config->limit_price_rate,
                'maker_limit' => $config->maker_limit,
                'limit_limit' => $config->limit_limit,
                'order_limit' => $config->order_limit,
                'trigger_protect' => $config->trigger_protect,
            ];
        }

        return $result;
    }

    /**
     * 获取合约列表
     */
    public function getContracts(): array
    {
        $contracts = $this->tradeConfig->newQuery()
            ->where('market_type', MarketType::MARGIN->value)
            ->with(['currency'])
            ->get();

        $result = [];
        foreach ($contracts as $contract) {
            $result[] = [
                'currency_id' => $contract->currency_id,
                'symbol' => $contract->currency->symbol ?? '',
                'base_asset' => $contract->currency->base_currency ?? '',
                'quote_asset' => $contract->currency->quote_currency ?? '',
                'status' => 'TRADING',
                'contract_type' => 'PERPETUAL',
                'delivery_date' => null,
                'onboard_date' => $contract->created_at->timestamp * 1000,
            ];
        }

        return $result;
    }

    /**
     * 获取杠杆档位配置
     */
    public function getLeverageBracket(?int $currencyId = null): array
    {
        $query = $this->tradeMarginLevel->newQuery()
            ->orderBy('currency_id')
            ->orderBy('level');

        if ($currencyId) {
            $query->where('currency_id', $currencyId);
        }

        $brackets = $query->get();

        $result = [];
        foreach ($brackets as $bracket) {
            $result[] = [
                'currency_id' => $bracket->currency_id,
                'level' => $bracket->level,
                'notional_floor' => $bracket->margin_min,
                'notional_cap' => $bracket->margin_max,
                'maintenance_margin_ratio' => $bracket->margin_rate,
                'cum' => $bracket->cum_amount,
                'min_leverage' => $bracket->leverage_min,
                'max_leverage' => $bracket->leverage_max,
            ];
        }

        return $result;
    }

    /**
     * 获取24小时价格变动统计
     */
    public function get24hrTicker(?int $currencyId = null): array
    {
        // TODO: 实现24小时价格统计逻辑
        // 这里需要从行情数据中获取24小时统计
        return [
            'symbol' => 'BTCUSDT',
            'price_change' => '0.00',
            'price_change_percent' => '0.00',
            'weighted_avg_price' => '0.00',
            'prev_close_price' => '0.00',
            'last_price' => '0.00',
            'last_qty' => '0.00',
            'bid_price' => '0.00',
            'ask_price' => '0.00',
            'open_price' => '0.00',
            'high_price' => '0.00',
            'low_price' => '0.00',
            'volume' => '0.00',
            'quote_volume' => '0.00',
            'open_time' => time() * 1000,
            'close_time' => time() * 1000,
            'first_id' => 0,
            'last_id' => 0,
            'count' => 0,
        ];
    }

    /**
     * 获取最新价格
     */
    public function getTickerPrice(?int $currencyId = null): array
    {
        // TODO: 实现最新价格获取逻辑
        // 这里需要从行情数据中获取最新价格
        return [
            'symbol' => 'BTCUSDT',
            'price' => '0.00',
            'time' => time() * 1000,
        ];
    }

    /**
     * 获取标记价格
     */
    public function getMarkPrice(?int $currencyId = null): array
    {
        // TODO: 实现标记价格计算逻辑
        // 标记价格 = 指数价格 + 基差
        return [
            'symbol' => 'BTCUSDT',
            'mark_price' => '0.00',
            'index_price' => '0.00',
            'estimated_settle_price' => '0.00',
            'last_funding_rate' => '0.00',
            'next_funding_time' => time() * 1000,
            'time' => time() * 1000,
        ];
    }

    /**
     * 获取资金费率
     */
    public function getFundingRate(?int $currencyId = null): array
    {
        // TODO: 实现资金费率计算逻辑
        // 资金费率 = 溢价率 + 利率
        return [
            'symbol' => 'BTCUSDT',
            'funding_rate' => '0.00010000',
            'funding_time' => time() * 1000,
            'next_funding_time' => (time() + 8 * 3600) * 1000,
        ];
    }

    /**
     * 获取资金费率历史
     */
    public function getFundingRateHistory(int $currencyId, int $limit = 100, ?int $startTime = null, ?int $endTime = null): array
    {
        // TODO: 实现资金费率历史查询逻辑
        return [];
    }

    /**
     * 获取深度信息
     */
    public function getDepth(int $currencyId, int $limit = 100): array
    {
        // TODO: 实现深度信息获取逻辑
        return [
            'last_update_id' => time(),
            'bids' => [],
            'asks' => [],
        ];
    }

    /**
     * 获取K线数据
     */
    public function getKlines(int $currencyId, string $interval, int $limit = 500, ?int $startTime = null, ?int $endTime = null): array
    {
        // TODO: 实现K线数据获取逻辑
        return [];
    }

    /**
     * 获取最近成交记录
     */
    public function getRecentTrades(int $currencyId, int $limit = 500): array
    {
        // TODO: 实现最近成交记录获取逻辑
        return [];
    }
}