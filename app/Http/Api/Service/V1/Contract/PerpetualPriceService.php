<?php

declare(strict_types=1);
/**
 * 永续合约价格服务
 */

namespace App\Http\Api\Service\V1\Contract;

use Hyperf\Di\Annotation\Inject;

class PerpetualPriceService
{
    /**
     * 获取标记价格
     */
    public function getMarkPrice(int $currencyId): array
    {
        // TODO: 实现标记价格计算逻辑
        return [
            'mark_price' => '0.00',
            'index_price' => '0.00',
            'time' => time() * 1000,
        ];
    }

    /**
     * 获取指数价格
     */
    public function getIndexPrice(int $currencyId): array
    {
        // TODO: 实现指数价格计算逻辑
        return [
            'index_price' => '0.00',
            'time' => time() * 1000,
        ];
    }

    /**
     * 计算标记价格
     */
    private function calculateMarkPrice(int $currencyId): float
    {
        // TODO: 标记价格 = 指数价格 + 基差
        return 0.0;
    }

    /**
     * 计算指数价格
     */
    private function calculateIndexPrice(int $currencyId): float
    {
        // TODO: 指数价格 = 多个现货交易所的加权平均价格
        return 0.0;
    }
}