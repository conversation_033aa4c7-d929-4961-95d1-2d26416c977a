<?php

declare(strict_types=1);
/**
 * 永续合约委托单管理服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Model\Enums\User\AccountType;
use App\Model\Trade\TradePerpetualConditionalOrder;
use App\Model\Currency\Currency;
use App\Model\Enums\Trade\Perpetual\ConditionalOrderType;
use App\Model\Enums\Trade\Perpetual\ConditionalOrderStatus;
use App\Model\Enums\Trade\Perpetual\TriggerCondition;
use App\Model\Enums\Trade\Perpetual\ExecutionMode;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use App\Model\Enums\Trade\Perpetual\MarginMode;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Enum\CurrencyConfigKey;
use App\Enum\Config\TradeConfigKey;
use App\Enum\MarketType;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;
use Carbon\Carbon;

class PerpetualConditionalOrderService
{
    #[Inject]
    protected TradePerpetualConditionalOrder $conditionalOrderModel;

    #[Inject]
    protected PerpetualConditionalCacheService $cacheService;

    #[Inject]
    protected UserAccountsAssetService $assetService;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('perpetual-conditional', 'perpetual-logs');
    }

    /**
     * 创建委托单
     * @param int $userId 用户ID
     * @param array $orderData 委托单数据
     * @return array
     * @throws BusinessException
     */
    public function createConditionalOrder(int $userId, array $orderData): array
    {
        try {
            // 1. 验证委托单参数
            $this->validateConditionalOrderData($orderData);

            // 2. 检查币种是否存在
            $this->validateCurrency((int)$orderData['currency_id']);

            // 3. 检查用户权限和余额（仅开仓委托单需要检查）
            if ($this->isOpenPosition($orderData['side'])) {
                $this->checkUserPermissions($userId, $orderData);
            }

            // 4. 构建委托单数据
            $conditionalOrderData = $this->buildConditionalOrderData($userId, $orderData);

            // 5. 创建委托单记录
            $conditionalOrder = Db::transaction(function () use ($conditionalOrderData) {
                return TradePerpetualConditionalOrder::create($conditionalOrderData);
            });

            // 6. 添加到缓存
            $this->cacheService->addOrderToZset($conditionalOrder);

            $this->logger->info('委托单创建成功', [
                'user_id' => $userId,
                'conditional_order_id' => $conditionalOrder->id,
                'order_data' => $orderData
            ]);

            return [
                'conditional_order_id' => $conditionalOrder->id,
                'status' => $conditionalOrder->status,
                'created_at' => $conditionalOrder->created_at->toDateTimeString()
            ];

        } catch (BusinessException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('创建委托单失败', [
                'user_id' => $userId,
                'order_data' => $orderData,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '创建委托单失败：' . $e->getMessage());
        }
    }

    /**
     * 撤销委托单
     * @param int $userId 用户ID
     * @param int $conditionalOrderId 委托单ID
     * @return array
     * @throws BusinessException
     */
    public function cancelConditionalOrder(int $userId, int $conditionalOrderId): array
    {
        try {
            // 1. 查询委托单
            $conditionalOrder = $this->getConditionalOrderByUser($userId, $conditionalOrderId);

            // 2. 检查是否可以撤销
            $this->validateCancellation($conditionalOrder);

            // 3. 更新状态为已撤销
            Db::transaction(function () use ($conditionalOrder) {
                $conditionalOrder->update([
                    'status' => ConditionalOrderStatus::CANCELLED->value,
                    'updated_at' => Carbon::now()
                ]);
            });

            // 4. 从缓存移除
            $this->cacheService->removeOrderFromZset($conditionalOrder);

            $this->logger->info('委托单撤销成功', [
                'user_id' => $userId,
                'conditional_order_id' => $conditionalOrderId
            ]);

            return [
                'conditional_order_id' => $conditionalOrderId,
                'status' => 'cancelled',
                'cancelled_at' => Carbon::now()->toDateTimeString()
            ];

        } catch (BusinessException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('撤销委托单失败', [
                'user_id' => $userId,
                'conditional_order_id' => $conditionalOrderId,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '撤销委托单失败：' . $e->getMessage());
        }
    }

    /**
     * 修改委托单
     * @param int $userId 用户ID
     * @param int $conditionalOrderId 委托单ID
     * @param array $updateData 更新数据
     * @return array
     * @throws BusinessException
     */
    public function updateConditionalOrder(int $userId, int $conditionalOrderId, array $updateData): array
    {
        try {
            // 1. 查询委托单
            $conditionalOrder = $this->getConditionalOrderByUser($userId, $conditionalOrderId);

            // 2. 检查是否可以修改
            $this->validateUpdate($conditionalOrder, $updateData);

            // 3. 验证更新数据
            $this->validateUpdateData($updateData);

            // 4. 更新委托单数据
            Db::transaction(function () use ($conditionalOrder, $updateData) {
                $conditionalOrder->update($updateData);
            });

            // 5. 更新缓存
            if (isset($updateData['trigger_price']) || isset($updateData['trigger_condition'])) {
                // 如果触发价格或条件发生变化，需要重新添加到缓存
                $this->cacheService->removeOrderFromZset($conditionalOrder);
                $conditionalOrder->refresh();
                $this->cacheService->addOrderToZset($conditionalOrder);
            }

            $this->logger->info('委托单修改成功', [
                'user_id' => $userId,
                'conditional_order_id' => $conditionalOrderId,
                'update_data' => $updateData
            ]);

            return [
                'conditional_order_id' => $conditionalOrderId,
                'status' => 'updated',
                'updated_at' => $conditionalOrder->updated_at->toDateTimeString()
            ];

        } catch (BusinessException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('修改委托单失败', [
                'user_id' => $userId,
                'conditional_order_id' => $conditionalOrderId,
                'update_data' => $updateData,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '修改委托单失败：' . $e->getMessage());
        }
    }

    /**
     * 查询委托单列表
     * @param int $userId 用户ID
     * @param array $filters 过滤条件
     * @return array
     */
    public function getConditionalOrders(int $userId, array $filters = []): array
    {
        try {
            $query = TradePerpetualConditionalOrder::query()
                ->where('user_id', $userId);

            // 应用过滤条件
            $this->applyFilters($query, $filters);

            $perPage = (int)$filters['per_page'] ?? 20;
            $orders = $query->with(['currency:id,symbol'])
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return [
                'list' => $orders->items(),
                'total' => $orders->total(),
                'page' => $orders->currentPage(),
                'page_size' => $orders->perPage(),
                'total_page' => $orders->lastPage(),
            ];

        } catch (\Exception $e) {
            $this->logger->error('查询委托单列表失败', [
                'user_id' => $userId,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '查询委托单列表失败：' . $e->getMessage());
        }
    }

    /**
     * 查询委托单详情
     * @param int $userId 用户ID
     * @param int $conditionalOrderId 委托单ID
     * @return array
     * @throws BusinessException
     */
    public function getConditionalOrderDetail(int $userId, int $conditionalOrderId): array
    {
        try {
            // 直接从数据库查询
            $conditionalOrder = TradePerpetualConditionalOrder::query()
                ->where('user_id', $userId)
                ->where('id', $conditionalOrderId)
                ->with(['currency'])
                ->first();

            if (!$conditionalOrder) {
                throw new BusinessException(ResultCode::FAIL, '委托单不存在');
            }

            return $this->formatConditionalOrderDetail($conditionalOrder->toArray());

        } catch (BusinessException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('查询委托单详情失败', [
                'user_id' => $userId,
                'conditional_order_id' => $conditionalOrderId,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '查询委托单详情失败：' . $e->getMessage());
        }
    }

    /**
     * 手动执行委托单
     * @param int $conditionalOrderId 委托单ID
     * @return array
     * @throws BusinessException
     */
    public function executeConditionalOrder(int $conditionalOrderId): array
    {
        try {
            $conditionalOrder = TradePerpetualConditionalOrder::findOrFail($conditionalOrderId);

            // 检查是否可以手动执行
            if ($conditionalOrder->status !== ConditionalOrderStatus::WAITING) {
                throw new BusinessException(ResultCode::FAIL, '委托单状态不允许手动执行');
            }

            // 获取执行锁
            if (!$this->cacheService->acquireExecutionLock($conditionalOrderId)) {
                throw new BusinessException(ResultCode::FAIL, '委托单正在执行中，请稍后再试');
            }

            // 提交到异步队列执行
            // 这里需要根据项目的异步队列实现来调用
            // $job = new \App\Job\Contract\PerpetualConditionalOrderExecutionJob($conditionalOrderId, [
            //     'execution_type' => 'manual',
            //     'trigger_price' => '0',
            //     'manual_execute' => true
            // ]);
            // pushAsyncJob(AsyncExecutorKey::PERPETUAL_CONTRACT_QUEUE->value, $job);

            $this->logger->info('委托单手动执行提交成功', [
                'conditional_order_id' => $conditionalOrderId
            ]);

            return [
                'conditional_order_id' => $conditionalOrderId,
                'status' => 'execution_submitted',
                'message' => '委托单已提交执行'
            ];

        } catch (BusinessException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('手动执行委托单失败', [
                'conditional_order_id' => $conditionalOrderId,
                'error' => $e->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '手动执行委托单失败：' . $e->getMessage());
        }
    }

    /**
     * 验证委托单数据
     * @param array $orderData
     * @throws BusinessException
     */
    private function validateConditionalOrderData(array $orderData): void
    {
        // 验证委托单类型
        $conditionalType = ConditionalOrderType::tryFrom((int)$orderData['conditional_type']);
        if (!$conditionalType) {
            throw new BusinessException(ResultCode::FAIL, '无效的委托单类型');
        }

        // 验证执行方式和执行价格
        if ((int)$orderData['execution_type'] === ContractOrderType::LIMIT->value && empty($orderData['execution_price'])) {
            throw new BusinessException(ResultCode::FAIL, '限价单必须设置执行价格');
        }

        // 验证止盈止损的执行模式
        if ($conditionalType->isStopOrder()) {
            if (isset($orderData['execution_mode'])) {
                $executionMode = ExecutionMode::tryFrom((int)$orderData['execution_mode']);
                if (!$executionMode) {
                    throw new BusinessException(ResultCode::FAIL, '无效的执行模式');
                }

                // 回调浮动模式必须设置回调幅度
                if ($executionMode->isCallbackFloat() && empty($orderData['callback_rate'])) {
                    throw new BusinessException(ResultCode::FAIL, '回调浮动模式必须设置回调幅度');
                }

                // 固定价格模式必须设置执行价格
                if ($executionMode->isFixedPrice() && empty($orderData['execution_price'])) {
                    throw new BusinessException(ResultCode::FAIL, '固定价格模式必须设置执行价格');
                }
            }
        }

        // 验证追踪委托的回调幅度
        if ($conditionalType === ConditionalOrderType::TRAILING && empty($orderData['callback_rate'])) {
            throw new BusinessException(ResultCode::FAIL, '追踪委托必须设置回调幅度');
        }
    }

    /**
     * 验证币种是否存在
     * @param int $currencyId
     * @throws BusinessException
     */
    private function validateCurrency(int $currencyId): void
    {
        $currencyKey = CurrencyConfigKey::getCurrencyKey($currencyId);
        $currencyData = $this->redis->hGetAll($currencyKey);

        if (empty($currencyData)) {
            throw new BusinessException(ResultCode::FAIL, '币种不存在');
        }

        // 检查币种是否支持永续合约交易
        if (!isset($currencyData['is_marginTrade']) || $currencyData['is_marginTrade'] != 1) {
            throw new BusinessException(ResultCode::FAIL, '该币种不支持永续合约交易');
        }
    }

    /**
     * 判断是否为开仓操作
     * @param mixed $side
     * @return bool
     */
    private function isOpenPosition($side): bool
    {
        return in_array((int)$side, [
            ContractSide::BUY_OPEN->value,
            ContractSide::SELL_OPEN->value
        ]);
    }

    /**
     * 检查用户权限和余额（仅开仓委托单）
     * @param int $userId
     * @param array $orderData
     * @throws BusinessException
     */
    private function checkUserPermissions(int $userId, array $orderData): void
    {
        // 1. 获取交易配置
        $tradeConfig = $this->getTradeConfig((int)$orderData['currency_id']);

        // 2. 检查最小下单数量
        if (bccomp($orderData['quantity'], (string)$tradeConfig['min_trade_num'], 8) < 0) {
            throw new BusinessException(ResultCode::FAIL, '委托数量不能小于最小下单数量：' . $tradeConfig['min_trade_num']);
        }

        // 3. 检查最大下单数量
        if ($tradeConfig['max_trade_num'] && bccomp($orderData['quantity'], (string)$tradeConfig['max_trade_num'], 8) > 0) {
            throw new BusinessException(ResultCode::FAIL, '委托数量不能大于最大下单数量：' . $tradeConfig['max_trade_num']);
        }

        // 4. 检查单笔最大开仓数量限制
        $maxSingleOrder = (int)$orderData['execution_type'] === ContractOrderType::MARKET->value
            ? $tradeConfig['maker_limit']  // 市价单单笔最大开仓数量
            : $tradeConfig['limit_limit']; // 限价单单笔最大开仓数量

        if ($maxSingleOrder && bccomp($orderData['quantity'], (string)$maxSingleOrder, 8) > 0) {
            $orderTypeName = (int)$orderData['execution_type'] === ContractOrderType::MARKET->value ? '市价单' : '限价单';
            throw new BusinessException(ResultCode::FAIL, $orderTypeName . '单笔最大开仓数量：' . $maxSingleOrder);
        }

        // 5. 检查价格范围（限价单）
        if ((int)$orderData['execution_type'] === ContractOrderType::LIMIT->value && isset($orderData['execution_price'])) {
            $this->validatePriceRange($orderData, $tradeConfig);
        }

        // 6. 检查用户余额（开仓需要保证金）
        $this->checkUserBalance($userId, $orderData, $tradeConfig);
    }

    /**
     * 获取交易配置
     * @param int $currencyId
     * @return array
     * @throws BusinessException
     */
    private function getTradeConfig(int $currencyId): array
    {
        $configKey = TradeConfigKey::getTradeConfigKey($currencyId, MarketType::MARGIN->value);
        $config = $this->redis->hGetAll($configKey);

        if (empty($config)) {
            throw new BusinessException(ResultCode::FAIL, '交易配置不存在');
        }

        return $config;
    }

    /**
     * 验证价格范围（限价单）
     * @param array $orderData
     * @param array $tradeConfig
     * @throws BusinessException
     */
    private function validatePriceRange(array $orderData, array $tradeConfig): void
    {
        $executionPrice = $orderData['execution_price'];

        // 检查最小价格
        if ($tradeConfig['min_trade_price'] && bccomp($executionPrice, (string)$tradeConfig['min_trade_price'], 8) < 0) {
            throw new BusinessException(ResultCode::FAIL, '执行价格不能小于最小价格：' . $tradeConfig['min_trade_price']);
        }

        // 检查最大价格
        if ($tradeConfig['max_trade_price'] && bccomp($executionPrice, (string)$tradeConfig['max_trade_price'], 8) > 0) {
            throw new BusinessException(ResultCode::FAIL, '执行价格不能大于最大价格：' . $tradeConfig['max_trade_price']);
        }

        // 检查价格精度（tick_size）
        if ($tradeConfig['tick_size']) {
            $tickSize = (string)$tradeConfig['tick_size'];
            $remainder = bcmod($executionPrice, $tickSize, 8);
            if (bccomp($remainder, '0', 8) !== 0) {
                throw new BusinessException(ResultCode::FAIL, '执行价格精度不符合要求，最小变化单位：' . $tickSize);
            }
        }
    }

    /**
     * 检查用户余额
     * @param int $userId
     * @param array $orderData
     * @param array $tradeConfig
     * @throws BusinessException
     */
    private function checkUserBalance(int $userId, array $orderData, array $tradeConfig): void
    {
        // 获取币种配置获取报价币种ID
        $currencyKey = CurrencyConfigKey::getCurrencyKey((int)$orderData['currency_id']);
        $currencyData = $this->redis->hGetAll($currencyKey);

        if (empty($currencyData['quote_assets_id'])) {
            throw new BusinessException(ResultCode::FAIL, '币种配置错误，无法获取报价币种');
        }

        $quoteCurrencyId = (int)$currencyData['quote_assets_id'];

        // 计算所需保证金（简化计算，实际应该根据当前价格计算）
        $requiredMargin = bcdiv(
            bcmul($orderData['quantity'], $orderData['trigger_price'], 8),
            (string)$orderData['leverage'],
            8
        );

        // 检查账户余额
        $hasEnoughBalance = $this->assetService->checkAvailableBalance(
            $userId,
            AccountType::FUTURES->value,
            $quoteCurrencyId,
            (float)$requiredMargin
        );

        if (!$hasEnoughBalance) {
            throw new BusinessException(ResultCode::FAIL, '账户余额不足，需要保证金：' . $requiredMargin);
        }
    }

    /**
     * 构建委托单数据
     * @param int $userId
     * @param array $orderData
     * @return array
     */
    private function buildConditionalOrderData(int $userId, array $orderData): array
    {
        return [
            'user_id' => $userId,
            'currency_id' => $orderData['currency_id'],
            'conditional_type' => $orderData['conditional_type'],
            'margin_mode' => $orderData['margin_mode'],
            'side' => $orderData['side'],
            'quantity' => $orderData['quantity'],
            'leverage' => $orderData['leverage'],
            'reduce_only' => $orderData['reduce_only'] ?? false,
            'time_in_force' => $orderData['time_in_force'] ?? 1,
            'trigger_price' => $orderData['trigger_price'],
            'trigger_condition' => $orderData['trigger_condition'],
            'execution_type' => $orderData['execution_type'],
            'execution_price' => $orderData['execution_price'] ?? null,
            'execution_mode' => $orderData['execution_mode'] ?? null,
            'callback_rate' => $orderData['callback_rate'] ?? 0,
            'status' => ConditionalOrderStatus::WAITING->value,
            'expires_at' => isset($orderData['expires_at']) ? new \DateTime($orderData['expires_at']) : null,
        ];
    }

    /**
     * 根据用户ID和委托单ID获取委托单
     * @param int $userId
     * @param int $conditionalOrderId
     * @return TradePerpetualConditionalOrder
     * @throws BusinessException
     */
    private function getConditionalOrderByUser(int $userId, int $conditionalOrderId): TradePerpetualConditionalOrder
    {
        $conditionalOrder = TradePerpetualConditionalOrder::query()
            ->where('user_id', $userId)
            ->where('id', $conditionalOrderId)
            ->first();

        if (!$conditionalOrder) {
            throw new BusinessException(ResultCode::FAIL, '委托单不存在');
        }

        return $conditionalOrder;
    }

    /**
     * 验证是否可以撤销
     * @param TradePerpetualConditionalOrder $conditionalOrder
     * @throws BusinessException
     */
    private function validateCancellation(TradePerpetualConditionalOrder $conditionalOrder): void
    {
        $status = ConditionalOrderStatus::tryFrom((int)$conditionalOrder->status);
        if (!$status || !$status->isActive()) {
            throw new BusinessException(ResultCode::FAIL, '委托单状态不允许撤销');
        }
    }

    /**
     * 验证是否可以修改
     * @param TradePerpetualConditionalOrder $conditionalOrder
     * @param array $updateData
     * @throws BusinessException
     */
    private function validateUpdate(TradePerpetualConditionalOrder $conditionalOrder, array $updateData): void
    {
        $status = ConditionalOrderStatus::tryFrom((int)$conditionalOrder->status);
        if (!$status || !$status->isActive()) {
            throw new BusinessException(ResultCode::FAIL, '委托单状态不允许修改');
        }

        // 检查是否有不允许修改的字段
        $forbiddenFields = ['user_id', 'currency_id', 'conditional_type', 'margin_mode', 'side'];
        foreach ($forbiddenFields as $field) {
            if (isset($updateData[$field])) {
                throw new BusinessException(ResultCode::FAIL, "字段 {$field} 不允许修改");
            }
        }
    }

    /**
     * 验证更新数据
     * @param array $updateData
     * @throws BusinessException
     */
    private function validateUpdateData(array $updateData): void
    {
        // 验证数量
        if (isset($updateData['quantity']) && $updateData['quantity'] <= 0) {
            throw new BusinessException(ResultCode::FAIL, '委托数量必须大于0');
        }

        // 验证触发价格
        if (isset($updateData['trigger_price']) && $updateData['trigger_price'] <= 0) {
            throw new BusinessException(ResultCode::FAIL, '触发价格必须大于0');
        }

        // 验证执行价格
        if (isset($updateData['execution_price']) && $updateData['execution_price'] <= 0) {
            throw new BusinessException(ResultCode::FAIL, '执行价格必须大于0');
        }

        // 验证回调幅度
        if (isset($updateData['callback_rate'])) {
            if ($updateData['callback_rate'] <= 0 || $updateData['callback_rate'] > 50) {
                throw new BusinessException(ResultCode::FAIL, '回调幅度必须在0.01%-50%之间');
            }
        }
    }

    /**
     * 应用过滤条件
     * @param $query
     * @param array $filters
     */
    private function applyFilters($query, array $filters): void
    {
        if (isset($filters['currency_id'])) {
            $query->where('currency_id', $filters['currency_id']);
        }

        if (isset($filters['conditional_type'])) {
            $query->where('conditional_type', $filters['conditional_type']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
    }

    /**
     * 格式化委托单详情
     * @param array $orderData
     * @return array
     */
    private function formatConditionalOrderDetail(array $orderData): array
    {
        return [
            'conditional_order_id' => $orderData['id'],
            'user_id' => $orderData['user_id'],
            'currency_id' => $orderData['currency_id'],
            'currency_symbol' => $orderData['currency']['symbol'] ?? '',
            'conditional_type' => $orderData['conditional_type'],
            'conditional_type_name' => ConditionalOrderType::tryFrom((int)$orderData['conditional_type'])?->getName() ?? '',
            'margin_mode' => $orderData['margin_mode'],
            'margin_mode_name' => MarginMode::tryFrom((int)$orderData['margin_mode'])?->getName() ?? '',
            'side' => $orderData['side'],
            'side_name' => ContractSide::tryFrom((int)$orderData['side'])?->getName() ?? '',
            'quantity' => $orderData['quantity'],
            'leverage' => $orderData['leverage'],
            'reduce_only' => $orderData['reduce_only'],
            'time_in_force' => $orderData['time_in_force'],
            'trigger_price' => $orderData['trigger_price'],
            'trigger_condition' => $orderData['trigger_condition'],
            'trigger_condition_name' => TriggerCondition::tryFrom((int)$orderData['trigger_condition'])?->getName() ?? '',
            'execution_type' => $orderData['execution_type'],
            'execution_price' => $orderData['execution_price'],
            'execution_mode' => $orderData['execution_mode'],
            'execution_mode_name' => $orderData['execution_mode'] ? ExecutionMode::tryFrom((int)$orderData['execution_mode'])?->getName() : '',
            'callback_rate' => $orderData['callback_rate'],
            'status' => $orderData['status'],
            'status_name' => ConditionalOrderStatus::tryFrom((int)$orderData['status'])?->getName() ?? '',
            'triggered_at' => $orderData['triggered_at'],
            'executed_at' => $orderData['executed_at'],
            'executed_order_id' => $orderData['executed_order_id'],
            'failure_reason' => $orderData['failure_reason'],
            'expires_at' => $orderData['expires_at'],
            'created_at' => $orderData['created_at'],
            'updated_at' => $orderData['updated_at'],
        ];
    }
}
