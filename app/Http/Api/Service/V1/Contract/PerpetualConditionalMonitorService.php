<?php

declare(strict_types=1);
/**
 * 永续合约委托单监控服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Model\Trade\TradePerpetualConditionalOrder;
use App\Model\Enums\Trade\Perpetual\ConditionalOrderType;
use App\Model\Enums\Trade\Perpetual\ConditionalOrderStatus;
use App\Model\Enums\Trade\Perpetual\TriggerCondition;
use App\Model\Enums\Trade\Perpetual\ExecutionMode;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use App\Enum\Contract\PerpetualConditionalRedisKey;
use App\Enum\AsyncExecutorKey;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;
use Carbon\Carbon;

class PerpetualConditionalMonitorService
{
    #[Inject]
    protected PerpetualConditionalCacheService $cacheService;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('perpetual-conditional', 'perpetual-logs');
    }

    /**
     * 检查止盈委托单
     * @param int $currencyId 币种ID
     * @param string $currentPrice 当前价格
     * @return void
     */
    public function checkTakeProfitOrders(int $currencyId, string $currentPrice): void
    {
        $this->checkConditionalOrdersByType($currencyId, $currentPrice, ConditionalOrderType::TAKE_PROFIT);
    }

    /**
     * 检查止损委托单
     * @param int $currencyId 币种ID
     * @param string $currentPrice 当前价格
     * @return void
     */
    public function checkStopLossOrders(int $currencyId, string $currentPrice): void
    {
        $this->checkConditionalOrdersByType($currencyId, $currentPrice, ConditionalOrderType::STOP_LOSS);
    }

    /**
     * 检查计划委托单
     * @param int $currencyId 币种ID
     * @param string $currentPrice 当前价格
     * @return void
     */
    public function checkScheduledOrders(int $currencyId, string $currentPrice): void
    {
        $this->checkConditionalOrdersByType($currencyId, $currentPrice, ConditionalOrderType::SCHEDULED);
    }

    /**
     * 检查追踪委托单
     * @param int $currencyId 币种ID
     * @param string $currentPrice 当前价格
     * @return void
     */
    public function checkTrailingOrders(int $currencyId, string $currentPrice): void
    {
        $this->checkConditionalOrdersByType($currencyId, $currentPrice, ConditionalOrderType::TRAILING);
    }

    /**
     * 根据类型检查委托单
     * @param int $currencyId 币种ID
     * @param string $currentPrice 当前价格
     * @param ConditionalOrderType $orderType 委托单类型
     * @return void
     */
    private function checkConditionalOrdersByType(int $currencyId, string $currentPrice, ConditionalOrderType $orderType): void
    {
        try {
            // 检查大于等于条件的委托单
            $gteOrderIds = $this->cacheService->getTriggeredOrderIds(
                $currencyId, 
                TriggerCondition::GREATER_THAN_OR_EQUAL->value, 
                $currentPrice
            );

            // 检查小于等于条件的委托单
            $lteOrderIds = $this->cacheService->getTriggeredOrderIds(
                $currencyId, 
                TriggerCondition::LESS_THAN_OR_EQUAL->value, 
                $currentPrice
            );

            $allTriggeredOrderIds = array_merge($gteOrderIds, $lteOrderIds);

            if (empty($allTriggeredOrderIds)) {
                return;
            }

            // 查询触发的委托单详情
            $triggeredOrders = TradePerpetualConditionalOrder::query()
                ->whereIn('id', $allTriggeredOrderIds)
                ->where('conditional_type', $orderType->value)
                ->where('status', ConditionalOrderStatus::WAITING->value)
                ->get();

            foreach ($triggeredOrders as $order) {
                $this->processTriggeredOrder($order, $currentPrice);
            }

        } catch (\Exception $e) {
            $this->logger->error('检查委托单失败', [
                'currency_id' => $currencyId,
                'current_price' => $currentPrice,
                'order_type' => $orderType->value,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理触发的委托单
     * @param TradePerpetualConditionalOrder $order
     * @param string $currentPrice
     * @return void
     */
    private function processTriggeredOrder(TradePerpetualConditionalOrder $order, string $currentPrice): void
    {
        try {
            // 验证触发条件
            $triggerCondition = TriggerCondition::tryFrom($order->trigger_condition);
            if (!$triggerCondition || !$triggerCondition->checkCondition($currentPrice, (string)$order->trigger_price)) {
                return;
            }

            // 特殊处理追踪委托单
            if ($order->conditional_type === ConditionalOrderType::TRAILING->value) {
                if (!$this->checkTrailingTrigger($order, $currentPrice)) {
                    return;
                }
            }

            // 执行触发的委托单
            $this->executeTriggerredOrder($order, $currentPrice);

        } catch (\Exception $e) {
            $this->logger->error('处理触发委托单失败', [
                'order_id' => $order->id,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查追踪委托单触发条件
     * @param TradePerpetualConditionalOrder $order
     * @param string $currentPrice
     * @return bool
     */
    private function checkTrailingTrigger(TradePerpetualConditionalOrder $order, string $currentPrice): bool
    {
        // 追踪委托单的特殊逻辑
        // 这里需要根据具体的追踪算法来实现
        // 比如计算回调触发价格等
        
        if (!$order->callback_rate) {
            return false;
        }

        // 简化的追踪逻辑示例
        // 实际实现需要根据具体的业务需求来完善
        return true;
    }

    /**
     * 执行触发的委托单
     * @param TradePerpetualConditionalOrder $order
     * @param string $currentPrice
     * @return bool
     */
    public function executeTriggerredOrder(TradePerpetualConditionalOrder $order, string $currentPrice): bool
    {
        try {
            // 获取执行锁
            if (!$this->cacheService->acquireExecutionLock($order->id)) {
                $this->logger->warning('委托单正在执行中，跳过', [
                    'order_id' => $order->id
                ]);
                return false;
            }

            // 更新委托单状态为已触发
            $order->update([
                'status' => ConditionalOrderStatus::TRIGGERED->value,
                'triggered_at' => Carbon::now()
            ]);

            // 从缓存中移除
            $this->cacheService->removeOrderFromZset($order);

            // 提交到异步队列执行
            $job = new \App\Job\Contract\PerpetualConditionalOrderExecutionJob($order->id, [
                'execution_type' => 'normal_trigger',
                'trigger_price' => $currentPrice,
                'triggered_at' => Carbon::now()->toDateTimeString()
            ]);

            // 这里需要根据项目的异步队列实现来调用
            // pushAsyncJob(AsyncExecutorKey::PERPETUAL_CONTRACT_QUEUE->value, $job);

            $this->logger->info('委托单触发成功', [
                'order_id' => $order->id,
                'trigger_price' => $currentPrice,
                'current_price' => $currentPrice
            ]);

            return true;

        } catch (\Exception $e) {
            // 释放执行锁
            $this->cacheService->releaseExecutionLock($order->id);
            
            $this->logger->error('执行触发委托单失败', [
                'order_id' => $order->id,
                'current_price' => $currentPrice,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 计算执行价格
     * @param TradePerpetualConditionalOrder $order
     * @param string $triggerPrice
     * @return string
     */
    public function calculateExecutionPrice(TradePerpetualConditionalOrder $order, string $triggerPrice): string
    {
        // 如果是固定价格模式，直接返回设置的执行价格
        if ($order->execution_mode === ExecutionMode::FIXED_PRICE->value) {
            return $order->execution_price ?? $triggerPrice;
        }

        // 回调浮动模式，根据触发价格和回调幅度计算执行价格
        if ($order->execution_mode === ExecutionMode::CALLBACK_FLOAT->value && $order->callback_rate) {
            return $this->calculateCallbackPrice($order, $triggerPrice);
        }

        // 默认返回触发价格
        return $triggerPrice;
    }

    /**
     * 计算回调浮动价格
     * @param TradePerpetualConditionalOrder $order
     * @param string $triggerPrice
     * @return string
     */
    private function calculateCallbackPrice(TradePerpetualConditionalOrder $order, string $triggerPrice): string
    {
        $callbackRate = bcdiv((string)$order->callback_rate, '100', 8); // 转换为小数
        
        $conditionalType = ConditionalOrderType::tryFrom($order->conditional_type);
        $side = ContractSide::tryFrom($order->side);
        
        if ($conditionalType === ConditionalOrderType::STOP_LOSS) {
            // 止损单计算
            if ($side && in_array($side, [ContractSide::BUY_OPEN, ContractSide::BUY_CLOSE])) {
                // 多头止损: 执行价格 = 触发价格 × (1 - 回调幅度)
                return bcmul($triggerPrice, bcsub('1', $callbackRate, 8), 8);
            } else {
                // 空头止损: 执行价格 = 触发价格 × (1 + 回调幅度)
                return bcmul($triggerPrice, bcadd('1', $callbackRate, 8), 8);
            }
        } elseif ($conditionalType === ConditionalOrderType::TAKE_PROFIT) {
            // 止盈单计算
            if ($side && in_array($side, [ContractSide::BUY_OPEN, ContractSide::BUY_CLOSE])) {
                // 多头止盈: 执行价格 = 触发价格 × (1 + 回调浮动)
                return bcmul($triggerPrice, bcadd('1', $callbackRate, 8), 8);
            } else {
                // 空头止盈: 执行价格 = 触发价格 × (1 - 回调浮动)
                return bcmul($triggerPrice, bcsub('1', $callbackRate, 8), 8);
            }
        }

        // 默认返回触发价格
        return $triggerPrice;
    }

    /**
     * 清理过期委托单
     * @return void
     */
    public function cleanExpiredOrders(): void
    {
        try {
            $expiredOrders = TradePerpetualConditionalOrder::query()
                ->where('status', ConditionalOrderStatus::WAITING->value)
                ->where('expires_at', '<', Carbon::now())
                ->get();

            foreach ($expiredOrders as $order) {
                $order->update([
                    'status' => ConditionalOrderStatus::EXPIRED->value,
                    'updated_at' => Carbon::now()
                ]);

                // 从缓存中移除
                $this->cacheService->removeOrderFromZset($order);

                $this->logger->info('委托单已过期', [
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'expires_at' => $order->expires_at
                ]);
            }

        } catch (\Exception $e) {
            $this->logger->error('清理过期委托单失败', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
