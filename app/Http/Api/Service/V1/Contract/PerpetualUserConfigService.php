<?php

declare(strict_types=1);
/**
 * 永续合约用户配置服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Model\User\UserPerprtualConfig;
use App\Exception\BusinessException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;

class PerpetualUserConfigService
{
    #[Inject]
    protected UserPerprtualConfig $userPerpetualConfig;

    /**
     * 获取用户合约交易配置
     */
    public function getUserConfig(int $userId, int $currencyId): array
    {
        $config = $this->userPerpetualConfig
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->first();
        
        if (!$config) {
            // 如果用户配置不存在，创建默认配置
            $config = $this->createDefaultConfig($userId, $currencyId);
        }

        return [
            'user_id' => $config->user_id,
            'currency_id' => $config->currency_id,
            'hold_units' => $config->hold_units,
            'hold_mode' => $config->hold_mode,
            'pnl_source' => $config->pnl_source,
            'tp_sl_source' => $config->tp_sl_source,
            'assets_mode' => $config->aassets_mode,
            'price_protect' => $config->price_protect,
            'lever' => $config->lever,
            'margin_type' => $config->margin_type,
            'created_at' => $config->created_at->toDateTimeString(),
            'updated_at' => $config->updated_at->toDateTimeString(),
        ];
    }

    /**
     * 更新用户合约交易配置
     */
    public function updateUserConfig(int $userId, int $currencyId, array $configData): array
    {
        Db::beginTransaction();
        try {
            // 验证配置参数
            $this->validateConfigData($configData);

            $config = $this->userPerpetualConfig
                ->where('user_id', $userId)
                ->where('currency_id', $currencyId)
                ->first();
            
            if (!$config) {
                // 如果配置不存在，创建新配置
                $config = new UserPerprtualConfig();
                $config->user_id = $userId;
                $config->currency_id = $currencyId;
            }

            // 更新配置字段
            if (isset($configData['hold_units'])) {
                $config->hold_units = (int)$configData['hold_units'];
            }
            if (isset($configData['hold_mode'])) {
                $config->hold_mode = (int)$configData['hold_mode'];
            }
            if (isset($configData['pnl_source'])) {
                $config->pnl_source = (int)$configData['pnl_source'];
            }
            if (isset($configData['tp_sl_source'])) {
                $config->tp_sl_source = (int)$configData['tp_sl_source'];
            }
            if (isset($configData['assets_mode'])) {
                $config->aassets_mode = (int)$configData['assets_mode'];
            }
            if (isset($configData['price_protect'])) {
                $config->price_protect = (int)$configData['price_protect'];
            }
            if (isset($configData['lever'])) {
                $config->lever = (int)$configData['lever'];
            }
            if (isset($configData['margin_type'])) {
                $config->margin_type = (int)$configData['margin_type'];
            }

            $config->save();

            Db::commit();

            return $this->getUserConfig($userId, $currencyId);
        } catch (\Exception $e) {
            Db::rollBack();
            throw new BusinessException('更新配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建默认配置
     */
    private function createDefaultConfig(int $userId, int $currencyId): UserPerprtualConfig
    {
        $config = new UserPerprtualConfig();
        $config->user_id = $userId;
        $config->currency_id = $currencyId;
        $config->hold_units = 1; // 默认：数量
        $config->hold_mode = 1; // 默认：单向持仓
        $config->pnl_source = 1; // 默认：标记价格
        $config->tp_sl_source = 1; // 默认：最新价格
        $config->aassets_mode = 1; // 默认：单币种保证金
        $config->price_protect = 1; // 默认：开启价差保护
        $config->lever = 20; // 默认：20倍杠杆
        $config->margin_type = 1; // 默认：全仓保证金
        $config->save();

        return $config;
    }

    /**
     * 验证配置数据
     */
    private function validateConfigData(array $configData): void
    {
        // 验证持仓单位
        if (isset($configData['hold_units']) && !in_array($configData['hold_units'], [1, 2, 3])) {
            throw new BusinessException('持仓单位参数错误');
        }

        // 验证持仓模式
        if (isset($configData['hold_mode']) && !in_array($configData['hold_mode'], [1, 2])) {
            throw new BusinessException('持仓模式参数错误');
        }

        // 验证盈亏来源
        if (isset($configData['pnl_source']) && !in_array($configData['pnl_source'], [1, 2])) {
            throw new BusinessException('盈亏来源参数错误');
        }

        // 验证止盈止损来源
        if (isset($configData['tp_sl_source']) && !in_array($configData['tp_sl_source'], [1, 2])) {
            throw new BusinessException('止盈止损来源参数错误');
        }

        // 验证资产模式
        if (isset($configData['assets_mode']) && !in_array($configData['assets_mode'], [1, 2])) {
            throw new BusinessException('资产模式参数错误');
        }

        // 验证价差保护
        if (isset($configData['price_protect']) && !in_array($configData['price_protect'], [0, 1])) {
            throw new BusinessException('价差保护参数错误');
        }

        // 验证杠杆倍数
        if (isset($configData['lever'])) {
            $lever = (int)$configData['lever'];
            if ($lever < 1 || $lever > 100) {
                throw new BusinessException('杠杆倍数必须在1-100之间');
            }
        }

        // 验证保证金类型
        if (isset($configData['margin_type']) && !in_array($configData['margin_type'], [1, 2])) {
            throw new BusinessException('保证金类型参数错误');
        }
    }

    /**
     * 获取持仓单位名称
     */
    private function getHoldUnitsName(int $holdUnits): string
    {
        return match($holdUnits) {
            1 => '数量',
            2 => '成本价值',
            3 => '名义价值',
            default => '未知'
        };
    }

    /**
     * 获取持仓模式名称
     */
    private function getHoldModeName(int $holdMode): string
    {
        return match($holdMode) {
            1 => '单向持仓',
            2 => '双向持仓',
            default => '未知'
        };
    }

    /**
     * 获取盈亏来源名称
     */
    private function getPnlSourceName(int $pnlSource): string
    {
        return match($pnlSource) {
            1 => '标记价格',
            2 => '最新价格',
            default => '未知'
        };
    }

    /**
     * 获取止盈止损来源名称
     */
    private function getTpSlSourceName(int $tpSlSource): string
    {
        return match($tpSlSource) {
            1 => '最新价格',
            2 => '标记价格',
            default => '未知'
        };
    }

    /**
     * 获取资产模式名称
     */
    private function getAssetModeName(int $assetsMode): string
    {
        return match($assetsMode) {
            1 => '单币种保证金',
            2 => '联合保证金',
            default => '未知'
        };
    }

    /**
     * 获取保证金类型名称
     */
    private function getMarginTypeName(int $marginType): string
    {
        return match($marginType) {
            1 => '全仓保证金',
            2 => '逐仓保证金',
            default => '未知'
        };
    }

    /**
     * 重置用户配置为默认值
     */
    public function resetUserConfig(int $userId, int $currencyId): array
    {
        $config = $this->userPerpetualConfig
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->first();
        
        if ($config) {
            $config->delete();
        }

        // 重新创建默认配置
        $newConfig = $this->createDefaultConfig($userId, $currencyId);
        
        return $this->getUserConfig($userId, $currencyId);
    }
}