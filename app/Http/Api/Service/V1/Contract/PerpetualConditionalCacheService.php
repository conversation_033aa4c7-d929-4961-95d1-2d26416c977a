<?php

declare(strict_types=1);
/**
 * 永续合约委托单缓存管理服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Enum\Contract\PerpetualConditionalRedisKey;
use App\Model\Trade\TradePerpetualConditionalOrder;
use App\Model\Enums\Trade\Perpetual\ConditionalOrderStatus;
use App\Model\Enums\Trade\Perpetual\TriggerCondition;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;

class PerpetualConditionalCacheService
{
    #[Inject]
    protected Redis $redis;

    /**
     * 添加委托单到有序集合
     * @param TradePerpetualConditionalOrder $order
     * @return void
     */
    public function addOrderToZset(TradePerpetualConditionalOrder $order): void
    {
        $key = PerpetualConditionalRedisKey::getConditionalOrdersZsetKey(
            $order->currency_id, 
            $order->trigger_condition
        );
        
        $score = PerpetualConditionalRedisKey::priceToScore((string)$order->trigger_price);
        
        $this->redis->zadd($key, $score, (string)$order->id);
    }

    /**
     * 从有序集合移除委托单
     * @param TradePerpetualConditionalOrder $order
     * @return void
     */
    public function removeOrderFromZset(TradePerpetualConditionalOrder $order): void
    {
        $key = PerpetualConditionalRedisKey::getConditionalOrdersZsetKey(
            $order->currency_id, 
            $order->trigger_condition
        );
        
        $this->redis->zrem($key, (string)$order->id);
    }

    /**
     * 根据价格范围获取触发的委托单ID列表
     * @param int $currencyId 币种ID
     * @param int $triggerCondition 触发条件
     * @param string $currentPrice 当前价格
     * @return array
     */
    public function getTriggeredOrderIds(int $currencyId, int $triggerCondition, string $currentPrice): array
    {
        $key = PerpetualConditionalRedisKey::getConditionalOrdersZsetKey($currencyId, $triggerCondition);
        $score = PerpetualConditionalRedisKey::priceToScore($currentPrice);
        
        // 根据触发条件查询
        if ($triggerCondition === TriggerCondition::GREATER_THAN_OR_EQUAL->value) {
            // 查询触发价格 <= 当前价格的委托单
            $orderIds = $this->redis->zrangebyscore($key, '-inf', (string)$score);
        } else {
            // 查询触发价格 >= 当前价格的委托单
            $orderIds = $this->redis->zrangebyscore($key, (string)$score, '+inf');
        }
        
        return array_map('intval', $orderIds);
    }

    /**
     * 获取执行锁
     * @param int $conditionalOrderId 委托单ID
     * @return bool
     */
    public function acquireExecutionLock(int $conditionalOrderId): bool
    {
        $lockKey = PerpetualConditionalRedisKey::getExecutionLockKey($conditionalOrderId);
        return $this->redis->set(
            $lockKey,
            '1',
            ['EX' => PerpetualConditionalRedisKey::EXECUTION_LOCK_TTL, 'NX']
        );
    }

    /**
     * 释放执行锁
     * @param int $conditionalOrderId 委托单ID
     * @return void
     */
    public function releaseExecutionLock(int $conditionalOrderId): void
    {
        $lockKey = PerpetualConditionalRedisKey::getExecutionLockKey($conditionalOrderId);
        $this->redis->del($lockKey);
    }



    /**
     * 更新用户委托单统计
     * @param int $userId 用户ID
     * @param array $stats 统计数据
     * @return void
     */
    public function updateUserStats(int $userId, array $stats): void
    {
        $key = PerpetualConditionalRedisKey::getUserStatsKey($userId);
        $this->redis->setex(
            $key, 
            PerpetualConditionalRedisKey::USER_STATS_TTL, 
            json_encode($stats)
        );
    }

    /**
     * 获取用户委托单统计
     * @param int $userId 用户ID
     * @return array|null
     */
    public function getUserStats(int $userId): ?array
    {
        $key = PerpetualConditionalRedisKey::getUserStatsKey($userId);
        $cached = $this->redis->get($key);
        
        return $cached ? json_decode($cached, true) : null;
    }

    /**
     * 初始化币种的委托单缓存
     * @param int $currencyId 币种ID
     * @return void
     */
    public function initCurrencyOrdersCache(int $currencyId): void
    {
        // 获取该币种所有活跃的委托单
        $orders = TradePerpetualConditionalOrder::query()
            ->where('currency_id', $currencyId)
            ->where('status', ConditionalOrderStatus::WAITING)
            ->get();

        // 按触发条件分组并添加到对应的有序集合
        foreach ($orders as $order) {
            $this->addOrderToZset($order);
        }
    }

    /**
     * 清理过期的缓存数据
     * @return void
     */
    public function cleanExpiredCache(): void
    {
        // 这里可以添加清理过期缓存的逻辑
        // 比如清理已完成的委托单缓存等
    }
}
