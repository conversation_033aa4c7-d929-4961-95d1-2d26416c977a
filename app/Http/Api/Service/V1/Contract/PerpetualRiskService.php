<?php

declare(strict_types=1);
/**
 * 永续合约风险控制服务
 */

namespace App\Http\Api\Service\V1\Contract;

use App\Model\Trade\TradeMarginLevel;
use App\Model\Trade\TradePerpetualPosition;
use Hyperf\Di\Annotation\Inject;

class PerpetualRiskService
{
    #[Inject]
    protected TradeMarginLevel $tradeMarginLevel;

    #[Inject]
    protected TradePerpetualPosition $tradePerpetualPosition;

    /**
     * 获取用户仓位风险
     */
    public function getPositionRisk(int $userId, ?int $currencyId = null, ?int $marginMode = null): array
    {
        // TODO: 实现仓位风险计算逻辑
        return [
            'data' => [],
        ];
    }

    /**
     * 检查强平风险
     */
    public function checkLiquidationRisk(int $userId, int $currencyId, int $marginMode): bool
    {
        // TODO: 实现强平风险检查逻辑
        return false;
    }

    /**
     * 计算维持保证金
     */
    public function calculateMaintenanceMargin(int $currencyId, float $positionValue): float
    {
        // TODO: 基于 trade_margin_level 计算维持保证金
        // 维持保证金 = 持仓价值 × 维持保证金率 - 维持保证金速算额
        return 0.0;
    }

    /**
     * 获取风险等级
     */
    public function getRiskLevel(int $currencyId, float $positionValue): ?array
    {
        // TODO: 根据持仓价值获取对应的风险等级
        return null;
    }
}