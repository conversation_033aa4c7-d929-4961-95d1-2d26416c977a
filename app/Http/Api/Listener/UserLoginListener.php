<?php

declare(strict_types=1);

namespace App\Http\Api\Listener;

use Hyperf\Event\Annotation\Listener;
use App\Http\Api\Event\UserLoginEvent;
use App\Model\User\Enums\LoginResult;
use App\Model\User\UserDevice;
use App\Model\User\UserLoginLog;
use Carbon\Carbon;
use Hyperf\Event\Contract\ListenerInterface;

#[Listener]
class UserLoginListener implements ListenerInterface
{
    public function listen(): array
    {
        // 返回一个该监听器要监听的事件数组，可以同时监听多个事件
        return [
            UserLoginEvent::class,
        ];
    }

    /**
     * @param UserLoginEvent $event
     */
    public function process(object $event): void
    {
        $user = $event->user;
        $accessToken = $event->accessToken;
        $isLogin = $event->isLogin;

        // 记录登录设备信息
        $userDevice = UserDevice::query()->where(UserDevice::FIELD_USER_ID, $user->id)->where(UserDevice::FIELD_DEVICE_ID, $event->getDeviceId())->first();
        if ($userDevice) {
            $userDevice->update([
                UserDevice::FIELD_LAST_LOGIN_IP => $event->getClientIp(),
                UserDevice::FIELD_LAST_LOGIN_AT => date('Y-m-d H:i:s'),
                UserDevice::FIELD_TOKEN => $accessToken,
            ]);
        } else {
            UserDevice::query()->create([
                UserDevice::FIELD_USER_ID => $user->id,
                UserDevice::FIELD_DEVICE_ID => $event->getDeviceId(),
                UserDevice::FIELD_DEVICE_NAME => $event->getDeviceName(),
                UserDevice::FIELD_DEVICE_TYPE => $event->getDeviceType(),
                UserDevice::FIELD_BROWSER => $event->getBrowser(),
                UserDevice::FIELD_OS => $event->getOs(),
                UserDevice::FIELD_APP_VERSION => $event->getAppVersion(),
                UserDevice::FIELD_FIRST_LOGIN_IP => $event->getClientIp(),
                UserDevice::FIELD_LAST_LOGIN_IP => $event->getClientIp(),
                UserDevice::FIELD_FIRST_LOGIN_AT => date('Y-m-d H:i:s'),
                UserDevice::FIELD_LAST_LOGIN_AT => date('Y-m-d H:i:s'),
                UserDevice::FIELD_TOKEN => $accessToken,
                UserDevice::FIELD_PUSH_TOKEN => $event->getPushToken(),
                UserDevice::FIELD_DEVICE_INFO => $event->getDeviceInfo(),
            ]);
        }

        $redis = redis();
        // 通过 IP 归属地接口由前端上报并保存到 Redis 中
        $ip_location = $redis->get('ip_location_' . $event->getClientIp());
        // 记录登录日志
        UserLoginLog::query()->create([
            UserLoginLog::FIELD_USER_ID => $user->id,
            UserLoginLog::FIELD_IP_ADDRESS => $event->getClientIp(),
            UserLoginLog::FIELD_USER_AGENT => $event->getBrowser(),
            UserLoginLog::FIELD_DEVICE_TYPE => $event->getDeviceType(),
            UserLoginLog::FIELD_DEVICE_NAME => $event->getDeviceName(),
            UserLoginLog::FIELD_DEVICE_ID => $event->getDeviceId(),
            UserLoginLog::FIELD_BROWSER => $event->getBrowser(),
            UserLoginLog::FIELD_OS => $event->getOs(),
            // 登录国家
            UserLoginLog::FIELD_LOCATION_COUNTRY => !empty($ip_location) ? $ip_location['country'] : '',
            // 登录省份
            UserLoginLog::FIELD_LOCATION_PROVINCE => !empty($ip_location) ? $ip_location['province'] : '',
            // 登录城市
            UserLoginLog::FIELD_LOCATION_CITY => !empty($ip_location) ? $ip_location['area'] : '',
            UserLoginLog::FIELD_LOGIN_RESULT => $isLogin ? LoginResult::SUCCESS : LoginResult::FAILED,
            UserLoginLog::FIELD_FAILURE_REASON => $isLogin ? null : $event->failureReason,
            UserLoginLog::FIELD_LOGIN_TIME => Carbon::now(),
        ]);
    }
}
