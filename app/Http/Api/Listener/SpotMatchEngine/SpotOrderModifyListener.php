<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 现货订单修改事件监听器
 */

namespace App\Http\Api\Listener\SpotMatchEngine;

use App\Enum\CurrencyConfigKey;
use App\Enum\MarketType;
use App\Http\Api\Event\Spot\OrderModifyEvent;
use App\Model\Currency\Currency;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Match\MatchOrder;
use App\Model\Trade\TradeSpotOrder;
use App\Model\WebsocketData\User\OrderModifyMessage;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\DbConnection\Db;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Hyperf\WebSocketServer\Sender;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class SpotOrderModifyListener implements ListenerInterface
{
    /**
     * 默认精度
     */
    protected const DEFAULT_PRECISION = 18;

    /**
     * 用户资产服务
     */
    protected UserAccountsAssetService $assetService;

    /**
     * Redis 客户端
     */
    protected Redis $redis;

    /**
     * WebSocket 发送器
     */
    protected Sender $sender;

    /**
     * 日志记录器
     */
    protected LoggerInterface $logger;

    public function __construct(ContainerInterface $container)
    {
        $this->assetService = $container->get(UserAccountsAssetService::class);
        $this->redis = $container->get(Redis::class);
        $this->sender = $container->get(Sender::class);
        $this->logger = $container->get(LoggerFactory::class)->get('spot_order_modify');
    }

    /**
     * 监听的事件
     */
    public function listen(): array
    {
        return [
            OrderModifyEvent::class,
        ];
    }

    /**
     * 处理订单修改事件
     */
    public function process(object $event): void
    {
        if (!$event instanceof OrderModifyEvent) {
            return;
        }

        try {
            $this->handleOrderModify($event->orderId, $event->newPrice, $event->newQuantity);
        } catch (\Throwable $e) {
            $this->logger->error('处理订单修改事件失败', [
                'order_id' => $event->orderId,
                'new_price' => $event->newPrice,
                'new_quantity' => $event->newQuantity,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理订单修改
     */
    protected function handleOrderModify(int $orderId, float $newPrice, float $newQuantity): void
    {
        Db::transaction(function () use ($orderId, $newPrice, $newQuantity) {
            // 1. 查找撮合引擎订单
            $matchOrder = MatchOrder::query()
                ->where('order_id', $orderId)
                ->where('market_type', MarketType::CRYPTO->value)
                ->first();

            if (!$matchOrder) {
                $this->logger->warning('未找到撮合引擎订单', ['order_id' => $orderId]);
                return;
            }

            // 2. 查找现货订单
            $spotOrder = TradeSpotOrder::query()
                ->where('match_order', $matchOrder->id)
                ->first();

            if (!$spotOrder) {
                $this->logger->warning('未找到现货订单', ['match_order_id' => $matchOrder->id]);
                return;
            }

            // 3. 计算资金变动
            $oldFrozenAmount = $spotOrder->frozen_amount;
            $newRequiredAmount = $this->calculateRequiredAmount($spotOrder, $newPrice, $newQuantity);
            $amountDifference = bcsub((string)$newRequiredAmount, (string)$oldFrozenAmount, self::DEFAULT_PRECISION);

            // 4. 处理资金变动
            if (bccomp($amountDifference, '0', self::DEFAULT_PRECISION) > 0) {
                // 需要额外冻结资金
                $requiredCurrencyId = $this->getRequiredCurrencyId($spotOrder);
                $this->assetService->freezeAsset(
                    $matchOrder->user_id,
                    AccountType::SPOT->value,
                    $requiredCurrencyId,
                    (float)$amountDifference,
                    FlowsType::SPOT_TRADE->value,
                    $spotOrder->id
                );

                $this->logger->info('订单修改-额外冻结资金', [
                    'order_id' => $spotOrder->id,
                    'match_order_id' => $matchOrder->order_id,
                    'user_id' => $matchOrder->user_id,
                    'currency_id' => $requiredCurrencyId,
                    'amount' => $amountDifference
                ]);

            } elseif (bccomp($amountDifference, '0', self::DEFAULT_PRECISION) < 0) {
                // 需要解冻部分资金
                $requiredCurrencyId = $this->getRequiredCurrencyId($spotOrder);
                $unfreezeAmount = bcmul($amountDifference, '-1', self::DEFAULT_PRECISION);
                
                $this->assetService->unfreezeAsset(
                    $matchOrder->user_id,
                    AccountType::SPOT->value,
                    $requiredCurrencyId,
                    (float)$unfreezeAmount,
                    FlowsType::SPOT_TRADE->value,
                    $spotOrder->id
                );

                $this->logger->info('订单修改-解冻资金', [
                    'order_id' => $spotOrder->id,
                    'match_order_id' => $matchOrder->order_id,
                    'user_id' => $matchOrder->user_id,
                    'currency_id' => $requiredCurrencyId,
                    'amount' => $unfreezeAmount
                ]);
            }

            // 5. 更新数据库订单信息
            $spotOrder->price = $newPrice;
            $spotOrder->amount = $newQuantity;
            $spotOrder->frozen_amount = $newRequiredAmount;
            $spotOrder->save();

            $matchOrder->price = $newPrice;
            $matchOrder->quantity = $newQuantity;
            $matchOrder->save();

            $this->logger->info('订单修改成功', [
                'order_id' => $spotOrder->id,
                'match_order_id' => $matchOrder->order_id,
                'user_id' => $matchOrder->user_id,
                'old_price' => $matchOrder->price,
                'new_price' => $newPrice,
                'old_quantity' => $matchOrder->quantity,
                'new_quantity' => $newQuantity,
                'old_frozen_amount' => $oldFrozenAmount,
                'new_frozen_amount' => $newRequiredAmount,
                'amount_difference' => $amountDifference
            ]);

            // 推送订单修改消息到前端
            $this->pushModifyMessageToUser($spotOrder, $matchOrder, floatval($oldFrozenAmount), floatval($newRequiredAmount), $amountDifference);
        });
    }

    /**
     * 计算修改订单后的资金需求
     */
    protected function calculateRequiredAmount(TradeSpotOrder $spotOrder, float $price, float $quantity): float
    {
        if ($spotOrder->direction == 1) { // 买单
            // 买单需要：数量 * 价格
            return (float)bcmul((string)$quantity, (string)$price, self::DEFAULT_PRECISION);
        } else { // 卖单
            // 卖单需要：数量
            return $quantity;
        }
    }

    /**
     * 获取订单所需的币种ID
     */
    protected function getRequiredCurrencyId(TradeSpotOrder $spotOrder): int
    {
        if ($spotOrder->direction == 1) { // 买单
            // 买单需要计价币种，从Redis配置中获取
            try {
                $currencyKey = CurrencyConfigKey::getCurrencyKey($spotOrder->currency_id);
                $quoteCurrencyId = $this->redis->hGet($currencyKey, Currency::FIELD_QUOTE_ASSETS_ID);
                
                if ($quoteCurrencyId === false || $quoteCurrencyId === null) {
                    $this->logger->warning('未找到计价币种配置，使用默认USDT', [
                        'currency_id' => $spotOrder->currency_id
                    ]);
                    return 2; // 默认USDT
                }
                
                return (int)$quoteCurrencyId;
            } catch (\Exception $e) {
                $this->logger->error('获取计价币种失败，使用默认USDT', [
                    'currency_id' => $spotOrder->currency_id,
                    'error' => $e->getMessage()
                ]);
                return 2; // 默认USDT
            }
        } else { // 卖单
            // 卖单需要基础币种
            return $spotOrder->currency_id;
        }
    }

    /**
     * 推送订单修改消息到用户
     */
    protected function pushModifyMessageToUser(
        TradeSpotOrder $spotOrder, 
        MatchOrder $matchOrder, 
        float $oldFrozenAmount, 
        float $newRequiredAmount, 
        string $amountDifference
    ): void {
        go(function () use ($spotOrder, $matchOrder, $oldFrozenAmount, $newRequiredAmount, $amountDifference) {
            try {
                // 获取币种符号
                $symbol = $this->getCurrencySymbol($spotOrder->currency_id);
                
                // 构造修改消息数据
                $modifyData = [
                    'order_id' => $spotOrder->id,
                    'match_order_id' => $matchOrder->order_id,
                    'original_order_id' => $matchOrder->order_id,
                    'currency_id' => $spotOrder->currency_id,
                    'symbol' => $symbol,
                    'side' => $spotOrder->direction == 1 ? 'buy' : 'sell',
                    'order_type' => $spotOrder->order_type == 1 ? 'market' : 'limit',
                    'old_price' => (string)$matchOrder->price,
                    'new_price' => (string)$spotOrder->price,
                    'old_quantity' => (string)$matchOrder->quantity,
                    'new_quantity' => (string)$spotOrder->amount,
                    'old_frozen_amount' => (string)$oldFrozenAmount,
                    'new_frozen_amount' => (string)$newRequiredAmount,
                    'amount_difference' => $amountDifference,
                    'frozen_currency_id' => $this->getRequiredCurrencyId($spotOrder),
                    'frozen_currency_symbol' => $this->getFrozenCurrencySymbol($spotOrder),
                    'status' => 'modified',
                    'created_at' => $spotOrder->created_at?->format('Y-m-d H:i:s'),
                    'updated_at' => $spotOrder->updated_at?->format('Y-m-d H:i:s'),
                    'modify_time' => date('Y-m-d H:i:s')
                ];

                // 创建消息对象
                $message = new OrderModifyMessage($modifyData);
                $message->setMarkeType(1); // 1 = 现货市场

                // 推送消息到用户
                $success = $this->pushMessageToUserDirect($matchOrder->user_id, $message->toJsonString());

                if ($success) {
                    $this->logger->info('订单修改消息推送成功', [
                        'user_id' => $matchOrder->user_id,
                        'order_id' => $spotOrder->id,
                        'match_order_id' => $matchOrder->order_id
                    ]);
                } else {
                    $this->logger->warning('订单修改消息推送失败', [
                        'user_id' => $matchOrder->user_id,
                        'order_id' => $spotOrder->id,
                        'match_order_id' => $matchOrder->order_id
                    ]);
                }

            } catch (\Throwable $e) {
                $this->logger->error('推送订单修改消息异常', [
                    'user_id' => $matchOrder->user_id,
                    'order_id' => $spotOrder->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        });
    }

    /**
     * 直接推送消息到用户
     */
    protected function pushMessageToUserDirect(int $userId, string $message): bool
    {
        try {
            // 从Redis获取用户的WebSocket连接fd
            $userFd = $this->redis->get("ws:user:{$userId}:fd");
            
            if (!$userFd) {
                $this->logger->debug('用户未在线或未建立WebSocket连接', ['user_id' => $userId]);
                return false;
            }

            // 推送消息
            $result = $this->sender->push((int)$userFd, $message);
            
            if ($result) {
                // 延长用户连接TTL
                $this->redis->expire("ws:user:{$userId}:fd", 300); // 5分钟
                $this->redis->expire("ws:fd:{$userFd}:user", 300); // 5分钟
            }
            
            return $result;

        } catch (\Throwable $e) {
            $this->logger->error('直接推送消息失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取币种符号
     */
    protected function getCurrencySymbol(int $currencyId): string
    {
        try {
            $currencyKey = CurrencyConfigKey::getCurrencyKey($currencyId);
            $symbol = $this->redis->hGet($currencyKey, Currency::FIELD_SYMBOL);
            
            if ($symbol === false || $symbol === null) {
                $this->logger->warning('未找到币种符号', ['currency_id' => $currencyId]);
                return 'UNKNOWN';
            }
            
            return $symbol;
        } catch (\Exception $e) {
            $this->logger->error('获取币种符号失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return 'UNKNOWN';
        }
    }

    /**
     * 获取冻结币种符号
     */
    protected function getFrozenCurrencySymbol(TradeSpotOrder $spotOrder): string
    {
        $frozenCurrencyId = $this->getRequiredCurrencyId($spotOrder);
        return $this->getCurrencySymbol($frozenCurrencyId);
    }
} 