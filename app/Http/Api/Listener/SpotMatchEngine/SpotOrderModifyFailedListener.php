<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 现货订单修改失败事件监听器
 */

namespace App\Http\Api\Listener\SpotMatchEngine;

use App\Enum\CurrencyConfigKey;
use App\Enum\MarketType;
use App\Http\Api\Event\Spot\OrderModifyFailedEvent;
use App\Model\Currency\Currency;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Match\MatchOrder;
use App\Model\Trade\TradeSpotOrder;
use App\Model\WebsocketData\User\OrderModifyFailedMessage;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\DbConnection\Db;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Hyperf\WebSocketServer\Sender;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class SpotOrderModifyFailedListener implements ListenerInterface
{
    /**
     * 默认精度
     */
    protected const DEFAULT_PRECISION = 18;

    /**
     * 用户资产服务
     */
    protected UserAccountsAssetService $assetService;

    /**
     * Redis 客户端
     */
    protected Redis $redis;

    /**
     * WebSocket 发送器
     */
    protected Sender $sender;

    /**
     * 日志记录器
     */
    protected LoggerInterface $logger;

    public function __construct(ContainerInterface $container)
    {
        $this->assetService = $container->get(UserAccountsAssetService::class);
        $this->redis = $container->get(Redis::class);
        $this->sender = $container->get(Sender::class);
        $this->logger = $container->get(LoggerFactory::class)->get('spot_order_modify_failed');
    }

    /**
     * 监听的事件
     */
    public function listen(): array
    {
        return [
            OrderModifyFailedEvent::class,
        ];
    }

    /**
     * 处理订单修改失败事件
     */
    public function process(object $event): void
    {
        if (!$event instanceof OrderModifyFailedEvent) {
            return;
        }

        try {
            $this->handleOrderModifyFailed($event);
        } catch (\Throwable $e) {
            $this->logger->error('处理订单修改失败事件异常', [
                'order_id' => $event->getOrderId(),
                'user_id' => $event->getUserId(),
                'reason' => $event->getReason(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理订单修改失败
     */
    protected function handleOrderModifyFailed(OrderModifyFailedEvent $event): void
    {
        Db::transaction(function () use ($event) {
            // 1. 查找撮合引擎订单
            $matchOrder = MatchOrder::query()
                ->where('order_id', $event->getOrderId())
                ->where('market_type', MarketType::CRYPTO->value)
                ->first();

            if (!$matchOrder) {
                $this->logger->warning('未找到撮合引擎订单', ['order_id' => $event->getOrderId()]);
                return;
            }

            // 2. 查找现货订单
            $spotOrder = TradeSpotOrder::query()
                ->where('match_order', $matchOrder->id)
                ->first();

            if (!$spotOrder) {
                $this->logger->warning('未找到现货订单', ['match_order_id' => $matchOrder->id]);
                return;
            }

            // 3. 验证用户身份
            if ($matchOrder->user_id != $event->getUserId()) {
                $this->logger->warning('用户身份不匹配', [
                    'order_user_id' => $matchOrder->user_id,
                    'event_user_id' => $event->getUserId()
                ]);
                return;
            }

            // 4. 计算需要解冻的资金（如果有临时冻结）
            $this->handleFrozenFundsOnFailure($spotOrder, $matchOrder, $event);

            // 5. 推送修改失败消息到前端
            $this->pushModifyFailedMessageToUser($spotOrder, $matchOrder, $event);

            $this->logger->info('订单修改失败处理完成', [
                'order_id' => $spotOrder->id,
                'match_order_id' => $matchOrder->order_id,
                'user_id' => $matchOrder->user_id,
                'reason' => $event->getReason(),
                'requested_price' => $event->getRequestedPrice(),
                'requested_quantity' => $event->getRequestedQuantity()
            ]);
        });
    }

    /**
     * 处理修改失败时的冻结资金
     */
    protected function handleFrozenFundsOnFailure(
        TradeSpotOrder $spotOrder, 
        MatchOrder $matchOrder, 
        OrderModifyFailedEvent $event
    ): void {
        try {
            // 计算如果修改成功需要的资金
            $newRequiredAmount = $this->calculateRequiredAmount($spotOrder, $event->getRequestedPrice(), $event->getRequestedQuantity());
            $currentFrozenAmount = $spotOrder->frozen_amount;
            $amountDifference = bcsub((string)$newRequiredAmount, (string)$currentFrozenAmount, self::DEFAULT_PRECISION);

            // 如果修改需要额外冻结资金，但修改失败了，需要确保没有额外冻结
            // 这里主要是防御性编程，正常情况下修改失败不应该有额外冻结
            if (bccomp($amountDifference, '0', self::DEFAULT_PRECISION) > 0) {
                $this->logger->info('订单修改失败，确认无额外资金冻结', [
                    'order_id' => $spotOrder->id,
                    'current_frozen' => $currentFrozenAmount,
                    'would_need' => $newRequiredAmount,
                    'difference' => $amountDifference
                ]);
            }

        } catch (\Throwable $e) {
            $this->logger->error('处理修改失败时的冻结资金异常', [
                'order_id' => $spotOrder->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 推送订单修改失败消息到用户
     */
    protected function pushModifyFailedMessageToUser(
        TradeSpotOrder $spotOrder, 
        MatchOrder $matchOrder, 
        OrderModifyFailedEvent $event
    ): void {
        go(function () use ($spotOrder, $matchOrder, $event) {
            try {
                // 获取币种符号
                $symbol = $this->getCurrencySymbol($spotOrder->currency_id);
                
                // 构造修改失败消息数据
                $failedData = [
                    'order_id' => $spotOrder->id,
                    'match_order_id' => $matchOrder->order_id,
                    'original_order_id' => $matchOrder->order_id,
                    'currency_id' => $spotOrder->currency_id,
                    'symbol' => $symbol,
                    'side' => $spotOrder->direction == 1 ? 'buy' : 'sell',
                    'order_type' => $spotOrder->order_type == 1 ? 'market' : 'limit',
                    'current_price' => (string)$spotOrder->price,
                    'current_quantity' => (string)$spotOrder->amount,
                    'requested_price' => (string)$event->getRequestedPrice(),
                    'requested_quantity' => (string)$event->getRequestedQuantity(),
                    'requested_time_in_force' => $event->getRequestedTimeInForce(),
                    'frozen_amount' => (string)$spotOrder->frozen_amount,
                    'frozen_currency_id' => $this->getRequiredCurrencyId($spotOrder),
                    'frozen_currency_symbol' => $this->getFrozenCurrencySymbol($spotOrder),
                    'failure_reason' => $event->getReason(),
                    'failure_reason_text' => $this->getFailureReasonText($event->getReason()),
                    'status' => 'modify_failed',
                    'created_at' => $spotOrder->created_at?->format('Y-m-d H:i:s'),
                    'updated_at' => $spotOrder->updated_at?->format('Y-m-d H:i:s'),
                    'failed_time' => date('Y-m-d H:i:s', $event->getTimestamp())
                ];

                // 创建消息对象
                $message = new OrderModifyFailedMessage($failedData);
                $message->setMarkeType(1); // 1 = 现货市场

                // 推送消息到用户
                $success = $this->pushMessageToUserDirect($matchOrder->user_id, $message->toJsonString());

                if ($success) {
                    $this->logger->info('订单修改失败消息推送成功', [
                        'user_id' => $matchOrder->user_id,
                        'order_id' => $spotOrder->id,
                        'match_order_id' => $matchOrder->order_id,
                        'reason' => $event->getReason()
                    ]);
                } else {
                    $this->logger->warning('订单修改失败消息推送失败', [
                        'user_id' => $matchOrder->user_id,
                        'order_id' => $spotOrder->id,
                        'match_order_id' => $matchOrder->order_id,
                        'reason' => $event->getReason()
                    ]);
                }

            } catch (\Throwable $e) {
                $this->logger->error('推送订单修改失败消息异常', [
                    'user_id' => $matchOrder->user_id,
                    'order_id' => $spotOrder->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        });
    }

    /**
     * 计算修改订单后的资金需求
     */
    protected function calculateRequiredAmount(TradeSpotOrder $spotOrder, float $price, float $quantity): float
    {
        if ($spotOrder->direction == 1) { // 买单
            // 买单需要：数量 * 价格
            return (float)bcmul((string)$quantity, (string)$price, self::DEFAULT_PRECISION);
        } else { // 卖单
            // 卖单需要：数量
            return $quantity;
        }
    }

    /**
     * 获取订单所需的币种ID
     */
    protected function getRequiredCurrencyId(TradeSpotOrder $spotOrder): int
    {
        if ($spotOrder->direction == 1) { // 买单
            // 买单需要计价币种，从Redis配置中获取
            try {
                $currencyKey = CurrencyConfigKey::getCurrencyKey($spotOrder->currency_id);
                $quoteCurrencyId = $this->redis->hGet($currencyKey, Currency::FIELD_QUOTE_ASSETS_ID);
                
                if ($quoteCurrencyId === false || $quoteCurrencyId === null) {
                    $this->logger->warning('未找到计价币种配置，使用默认USDT', [
                        'currency_id' => $spotOrder->currency_id
                    ]);
                    return 2; // 默认USDT
                }
                
                return (int)$quoteCurrencyId;
            } catch (\Exception $e) {
                $this->logger->error('获取计价币种失败，使用默认USDT', [
                    'currency_id' => $spotOrder->currency_id,
                    'error' => $e->getMessage()
                ]);
                return 2; // 默认USDT
            }
        } else { // 卖单
            // 卖单需要基础币种
            return $spotOrder->currency_id;
        }
    }

    /**
     * 直接推送消息到用户
     */
    protected function pushMessageToUserDirect(int $userId, string $message): bool
    {
        try {
            // 从Redis获取用户的WebSocket连接fd
            $userFd = $this->redis->get("ws:user:{$userId}:fd");
            
            if (!$userFd) {
                $this->logger->debug('用户未在线或未建立WebSocket连接', ['user_id' => $userId]);
                return false;
            }

            // 推送消息
            $result = $this->sender->push((int)$userFd, $message);
            
            if ($result) {
                // 延长用户连接TTL
                $this->redis->expire("ws:user:{$userId}:fd", 300); // 5分钟
                $this->redis->expire("ws:fd:{$userFd}:user", 300); // 5分钟
            }
            
            return $result;

        } catch (\Throwable $e) {
            $this->logger->error('直接推送消息失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取币种符号
     */
    protected function getCurrencySymbol(int $currencyId): string
    {
        try {
            $currencyKey = CurrencyConfigKey::getCurrencyKey($currencyId);
            $symbol = $this->redis->hGet($currencyKey, Currency::FIELD_SYMBOL);
            
            if ($symbol === false || $symbol === null) {
                $this->logger->warning('未找到币种符号', ['currency_id' => $currencyId]);
                return 'UNKNOWN';
            }
            
            return $symbol;
        } catch (\Exception $e) {
            $this->logger->error('获取币种符号失败', [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return 'UNKNOWN';
        }
    }

    /**
     * 获取冻结币种符号
     */
    protected function getFrozenCurrencySymbol(TradeSpotOrder $spotOrder): string
    {
        $frozenCurrencyId = $this->getRequiredCurrencyId($spotOrder);
        return $this->getCurrencySymbol($frozenCurrencyId);
    }

    /**
     * 获取失败原因的友好文本
     */
    protected function getFailureReasonText(string $reason): string
    {
        $reasonMap = [
            'already_partially_filled' => '订单已部分成交，无法修改',
            'order_not_found' => '订单不存在',
            'invalid_status' => '订单状态不允许修改',
            'invalid_price' => '价格无效',
            'invalid_quantity' => '数量无效',
            'insufficient_balance' => '余额不足',
            'market_closed' => '市场已关闭',
            'order_expired' => '订单已过期',
            'system_error' => '系统错误'
        ];

        return $reasonMap[$reason] ?? '修改失败：' . $reason;
    }
} 