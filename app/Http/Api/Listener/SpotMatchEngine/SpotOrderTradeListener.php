<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\SpotMatchEngine;

use App\Enum\Config\UserVipLevelKey;
use App\Enum\CurrencyConfigKey;
use App\Http\Api\Event\Spot\OrderTradeEvent;
use App\Model\Currency\Currency;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Trade\TradeSpotOrder;
use App\Model\User\UserVipLevel;
use App\Model\User\VipLevel;
use App\Model\WebsocketData\User\OrderTradeMessage;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Hyperf\WebSocketServer\Sender;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class SpotOrderTradeListener implements ListenerInterface
{
    /**
     * 默认金额精度
     */
    protected const DEFAULT_PRECISION = 8;

    /**
     * 精度容忍度阈值（小于此值的金额差异将被忽略）
     */
    protected const PRECISION_TOLERANCE = '0.0000001'; // 1e-7

    #[Inject]
    protected UserAccountsAssetService $assetService;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected ContainerInterface $container;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('spot-trade', 'spot-trade-logs');
    }

    public function listen(): array
    {
        return [
            OrderTradeEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderTradeEvent) {
            return;
        }

        $tradeData = $event->order;

        $this->logger->info('收到订单成交事件', $event->order);

        try {
            // 获取需要锁定的订单ID
            $lockKeys = [];
            if (isset($tradeData['buy_order_id'])) {
                $lockKeys[] = "order_lock:{$tradeData['buy_order_id']}";
            }
            if (isset($tradeData['sell_order_id'])) {
                $lockKeys[] = "order_lock:{$tradeData['sell_order_id']}";
            }

            // 使用Redis分布式锁
            $this->executeWithLock($lockKeys, function () use ($tradeData) {
                Db::transaction(function () use ($tradeData) {
                    // 检查买方数据是否完整，如果是则处理买方成交
                    if (isset($tradeData['buy_order_id']) && isset($tradeData['buyer_user_id'])) {
                        $this->processTradeForUser($tradeData, 'buy');
                    } else {
                        $this->logger->info('买方订单数据不完整，跳过处理', [
                            'trade_id' => $tradeData['trade_id'] ?? 'unknown',
                            'has_buy_order_id' => isset($tradeData['buy_order_id']),
                            'has_buyer_user_id' => isset($tradeData['buyer_user_id'])
                        ]);
                    }

                    // 检查卖方数据是否完整，如果是则处理卖方成交
                    if (isset($tradeData['sell_order_id']) && isset($tradeData['seller_user_id'])) {
                        $this->processTradeForUser($tradeData, 'sell');
                    } else {
                        $this->logger->info('卖方订单数据不完整，跳过处理', [
                            'trade_id' => $tradeData['trade_id'] ?? 'unknown',
                            'has_sell_order_id' => isset($tradeData['sell_order_id']),
                            'has_seller_user_id' => isset($tradeData['seller_user_id'])
                        ]);
                    }
                });
            });

            $this->logger->info('现货成交处理完成', [
                'trade_id' => $tradeData['trade_id'],
                'buy_order_id' => $tradeData['buy_order_id'],
                'sell_order_id' => $tradeData['sell_order_id'],
                'price' => $tradeData['price'],
                'quantity' => $tradeData['quantity']
            ]);

        } catch (\Exception $e) {
            $this->logger->error('现货成交处理失败', [
                'trade_id' => $tradeData['trade_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理单个用户的成交
     */
    protected function processTradeForUser(array $tradeData, string $side): void
    {
        // 安全获取订单ID和用户ID
        $orderId = $side === 'buy' ? ($tradeData['buy_order_id'] ?? null) : ($tradeData['sell_order_id'] ?? null);
        $userId = $side === 'buy' ? ($tradeData['buyer_user_id'] ?? null) : ($tradeData['seller_user_id'] ?? null);

        // 双重检查，确保数据完整
        if ($orderId === null || $userId === null) {
            $this->logger->warning('成交数据字段缺失', [
                'side' => $side,
                'order_id' => $orderId,
                'user_id' => $userId,
                'trade_id' => $tradeData['trade_id'] ?? 'unknown'
            ]);
            return;
        }

        // 先查找撮合引擎订单获取ID
        $matchOrder = \App\Model\Match\MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('订单不存在于数据库，可能是做市机器人订单', [
                'order_id' => $orderId,
                'user_id' => $userId,
                'side' => $side
            ]);
            return; // 机器人订单不需要处理资金变动
        }

        // 查找现货订单
        $spotOrder = TradeSpotOrder::query()
            ->where('match_order', $matchOrder->id)
            ->first();

        if (!$spotOrder) {
            $this->logger->warning('未找到对应的现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId,
                'user_id' => $userId,
                'side' => $side
            ]);
            return; // 可能也是机器人订单或数据异常
        }

        // 安全获取成交数据（根据撮合引擎数据结构）
        $priceStr = isset($tradeData['price']) ? (string)$tradeData['price'] : '0';
        $quantityStr = isset($tradeData['quantity']) ? (string)$tradeData['quantity'] : '0';

        // 检查关键数据是否完整
        if (bccomp($priceStr, '0', 18) <= 0 || bccomp($quantityStr, '0', 18) <= 0) {
            $this->logger->warning('成交数据不完整或无效', [
                'side' => $side,
                'order_id' => $orderId,
                'user_id' => $userId,
                'price' => $priceStr,
                'quantity' => $quantityStr,
                'trade_id' => $tradeData['trade_id'] ?? 'unknown'
            ]);
            return;
        }

        // 从match_orders表获取currency_id
        $currencyId = $spotOrder->currency_id;

        // 获取价格精度用于成交金额计算
        $pricePrecision = $this->getCurrencyPrecision($currencyId, 's_price_precision');
        $tradeAmountStr = bcmul($priceStr, $quantityStr, $pricePrecision);

        // 获取用户VIP等级和手续费率（区分maker和taker）
        $feeRateStr = (string)$this->getUserSpotFeeRate((int)$userId, $spotOrder->order_type);

        if ($side === 'buy') {
            $this->processBuyTrade($spotOrder, $currencyId, $priceStr, $quantityStr, $tradeAmountStr, $feeRateStr);
        } else {
            $this->processSellTrade($spotOrder, $currencyId, $priceStr, $quantityStr, $tradeAmountStr, $feeRateStr);
        }
    }

    /**
     * 处理买单成交
     */
    protected function processBuyTrade(TradeSpotOrder $spotOrder, int $currencyId, string $priceStr, string $quantityStr, string $tradeAmountStr, string $feeRateStr): void
    {
        $userId = $spotOrder->user_id;
        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);

        // 获取精度配置
        $quantityPrecision = $this->getCurrencyPrecision($currencyId, 's_quantity_precision');
        $pricePrecision = $this->getCurrencyPrecision($currencyId, 's_price_precision');

        // 1. 扣减冻结的计价币种（USDT）
        $tradeAmount = $this->safeFloatConversion($tradeAmountStr, $pricePrecision);
        $this->assetService->deductFrozenAsset(
            $userId,
            AccountType::SPOT->value,
            $quoteCurrencyId,
            $tradeAmount,
            FlowsType::SPOT_TRADE->value,
            $spotOrder->id
        );

        // 2. 计算手续费（买单：从获得的基础币种中扣除手续费）
        // 手续费 = 获得的基础币种数量 × 费率（使用数量精度）
        $feeStr = bcmul($quantityStr, $feeRateStr, $quantityPrecision);
        $fee = $this->safeFloatConversion($feeStr, $quantityPrecision);
        $actualQuantityStr = bcsub($quantityStr, $feeStr, $quantityPrecision);
        $actualQuantity = $this->safeFloatConversion($actualQuantityStr, $quantityPrecision);

        // 3. 增加基础币种（扣除手续费后的数量）
        if (bccomp($actualQuantityStr, '0', $quantityPrecision) > 0) {
            $this->assetService->addAvailableAsset(
                $userId,
                AccountType::SPOT->value,
                $currencyId,
                $actualQuantity,
                FlowsType::SPOT_TRADE->value,
                $spotOrder->id
            );
        }

        // 4. 更新订单已使用金额和手续费
        $this->updateOrderAmounts($spotOrder, $tradeAmountStr, $feeStr);

        // 5. 异步推送成交消息到用户
        $this->pushTradeMessageToUser($userId, $spotOrder, $priceStr, $quantityStr, $tradeAmountStr, $feeStr, 'buy');
    }

    /**
     * 处理卖单成交
     */
    protected function processSellTrade(TradeSpotOrder $spotOrder, int $currencyId, string $priceStr, string $quantityStr, string $tradeAmountStr, string $feeRateStr): void
    {
        $userId = $spotOrder->user_id;
        $quoteCurrencyId = $this->getQuoteCurrencyId($currencyId);

        // 获取精度配置
        $quantityPrecision = $this->getCurrencyPrecision($currencyId, 's_quantity_precision');
        $pricePrecision = $this->getCurrencyPrecision($currencyId, 's_price_precision');

        // 1. 扣减冻结的基础币种
        $quantity = $this->safeFloatConversion($quantityStr, $quantityPrecision);
        $this->assetService->deductFrozenAsset(
            $userId,
            AccountType::SPOT->value,
            $currencyId,
            $quantity,
            FlowsType::SPOT_TRADE->value,
            $spotOrder->id
        );

        // 2. 计算手续费（卖单：从获得的计价币种中扣除手续费）
        // 手续费 = 成交金额 × 费率（使用价格精度）
        $feeStr = bcmul($tradeAmountStr, $feeRateStr, $pricePrecision);
        $fee = $this->safeFloatConversion($feeStr, $pricePrecision);
        $actualAmountStr = bcsub($tradeAmountStr, $feeStr, $pricePrecision);
        $actualAmount = $this->safeFloatConversion($actualAmountStr, $pricePrecision);

        // 3. 增加计价币种（扣除手续费后）
        if (bccomp($actualAmountStr, '0', $pricePrecision) > 0) {
            $this->assetService->addAvailableAsset(
                $userId,
                AccountType::SPOT->value,
                $quoteCurrencyId,
                $actualAmount,
                FlowsType::SPOT_TRADE->value,
                $spotOrder->id
            );
        }

        // 4. 更新订单已使用金额和手续费
        $this->updateOrderAmounts($spotOrder, $tradeAmountStr, $feeStr);

        // 5. 异步推送成交消息到用户
        $this->pushTradeMessageToUser($userId, $spotOrder, $priceStr, $quantityStr, $tradeAmountStr, $feeStr, 'sell');

        $this->logger->info('卖单成交处理完成', [
            'user_id' => $userId,
            'order_id' => $spotOrder->id,
            'currency_id' => $currencyId,
            'quote_currency_id' => $quoteCurrencyId,
            'trade_amount' => $tradeAmountStr,
            'quantity' => $quantityStr,
            'fee' => $feeStr,
            'actual_amount' => $actualAmountStr
        ]);
    }

    /**
     * 获取用户现货交易费率
     */
    protected function getUserSpotFeeRate(int $userId, int $orderType): float
    {
        try {
            // 查询用户当前活跃的VIP等级
            $userVipLevel = UserVipLevel::query()
                ->where('user_id', $userId)
                ->where('is_active', 1)
                ->first();

            if (!$userVipLevel) {
                return 0.001; // 默认费率 0.1%
            }

            // 根据订单类型选择费率字段
            // 限价单使用maker费率，市价单使用taker费率
            $feeRateField = ($orderType == 1) ? VipLevel::FIELD_SPOT_TAKER_FEE_RATE : VipLevel::FIELD_SPOT_MAKER_FEE_RATE;

            // 从Redis Hash获取对应的费率
            $vipLevelKey = UserVipLevelKey::getConfigKey($userVipLevel->vip_level_id);
            $feeRate = $this->redis->hGet($vipLevelKey, $feeRateField);

            if ($feeRate === false || $feeRate === null) {
                $this->logger->warning('Redis中未找到VIP等级费率配置', [
                    'user_id' => $userId,
                    'vip_level_id' => $userVipLevel->vip_level_id,
                    'order_type' => $orderType,
                    'redis_key' => $vipLevelKey,
                    'field' => $feeRateField
                ]);
                return 0.001; // 默认费率 0.1%
            }

            return (float)$feeRate;

        } catch (\Exception $e) {
            $this->logger->warning('获取用户费率失败，使用默认费率', [
                'user_id' => $userId,
                'order_type' => $orderType,
                'error' => $e->getMessage()
            ]);
            return 0.001; // 默认费率 0.1%
        }
    }

    /**
     * 获取计价币种ID
     */
    protected function getQuoteCurrencyId(int $baseCurrencyId): int
    {
        try {
            // 从Redis获取币种配置
            $currencyKey = CurrencyConfigKey::getCurrencyKey($baseCurrencyId);
            $quoteCurrencyId = $this->redis->hGet($currencyKey, Currency::FIELD_QUOTE_ASSETS_ID);

            if ($quoteCurrencyId === false || $quoteCurrencyId === null) {
                $this->logger->warning('Redis中未找到计价币种配置', [
                    'base_currency_id' => $baseCurrencyId,
                    'redis_key' => $currencyKey,
                    'field' => Currency::FIELD_QUOTE_ASSETS_ID
                ]);
                return 1; // 默认返回USDT的ID
            }

            return (int)$quoteCurrencyId;

        } catch (\Exception $e) {
            $this->logger->warning('获取计价币种ID失败，使用默认值', [
                'base_currency_id' => $baseCurrencyId,
                'error' => $e->getMessage()
            ]);
            return 1; // 默认返回USDT的ID
        }
    }

    /**
     * 更新订单已使用金额和手续费
     */
    protected function updateOrderAmounts(TradeSpotOrder $spotOrder, string $tradeAmountStr, string $feeStr): void
    {
        // 更新已使用金额（使用精度处理）
        $currentUsedAmount = (string)($spotOrder->used_amount ?? '0');
        $newUsedAmountStr = bcadd($currentUsedAmount, $tradeAmountStr, self::DEFAULT_PRECISION);
        $newUsedAmount = $this->safeFloatConversion($newUsedAmountStr, self::DEFAULT_PRECISION);
        $spotOrder->used_amount = $newUsedAmount;

        // 更新累计手续费（使用精度处理）
        $currentCharge = (string)($spotOrder->charge ?? '0');
        $newChargeStr = bcadd($currentCharge, $feeStr, self::DEFAULT_PRECISION);
        $newCharge = $this->safeFloatConversion($newChargeStr, self::DEFAULT_PRECISION);
        $spotOrder->charge = $newCharge;

        $spotOrder->save();

        $this->logger->debug('订单金额更新完成', [
            'order_id' => $spotOrder->id,
            'trade_amount' => $tradeAmountStr,
            'fee' => $feeStr,
            'previous_used_amount' => $currentUsedAmount,
            'new_used_amount' => $newUsedAmountStr,
            'previous_charge' => $currentCharge,
            'new_charge' => $newChargeStr
        ]);
    }

    /**
     * 使用Redis分布式锁执行操作
     */
    protected function executeWithLock(array $lockKeys, callable $callback): void
    {
        $lockTimeout = 10; // 锁超时时间（秒）
        $maxRetries = 30; // 最大重试次数
        $retryDelay = 100; // 重试间隔（毫秒）
        $acquiredLocks = [];

        try {
            // 按顺序获取所有锁（避免死锁）
            sort($lockKeys);

            foreach ($lockKeys as $lockKey) {
                $lockValue = uniqid();
                $acquired = false;
                $retries = 0;

                // 重试获取锁
                while (!$acquired && $retries < $maxRetries) {
                    $acquired = $this->redis->set($lockKey, $lockValue, ['NX', 'EX' => $lockTimeout]);

                    if (!$acquired) {
                        $retries++;
                        if ($retries < $maxRetries) {
                            // 等待一段时间后重试
                            usleep((int)($retryDelay * 1000)); // 转换为微秒
                            $retryDelay = min($retryDelay * 1.2, 500); // 指数退避，最大500ms
                        }
                    }
                }

                if (!$acquired) {
                    throw new \RuntimeException("获取锁超时: {$lockKey}，已重试 {$maxRetries} 次");
                }

                $acquiredLocks[$lockKey] = $lockValue;
            }

            // 执行业务逻辑
            $callback();

        } finally {
            // 释放所有获取的锁
            foreach ($acquiredLocks as $lockKey => $lockValue) {
                $this->releaseLock($lockKey, $lockValue);
            }
        }
    }

    /**
     * 释放Redis锁
     */
    protected function releaseLock(string $lockKey, string $lockValue): void
    {
        $script = "
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
        ";

        $this->redis->eval($script, [$lockKey, $lockValue], 1);
    }

    /**
     * 获取币种精度配置
     */
    protected function getCurrencyPrecision(int $currencyId, string $precisionField): int
    {
        try {
            // 从Redis获取币种配置
            $currencyKey = CurrencyConfigKey::getCurrencyKey($currencyId);
            $precision = $this->redis->hGet($currencyKey, $precisionField);

            if ($precision === false || $precision === null) {
                $this->logger->warning('未找到币种精度配置，使用默认值', [
                    'currency_id' => $currencyId,
                    'precision_field' => $precisionField,
                    'redis_key' => $currencyKey
                ]);
                return self::DEFAULT_PRECISION; // 默认精度
            }

            return (int)$precision;

        } catch (\Exception $e) {
            $this->logger->error('获取币种精度失败，使用默认值', [
                'currency_id' => $currencyId,
                'precision_field' => $precisionField,
                'error' => $e->getMessage()
            ]);
            return self::DEFAULT_PRECISION; // 默认精度
        }
    }

    /**
     * 精度规范化处理
     * 处理浮点数计算中的微小误差
     */
    protected function normalizePrecision(string $amount, int $precision = null): string
    {
        $precision = $precision ?? self::DEFAULT_PRECISION;
        
        // 如果金额绝对值小于容忍度，直接返回0
        if (bccomp($this->bcabs($amount, $precision + 2), self::PRECISION_TOLERANCE, $precision + 2) < 0) {
            $this->logger->debug('金额差异小于容忍度，归零处理', [
                'original_amount' => $amount,
                'tolerance' => self::PRECISION_TOLERANCE,
                'precision' => $precision
            ]);
            return '0';
        }
        
        // 按指定精度进行舍入
        return bcadd($amount, '0', $precision);
    }

    /**
     * 计算绝对值
     */
    protected function bcabs(string $number, int $precision = null): string
    {
        $precision = $precision ?? self::DEFAULT_PRECISION;
        return bccomp($number, '0', $precision) < 0 ? bcmul($number, '-1', $precision) : $number;
    }

    /**
     * 安全转换 BCMath 结果为 float（带精度处理）
     */
    protected function safeFloatConversion(string $amount, int $precision = null): float
    {
        $precision = $precision ?? self::DEFAULT_PRECISION;
        $normalized = $this->normalizePrecision($amount, $precision);
        return (float)$normalized;
    }

    /**
     * 异步推送成交消息到用户
     */
    protected function pushTradeMessageToUser(int $userId, TradeSpotOrder $spotOrder, string $priceStr, string $quantityStr, string $tradeAmountStr, string $feeStr, string $side): void
    {
        // 使用协程异步处理推送，避免阻塞主流程
        go(function () use ($userId, $spotOrder, $priceStr, $quantityStr, $tradeAmountStr, $feeStr, $side) {
            try {
                // 查询现货交易订单的完整信息
                $orderData = $this->getSpotOrderData($spotOrder);
                
                if (!$orderData) {
                    $this->logger->warning('无法获取订单数据，跳过推送', [
                        'user_id' => $userId,
                        'order_id' => $spotOrder->id
                    ]);
                    return;
                }

                // 构造成交消息数据
                $tradeData = [
                    'order_id' => $spotOrder->id,
                    'match_order_id' => $spotOrder->match_order,
                    'currency_id' => $spotOrder->currency_id,
                    'symbol' => $orderData['symbol'] ?? '',
                    'side' => $side,
                    'order_type' => $spotOrder->order_type == 1 ? 'market' : 'limit',
                    'price' => $priceStr,
                    'quantity' => $quantityStr,
                    'trade_amount' => $tradeAmountStr,
                    'fee' => $feeStr,
                    'fee_rate' => $orderData['fee_rate'] ?? '0',
                    'status' => $this->getOrderStatus($spotOrder),
                    'filled_quantity' => (string)($spotOrder->filled_quantity ?? '0'),
                    'avg_price' => (string)($spotOrder->avg_price ?? '0'),
                    'created_at' => $spotOrder->created_at?->format('Y-m-d H:i:s'),
                    'updated_at' => $spotOrder->updated_at?->format('Y-m-d H:i:s'),
                    'trade_time' => date('Y-m-d H:i:s')
                ];

                // 创建消息对象
                $message = new OrderTradeMessage($tradeData);
                $message->setMarkeType(1); // 1 = 现货市场

                // 直接推送消息到用户，避免循环依赖
                $success = $this->pushMessageToUserDirect($userId, $message->toJsonString());

                if ($success) {
                    $this->logger->info('成交消息推送成功', [
                        'user_id' => $userId,
                        'order_id' => $spotOrder->id,
                        'side' => $side,
                        'price' => $priceStr,
                        'quantity' => $quantityStr
                    ]);
                } else {
                    $this->logger->info('用户未在线，跳过消息推送', [
                        'user_id' => $userId,
                        'order_id' => $spotOrder->id
                    ]);
                }

            } catch (\Throwable $e) {
                $this->logger->error('推送成交消息失败', [
                    'user_id' => $userId,
                    'order_id' => $spotOrder->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        });
    }

    /**
     * 获取现货订单的详细数据
     */
    protected function getSpotOrderData(TradeSpotOrder $spotOrder): ?array
    {
        try {
            // 通过 match_order 字段关联查询 match_order 表
            $matchOrder = \App\Model\Match\MatchOrder::query()
                ->where('id', $spotOrder->match_order)
                ->first();

            if (!$matchOrder) {
                return null;
            }

            // 获取币种信息
            $currency = \App\Model\Currency\Currency::query()
                ->where('id', $spotOrder->currency_id)
                ->first();

            return [
                'symbol' => $currency?->symbol ?? '',
                'currency_name' => $currency?->name ?? [],
                'original_quantity' => (string)$matchOrder->quantity,
                'original_price' => (string)$matchOrder->price,
                'fee_rate' => '0', // 可以根据需要计算
                'match_order' => $matchOrder->toArray()
            ];

        } catch (\Throwable $e) {
            $this->logger->error('获取订单数据失败', [
                'order_id' => $spotOrder->id,
                'match_order_id' => $spotOrder->match_order,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取订单状态
     */
    protected function getOrderStatus(TradeSpotOrder $spotOrder): string
    {
        // 根据订单的完成情况判断状态
        $filledQuantity = (float)($spotOrder->filled_quantity ?? 0);
        $originalQuantity = (float)($spotOrder->quantity ?? 0);

        if ($filledQuantity >= $originalQuantity) {
            return 'filled'; // 完全成交
        } elseif ($filledQuantity > 0) {
            return 'partial_filled'; // 部分成交
        } else {
            return 'pending'; // 待成交
        }
    }

    /**
     * 直接推送消息到用户（避免循环依赖）
     */
    protected function pushMessageToUserDirect(int $userId, string $message): bool
    {
        try {
            // 从Redis获取用户连接的fd
            $userFd = $this->redis->get("ws:user:{$userId}:fd");
            
            if (!$userFd) {
                return false; // 用户未连接
            }

            // 获取WebSocket Sender实例
            $sender = $this->container->get(Sender::class);
            
            // 推送消息到指定fd
            $sender->push((int)$userFd, $message);
            
            return true;

        } catch (\Throwable $e) {
            $this->logger->error('直接推送消息失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
