<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\SpotMatchEngine;

use App\Enum\CurrencyConfigKey;
use App\Http\Api\Event\Spot\OrderCancelEvent;
use App\Model\Currency\Currency;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Match\MatchOrder;
use App\Model\Trade\TradeSpotOrder;
use App\Model\WebsocketData\User\OrderCancelMessage;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Hyperf\WebSocketServer\Sender;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class SpotOrderCancelListener implements ListenerInterface
{
    #[Inject]
    protected UserAccountsAssetService $assetService;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected ContainerInterface $container;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('spot-cancel', 'spot-trade-logs');
    }

    public function listen(): array
    {
        return [
            OrderCancelEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderCancelEvent) {
            return;
        }

        $orderId = $event->order_id;

        try {
            // 使用Redis分布式锁
            $lockKey = "order_lock:{$orderId}";
            $this->executeWithLock([$lockKey], function () use ($orderId) {
                Db::transaction(function () use ($orderId) {
                    $this->processOrderCancel($orderId);
                });
            });

            $this->logger->info('现货订单取消处理完成', [
                'order_id' => $orderId
            ]);

        } catch (\Exception $e) {
            $this->logger->error('现货订单取消处理失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理订单取消
     */
    protected function processOrderCancel(int $orderId): void
    {
        // 查找撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('订单不存在于数据库', [
                'order_id' => $orderId
            ]);
            return;
        }

        // 查找现货订单
        $spotOrder = TradeSpotOrder::query()
            ->where('match_order', $matchOrder->id)
            ->first();

        if (!$spotOrder) {
            $this->logger->warning('未找到对应的现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId
            ]);
            return;
        }

        // 计算需要解冻的资金
        $unfreezeAmount = $this->calculateUnfreezeAmount($spotOrder, $matchOrder);
        $unfreezeCurrencyId = $this->getUnfreezeCurrencyId($spotOrder);

        // 解冻资金
        $unfreezeAmountStr = sprintf('%.18f', $unfreezeAmount);
        if (bccomp($unfreezeAmountStr, '0', 8) > 0) {
            $this->assetService->unfreezeAsset(
                $spotOrder->user_id,
                AccountType::SPOT->value,
                $unfreezeCurrencyId,
                $unfreezeAmount,
                FlowsType::SPOT_TRADE->value,
                $spotOrder->id
            );

            $this->logger->info('解冻资金成功', [
                'user_id' => $spotOrder->user_id,
                'order_id' => $spotOrder->id,
                'currency_id' => $unfreezeCurrencyId,
                'unfreeze_amount' => $unfreezeAmount,
                'order_direction' => $spotOrder->direction,
                'order_type' => $spotOrder->order_type
            ]);
        } else {
            $this->logger->info('无需解冻资金', [
                'user_id' => $spotOrder->user_id,
                'order_id' => $spotOrder->id,
                'unfreeze_amount' => $unfreezeAmount
            ]);
        }

        // 异步推送取消消息到用户
        $this->pushCancelMessageToUser($spotOrder, $matchOrder, $unfreezeAmount, $unfreezeCurrencyId);
    }

    /**
     * 计算需要解冻的资金（基于订单级别的精确计算）
     */
    protected function calculateUnfreezeAmount(TradeSpotOrder $spotOrder, MatchOrder $matchOrder): float
    {
        // 使用订单级别的冻结金额和已使用金额进行精确计算
        $frozenAmount = $spotOrder->frozen_amount ?? 0;
        $usedAmount = $spotOrder->used_amount ?? 0;

        // 计算剩余需要解冻的金额（使用8位精度）
        $frozenAmountStr = sprintf('%.18f', $frozenAmount);
        $usedAmountStr = sprintf('%.18f', $usedAmount);
        $remainingFrozen = bcsub($frozenAmountStr, $usedAmountStr, 8);

        if (bccomp($remainingFrozen, '0', 8) <= 0) {
            return 0.0; // 无剩余冻结资金
        }

        return (float)$remainingFrozen;
    }

    /**
     * 获取需要解冻的币种ID
     */
    protected function getUnfreezeCurrencyId(TradeSpotOrder $spotOrder): int
    {
        // 从match_orders表获取基础币种ID
        $baseCurrencyId = $spotOrder->currency_id;

        if ($spotOrder->direction == 1) { // 买单
            // 买单解冻计价币种（如USDT）
            return $this->getQuoteCurrencyId($baseCurrencyId);
        } else { // 卖单
            // 卖单解冻基础币种（如BTC）
            return $baseCurrencyId;
        }
    }

    /**
     * 获取计价币种ID
     */
    protected function getQuoteCurrencyId(int $baseCurrencyId): int
    {
        try {
            // 从Redis获取币种配置
            $currencyKey = CurrencyConfigKey::getCurrencyKey($baseCurrencyId);
            $quoteCurrencyId = $this->redis->hGet($currencyKey, Currency::FIELD_QUOTE_ASSETS_ID);

            if ($quoteCurrencyId === false || $quoteCurrencyId === null) {
                $this->logger->warning('Redis中未找到计价币种配置', [
                    'base_currency_id' => $baseCurrencyId,
                    'redis_key' => $currencyKey,
                    'field' => Currency::FIELD_QUOTE_ASSETS_ID
                ]);
                return 1; // 默认返回USDT的ID
            }

            return (int)$quoteCurrencyId;

        } catch (\Exception $e) {
            $this->logger->warning('获取计价币种ID失败，使用默认值', [
                'base_currency_id' => $baseCurrencyId,
                'error' => $e->getMessage()
            ]);
            return 1; // 默认返回USDT的ID
        }
    }

    /**
     * 获取估算价格（用于市价单）
     */
    protected function getEstimatedPrice(int $currencyId): float
    {
        // 这里应该从市场数据获取当前价格
        // 简化处理，可以从Redis获取最新价格或使用固定价格
        // 实际应该从市场数据服务获取
        return 50000.0; // 简化处理，返回固定价格
    }



    /**
     * 使用Redis分布式锁执行操作
     */
    protected function executeWithLock(array $lockKeys, callable $callback): void
    {
        $lockTimeout = 10; // 锁超时时间（秒）
        $maxRetries = 30; // 最大重试次数
        $retryDelay = 100; // 重试间隔（毫秒）
        $acquiredLocks = [];

        try {
            // 按顺序获取所有锁（避免死锁）
            sort($lockKeys);

            foreach ($lockKeys as $lockKey) {
                $lockValue = uniqid();
                $acquired = false;
                $retries = 0;

                // 重试获取锁
                while (!$acquired && $retries < $maxRetries) {
                    $acquired = $this->redis->set($lockKey, $lockValue, ['NX', 'EX' => $lockTimeout]);

                    if (!$acquired) {
                        $retries++;
                        if ($retries < $maxRetries) {
                            // 等待一段时间后重试
                            usleep((int)($retryDelay * 1000)); // 转换为微秒
                            $retryDelay = min($retryDelay * 1.2, 500); // 指数退避，最大500ms
                        }
                    }
                }

                if (!$acquired) {
                    throw new \RuntimeException("获取锁超时: {$lockKey}，已重试 {$maxRetries} 次");
                }

                $acquiredLocks[$lockKey] = $lockValue;
            }

            // 执行业务逻辑
            $callback();

        } finally {
            // 释放所有获取的锁
            foreach ($acquiredLocks as $lockKey => $lockValue) {
                $this->releaseLock($lockKey, $lockValue);
            }
        }
    }

    /**
     * 释放Redis锁
     */
    protected function releaseLock(string $lockKey, string $lockValue): void
    {
        $script = "
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
        ";

        $this->redis->eval($script, [$lockKey, $lockValue], 1);
    }

    /**
     * 异步推送取消消息到用户
     */
    protected function pushCancelMessageToUser(TradeSpotOrder $spotOrder, MatchOrder $matchOrder, float $unfreezeAmount, int $unfreezeCurrencyId): void
    {
        // 使用协程异步处理推送，避免阻塞主流程
        go(function () use ($spotOrder, $matchOrder, $unfreezeAmount, $unfreezeCurrencyId) {
            try {
                // 获取订单的详细信息
                $orderData = $this->getSpotOrderData($spotOrder, $matchOrder);
                
                if (!$orderData) {
                    $this->logger->warning('无法获取订单数据，跳过推送', [
                        'user_id' => $spotOrder->user_id,
                        'order_id' => $spotOrder->id
                    ]);
                    return;
                }

                // 构造取消消息数据
                $cancelData = [
                    'order_id' => $spotOrder->id,
                    'match_order_id' => $matchOrder->id,
                    'original_order_id' => $matchOrder->order_id,
                    'currency_id' => $spotOrder->currency_id,
                    'symbol' => $orderData['symbol'] ?? '',
                    'side' => $spotOrder->direction == 1 ? 'buy' : 'sell',
                    'order_type' => $spotOrder->order_type == 1 ? 'market' : 'limit',
                    'original_price' => (string)$matchOrder->price,
                    'original_quantity' => (string)$matchOrder->quantity,
                    'filled_quantity' => (string)($spotOrder->filled_quantity ?? '0'),
                    'unfilled_quantity' => (string)$this->calculateUnfilledQuantity($spotOrder, $matchOrder),
                    'unfreeze_amount' => (string)$unfreezeAmount,
                    'unfreeze_currency_id' => $unfreezeCurrencyId,
                    'unfreeze_currency_symbol' => $orderData['unfreeze_currency_symbol'] ?? '',
                    'status' => 'cancelled',
                    'cancel_reason' => 'user_cancel',
                    'avg_price' => (string)($spotOrder->avg_price ?? '0'),
                    'total_fee' => (string)($spotOrder->charge ?? '0'),
                    'created_at' => $spotOrder->created_at?->format('Y-m-d H:i:s'),
                    'updated_at' => $spotOrder->updated_at?->format('Y-m-d H:i:s'),
                    'cancel_time' => date('Y-m-d H:i:s')
                ];

                // 创建消息对象
                $message = new OrderCancelMessage($cancelData);
                $message->setMarkeType(1); // 1 = 现货市场

                // 推送消息到用户
                $success = $this->pushMessageToUserDirect($spotOrder->user_id, $message->toJsonString());

                if ($success) {
                    $this->logger->info('取消消息推送成功', [
                        'user_id' => $spotOrder->user_id,
                        'order_id' => $spotOrder->id,
                        'original_order_id' => $matchOrder->order_id,
                        'unfreeze_amount' => $unfreezeAmount
                    ]);
                } else {
                    $this->logger->info('用户未在线，跳过消息推送', [
                        'user_id' => $spotOrder->user_id,
                        'order_id' => $spotOrder->id
                    ]);
                }

            } catch (\Throwable $e) {
                $this->logger->error('推送取消消息失败', [
                    'user_id' => $spotOrder->user_id,
                    'order_id' => $spotOrder->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        });
    }

    /**
     * 获取现货订单的详细数据
     */
    protected function getSpotOrderData(TradeSpotOrder $spotOrder, MatchOrder $matchOrder): ?array
    {
        try {
            // 获取基础币种信息
            $baseCurrency = \App\Model\Currency\Currency::query()
                ->where('id', $spotOrder->currency_id)
                ->first();

            // 获取计价币种信息
            $quoteCurrencyId = $this->getQuoteCurrencyId($spotOrder->currency_id);
            $quoteCurrency = \App\Model\Currency\Currency::query()
                ->where('id', $quoteCurrencyId)
                ->first();

            // 根据订单方向确定解冻币种的符号
            $unfreezeCurrencySymbol = '';
            if ($spotOrder->direction == 1) { // 买单
                $unfreezeCurrencySymbol = $quoteCurrency?->symbol ?? 'USDT';
            } else { // 卖单
                $unfreezeCurrencySymbol = $baseCurrency?->symbol ?? '';
            }

            return [
                'symbol' => $baseCurrency?->symbol ?? '',
                'base_currency_name' => $baseCurrency?->name ?? [],
                'quote_currency_name' => $quoteCurrency?->name ?? [],
                'unfreeze_currency_symbol' => $unfreezeCurrencySymbol,
                'match_order' => $matchOrder->toArray()
            ];

        } catch (\Throwable $e) {
            $this->logger->error('获取订单数据失败', [
                'order_id' => $spotOrder->id,
                'match_order_id' => $matchOrder->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 计算未成交数量
     */
    protected function calculateUnfilledQuantity(TradeSpotOrder $spotOrder, MatchOrder $matchOrder): float
    {
        $originalQuantity = (float)$matchOrder->quantity;
        $filledQuantity = (float)($spotOrder->filled_quantity ?? 0);
        
        return max(0, $originalQuantity - $filledQuantity);
    }

    /**
     * 直接推送消息到用户（避免循环依赖）
     */
    protected function pushMessageToUserDirect(int $userId, string $message): bool
    {
        try {
            // 从Redis获取用户连接的fd
            $userFd = $this->redis->get("ws:user:{$userId}:fd");
            
            if (!$userFd) {
                return false; // 用户未连接
            }

            // 获取WebSocket Sender实例
            $sender = $this->container->get(Sender::class);
            
            // 推送消息到指定fd
            $sender->push((int)$userFd, $message);
            
            return true;

        } catch (\Throwable $e) {
            $this->logger->error('直接推送消息失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
