<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual\Traits;

use App\Model\Trade\TradePerpetualPosition;
use App\Model\Enums\Trade\Perpetual\PositionStatus;
use App\Model\Enums\Trade\Perpetual\ContractSide;
use App\Model\Enums\Trade\Perpetual\MarginMode;

trait PerpetualPositionTrait
{
    /**
     * 查找或创建仓位
     */
    protected function findOrCreatePosition(int $userId, int $currencyId, int $marginMode, int $side): TradePerpetualPosition
    {
        $position = TradePerpetualPosition::query()
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', $marginMode)
            ->where('side', $side)
            ->where('status', PositionStatus::HOLDING->value)
            ->first();

        if (!$position) {
            $position = new TradePerpetualPosition();
            $position->user_id = $userId;
            $position->currency_id = $currencyId;
            $position->margin_mode = $marginMode;
            $position->side = $side;
            $position->quantity = 0;
            $position->available_quantity = 0;
            $position->frozen_quantity = 0;
            $position->entry_price = 0;
            $position->margin_amount = 0;
            $position->initial_margin = 0;
            $position->maintenance_margin = 0;
            $position->realized_pnl = 0;
            $position->leverage = 1;
            $position->auto_add_margin = 0;
            $position->status = PositionStatus::HOLDING->value;
            $position->save();
        }

        return $position;
    }

    /**
     * 更新开仓仓位
     */
    protected function updateOpenPosition(TradePerpetualPosition $position, float $filledQuantity, float $filledPrice, float $usedMargin, float $leverage): void
    {
        // 计算新的持仓数量
        $newQuantity = bcadd((string)$position->quantity, (string)$filledQuantity, 8);
        
        // 计算新的持仓均价
        $currentValue = bcmul((string)$position->quantity, (string)$position->entry_price, 8);
        $newValue = bcmul((string)$filledQuantity, (string)$filledPrice, 8);
        $totalValue = bcadd($currentValue, $newValue, 8);
        
        $newEntryPrice = bccomp($newQuantity, '0', 8) > 0
            ? bcdiv($totalValue, $newQuantity, 8)
            : '0';

        // 更新仓位数据
        $position->quantity = (float)$newQuantity;
        $position->available_quantity = (float)$newQuantity;
        $position->entry_price = (float)$newEntryPrice;
        $position->margin_amount = (float)bcadd((string)$position->margin_amount, (string)$usedMargin, 8);
        $position->leverage = $leverage;
        
        // 计算初始保证金和维持保证金
        $notionalValue = bcmul($newQuantity, $newEntryPrice, 8);
        $position->initial_margin = (float)bcdiv($notionalValue, (string)$leverage, 8);

        // 获取用户对应档位的维持保证金率
        $marginRate = $this->getMaintenanceMarginRate(
            $position->currency_id,
            (float)$notionalValue,
            $position->margin_mode,
            $position->user_id,
            $leverage
        );
        $position->maintenance_margin = (float)bcmul($notionalValue, (string)$marginRate, 8);
        
        $position->save();
    }

    /**
     * 更新平仓仓位
     */
    protected function updateClosePosition(TradePerpetualPosition $position, float $filledQuantity, float $filledPrice): float
    {
        // 计算已实现盈亏
        $pnlPerUnit = bcsub((string)$filledPrice, (string)$position->entry_price, 8);
        
        // 根据仓位方向调整盈亏计算
        if ($position->side == 2) { // 空头仓位
            $pnlPerUnit = bcsub((string)$position->entry_price, (string)$filledPrice, 8);
        }
        
        $realizedPnl = bcmul($pnlPerUnit, (string)$filledQuantity, 8);
        
        // 计算释放的保证金比例（平仓数量 / 原有持仓数量）
        $marginRatio = bccomp((string)$position->quantity, '0', 8) > 0
            ? bcdiv((string)$filledQuantity, (string)$position->quantity, 8)
            : '0';
        
        $releasedMargin = bcmul((string)$position->margin_amount, $marginRatio, 8);
        
        // 更新仓位数据
        $position->frozen_quantity = (float)bcsub((string)$position->frozen_quantity, (string)$filledQuantity, 8);
        $position->realized_pnl = (float)bcadd((string)$position->realized_pnl, $realizedPnl, 8);
        $position->margin_amount = (float)bcsub((string)$position->margin_amount, $releasedMargin, 8);

        // 确保冻结数量不会变成负数
        if ($position->frozen_quantity < 0) {
            $position->frozen_quantity = 0;
        }

        // 检查是否完全平仓（可用数量 + 冻结数量 = 0）
        $totalRemaining = bcadd((string)$position->available_quantity, (string)$position->frozen_quantity, 8);
        if (bccomp($totalRemaining, '0', 8) <= 0) {
            // 完全平仓：只更新状态和保证金，quantity保持不变（作为历史记录）
            $position->initial_margin = 0;
            $position->maintenance_margin = 0;
            $position->status = PositionStatus::CLOSED->value;
        } else {
            // 部分平仓：重新计算保证金，基于当前实际持仓数量（available + frozen）
            $currentHolding = bcadd((string)$position->available_quantity, (string)$position->frozen_quantity, 8);
            $notionalValue = bcmul($currentHolding, (string)$position->entry_price, 8);
            $position->initial_margin = (float)bcdiv($notionalValue, (string)$position->leverage, 8);

            // 获取用户对应档位的维持保证金率
            $marginRate = $this->getMaintenanceMarginRate(
                $position->currency_id,
                (float)$notionalValue,
                $position->margin_mode,
                $position->user_id,
                $position->leverage
            );
            $position->maintenance_margin = (float)bcmul($notionalValue, (string)$marginRate, 8);
        }
        
        $position->save();
        
        return (float)$releasedMargin;
    }

    /**
     * 解冻仓位数量
     */
    protected function unfreezePositionQuantity(int $userId, int $currencyId, int $marginMode, int $side, float $quantity): void
    {
        $position = TradePerpetualPosition::query()
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', $marginMode)
            ->where('side', $side)
            ->where('status', PositionStatus::HOLDING->value)
            ->first();

        if ($position) {
            $position->available_quantity = (float)bcadd((string)$position->available_quantity, (string)$quantity, 8);
            $position->frozen_quantity = (float)bcsub((string)$position->frozen_quantity, (string)$quantity, 8);

            // 确保数量不会变成负数
            if ($position->frozen_quantity < 0) {
                $position->frozen_quantity = 0;
            }
            if ($position->available_quantity < 0) {
                $position->available_quantity = 0;
            }

            $position->save();
        }
    }

    /**
     * 根据合约方向获取仓位方向
     */
    protected function getPositionSideFromContractSide(int $contractSide): int
    {
        return match($contractSide) {
            ContractSide::BUY_OPEN->value, ContractSide::SELL_CLOSE->value => 1, // 多头
            ContractSide::SELL_OPEN->value, ContractSide::BUY_CLOSE->value => 2, // 空头
            default => 1
        };
    }

    /**
     * 判断是否为开仓操作
     */
    protected function isOpenOperation(int $contractSide): bool
    {
        return in_array($contractSide, [
            ContractSide::BUY_OPEN->value,
            ContractSide::SELL_OPEN->value
        ]);
    }

    /**
     * 判断是否为平仓操作
     */
    protected function isCloseOperation(int $contractSide): bool
    {
        return in_array($contractSide, [
            ContractSide::BUY_CLOSE->value,
            ContractSide::SELL_CLOSE->value
        ]);
    }

    /**
     * 获取维持保证金率
     */
    protected function getMaintenanceMarginRate(int $currencyId, float $currentNotionalValue, int $marginMode = 5, int $userId = 0, float $leverage = 20): float
    {
        // 根据保证金模式计算总名义价值
        if ($marginMode == MarginMode::CROSS->value) {
            // 全仓模式：获取用户所有持仓的总名义价值
            $totalNotionalValue = $this->getTotalNotionalValueForCrossMargin($userId, $currencyId);
        } else {
            // 逐仓模式：只使用当前仓位的名义价值
            $totalNotionalValue = $currentNotionalValue;
        }

        // 查找适用的风险档位
        $marginLevel = \App\Model\Trade\TradeMarginLevel::query()
            ->where('currency_id', $currencyId)
            ->where('margin_min', '<=', $totalNotionalValue)
            ->where('margin_max', '>=', $totalNotionalValue)
            ->orderBy('level')
            ->first();

        if (!$marginLevel) {
            // 如果没有找到对应档位，使用杠杆倍数匹配档位
            $marginLevel = \App\Model\Trade\TradeMarginLevel::query()
                ->where('currency_id', $currencyId)
                ->where('leverage_min', '<=', $leverage)
                ->where('leverage_max', '>=', $leverage)
                ->orderBy('level')
                ->first();
        }

        if (!$marginLevel) {
            // 如果还是没有找到，查找最大档位
            $marginLevel = \App\Model\Trade\TradeMarginLevel::query()
                ->where('currency_id', $currencyId)
                ->orderBy('margin_max', 'desc')
                ->first();
        }

        if (!$marginLevel) {
            // 如果完全没有配置，使用默认维持保证金率 0.5%
            return 0.005;
        }

        return (float)$marginLevel->margin_rate;
    }

    /**
     * 获取全仓模式下用户所有持仓的总名义价值
     */
    protected function getTotalNotionalValueForCrossMargin(int $userId, int $currencyId): float
    {
        $positions = TradePerpetualPosition::query()
            ->where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_mode', 1) // 全仓模式
            ->where('status', PositionStatus::HOLDING->value)
            ->get();

        $totalValue = 0;
        foreach ($positions as $position) {
            $positionValue = bcmul((string)$position->quantity, (string)$position->entry_price, 8);
            $totalValue = bcadd((string)$totalValue, $positionValue, 8);
        }

        return (float)$totalValue;
    }
}
