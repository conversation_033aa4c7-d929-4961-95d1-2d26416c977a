<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual\Traits;

trait PerpetualLockTrait
{
    /**
     * 使用Redis分布式锁执行操作
     */
    protected function executeWithLock(string $lockKey, callable $callback): void
    {
        $lockValue = uniqid() . '-' . getmypid();
        $maxRetryTime = 5; // 最大重试5秒
        $retryInterval = 0.1; // 每次重试间隔100ms
        $startTime = microtime(true);
        
        while (true) {
            $acquired = $this->redis->set($lockKey, $lockValue, ['NX', 'EX' => 30]);
            
            if ($acquired) {
                // 成功获取锁
                break;
            }
            
            // 检查是否超过最大重试时间
            if (microtime(true) - $startTime >= $maxRetryTime) {
                throw new \RuntimeException("获取锁超时: {$lockKey}，已重试{$maxRetryTime}秒");
            }
            
            // 等待后重试
            usleep((int)($retryInterval * 1000000)); // 转换为微秒
        }

        try {
            $callback();
        } finally {
            $this->releaseLock($lockKey, $lockValue);
        }
    }

    /**
     * 使用Redis分布式锁执行操作（支持多个锁）
     */
    protected function executeWithMultipleLocks(array $lockKeys, callable $callback): void
    {
        $lockValues = [];
        $lockedKeys = [];
        $maxRetryTime = 5; // 最大重试5秒
        $retryInterval = 0.1; // 每次重试间隔100ms

        try {
            // 获取所有锁
            foreach ($lockKeys as $lockKey) {
                $lockValue = uniqid() . '-' . getmypid();
                $startTime = microtime(true);
                
                while (true) {
                    $acquired = $this->redis->set($lockKey, $lockValue, ['NX', 'EX' => 30]);
                    
                    if ($acquired) {
                        // 成功获取锁
                        break;
                    }
                    
                    // 检查是否超过最大重试时间
                    if (microtime(true) - $startTime >= $maxRetryTime) {
                        throw new \RuntimeException("获取锁超时: {$lockKey}，已重试{$maxRetryTime}秒");
                    }
                    
                    // 等待后重试
                    usleep((int)($retryInterval * 1000000)); // 转换为微秒
                }

                $lockValues[$lockKey] = $lockValue;
                $lockedKeys[] = $lockKey;
            }

            // 执行业务逻辑
            $callback();

        } finally {
            // 释放所有锁
            foreach ($lockedKeys as $lockKey) {
                $this->releaseLock($lockKey, $lockValues[$lockKey] ?? '');
            }
        }
    }

    /**
     * 释放Redis分布式锁
     */
    protected function releaseLock(string $lockKey, string $lockValue): void
    {
        $script = "
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
        ";

        $this->redis->eval($script, [$lockKey, $lockValue], 1);
    }

    /**
     * 生成订单锁键
     */
    protected function getOrderLockKey(int $orderId): string
    {
        return "perpetual_order_lock:{$orderId}";
    }

    /**
     * 生成用户仓位锁键
     */
    protected function getUserPositionLockKey(int $userId, int $currencyId, int $marginMode): string
    {
        return "perpetual_position_lock:{$userId}:{$currencyId}:{$marginMode}";
    }

    /**
     * 生成成交锁键
     */
    protected function getTradeLockKey(string $tradeId): string
    {
        return "perpetual_trade_lock:{$tradeId}";
    }
}
