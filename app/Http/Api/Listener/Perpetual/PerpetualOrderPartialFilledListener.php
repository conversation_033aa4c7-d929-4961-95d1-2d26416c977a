<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\Perpetual;

use App\Http\Api\Event\Perpetual\OrderPartialFilledEvent;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualPositionTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualMarginTrait;
use App\Http\Api\Listener\Perpetual\Traits\PerpetualLockTrait;
use App\Model\Trade\TradePerpetualOrder;
use App\Model\Match\MatchOrder;
use App\Model\Enums\Trade\Perpetual\ContractOrderType;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

#[Listener]
class PerpetualOrderPartialFilledListener implements ListenerInterface
{
    use PerpetualPositionTrait, PerpetualMarginTrait, PerpetualLockTrait;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('perpetual-trade', 'perpetual-logs');
    }

    public function listen(): array
    {
        return [
            OrderPartialFilledEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderPartialFilledEvent) {
            return;
        }

        $orderId = $event->order_id;

        $this->logger->info('收到永续合约订单部分成交事件', ['order_id' => $orderId]);

        try {
            $lockKey = $this->getOrderLockKey($orderId);
            $this->executeWithLock($lockKey, function () use ($orderId) {
                Db::transaction(function () use ($orderId) {
                    $this->processPerpetualOrderPartialFilled($orderId);
                });
            });

            $this->logger->info('永续合约订单部分成交处理完成', ['order_id' => $orderId]);

        } catch (\Exception $e) {
            $this->logger->error('永续合约订单部分成交处理失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理永续合约订单部分成交
     */
    protected function processPerpetualOrderPartialFilled(int $orderId): void
    {
        // 查找撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('撮合引擎订单不存在，可能是机器人订单', [
                'order_id' => $orderId
            ]);
            return;
        }

        // 查找永续合约订单
        $perpetualOrder = TradePerpetualOrder::query()
            ->where('match_order_id', $matchOrder->id)
            ->first();

        if (!$perpetualOrder) {
            $this->logger->info('永续合约订单不存在，可能是现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId
            ]);
            return;
        }

        // 处理部分成交状态
        $this->processPartialFilledStatus($perpetualOrder, $matchOrder);
    }

    /**
     * 处理部分成交状态
     */
    protected function processPartialFilledStatus(TradePerpetualOrder $order, MatchOrder $matchOrder): void
    {
        // 对于市价单，部分成交时可能需要调整保证金使用情况
        // 这里主要是记录和监控，具体的仓位更新在OrderTradeEvent中处理
        
        $filledRatio = bccomp((string)$matchOrder->quantity, '0', 18) > 0 
            ? bcdiv((string)$matchOrder->fill_quantity, (string)$matchOrder->quantity, 18)
            : '0';

        $this->logger->info('永续合约订单部分成交状态更新', [
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'total_quantity' => $matchOrder->quantity,
            'filled_quantity' => $matchOrder->fill_quantity,
            'filled_ratio' => (float)$filledRatio,
            'avg_price' => $matchOrder->avg_price,
            'frozen_amount' => $order->frozen_amount,
            'used_amount' => $order->used_amount
        ]);

        // 对于开仓的市价单，如果部分成交后价格变化较大，可能需要调整保证金预估
        if ($this->isOpenOperation($order->side) && $order->order_type == ContractOrderType::MARKET->value) {
            $this->adjustMarketOrderMargin($order, $matchOrder, (float)$filledRatio);
        }
    }

    /**
     * 调整市价单保证金（如果需要）
     */
    protected function adjustMarketOrderMargin(TradePerpetualOrder $order, MatchOrder $matchOrder, float $filledRatio): void
    {
        // 如果平均成交价格与预期差异较大，可能需要调整剩余部分的保证金预估
        // 这里主要是监控和记录，实际的保证金调整在具体成交时处理
        
        if ($matchOrder->avg_price > 0) {
            $avgPrice = $matchOrder->avg_price;
            $remainingQuantity = bcsub((string)$matchOrder->quantity, (string)$matchOrder->fill_quantity, 18);
            
            if (bccomp($remainingQuantity, '0', 18) > 0) {
                // 计算剩余部分按当前均价需要的保证金
                $estimatedRemainingMargin = $this->calculateUsedMargin(
                    (float)$remainingQuantity,
                    $avgPrice,
                    $order->leverage
                );
                
                $this->logger->info('市价单部分成交保证金预估', [
                    'order_id' => $order->id,
                    'avg_price' => $avgPrice,
                    'remaining_quantity' => (float)$remainingQuantity,
                    'estimated_remaining_margin' => $estimatedRemainingMargin,
                    'current_frozen_amount' => $order->frozen_amount,
                    'current_used_amount' => $order->used_amount
                ]);
            }
        }
    }
}
