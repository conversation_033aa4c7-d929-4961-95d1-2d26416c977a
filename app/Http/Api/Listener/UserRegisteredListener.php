<?php

declare(strict_types=1);

namespace App\Http\Api\Listener;

use Hyperf\Event\Annotation\Listener;
use App\Http\Api\Event\UserRegisteredEvent;
use App\Model\User\Enums\PasswordType;
use App\Model\User\UserPasswordHistory;
use Hyperf\Event\Contract\ListenerInterface;

#[Listener]
class UserRegisteredListener implements ListenerInterface
{
    public function listen(): array
    {
        // 返回一个该监听器要监听的事件数组，可以同时监听多个事件
        return [
            UserRegisteredEvent::class,
        ];
    }

    /**
     * @param UserRegisteredEvent $event
     */
    public function process(object $event): void
    {
        $user = $event->user;
        $request = $event->request;

        // 记录历史密码
        UserPasswordHistory::query()->create([
            UserPasswordHistory::FIELD_USER_ID => $user->id,
            UserPasswordHistory::FIELD_PASSWORD_TYPE => PasswordType::LOGIN,
            UserPasswordHistory::FIELD_PASSWORD_HASH => $user->password,
            UserPasswordHistory::FIELD_CHANGE_IP => $event->getClientIp(),
        ]);
    }
}
