<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 委托订单取消监听器
 */

namespace App\Http\Api\Listener\CommissionOrder;

use App\Http\Api\Event\CommissionOrder\CommissionOrderCancelledEvent;
use Carbon\Carbon;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Hyperf\WebSocketServer\Sender;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class CommissionOrderCancelledListener implements ListenerInterface
{
    #[Inject]
    protected ContainerInterface $container;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected Sender $sender;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('commission_order');
    }

    public function listen(): array
    {
        return [
            CommissionOrderCancelledEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof CommissionOrderCancelledEvent) {
            return;
        }

        try {
            $commission = $event->commission;
            $cancelReason = $event->cancelReason;

            // 记录日志
            $this->logger->info('委托订单取消成功', [
                'commission_id' => $commission->id,
                'user_id' => $commission->user_id,
                'currency_id' => $commission->currency_id,
                'cancel_reason' => $cancelReason,
            ]);

            // 推送消息到用户
            $this->pushMessageToUser($commission, $cancelReason);

        } catch (\Exception $e) {
            $this->logger->error('处理委托订单取消事件时发生错误', [
                'commission_id' => $event->commission->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 推送消息到用户
     */
    protected function pushMessageToUser(
        \App\Model\Trade\TradeSpotCommission $commission,
        string $cancelReason
    ): void {
        try {
            // 构造消息数据
            $messageData = [
                'type' => 'commission_cancelled',
                'commission_id' => $commission->id,
                'currency_id' => $commission->currency_id,
                'side' => $commission->side,
                'order_type' => $commission->order_type,
                'trigger_price' => $commission->trigger_price,
                'amount' => $commission->amount,
                'cancel_reason' => $cancelReason,
                'cancelled_at' => Carbon::now()->toDateTimeString(),
                'message' => '委托订单已取消'
            ];

            // 直接推送消息，避免循环依赖
            $this->pushDirectToUser(
                $commission->user_id,
                json_encode($messageData, JSON_UNESCAPED_UNICODE)
            );

        } catch (\Exception $e) {
            $this->logger->error('推送委托订单取消消息时发生错误', [
                'commission_id' => $commission->id,
                'user_id' => $commission->user_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 直接推送消息给用户，避免循环依赖
     */
    protected function pushDirectToUser(int $userId, string $message): bool
    {
        try {
            // 获取用户连接fd
            $userFdKey = "ws:user:{$userId}:fd";
            $fd = $this->redis->get($userFdKey);
            
            if ($fd === false || $fd === null) {
                $this->logger->warning('用户未连接WebSocket', ['user_id' => $userId]);
                return false;
            }
            
            // 直接推送消息
            go(function () use ($fd, $message) {
                try {
                    $this->sender->push((int)$fd, $message, WEBSOCKET_OPCODE_BINARY);
                } catch (\Throwable $e) {
                    // 推送失败，清理连接
                    $this->cleanupUserConnection((int)$fd);
                }
            });
            
            $this->logger->info('委托订单取消消息推送成功', ['user_id' => $userId]);
            return true;
            
        } catch (\Exception $e) {
            $this->logger->error('直接推送消息失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 清理用户连接
     */
    protected function cleanupUserConnection(int $fd): void
    {
        try {
            // 获取用户ID
            $userKey = "ws:fd:{$fd}:user";
            $userId = $this->redis->get($userKey);
            
            if ($userId !== false && $userId !== null) {
                // 清理用户连接映射
                $userFdKey = "ws:user:{$userId}:fd";
                $this->redis->del($userFdKey);
            }
            
            // 清理fd到用户的映射
            $this->redis->del($userKey);
            
        } catch (\Exception $e) {
            $this->logger->error('清理用户连接失败', [
                'fd' => $fd,
                'error' => $e->getMessage()
            ]);
        }
    }
} 