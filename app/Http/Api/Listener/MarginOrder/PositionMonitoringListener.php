<?php

declare(strict_types=1);

namespace App\Http\Api\Listener\MarginOrder;

use App\Http\Api\Event\Margin\PositionChangedEvent;
use App\Http\Api\Event\Margin\BorrowingChangedEvent;
use App\Http\Api\Service\V1\Margin\MarginPositionCacheService;
use App\Model\Trade\TradeMarginPosition;
use App\Model\User\UserMarginBorrow;
use App\Model\Enums\Trade\Margin\MarginPositionStatus;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\Trade\Margin\MarginBorrowStatus;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Di\Annotation\Inject;
use Psr\Log\LoggerInterface;

/**
 * 仓位监控监听器
 * 负责根据仓位和借贷变动事件，动态调整风险监控缓存
 */
#[Listener]
class PositionMonitoringListener implements ListenerInterface
{
    #[Inject]
    protected MarginPositionCacheService $positionCacheService;

    #[Inject]
    protected LoggerInterface $logger;

    public function listen(): array
    {
        return [
            PositionChangedEvent::class,
            BorrowingChangedEvent::class,
        ];
    }

    public function process(object $event): void
    {
        try {
            if ($event instanceof PositionChangedEvent) {
                $this->handlePositionChanged($event);
            } elseif ($event instanceof BorrowingChangedEvent) {
                $this->handleBorrowingChanged($event);
            }
        } catch (\Exception $e) {
            $this->logger->error('处理仓位监控事件异常', [
                'event_type' => get_class($event),
                'event_data' => method_exists($event, 'toArray') ? $event->toArray() : [],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理仓位变动事件
     */
    protected function handlePositionChanged(PositionChangedEvent $event): void
    {
        $position = $event->position;
        $changeType = $event->changeType;

        $this->logger->debug('处理仓位变动事件', [
            'position_id' => $position->getId(),
            'change_type' => $changeType,
            'user_id' => $position->getUserId(),
            'currency_id' => $position->getCurrencyId(),
            'description' => $event->getDescription()
        ]);

        switch ($changeType) {
            case 'created':
            case 'increased':
            case 'reversed':
                $this->evaluatePositionForMonitoring($position, $changeType);
                break;
                
            case 'decreased':
                // 减仓时重新评估是否还需要监控
                $this->reevaluatePositionMonitoring($position, $changeType);
                break;
                
            case 'closed':
                // 平仓时从监控中移除
                $this->removePositionFromMonitoring($position, $changeType);
                break;
        }
    }

    /**
     * 处理借贷变动事件
     */
    protected function handleBorrowingChanged(BorrowingChangedEvent $event): void
    {
        $this->logger->debug('处理借贷变动事件', [
            'user_id' => $event->userId,
            'currency_id' => $event->currencyId,
            'change_type' => $event->changeType,
            'amount' => $event->amount,
            'description' => $event->getDescription()
        ]);

        // 查找相关的仓位
        $positions = $this->findRelatedPositions($event->userId, $event->currencyId, $event->marginType);

        foreach ($positions as $position) {
            switch ($event->changeType) {
                case 'created':
                case 'increased':
                    // 新增或增加借贷，将相关仓位加入监控
                    $this->addPositionToMonitoring($position, "borrowing_{$event->changeType}");
                    break;
                    
                case 'repaid':
                case 'decreased':
                    // 还款或减少借贷，重新评估是否还需要监控
                    $this->reevaluatePositionMonitoring($position, "borrowing_{$event->changeType}");
                    break;
            }
        }
    }

    /**
     * 评估仓位是否需要加入监控
     */
    protected function evaluatePositionForMonitoring(TradeMarginPosition $position, string $reason): void
    {
        // 检查是否有借贷
        $hasBorrowing = $this->checkIfHasBorrowing(
            $position->getUserId(),
            $position->getCurrencyId(),
            $position->getMarginType()
        );

        if ($hasBorrowing) {
            $this->addPositionToMonitoring($position, $reason);
        } else {
            // 确保不在监控中（可能之前有借贷，现在还清了）
            $this->removePositionFromMonitoring($position, "no_borrowing_{$reason}");
        }
    }

    /**
     * 重新评估仓位监控需求
     */
    protected function reevaluatePositionMonitoring(TradeMarginPosition $position, string $reason): void
    {
        $this->evaluatePositionForMonitoring($position, "reevaluate_{$reason}");
    }

    /**
     * 将仓位加入监控
     */
    protected function addPositionToMonitoring(TradeMarginPosition $position, string $reason): void
    {
        $isInCache = $this->positionCacheService->isPositionInCache($position);

        if (!$isInCache) {
            $this->positionCacheService->syncPositionToCache($position);

            $this->logger->info('仓位加入风险监控', [
                'position_id' => $position->getId(),
                'user_id' => $position->getUserId(),
                'currency_id' => $position->getCurrencyId(),
                'reason' => $reason,
                'leverage' => $position->getLeverage(),
                'margin_type' => $position->getMarginType()->value
            ]);
        } else {
            // 已在监控中，更新缓存数据
            $this->positionCacheService->syncPositionToCache($position);

            $this->logger->debug('更新监控中的仓位数据', [
                'position_id' => $position->getId(),
                'reason' => $reason
            ]);
        }
    }

    /**
     * 从监控中移除仓位
     */
    protected function removePositionFromMonitoring(TradeMarginPosition $position, string $reason): void
    {
        $isInCache = $this->positionCacheService->isPositionInCache($position);
        
        if ($isInCache) {
            $this->positionCacheService->removePositionFromCache($position);
            
            $this->logger->info('仓位移出风险监控', [
                'position_id' => $position->getId(),
                'user_id' => $position->getUserId(),
                'currency_id' => $position->getCurrencyId(),
                'reason' => $reason
            ]);
        }
    }

    /**
     * 检查是否有借贷
     */
    protected function checkIfHasBorrowing(int $userId, int $currencyId, MarginType $marginType): bool
    {
        // 将MarginType转换为对应的account_type
        $accountType = $this->getAccountTypeFromMarginType($marginType);

        return UserMarginBorrow::where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('account_type', $accountType)
            ->where('status', MarginBorrowStatus::ACTIVE) // 只查询借贷中的记录
            ->where('borrow_amount', '>', 0)
            ->exists();
    }

    /**
     * 将MarginType转换为对应的account_type
     */
    protected function getAccountTypeFromMarginType(MarginType $marginType): int
    {
        return match ($marginType) {
            MarginType::CROSS => 2,    // 全仓杠杆
            MarginType::ISOLATED => 3, // 逐仓杠杆
        };
    }

    /**
     * 查找相关仓位
     */
    protected function findRelatedPositions(int $userId, int $currencyId, int $marginType): array
    {
        // 将account_type转换为MarginType枚举值
        $marginTypeEnum = $this->getMarginTypeFromAccountType($marginType);

        return TradeMarginPosition::where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('margin_type', $marginTypeEnum)
            ->where('status', MarginPositionStatus::HOLDING)
            ->get()
            ->toArray();
    }

    /**
     * 将account_type转换为MarginType枚举值
     */
    protected function getMarginTypeFromAccountType(int $accountType): int
    {
        return match ($accountType) {
            2 => MarginType::CROSS->value,    // 全仓杠杆
            3 => MarginType::ISOLATED->value, // 逐仓杠杆
            default => MarginType::ISOLATED->value, // 默认逐仓
        };
    }
}
