<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆订单成交事件监听器
 */

namespace App\Http\Api\Listener\MarginOrder;

use App\Http\Api\Event\Margin\OrderTradeEvent;
use App\Http\Api\Service\V1\Margin\TradeMarginService;
use App\Model\Trade\TradeMarginOrder;
use App\Model\Match\MatchOrder;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class MarginOrderTradeListener implements ListenerInterface
{
    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('margin-trade');
    }

    public function listen(): array
    {
        return [
            OrderTradeEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderTradeEvent) {
            return;
        }

        $tradeData = $event->order;

        $this->logger->info('收到杠杆订单成交事件', $tradeData);

        try {
            // 获取需要锁定的订单ID
            $lockKeys = [];
            if (isset($tradeData['buy_order_id'])) {
                $lockKeys[] = "margin_order_lock:{$tradeData['buy_order_id']}";
            }
            if (isset($tradeData['sell_order_id'])) {
                $lockKeys[] = "margin_order_lock:{$tradeData['sell_order_id']}";
            }

            // 使用Redis分布式锁
            $this->executeWithLock($lockKeys, function () use ($tradeData) {
                Db::transaction(function () use ($tradeData) {
                    // 处理买方订单成交
                    if (isset($tradeData['buy_order_id'])) {
                        $this->processMarginTradeForOrder($tradeData, $tradeData['buy_order_id']);
                    }

                    // 处理卖方订单成交
                    if (isset($tradeData['sell_order_id'])) {
                        $this->processMarginTradeForOrder($tradeData, $tradeData['sell_order_id']);
                    }
                });
            });

            $this->logger->info('杠杆订单成交处理完成', [
                'trade_id' => $tradeData['trade_id'],
                'buy_order_id' => $tradeData['buy_order_id'] ?? null,
                'sell_order_id' => $tradeData['sell_order_id'] ?? null,
                'price' => $tradeData['price'],
                'quantity' => $tradeData['quantity']
            ]);

        } catch (\Exception $e) {
            $this->logger->error('杠杆订单成交处理失败', [
                'trade_id' => $tradeData['trade_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理杠杆订单成交
     */
    protected function processMarginTradeForOrder(array $tradeData, int $orderId): void
    {
        // 查找撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('撮合引擎订单不存在，可能是机器人订单', [
                'order_id' => $orderId
            ]);
            return;
        }

        // 查找杠杆订单
        $marginOrder = TradeMarginOrder::query()
            ->where('match_order_id', $matchOrder->id)
            ->first();

        if (!$marginOrder) {
            $this->logger->info('杠杆订单不存在，可能是现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId
            ]);
            return;
        }

        // 提取成交数据
        $filledQuantity = (float)($tradeData['quantity'] ?? 0);
        $filledPrice = (float)($tradeData['price'] ?? 0);

        if ($filledQuantity <= 0 || $filledPrice <= 0) {
            $this->logger->warning('成交数据无效', [
                'order_id' => $orderId,
                'filled_quantity' => $filledQuantity,
                'filled_price' => $filledPrice
            ]);
            return;
        }

        // 调用杠杆服务处理成交
        ApplicationContext::getContainer()->get(TradeMarginService::class)->processOrderFilled(
            $matchOrder->id,
            $filledQuantity,
            $filledPrice
        );

        $this->logger->info('杠杆订单成交处理完成', [
            'margin_order_id' => $marginOrder->getId(),
            'match_order_id' => $matchOrder->id,
            'user_id' => $marginOrder->getUserId(),
            'filled_quantity' => $filledQuantity,
            'filled_price' => $filledPrice
        ]);
    }

    /**
     * 使用Redis分布式锁执行操作
     */
    protected function executeWithLock(array $lockKeys, callable $callback): void
    {
        $lockValues = [];
        $lockedKeys = [];
        $maxRetryTime = 5; // 最大重试5秒
        $retryInterval = 0.1; // 每次重试间隔100ms

        try {
            // 获取所有锁
            foreach ($lockKeys as $lockKey) {
                $lockValue = uniqid() . '-' . getmypid();
                $startTime = microtime(true);
                
                while (true) {
                    $acquired = $this->redis->set($lockKey, $lockValue, ['NX', 'EX' => 30]);
                    
                    if ($acquired) {
                        // 成功获取锁
                        break;
                    }
                    
                    // 检查是否超过最大重试时间
                    if (microtime(true) - $startTime >= $maxRetryTime) {
                        throw new \RuntimeException("获取锁超时: {$lockKey}，已重试{$maxRetryTime}秒");
                    }
                    
                    // 等待后重试
                    usleep((int)($retryInterval * 1000000)); // 转换为微秒
                }

                $lockValues[$lockKey] = $lockValue;
                $lockedKeys[] = $lockKey;
            }

            // 执行业务逻辑
            $callback();

        } finally {
            // 释放所有锁
            foreach ($lockedKeys as $lockKey) {
                $this->releaseLock($lockKey, $lockValues[$lockKey] ?? '');
            }
        }
    }

    /**
     * 释放Redis分布式锁
     */
    protected function releaseLock(string $lockKey, string $lockValue): void
    {
        $script = "
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
        ";

        $this->redis->eval($script, [$lockKey, $lockValue], 1);
    }
} 