<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆订单完全成交事件监听器
 */

namespace App\Http\Api\Listener\MarginOrder;

use App\Http\Api\Event\Margin\OrderFilledEvent;
use App\Model\Trade\TradeMarginOrder;
use App\Model\Match\MatchOrder;
use App\Model\Enums\User\AccountType;
use App\Model\Enums\User\FlowsType;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Service\UserAccounts\UserAccountsAssetService;
use Hyperf\Context\ApplicationContext;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class MarginOrderFilledListener implements ListenerInterface
{
    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('margin-trade');
    }

    public function listen(): array
    {
        return [
            OrderFilledEvent::class,
        ];
    }

    public function process(object $event): void
    {
        if (!$event instanceof OrderFilledEvent) {
            return;
        }

        $orderId = $event->order_id;

        $this->logger->info('收到杠杆订单完全成交事件', ['order_id' => $orderId]);

        try {
            $lockKey = "margin_filled_lock:{$orderId}";
            $this->executeWithLock($lockKey, function () use ($orderId) {
                Db::transaction(function () use ($orderId) {
                    $this->processMarginOrderFilled($orderId);
                });
            });

            $this->logger->info('杠杆订单完全成交处理完成', ['order_id' => $orderId]);

        } catch (\Exception $e) {
            $this->logger->error('杠杆订单完全成交处理失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 处理杠杆订单完全成交
     */
    protected function processMarginOrderFilled(int $orderId): void
    {
        // 查找撮合引擎订单
        $matchOrder = MatchOrder::query()
            ->where('order_id', $orderId)
            ->first();

        if (!$matchOrder) {
            $this->logger->info('撮合引擎订单不存在，可能是机器人订单', [
                'order_id' => $orderId
            ]);
            return;
        }

        // 查找杠杆订单
        $marginOrder = TradeMarginOrder::query()
            ->where('match_order_id', $matchOrder->id)
            ->first();

        if (!$marginOrder) {
            $this->logger->info('杠杆订单不存在，可能是现货订单', [
                'match_order_id' => $matchOrder->id,
                'order_id' => $orderId
            ]);
            return;
        }

        // 返还剩余冻结资金
        $this->returnRemainingFrozenFunds($marginOrder, $matchOrder);
    }

    /**
     * 返还剩余冻结资金
     */
    protected function returnRemainingFrozenFunds(TradeMarginOrder $marginOrder, MatchOrder $matchOrder): void
    {
        $frozenAmount = $marginOrder->getFrozenAmount();
        $usedAmount = $marginOrder->getUsedAmount();
        
        // 计算剩余冻结资金
        $remainingAmount = bcsub((string)$frozenAmount, (string)$usedAmount, 8);
        
        if (bccomp($remainingAmount, '0', 8) <= 0) {
            $this->logger->info('杠杆订单无剩余冻结资金需要返还', [
                'margin_order_id' => $marginOrder->getId(),
                'frozen_amount' => $frozenAmount,
                'used_amount' => $usedAmount
            ]);
            return;
        }

        // 确定资金币种和账户类型
        $accountType = $marginOrder->getMarginType() === MarginType::CROSS 
            ? AccountType::MARGIN 
            : AccountType::ISOLATED;

        // 确定币种ID和字段
        if ($matchOrder->side === 1) { // 买单
            if ($marginOrder->getMarginType() === MarginType::CROSS) {
                // 全仓买单：返还独立的计价币资产记录
                $currencyId = $this->getQuoteCurrencyId($marginOrder->getCurrencyId());
                $frozenField = 'frozen';
                $targetField = 'available';
            } else {
                // 逐仓买单：从基础币记录的locked字段返还到margin_quote字段
                $currencyId = $marginOrder->getCurrencyId();
                $frozenField = 'locked';
                $targetField = 'margin_quote';
            }
        } else { // 卖单
            // 卖单返还基础币（全仓和逐仓都一样）
            $currencyId = $marginOrder->getCurrencyId();
            $frozenField = 'frozen';
            $targetField = 'available';
        }
        // 返还剩余冻结资金到可用余额
        $result = ApplicationContext::getContainer()->get(UserAccountsAssetService::class)->unfreezeAsset(
            $marginOrder->getUserId(),
            $accountType->value,
            $currencyId,
            (float)$remainingAmount,
            FlowsType::MARGIN_TRADE->value,
            0,
            $frozenField,
            $targetField
        );

        if (!$result) {
            throw new \RuntimeException('返还剩余冻结资金失败');
        }

        $this->logger->info('杠杆订单完全成交，返还剩余冻结资金成功', [
            'user_id' => $marginOrder->getUserId(),
            'margin_order_id' => $marginOrder->getId(),
            'currency_id' => $currencyId,
            'frozen_amount' => $frozenAmount,
            'used_amount' => $usedAmount,
            'returned_amount' => (float)$remainingAmount,
            'account_type' => $accountType->value,
            'frozen_field' => $frozenField,
            'target_field' => $targetField
        ]);
    }

    /**
     * 获取计价币种ID
     */
    protected function getQuoteCurrencyId(int $currencyId): int
    {
        $currencyKey = \App\Enum\CurrencyConfigKey::getCurrencyKey($currencyId);
        $quoteAssetsId = $this->redis->hGet($currencyKey, 'quote_assets_id');
        
        if ($quoteAssetsId === false || $quoteAssetsId === null) {
            throw new \RuntimeException("无法获取币种 {$currencyId} 的计价币种ID");
        }
        
        return (int)$quoteAssetsId;
    }

    /**
     * 使用Redis分布式锁执行操作
     */
    protected function executeWithLock(string $lockKey, callable $callback): void
    {
        $lockValue = uniqid() . '-' . getmypid();
        $maxRetryTime = 5; // 最大重试5秒
        $retryInterval = 0.1; // 每次重试间隔100ms
        $startTime = microtime(true);
        
        while (true) {
            $acquired = $this->redis->set($lockKey, $lockValue, ['NX', 'EX' => 30]);
            
            if ($acquired) {
                // 成功获取锁
                break;
            }
            
            // 检查是否超过最大重试时间
            if (microtime(true) - $startTime >= $maxRetryTime) {
                throw new \RuntimeException("获取锁超时: {$lockKey}，已重试{$maxRetryTime}秒");
            }
            
            // 等待后重试
            usleep((int)($retryInterval * 1000000)); // 转换为微秒
        }

        try {
            $callback();
        } finally {
            $this->releaseLock($lockKey, $lockValue);
        }
    }

    /**
     * 释放Redis分布式锁
     */
    protected function releaseLock(string $lockKey, string $lockValue): void
    {
        $script = "
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
        ";

        $this->redis->eval($script, [$lockKey, $lockValue], 1);
    }
} 