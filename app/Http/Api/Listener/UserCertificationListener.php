<?php

/**
 * UserCertificationListener.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/4
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Http\Api\Listener;

use App\Enum\AsyncExecutorKey;
use App\Http\Api\Event\UserCertificationEvent;
use App\Job\AsyncFunExecutorJob;
use App\Model\Currency\Currency;
use App\Model\Enums\User\AccountType;
use App\Model\User\User;
use App\Model\User\UserAccountsAsset;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;

#[Listener]
class UserCertificationListener implements ListenerInterface
{
    public function listen():array
    {
        return [
            UserCertificationEvent::class
        ];
    }

    /**
     * 事件处理初始化账户资产相关数据
     * @param object $event
     * @return void
     */
    public function process(object $event):void
    {
        try {
            $user = User::query()->where('id',$event->user_id)->first();
            if(!$user){
                return;
            }

            $currency_ids = Currency::query()->where('status',1)->get('id');
            foreach ($currency_ids as $currency_id){
                foreach (AccountType::cases() as $case){
                    UserAccountsAsset::query()->updateOrCreate([
                        'user_id' => $user->id,
                        'account_type' => $case->value,
                        'currency_id' => $currency_id->id,
                    ],[
                        UserAccountsAsset::FIELD_USER_ID => $user->id,
                        UserAccountsAsset::FIELD_ACCOUNT_TYPE => $case->value,
                        UserAccountsAsset::FIELD_CURRENCY_ID => $currency_id->id,
                        UserAccountsAsset::FIELD_AVAILABLE => 0,
                        UserAccountsAsset::FIELD_FROZEN => 0,
                        UserAccountsAsset::FIELD_LOCKED => 0,
                        UserAccountsAsset::FIELD_STATUS => 1,
                    ])->save();
                }
            }
        }catch (\Throwable){
            pushAsyncJob(AsyncExecutorKey::ASYNC_EXECUTOR_QUEUE->value,new AsyncFunExecutorJob(
                'App\Http\Api\Listener\UserCertificationListener',
                'process',
                [$event]
            ));
        }
    }
}