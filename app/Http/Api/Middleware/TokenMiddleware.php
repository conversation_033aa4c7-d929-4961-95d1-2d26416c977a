<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq

 */

namespace App\Http\Api\Middleware;

use App\Exception\BusinessException;
use App\Http\Api\Service\User\UserService;
use Hyperf\Context\Context;
use App\Http\Common\ResultCode;
use App\Model\User\User;
use Mine\Jwt\JwtInterface;
use Mine\JwtAuth\Middleware\AbstractTokenMiddleware;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Hyperf\Redis\Redis;
use Hyperf\Di\Annotation\Inject;
use Lcobucci\JWT\Token\RegisteredClaims;

final class TokenMiddleware extends AbstractTokenMiddleware
{
    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected UserService $userService;

    public function getJwt(): JwtInterface
    {
        return $this->jwtFactory->get('api');
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        // 1. 解析并验证token
        try {
            /** @var \Lcobucci\JWT\UnencryptedToken $token */
            $token = $this->parserToken($request);
        } catch (\Throwable $th) {
            throw new BusinessException(ResultCode::UNAUTHORIZED, '登录状态已失效，请重新登录');
        }

        // 2. 检查token有效性
        $expireAt = $token->claims()->get(RegisteredClaims::EXPIRATION_TIME)->getTimestamp();
        if ($expireAt < time()) {
            $this->getJwt()->addBlackList($token);
            throw new BusinessException(ResultCode::UNAUTHORIZED, '登录状态已过期，请重新登录');
        }

        $userId = (int) $token->claims()->get(RegisteredClaims::ID);

        // 检查 Redis 中的登录信息（当前设备缓存的 token 是否存在且匹配）
        $tokenCache = $this->userService->getTokenCache($userId, $request->getHeaderLine('x-device-info'));
        if (!$tokenCache || $tokenCache !== $token->toString()) {
            $this->getJwt()->addBlackList($token);
            throw new BusinessException(ResultCode::UNAUTHORIZED, '登录状态异常，请重新登录');
        }

        /** @var User $user */
        $user = $this->userService->getUserCache($userId);
        if (!$user) {
            $user = User::query()->find($userId);
            $this->userService->setUserCache($user);
        }
        $request = $request
            ->withAttribute('user_id', $userId)
            ->withAttribute('user', $user)
            ->withAttribute('token', $token);

        Context::set('user_id', $userId);
        Context::set('user', $user);

        $httpServerRequest = container()->get(\Hyperf\HttpServer\Request::class);
        // 往 $request 中扩展一个方法 userId 用于获取当前登录用户ID
        // 在未继承 \App\Http\Api\Request\BaseFormRequest 的 $request 中，也可使用 $request->userId() 获取当前登录用户ID
        $httpServerRequest->macro('userId', function () use ($userId) {
            return $userId;
        });
        // 往 $request 中扩展一个方法 user 用于获取当前登录用户
        // 在未继承 \App\Http\Api\Request\BaseFormRequest 的 $request 中，也可使用 $request->user() 获取当前登录用户
        $httpServerRequest->macro('user', function () use ($user) {
            return $user;
        });

        // 确保请求对象被正确传递给下一个处理器
        return $handler->handle($request);
    }
}
