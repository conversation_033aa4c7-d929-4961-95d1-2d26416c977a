<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq

 */

namespace App\Http\Api\Middleware;

use Mine\Jwt\JwtInterface;

class RefreshTokenMiddleware extends \App\Http\Common\Middleware\RefreshTokenMiddleware
{
    public function getJwt(): JwtInterface
    {
        return $this->jwtFactory->get('api');
    }
}
