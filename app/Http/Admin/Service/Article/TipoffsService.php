<?php

declare(strict_types=1);

namespace App\Http\Admin\Service\Article;

use ApiElf\QueryBuilder\AllowedFilter;
use App\Model\Article\Tipoffs;
use App\QueryBuilder\QueryBuilder;
use App\Service\IService;
use App\Repository\Article\TipoffsRepository as Repository;
use Hyperf\HttpServer\Contract\RequestInterface;



class TipoffsService extends IService
{
    public function __construct(
        protected readonly Repository $repository
    ) {}

    public function list(RequestInterface $request): mixed
    {
        $language = $request->getHeaderLine('Accept-Language');
        return QueryBuilder::for(Tipoffs::class, $request)
            ->with(['user:id,display_name,avatar,email','tipOffsUser:id,display_name,avatar,email','dynamics'])
            ->filters([
            'title'
            ])
            ->defaultSort('id')
            ->allowedSorts(['id', 'created_at'])
            ->pagex();
    }
}
