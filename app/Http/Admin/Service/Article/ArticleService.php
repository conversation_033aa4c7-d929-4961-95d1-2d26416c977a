<?php

declare(strict_types=1);

namespace App\Http\Admin\Service\Article;

use ApiElf\QueryBuilder\AllowedFilter;
use App\Model\Article\Article;
use App\QueryBuilder\QueryBuilder;
use App\Service\IService;
use App\Repository\Article\ArticleRepository as Repository;
use App\Service\SystemConfigService;
use App\Service\TranslationService;
use Hyperf\DbConnection\Db;
use Hyperf\HttpServer\Contract\RequestInterface;



class ArticleService extends IService
{
    public function __construct(
        protected readonly Repository $repository
    ) {}

    public function page(array $params, int $page = 1, int $pageSize = 10): array
    {
        return $this->repository->page($params, $page, $pageSize);
    }

    public function list(RequestInterface $request): mixed
    {
        $language = $request->getHeaderLine('Accept-Language');
        return QueryBuilder::for(Article::class, $request)
            ->with(['category'])
            ->filters([
            AllowedFilter::callback('title', function ($query, $title) {
                $query->whereRaw(
                    "JSON_SEARCH(title, 'one', '%".$title."%', NULL, '$[*].text') IS NOT NULL",
                );
            }),
            'summary',
            'category_id'
            ])
            ->defaultSort('id')
            ->allowedSorts(['id', 'created_at'])
            ->pagex(function ($item) use ($request,$language) {
                // // 提取 title
                $titles = array_column($item->title, 'text', 'lang');
                $item->title = $titles[$language] ?? '';

                // 提取 remark
                $remarks = array_column($item->remark, 'text', 'lang');
                $item->remark = $remarks[$language] ?? '';

                // 提取 content
                $contents = array_column($item->content, 'text', 'lang');
                $item->content = $contents[$language] ?? '';

                // 提取 summary
                $summaries = array_column($item->summary, 'text', 'lang');
                $item->summary = $summaries[$language] ?? '';
                return $item;
            });
    }

    public function create(array $data): mixed
    {
        $language = $data['language']??'zh_CN';
        unset($data['language']);
        $lang = $data['lang']??['zh_CN'];
        $translations = new TranslationService();
        go(function () use ($data,$translations,$lang,$language) { 
            $fields = ['title', 'content', 'summary', 'remark'];
            $translationsData = [];

            collect($lang ?? [])->each(function ($langCode) use ($data, $translations, $language, &$translationsData, $fields) {
                if ($language === 'zh_CN') {
                    $language = 'zh';
                }

                // 提取所有需要翻译的字段内容
                $sourceTexts = array_map(fn($field) => $data[$field] ?? '', $fields);

                // 调用翻译接口
                $translatedTexts = $translations->batchTranslate($sourceTexts, $language, $langCode);

                // 构建每个字段对应的翻译结果
                foreach ($fields as $index => $field) {
                    $translationsData[$field][] = [
                        'lang' => $langCode,
                        'text' => $translatedTexts[$index],
                    ];
                }
            });

            // 将翻译结果赋值回 $data
            foreach ($fields as $field) {
                $data[$field] = $translationsData[$field] ?? [];
            }
            $this->repository->create($data);  
        });
        return true;
    }

    public function updateById(mixed $id, array $data): mixed
    {
        $info = $this->repository->findById($id);
        //处理title的值 通过语言处理
        $language = $data['language'];
        if($info){
            foreach (['title', 'content', 'summary', 'remark'] as $field) {
                $this->updateLocalizedField($info, $data, $field, $language);
            }
    
            // 将更新后的值赋回 data
            $data['title'] = $info->title;
            $data['content'] = $info->content;
            $data['summary'] = $info->summary;
            $data['remark'] = $info->remark;
        }
        return $this->repository->updateById($id, $data); 
    }

    private function updateLocalizedField(object $info, array $data, string $field, string $language): void
    {
        $values = $info->{$field};
        $has = false;
        foreach ($values as &$item) {
            if (isset($item['lang']) && $item['lang'] === $language) {
                $item['text'] = $data[$field] ?? '';
            }
        }
        if (!$has) {
            $values[] = [
                'lang' => $language,
                'text' => $data[$field] ?? '',
            ];
        }

        unset($item);
        $info->{$field} = $values;
    }


}
