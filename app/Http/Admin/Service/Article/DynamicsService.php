<?php

declare(strict_types=1);

namespace App\Http\Admin\Service\Article;

use App\Model\Article\Dynamics;
use App\QueryBuilder\QueryBuilder;
use App\Service\IService;
use App\Repository\Article\DynamicsRepository as Repository;
use Hyperf\HttpServer\Contract\RequestInterface;



class DynamicsService extends IService
{
    public function __construct(
        protected readonly Repository $repository
    ) {
       
    }

    public function list(RequestInterface $request): mixed
    {
        return QueryBuilder::for(Dynamics::class, $request)
            ->with(['user:id,display_name,avatar,email'])
            ->filters(['title','type'])
            ->defaultSort('id')
            ->allowedSorts(['id', 'created_at'])
            ->pagex();
    }
}
