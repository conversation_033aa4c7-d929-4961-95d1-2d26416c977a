<?php

declare(strict_types=1);

namespace App\Http\Admin\Service\Article;

use ApiElf\QueryBuilder\AllowedFilter;
use App\Model\Article\Notice;
use App\QueryBuilder\QueryBuilder;
use App\Service\IService;
use App\Repository\Article\NoticeRepository as Repository;
use App\Service\TranslationService;
use Hyperf\DbConnection\Db;
use Hyperf\HttpServer\Contract\RequestInterface;



class NoticeService extends IService
{
    public function __construct(
        protected readonly Repository $repository
    ) {}

    public function list(RequestInterface $request): mixed
    {
        $language = $request->getHeaderLine('Accept-Language');
        return QueryBuilder::for(Notice::class, $request)
            ->with(['category'])
            ->filters([
            AllowedFilter::callback('title', function ($query, $title) {
                $query->whereRaw(
                    "JSON_SEARCH(title, 'one', '%".$title."%', NULL, '$[*].text') IS NOT NULL",
                );
            }),
            'category_id'
            ])
            ->defaultSort('id')
            ->allowedSorts(['id', 'created_at'])
            ->pagex(function ($item) use ($request,$language) {
                // 提取 title
                $titles = array_column($item->title, 'text', 'lang');
                $item->title = $titles[$language] ?? '';

                // 提取 content
                $contents = array_column($item->content, 'text', 'lang');
                $item->content = $contents[$language] ?? '';

                return $item;
            });
    }

    public function create(array $data): mixed
    {
        $language = $data['language']??'zh';
        unset($data['language']);
        $lang = $data['lang']??['zh_CN'];
        $translations = new TranslationService();
        go(function () use ($data,$translations,$lang,$language) { 
            $fields = ['title', 'content'];
            $translationsData = [];

            // 统一处理 zh_CN => zh
            $sourceLanguage = $language === 'zh_CN' ? 'zh' : $language;

            collect($lang ?? [])->each(function ($targetLang) use ($data, $translations, $sourceLanguage, $fields, &$translationsData) {
                // 提取所有需要翻译的字段内容
                $texts = $translations->batchTranslate(
                    array_map(fn($field) => $data[$field] ?? '', $fields),
                    $sourceLanguage,
                    $targetLang
                );

                // 构建每个字段对应的翻译结果
                foreach ($fields as $index => $field) {
                    $translationsData[$field][] = [
                        'lang' => $targetLang,
                        'text' => $texts[$index],
                    ];
                }
            });

            // 将翻译结果赋值回 $data
            foreach ($fields as $field) {
                $data[$field] = $translationsData[$field] ?? [];
            }
            $this->repository->create($data);  
        });
        return true;
    }


    public function updateById(mixed $id, array $data): mixed
    {
        $info = $this->repository->findById($id);
        //处理title的值 通过语言处理
        $language = $data['language'];
        if($info){
            $has = false;
            $title = $info->title; // 先取出属性为变量
            foreach ($title as &$item) { // 使用引用修改
                if ($item['lang'] == $language) {
                    $item['text'] = $data['title'];
                    $has = true;
                }
            }
            if (!$has) {
                $title[] = [
                    'lang' => $language,
                    'text' => $data['title'],
                ];
            }
            unset($item); // 避免引用污染
            $info->title = $title; // 再赋值回去

            $content = $info->content;
            $has = false;
            foreach ($content as &$item) {
                if ($item['lang'] == $language) {
                    $item['text'] = $data['content'];
                    $has = true;
                }
            }
            if (!$has) {
                $content[] = [
                    'lang' => $language,
                    'text' => $data['content'],
                ];
            }
            unset($item);

            $info->content = $content;
            
            $data['title'] = $info->title;
            $data['content'] = $info->content;
        }
        return $this->repository->updateById($id, $data); 
    }
}
