<?php

declare(strict_types=1);

namespace App\Http\Admin\Service\Article;

use App\Model\Article\Category;
use App\QueryBuilder\QueryBuilder;
use App\Service\IService;
use App\Repository\Article\CategoryRepository as Repository;
use Hyperf\HttpServer\Contract\RequestInterface;



class CategoryService extends IService
{
    public function __construct(
        protected readonly Repository $repository
    ) {}

    public function list(RequestInterface $request): mixed
    {
        return QueryBuilder::for(Category::class, $request)
            ->filters(['name'])
            ->defaultSort('id')
            ->allowedSorts(['id', 'created_at'])
            ->pagex();
    }

}
