<?php

declare(strict_types=1);

namespace App\Http\Admin\Service\Article;

use App\Model\Article\HotTopic;
use App\QueryBuilder\QueryBuilder;
use App\Service\IService;
use App\Repository\Article\HotTopicRepository as Repository;
use Hyperf\HttpServer\Contract\RequestInterface;



class HotTopicService extends IService
{
    public function __construct(
        protected readonly Repository $repository
    ) {}

    public function list(RequestInterface $request): mixed
    {
        return QueryBuilder::for(HotTopic::class, $request)
            ->filters(['title'])
            ->defaultSort('id')
            ->allowedSorts(['id', 'created_at'])
            ->pagex();
    }
}
