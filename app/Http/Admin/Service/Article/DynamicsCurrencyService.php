<?php

declare(strict_types=1);

namespace App\Http\Admin\Service\Article;

use App\Model\Article\DynamicsCurrency;
use App\Model\Currency\Currency;
use App\QueryBuilder\QueryBuilder;
use App\Repository\Article\DynamicsCurrencyRepository;
use App\Service\IService;
use App\Repository\Article\DynamicsCurrencyRepository as Repository;
use Hyperf\HttpServer\Contract\RequestInterface;



class DynamicsCurrencyService extends IService
{
    public function __construct(
        protected readonly Repository $repository
    ) {}


    public function list(RequestInterface $request): mixed
    {
        return QueryBuilder::for(DynamicsCurrency::class, $request)
            ->with(['currency'])
            ->filters(['symbol_id'])
            ->defaultSort('id')
            ->allowedSorts(['id', 'created_at'])
            ->pagex();
    }

    public function updateById($id, array $data): bool
    {
        $data['symbol'] = Currency::where(['id'=>$data['symbol_id']])->value('symbol');
        return $this->repository->updateById($id, $data);   
    }

    public function create(array $data): mixed
    {
        $data['symbol'] = Currency::where(['id'=>$data['symbol_id']])->value('symbol');
        return $this->repository->create($data);
    }
}
