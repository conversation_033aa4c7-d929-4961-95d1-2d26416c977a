<?php

declare(strict_types=1);
namespace App\Http\Admin\Request\Article;

use Hyperf\Validation\Request\FormRequest;
use App\Http\Common\Request\Traits\ActionRulesTrait;


class DynamicsRequest extends FormRequest
{
    use ActionRulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    // 自动匹配create方法验证
    public function createRules(): array
    {
        return [
            'pid_id' => ['integer'],
            'member_id' => ['integer'],
            'content' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    // 自动匹配save方法验证
    public function saveRules(): array
    {
        return [
            'pid_id' => ['integer'],
            'member_id' => ['integer'],
            'content' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    public function attributes(): array
    {
        return [
            'pid_id' => trans('dynamics.pid_id') ?: '上级ID',
            'member_id' => trans('dynamics.member_id') ?: '会员ID',
            'content' => trans('dynamics.content') ?: '内容',
            'created_by' => trans('dynamics.created_by') ?: '创建者',
            'updated_by' => trans('dynamics.updated_by') ?: '更新者',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}