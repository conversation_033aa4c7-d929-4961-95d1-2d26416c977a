<?php

declare(strict_types=1);
namespace App\Http\Admin\Request\Article;

use Hyperf\Validation\Request\FormRequest;
use App\Http\Common\Request\Traits\ActionRulesTrait;


class ArticleRequest extends FormRequest
{
    use ActionRulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    // 自动匹配create方法验证
    public function createRules(): array
    {
        return [
            'category_id' => ['integer'],
            'title' => ['sometimes'],
            'content' => ['sometimes'],
            'summary' => ['sometimes'],
            'remark' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    // 自动匹配save方法验证
    public function saveRules(): array
    {
        return [
            'category_id' => ['integer'],
            'title' => ['sometimes'],
            'content' => ['sometimes'],
            'summary' => ['sometimes'],
            'remark' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    public function attributes(): array
    {
        return [
            'category_id' => trans('article.category_id') ?: '分类ID',
            'title' => trans('article.title') ?: '标题',
            'content' => trans('article.content') ?: '内容',
            'summary' => trans('article.summary') ?: '摘要',
            'remark' => trans('article.remark') ?: '备注',
            'created_by' => trans('article.created_by') ?: '创建者',
            'updated_by' => trans('article.updated_by') ?: '更新者',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}