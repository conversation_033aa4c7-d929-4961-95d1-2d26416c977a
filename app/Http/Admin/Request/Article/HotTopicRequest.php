<?php

declare(strict_types=1);
namespace App\Http\Admin\Request\Article;

use App\Http\Common\Request\Traits\ActionRulesTrait;
use Hyperf\Validation\Request\FormRequest;

class HotTopicRequest extends FormRequest
{
    use ActionRulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    // 自动匹配create方法验证
    public function createRules(): array
    {
        return [
            'currency' => ['sometimes'],
            'title' => ['sometimes'],
            'content' => ['sometimes'],
            'look_num' => ['integer'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    // 自动匹配save方法验证
    public function saveRules(): array
    {
        return [
            'currency' => ['sometimes'],
            'title' => ['sometimes'],
            'content' => ['sometimes'],
            'look_num' => ['integer'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    public function attributes(): array
    {
        return [
            'currency' => trans('hot_topic.currency') ?: '关联币种ID（cpx_dynamics_currency表）',
            'title' => trans('hot_topic.title') ?: '标题',
            'content' => trans('hot_topic.content') ?: '内容',
            'look_num' => trans('hot_topic.look_num') ?: '查看数量',
            'created_by' => trans('hot_topic.created_by') ?: '创建者',
            'updated_by' => trans('hot_topic.updated_by') ?: '更新者',
        ];
    }

    /**
     * 获取验证错误的自定义消息.
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}