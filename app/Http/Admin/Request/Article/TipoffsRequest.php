<?php

declare(strict_types=1);
namespace App\Http\Admin\Request\Article;

use App\Http\Common\Request\Traits\ActionRulesTrait;
use Hyperf\Validation\Request\FormRequest;

class TipoffsRequest extends FormRequest
{
    use ActionRulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    // 自动匹配create方法验证
    public function createRules(): array
    {
        return [
            'dynamics_id' => ['integer'],
            'user_id' => ['integer'],
            'content' => ['sometimes'],
            'voucher' => ['sometimes'],
            'reason' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    // 自动匹配save方法验证
    public function saveRules(): array
    {
        return [
            'dynamics_id' => ['integer'],
            'user_id' => ['integer'],
            'content' => ['sometimes'],
            'voucher' => ['sometimes'],
            'reason' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    public function attributes(): array
    {
        return [
            'dynamics_id' => trans('tipoffs.dynamics_id') ?: '动态ID',
            'user_id' => trans('tipoffs.user_id') ?: '会员ID',
            'content' => trans('tipoffs.content') ?: '举报内容',
            'voucher' => trans('tipoffs.voucher') ?: '凭证图片',
            'reason' => trans('tipoffs.reason') ?: '举报理由',
            'created_by' => trans('tipoffs.created_by') ?: '创建者',
            'updated_by' => trans('tipoffs.updated_by') ?: '更新者',
        ];
    }

    /**
     * 获取验证错误的自定义消息.
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}