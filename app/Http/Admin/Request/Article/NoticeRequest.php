<?php

declare(strict_types=1);
namespace App\Http\Admin\Request\Article;

use Hyperf\Validation\Request\FormRequest;
use App\Http\Common\Request\Traits\ActionRulesTrait;


class NoticeRequest extends FormRequest
{
    use ActionRulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    // 自动匹配create方法验证
    public function createRules(): array
    {
        return [
            'category_id' => ['integer'],
            'title' => ['sometimes'],
            'content' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    // 自动匹配save方法验证
    public function saveRules(): array
    {
        return [
            'category_id' => ['integer'],
            'title' => ['sometimes'],
            'content' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    public function attributes(): array
    {
        return [
            'category_id' => trans('notice.category_id') ?: '分类ID',
            'title' => trans('notice.title') ?: '标题',
            'content' => trans('notice.content') ?: '内容',
            'created_by' => trans('notice.created_by') ?: '创建者',
            'updated_by' => trans('notice.updated_by') ?: '更新者',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}