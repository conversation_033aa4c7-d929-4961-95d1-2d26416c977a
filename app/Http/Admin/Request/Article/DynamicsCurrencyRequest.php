<?php

declare(strict_types=1);
namespace App\Http\Admin\Request\Article;

use App\Http\Common\Request\Traits\ActionRulesTrait;
use Hyperf\Validation\Request\FormRequest;

class DynamicsCurrencyRequest extends FormRequest
{
    use ActionRulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    // 自动匹配create方法验证
    public function createRules(): array
    {
        return [
            'symbol_id' => ['integer'],
            'symbol' => ['integer'],
            'url' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    // 自动匹配save方法验证
    public function saveRules(): array
    {
        return [
            'symbol_id' => ['integer'],
            'symbol' => ['integer'],
            'url' => ['sometimes'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    public function attributes(): array
    {
        return [
            'symbol_id' => trans('dynamics_currency.symbol_id') ?: '币种ID',
            'symbol' => trans('dynamics_currency.symbol') ?: '币种标的',
            'url' => trans('dynamics_currency.url') ?: '链接',
            'created_by' => trans('dynamics_currency.created_by') ?: '创建者',
            'updated_by' => trans('dynamics_currency.updated_by') ?: '更新者',
        ];
    }

    /**
     * 获取验证错误的自定义消息.
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}