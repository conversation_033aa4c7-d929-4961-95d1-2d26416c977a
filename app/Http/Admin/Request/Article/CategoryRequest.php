<?php

declare(strict_types=1);
namespace App\Http\Admin\Request\Article;

use Hyperf\Validation\Request\FormRequest;
use App\Http\Common\Request\Traits\ActionRulesTrait;


class CategoryRequest extends FormRequest
{
    use ActionRulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    // 自动匹配create方法验证
    public function createRules(): array
    {
        return [
            'name' => ['required','array'],
            'sort' => ['required','integer'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    // 自动匹配save方法验证
    public function saveRules(): array
    {
        return [
            'name' => ['array'],
            'sort' => ['integer'],
            'created_by' => ['integer'],
            'updated_by' => ['integer'],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => trans('category.name') ?: '分类名称',
            'sort' => trans('category.sort') ?: '排序',
            'created_by' => trans('category.created_by') ?: '创建者',
            'updated_by' => trans('category.updated_by') ?: '更新者',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // 可以在这里添加自定义的错误消息
        ];
    }
}