<?php

declare(strict_types=1);
namespace App\Http\Admin\Controller\Message;

use App\Http\Admin\Service\Message\MessageService as Service;
use App\Http\Admin\Request\Message\MessageRequest as Request;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Swagger\Annotation as OA;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Mine\Access\Attribute\Permission;
use Hyperf\HttpServer\Annotation\Middleware;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;



#[OA\Tag('消息')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class MessageController extends AbstractController
{
    public function __construct(
        private readonly CurrentUser $currentUser
    ) {
        parent::__construct();
    }

    #[Inject]
    protected RequestInterface $request;

    #[Get(
        path: '/admin/message/message/list',
        operationId: 'message:message:list',
        summary: '消息列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['消息'],
    )]
    #[Permission(code: 'message:message:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        // return $this->success(
        //     $this->service->page(
        //         $this->getRequestData(),
        //         $this->getCurrentPage(),
        //         $this->getPageSize()
        //     )
        // );
        return $this->success(
            $this->service->list($this->request)
        );
    }

    #[Post(
        path: '/admin/message/message/create',
        operationId: 'message:message:create',
        summary: '消息新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['消息'],
    )]
    #[Permission(code: 'message:message:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->service->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/message/message/save/{id}',
        operationId: 'message:message:save',
        summary: '消息保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['消息'],
    )]
    #[Permission(code: 'message:message:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->service->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
            'language' => $request->getHeaderLine('Accept-Language')
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/message/message/delete',
        operationId: 'message:message:delete',
        summary: '消息删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['消息'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'message:message:delete')]
    public function delete(): Result
    {
        $this->service->deleteById($this->getRequestData());
        return $this->success();
    }

    #[Get(
        path: '/admin/message/message/userList'
    )]
    public function userList()
    {
        return $this->success(
            $this->service->userList($this->request)
        );
    }

}
