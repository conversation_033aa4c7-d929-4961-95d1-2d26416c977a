<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Http\Admin\Controller\Article;

use App\Http\Admin\Controller\AbstractController;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Admin\Request\Article\HotTopicRequest as Request;
use App\Http\Admin\Service\Article\HotTopicService;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\Swagger\Annotation as OA;
use Hyperf\Swagger\Annotation\Delete;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Mine\Access\Attribute\Permission;
use Mine\Swagger\Attributes\ResultResponse;


#[OA\Tag('热门话题')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class HotTopicController extends AbstractController
{
    public function __construct(
        private readonly CurrentUser $currentUser
    ) {}


    #[Inject]
    protected HotTopicService $hotTopicService;

    #[Get(
        path: '/admin/article/hot_topic/list',
        operationId: 'article:hot_topic:list',
        summary: '热门话题列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['热门话题'],
    )]
    #[Permission(code: 'article:hot_topic:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        // return $this->success(
        //     $this->hotTopicService->page(
        //         $this->getRequestData(),
        //         $this->getCurrentPage(),
        //         $this->getPageSize()
        //     )
        // );
        return $this->success(
            $this->hotTopicService->list($this->request)
        );
    }

    #[Post(
        path: '/admin/article/hot_topic/create',
        operationId: 'article:hot_topic:create',
        summary: '热门话题新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['热门话题'],
    )]
    #[Permission(code: 'article:hot_topic:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->hotTopicService->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/article/hot_topic/save/{id}',
        operationId: 'article:hot_topic:save',
        summary: '热门话题保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['热门话题'],
    )]
    #[Permission(code: 'article:hot_topic:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->hotTopicService->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/article/hot_topic/delete',
        operationId: 'article:hot_topic:delete',
        summary: '热门话题删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['热门话题'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'article:hot_topic:delete')]
    public function delete(): Result
    {
        $this->hotTopicService->deleteById($this->getRequestData());
        return $this->success();
    }

}
