<?php

declare(strict_types=1);
namespace App\Http\Admin\Controller\Article;

use App\Http\Admin\Service\Article\NoticeService as Service;
use App\Http\Admin\Request\Article\NoticeRequest as Request;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Swagger\Annotation as OA;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Mine\Access\Attribute\Permission;
use Hyperf\HttpServer\Annotation\Middleware;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;



#[OA\Tag('公告')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class NoticeController extends AbstractController
{
    protected RequestInterface $request;

    public function __construct(
        private readonly CurrentUser $currentUser,
        RequestInterface $request
    ) {
        $this->request = $request;
        parent::__construct();
    }

    #[Get(
        path: '/admin/article/notice/list',
        operationId: 'article:notice:list',
        summary: '公告列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['公告'],
    )]
    #[Permission(code: 'article:notice:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        // return $this->success(
        //     $this->service->page(
        //         $this->getRequestData(),
        //         $this->getCurrentPage(),
        //         $this->getPageSize()
        //     )
        // );
        return $this->success(
            $this->service->list(request: $this->request)
        );
    }

    #[Post(
        path: '/admin/article/notice/create',
        operationId: 'article:notice:create',
        summary: '公告新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['公告'],
    )]
    #[Permission(code: 'article:notice:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->service->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
            'language'   => $request->getHeaderLine('Accept-Language'),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/article/notice/save/{id}',
        operationId: 'article:notice:save',
        summary: '公告保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['公告'],
    )]
    #[Permission(code: 'article:notice:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->service->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
            'language' => $request->getHeaderLine('Accept-Language'),
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/article/notice/delete',
        operationId: 'article:notice:delete',
        summary: '公告删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['公告'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'article:notice:delete')]
    public function delete(): Result
    {
        $this->service->deleteById($this->getRequestData());
        return $this->success();
    }

}
