<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Http\Admin\Controller\Article;

use App\Http\Admin\Controller\AbstractController;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Admin\Request\Article\TipoffsRequest as Request;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Service\Article\TipoffsService as Service;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\Swagger\Annotation as OA;
use Hyperf\Swagger\Annotation\Delete;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Mine\Access\Attribute\Permission;
use Mine\Swagger\Attributes\ResultResponse;


#[OA\Tag('举报')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class TipoffsController extends AbstractController
{
    public function __construct(
        private readonly CurrentUser $currentUser
    ) {
        parent::__construct();
    }


    #[Get(
        path: '/admin/article/tipoffs/list',
        operationId: 'article:tipoffs:list',
        summary: '举报列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['举报'],
    )]
    #[Permission(code: 'article:tipoffs:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        // return $this->success(
        //     $this->service->page(
        //         $this->getRequestData(),
        //         $this->getCurrentPage(),
        //         $this->getPageSize()
        //     )
        // );
        return $this->success(
            $this->service->list($this->request)
        );
    }

    #[Post(
        path: '/admin/article/tipoffs/create',
        operationId: 'article:tipoffs:create',
        summary: '举报新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['举报'],
    )]
    #[Permission(code: 'article:tipoffs:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->service->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/article/tipoffs/save/{id}',
        operationId: 'article:tipoffs:save',
        summary: '举报保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['举报'],
    )]
    #[Permission(code: 'article:tipoffs:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->service->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/article/tipoffs/delete',
        operationId: 'article:tipoffs:delete',
        summary: '举报删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['举报'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'article:tipoffs:delete')]
    public function delete(): Result
    {
        $this->service->deleteById($this->getRequestData());
        return $this->success();
    }

}
