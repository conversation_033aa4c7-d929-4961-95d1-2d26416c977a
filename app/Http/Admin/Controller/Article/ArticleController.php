<?php

declare(strict_types=1);
namespace App\Http\Admin\Controller\Article;

use App\Http\Admin\Request\Article\ArticleRequest as Request;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Swagger\Annotation as OA;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Mine\Access\Attribute\Permission;
use Hyperf\HttpServer\Annotation\Middleware;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;



#[OA\Tag('文章')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class ArticleController extends AbstractController
{
    protected RequestInterface $request;

    public function __construct(
        private readonly CurrentUser $currentUser,
        RequestInterface $request
    ) {
        $this->request = $request;
        parent::__construct();
    }


    #[Get(
        path: '/admin/article/article/list',
        operationId: 'article:article:list',
        summary: '文章列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['文章'],
    )]
    #[Permission(code: 'article:article:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        // return $this->success(
        //     $this->service->page(
        //         $this->getRequestData(),
        //         $this->getCurrentPage(),
        //         $this->getPageSize()
        //     )
        // );
        return $this->success(
            $this->service->list($this->request)
        );
    }

    #[Post(
        path: '/admin/article/article/create',
        operationId: 'article:article:create',
        summary: '文章新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['文章'],
    )]
    #[Permission(code: 'article:article:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->service->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
            'language' => $request->getHeaderLine('Accept-Language'),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/article/article/save/{id}',
        operationId: 'article:article:save',
        summary: '文章保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['文章'],
    )]
    #[Permission(code: 'article:article:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->service->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
            'language' => $request->getHeaderLine('Accept-Language'),
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/article/article/delete',
        operationId: 'article:article:delete',
        summary: '文章删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['文章'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'article:article:delete')]
    public function delete(): Result
    {
        $this->service->deleteById($this->getRequestData());
        return $this->success();
    }

}
