<?php

declare(strict_types=1);
namespace App\Http\Admin\Controller\Article;

use App\Http\Admin\Request\Article\DynamicsRequest as Request;
use App\Http\Admin\Service\Article\DynamicsService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Swagger\Annotation as OA;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Mine\Access\Attribute\Permission;
use Hyperf\HttpServer\Annotation\Middleware;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;



#[OA\Tag('动态')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class DynamicsController extends AbstractController
{
    protected RequestInterface $request;

    public function __construct(
        private readonly CurrentUser $currentUser,
        RequestInterface $request
    ) {
        $this->request = $request;
    }

    #[Inject]
    protected DynamicsService $dynamicsService;

    #[Get(
        path: '/admin/article/dynamics/list',
        operationId: 'article:dynamics:list',
        summary: '动态列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['动态'],
    )]
    #[Permission(code: 'article:dynamics:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        // return $this->success(
        //     $this->service->page(
        //         $this->getRequestData(),
        //         $this->getCurrentPage(),
        //         $this->getPageSize()
        //     )
        // );

        return $this->success(
            $this->dynamicsService->list($this->request)
        );
    }

    #[Post(
        path: '/admin/article/dynamics/create',
        operationId: 'article:dynamics:create',
        summary: '动态新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['动态'],
    )]
    #[Permission(code: 'article:dynamics:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->dynamicsService->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/article/dynamics/save/{id}',
        operationId: 'article:dynamics:save',
        summary: '动态保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['动态'],
    )]
    #[Permission(code: 'article:dynamics:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->dynamicsService->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/article/dynamics/delete',
        operationId: 'article:dynamics:delete',
        summary: '动态删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['动态'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'article:dynamics:delete')]
    public function delete(): Result
    {
        $this->dynamicsService->deleteById($this->getRequestData());
        return $this->success();
    }

}
