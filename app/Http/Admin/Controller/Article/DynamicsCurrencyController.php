<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Http\Admin\Controller\Article;

use App\Http\Admin\Controller\AbstractController;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Admin\Request\Article\DynamicsCurrencyRequest as Request;
use App\Http\Admin\Service\Article\DynamicsCurrencyService;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\Swagger\Annotation as OA;
use Hyperf\Swagger\Annotation\Delete;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Mine\Access\Attribute\Permission;
use Mine\Swagger\Attributes\ResultResponse;


#[OA\Tag('动态币种')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class DynamicsCurrencyController extends AbstractController
{
    public function __construct(
        private readonly CurrentUser $currentUser
    ) {}



    #[Inject]
    protected DynamicsCurrencyService $dynamicsCurrencyService;

    #[Get(
        path: '/admin/article/dynamics_currency/list',
        operationId: 'article:dynamics_currency:list',
        summary: '动态币种列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['动态币种'],
    )]
    #[Permission(code: 'article:dynamics_currency:list')]
    #[ResultResponse(instance: new Result())]
    public function pageList(): Result
    {
        // return $this->success(
        //     $this->dynamicsCurrencyService->page(
        //         $this->getRequestData(),
        //         $this->getCurrentPage(),
        //         $this->getPageSize()
        //     )
        // );
        return $this->success(
            $this->dynamicsCurrencyService->list($this->request)
        );
    }

    #[Post(
        path: '/admin/article/dynamics_currency/create',
        operationId: 'article:dynamics_currency:create',
        summary: '动态币种新增',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['动态币种'],
    )]
    #[Permission(code: 'article:dynamics_currency:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->dynamicsCurrencyService->create(array_merge($request->all(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/article/dynamics_currency/save/{id}',
        operationId: 'article:dynamics_currency:save',
        summary: '动态币种保存',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['动态币种'],
    )]
    #[Permission(code: 'article:dynamics_currency:save')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->dynamicsCurrencyService->updateById($id, array_merge($request->all(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Delete(
        path: '/admin/article/dynamics_currency/delete',
        operationId: 'article:dynamics_currency:delete',
        summary: '动态币种删除',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['动态币种'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'article:dynamics_currency:delete')]
    public function delete(): Result
    {
        $this->dynamicsCurrencyService->deleteById($this->getRequestData());
        return $this->success();
    }

}
