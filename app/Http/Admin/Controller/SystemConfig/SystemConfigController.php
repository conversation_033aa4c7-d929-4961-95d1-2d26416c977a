<?php

declare(strict_types=1);
namespace App\Http\Admin\Controller\SystemConfig;

use App\Service\SystemConfigService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Swagger\Annotation as OA;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use Mine\Swagger\Attributes\ResultResponse;



#[OA\Tag('系统配置')]
#[OA\HyperfServer('http')]
#[Controller(prefix: 'admin/system/config')]
class SystemConfigController extends AbstractController
{
    protected RequestInterface $request;

    public function __construct(
        private readonly CurrentUser $currentUser,
        RequestInterface $request
    ) {
        $this->request = $request;
    }

    #[Inject]
    protected SystemConfigService $systemConfigService;

    /**
     * config_key 系统配置（应用标识）
     * 系统配置列表
     */
    #[GetMapping('list')]
    #[ResultResponse(instance: new Result())]
    public function list(): Result
    {
        return $this->success(
            $this->systemConfigService->configList($this->request->input('config_key'))
        );
    }


}
