<?php

declare(strict_types=1);

/**
 * MatchEngineController.php
 * 撮合引擎管理控制器 - 用于测试和管理撮合引擎功能
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/26
 * Website:algoquant.org
 */

namespace App\Http\Admin\Controller;

use App\Http\Api\Service\V1\MatchService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use App\Model\CpxUser;
use App\Process\MatchEngineBaseProcess;
use App\Enum\MatchEngine\ManagerStreamKey;
use App\Enum\OrderType;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\HttpServer\Request;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerInterface;

#[Controller(prefix: '/admin/match-engine')]
class MatchEngineController extends AbstractController
{
    protected Redis $redis;

    #[Inject]
    protected MatchService $matchService;

    public function __construct(ContainerInterface $container)
    {
        parent::__construct($container);
        $this->redis = $container->get(Redis::class);
    }
    /**
     * 获取撮合引擎状态
     */
    #[GetMapping('status')]
    public function getStatus(): Result
    {
        try {
            // 获取进程状态
            $processStatus = MatchEngineBaseProcess::getProcessStatus();
            
            // 获取币种映射
            $currencyMapping = MatchEngineBaseProcess::getCurrencyMapping();
            
            // 获取订单队列统计
            $orderTable = MatchEngineBaseProcess::getOrderTable();
            $orderCount = 0;
            $pendingOrders = [];
            
            if ($orderTable !== null) {
                foreach ($orderTable as $key => $order) {
                    $orderCount++;
                    if ($orderCount <= 10) { // 只显示前10个订单
                        $pendingOrders[] = [
                            'id' => $order['id'] ?? $key,
                            'type' => $order['type'] ?? 'unknown',
                            'data' => json_decode($order['data'] ?? '{}', true),
                            'target_process' => $order['target_process'] ?? 0,
                            'timestamp' => date('Y-m-d H:i:s', $order['timestamp'] ?? 0),
                            'processed' => $order['processed'] ?? 0
                        ];
                    }
                }
            }
            
            return $this->success([
                'process_status' => $processStatus,
                'currency_mapping' => $currencyMapping,
                'order_queue' => [
                    'total_count' => $orderCount,
                    'pending_orders' => $pendingOrders
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Throwable $e) {
            return $this->error('获取状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加测试订单 - 使用 Redis Stream
     */
    #[PostMapping('order')]
    public function addOrder(Request $request): Result
    {
        $params = $request->all();
        
        $symbol = strtoupper($params['symbol'] ?? 'BTCUSDT');
        $userId = $params['user_id'] ?? '1001';
        $side = $params['side'] ?? 'buy';
        $type = $params['type'] ?? 'limit';
        $timeInForce = $params['time_in_force'] ?? 'gtc';
        $price = $params['price'] ?? '50000.00';
        $quantity = $params['quantity'] ?? '0.01';
        $leverage = $params['leverage'] ?? '1.0';
        $marketType = $params['market_type'] ?? 'spot'; // 默认现货市场
        
        // 验证参数
        if (!in_array($side, ['buy', 'sell'])) {
            return $this->error('订单方向必须是 buy 或 sell');
        }
        
        if (!in_array($type, ['limit', 'market'])) {
            return $this->error('订单类型必须是 limit 或 market');
        }
        
        if (!in_array($timeInForce, ['gtc', 'ioc', 'fok'])) {
            return $this->error('时效类型必须是 gtc、ioc 或 fok');
        }
        
        if (!in_array($marketType, ['spot', 'contract', 'futures'])) {
            return $this->error('市场类型必须是 spot、contract 或 futures');
        }
        
        try {
            // 获取对应市场的订单流
            $orderStream = ManagerStreamKey::getOrderStreamByMarket($marketType);
            if (!$orderStream) {
                return $this->error('不支持的市场类型');
            }
            
            // 构造订单数据
            $orderData = [
                'symbol' => $symbol,
                'user_id' => $userId,
                'side' => $side,
                'order_type' => $type,
                'order_force' => $timeInForce,
                'price' => $price,
                'quantity' => $quantity,
                'leverage' => $leverage,
                'timestamp' => time(),
                'market_type' => $marketType == 'spot' ? 1 : 5,
                'currency_id' => $params['currency_id']
            ];

            context_set("user" ,(new CpxUser)->fill(['id' => $userId]));
            $messageId = $this->matchService->createOrder($orderData);

            if ($messageId) {
                return $this->success([
                    'message' => '订单发送成功',
                    'message_id' => $messageId,
                    'market_type' => $marketType,
                    'stream' => $orderStream->value,
                    'order' => $orderData,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } else {
                return $this->error('订单发送失败');
            }
            
        } catch (\Throwable $e) {
            return $this->error('添加订单失败: ' . $e->getMessage());
        }
    }

    /**
     * 撤销订单 - 使用 Redis Stream
     */
    #[PostMapping('cancel')]
    public function cancelOrder(Request $request): Result
    {
        $symbol = strtoupper($request->input('symbol', ''));
        $orderId = $request->input('order_id', '');
        $userId = $request->input('user_id', '');
        $marketType = $request->input('market_type', 'spot'); // 默认现货市场
        
        // 验证参数
        if (empty($symbol)) {
            return $this->error('币种符号不能为空');
        }
        
        if (empty($orderId)) {
            return $this->error('订单ID不能为空');
        }
        
        if (!in_array($marketType, ['spot', 'contract', 'futures'])) {
            return $this->error('市场类型必须是 spot、contract 或 futures');
        }
        
        try {
            // 获取对应市场的订单流
            $orderStream = ManagerStreamKey::getOrderStreamByMarket($marketType);
            if (!$orderStream) {
                return $this->error('不支持的市场类型');
            }
            
            // 构造撤单数据
            $cancelData = [
                'symbol' => $symbol,
                'order_id' => $orderId,
                'user_id' => $userId,
                'timestamp' => time(),
            ];


            context_set("user",(new CpxUser())->fill(['id' => $userId]));
            // 发送到 Redis Stream
            $messageId = $this->matchService->cancelOrder($cancelData);
            
            if ($messageId) {
                return $this->success([
                    'message' => '撤单命令发送成功',
                    'message_id' => $messageId,
                    'market_type' => $marketType,
                    'stream' => $orderStream->value,
                    'cancel_data' => $cancelData,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } else {
                return $this->error('撤单命令发送失败');
            }
            
        } catch (\Throwable $e) {
            return $this->error('撤单失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加币种
     */
    #[PostMapping('currency')]
    public function addCurrency(Request $request): Result
    {
        $symbol = strtoupper($request->input('symbol', ''));
        
        if (empty($symbol)) {
            return $this->error('币种符号不能为空');
        }
        
        try {
            $result = MatchEngineBaseProcess::addCommand('add_currency', ['symbol' => $symbol]);
            
            if ($result) {
                return $this->success([
                    'message' => '添加币种命令发送成功',
                    'symbol' => $symbol,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } else {
                return $this->error('发送添加币种命令失败，请确保撮合引擎正在运行');
            }
            
        } catch (\Throwable $e) {
            return $this->error('添加币种失败: ' . $e->getMessage());
        }
    }

    /**
     * 移除币种
     */
    #[DeleteMapping('currency')]
    public function removeCurrency(Request $request): Result
    {
        $symbol = strtoupper($request->input('symbol', ''));
        
        if (empty($symbol)) {
            return $this->error('币种符号不能为空');
        }
        
        try {
            $result = MatchEngineBaseProcess::addCommand('remove_currency', ['symbol' => $symbol]);
            
            if ($result) {
                return $this->success([
                    'message' => '移除币种命令发送成功',
                    'symbol' => $symbol,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } else {
                return $this->error('发送移除币种命令失败，请确保撮合引擎正在运行');
            }
            
        } catch (\Throwable $e) {
            return $this->error('移除币种失败: ' . $e->getMessage());
        }
    }

    /**
     * 停止币种交易
     */
    #[PostMapping('currency/stop')]
    public function stopCurrency(Request $request): Result
    {
        $symbol = strtoupper($request->input('symbol', ''));
        
        if (empty($symbol)) {
            return $this->error('币种符号不能为空');
        }
        
        try {
            $result = MatchEngineBaseProcess::addCommand('stop_currency', ['symbol' => $symbol]);
            
            if ($result) {
                return $this->success([
                    'message' => '停止币种交易命令发送成功',
                    'symbol' => $symbol,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } else {
                return $this->error('发送停止币种交易命令失败');
            }
            
        } catch (\Throwable $e) {
            return $this->error('停止币种交易失败: ' . $e->getMessage());
        }
    }

    /**
     * 启动币种交易
     */
    #[PostMapping('currency/start')]
    public function startCurrency(Request $request): Result
    {
        $symbol = strtoupper($request->input('symbol', ''));
        
        if (empty($symbol)) {
            return $this->error('币种符号不能为空');
        }
        
        try {
            $result = MatchEngineBaseProcess::addCommand('start_currency', ['symbol' => $symbol]);
            
            if ($result) {
                return $this->success([
                    'message' => '启动币种交易命令发送成功',
                    'symbol' => $symbol,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } else {
                return $this->error('发送启动币种交易命令失败');
            }
            
        } catch (\Throwable $e) {
            return $this->error('启动币种交易失败: ' . $e->getMessage());
        }
    }

    /**
     * 重载配置
     */
    #[PostMapping('reload')]
    public function reloadConfig(): Result
    {
        try {
            $result = MatchEngineBaseProcess::addCommand('reload_config', []);
            
            if ($result) {
                return $this->success([
                    'message' => '重载配置命令发送成功',
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } else {
                return $this->error('发送重载配置命令失败');
            }
            
        } catch (\Throwable $e) {
            return $this->error('重载配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取详细的进程信息
     */
    #[GetMapping('processes')]
    public function getProcesses(): Result
    {
        try {
            $processStatus = MatchEngineBaseProcess::getProcessStatus();
            $currencyMapping = MatchEngineBaseProcess::getCurrencyMapping();
            
            // 组织进程信息
            $processes = [];
            foreach ($processStatus as $processIndex => $status) {
                $assignedCurrencies = [];
                foreach ($currencyMapping as $symbol => $mapping) {
                    if (($mapping['process_index'] ?? -1) == $processIndex) {
                        $assignedCurrencies[] = $symbol;
                    }
                }
                
                $processes[] = [
                    'process_index' => $processIndex,
                    'status' => $status['status'] ?? 'unknown',
                    'currency_count' => $status['currency_count'] ?? 0,
                    'assigned_currencies' => $assignedCurrencies,
                    'last_heartbeat' => $status['last_heartbeat'] ?? 0,
                    'last_heartbeat_time' => date('Y-m-d H:i:s', $status['last_heartbeat'] ?? 0),
                    'created_at' => $status['created_at'] ?? 0,
                    'created_at_time' => date('Y-m-d H:i:s', $status['created_at'] ?? 0),
                    'is_timeout' => (time() - ($status['last_heartbeat'] ?? 0)) > 30
                ];
            }
            
            return $this->success([
                'processes' => $processes,
                'total_processes' => count($processes),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Throwable $e) {
            return $this->error('获取进程信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量添加测试订单 - 使用 Redis Stream
     */
    #[PostMapping('orders/batch')]
    public function addBatchOrders(Request $request): Result
    {
        $count = (int) $request->input('count', 5);
        $symbol = strtoupper($request->input('symbol', 'BTCUSDT'));
        $baseUserId = (int) $request->input('base_user_id', 1000);
        $marketType = $request->input('market_type', 'spot'); // 默认现货市场
        
        if ($count <= 0 || $count > 100) {
            return $this->error('批量数量必须在 1-100 之间');
        }
        
        if (!in_array($marketType, ['spot', 'contract', 'futures'])) {
            return $this->error('市场类型必须是 spot、contract 或 futures');
        }
        
        try {
            // 获取对应市场的订单流
            $orderStream = ManagerStreamKey::getOrderStreamByMarket($marketType);
            if (!$orderStream) {
                return $this->error('不支持的市场类型');
            }
            
            $results = [];
            $successCount = 0;
            
            for ($i = 0; $i < $count; $i++) {
                $userId = $baseUserId + $i;
                $side = $i % 2 === 0 ? 'buy' : 'sell';
                $price = $side === 'buy' ? (50000 - $i * 10) : (50000 + $i * 10);
                $quantity = '0.01';
                
                // 构造订单数据
                $orderData = [
                    'symbol' => $symbol,
                    'user_id' => (string)$userId,
                    'side' => $side,
                    'type' => 'limit',
                    'time_in_force' => 'gtc',
                    'price' => (string)$price,
                    'quantity' => $quantity,
                    'leverage' => '1.0',
                    'timestamp' => time(),
                    'order_id' => uniqid('batch_', true),
                ];
                
                // 发送到 Redis Stream
                $messageId = $this->redis->xAdd(
                    $orderStream->value,
                    '*',
                    [
                        'type' => 'order', // 新订单类型，会被转换为 process_order
                        'data' => json_encode($orderData),
                        'symbol' => $symbol,
                        'user_id' => (string)$userId,
                        'timestamp' => time(),
                    ]
                );
                
                $orderResult = [
                    'index' => $i + 1,
                    'user_id' => $userId,
                    'side' => $side,
                    'price' => $price,
                    'quantity' => $quantity,
                    'order_id' => $orderData['order_id'],
                    'message_id' => $messageId,
                    'success' => !empty($messageId)
                ];
                
                $results[] = $orderResult;
                
                if (!empty($messageId)) {
                    $successCount++;
                }
            }
            
            return $this->success([
                'message' => "批量添加完成，成功 {$successCount}/{$count} 个订单",
                'symbol' => $symbol,
                'market_type' => $marketType,
                'stream' => $orderStream->value,
                'total_count' => $count,
                'success_count' => $successCount,
                'failed_count' => $count - $successCount,
                'orders' => $results,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Throwable $e) {
            return $this->error('批量添加订单失败: ' . $e->getMessage());
        }
    }
} 