<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 */

namespace App\Http\Common\Controller;

use App\Exception\BusinessException;
use App\Exception\NormalStatusException;
use App\Http\Common\Result;
use App\Http\Common\ResultCode;
use Hyperf\Context\ApplicationContext;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Di\Aop\PropertyHandlerTrait;
use Hyperf\Di\Aop\ProxyTrait;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Paginator\LengthAwarePaginator;
use Hyperf\Resource\Json\JsonResource;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

class AbstractController
{
    use ProxyTrait;
    use PropertyHandlerTrait;

    /**
     * 各控制器的服务层
     * @var mixed
     */
    public mixed $service;

    /**
     * 各控制器的请求验证
     * @var mixed
     */
    public mixed $rules;

    #[Inject]
    public ValidatorFactoryInterface $validatorFactory;

    #[Inject]
    protected RequestInterface $request;

    /**
     * service 层自动加载规约
     */
    public function __construct()
    {
        $this->__handlePropertyHandler(__CLASS__);
        $serviceClass = '\\' . str_replace('Controller', 'Service', static::class);
        if (class_exists($serviceClass)) {
            if (!ApplicationContext::getContainer()->has($serviceClass)) {
                ApplicationContext::getContainer()->set($serviceClass, new $serviceClass());
            }
            $this->service = ApplicationContext::getContainer()->get($serviceClass);
        } else {
            throw new BusinessException(ResultCode::FAIL, "Not found service : {$serviceClass}");
        }

        $rulesClass = '\\' . str_replace('Controller', 'Request', static::class);
        if (class_exists($rulesClass)) {
            if (!ApplicationContext::getContainer()->has($rulesClass)) {
                ApplicationContext::getContainer()->set($rulesClass, new $rulesClass());
            }
            $this->rules = ApplicationContext::getContainer()->get($rulesClass);
        }
    }

    protected function success(mixed $data = [], ?string $message = null): Result
    {
        // 处理分页情况
        if ($data instanceof LengthAwarePaginator) {
            // dump("分页");
            $data = [
                'list' => $data->items(),
                'total' => $data->total(),
                'page' => $data->currentPage(),
                'page_size' => $data->perPage(),
                'total_page' => $data->lastPage(),
            ];
        }
        if ($data instanceof JsonResource && $data->resource instanceof LengthAwarePaginator) {
            // dump("资源分页");
            $data = [
                'list' => $data->resource->items(),
                'total' => $data->resource->total(),
                'page' => $data->resource->currentPage(),
                'page_size' => $data->resource->perPage(),
                'total_page' => $data->resource->lastPage(),
            ];
        }
        return new Result(ResultCode::SUCCESS, $message, $data);
    }

    protected function error(?string $message = null, mixed $data = []): Result
    {
        return new Result(ResultCode::FAIL, $message, $data);
    }

    protected function json(ResultCode $code, mixed $data = [], ?string $message = null): Result
    {
        return new Result($code, $message, $data);
    }

    /**
     * 请求验证
     * @param array $rules 验证规则
     * @param array $message 自定义消息
     * @return array
     */
    public function requestValidate(array $rules,array $message = []): array
    {
        $validator = $this->validatorFactory->make(
            $this->request->all(),
            $rules,
            $message
        );
        return $validator->validate();
    }
}
