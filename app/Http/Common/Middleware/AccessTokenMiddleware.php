<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 
 */

namespace App\Http\Common\Middleware;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Token\RegisteredClaims;
use Mine\Jwt\JwtInterface;
use Mine\JwtAuth\Middleware\AbstractTokenMiddleware;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Psr\Http\Message\ResponseInterface;

final class AccessTokenMiddleware extends AbstractTokenMiddleware
{
    public function getJwt(): JwtInterface
    {
        return $this->jwtFactory->get('api');
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        try {
            /** @var \Lcobucci\JWT\UnencryptedToken $token */
            $token = $this->parserToken($request);
        } catch (\Throwable $t) {
            return $handler->handle($request);
        }

        $expireAt = $token->claims()->get(RegisteredClaims::EXPIRATION_TIME)->getTimestamp();
        if ($expireAt < time()) {
            $this->getJwt()->addBlackList($token);
            return $handler->handle($request);
        }

        $userId = (int) $token->claims()->get(RegisteredClaims::ID);

        $request = $request
            ->withAttribute('user_id', $userId)
            ->withAttribute('token', $token);

        $httpServerRequest = container()->get(\Hyperf\HttpServer\Request::class);
        $httpServerRequest->macro('userId', function () use ($userId) {
            return $userId;
        });

        return $handler->handle($request);
    }
}
