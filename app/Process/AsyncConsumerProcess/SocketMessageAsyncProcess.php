<?php

/**
 * SocketMessageAsyncProcess.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/8
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Process\AsyncConsumerProcess;

use Hyperf\AsyncQueue\Process\ConsumerProcess;
use Hyperf\Process\Annotation\Process;

/**
 * 用于处理服务端处理的socket消息推送，此进程运行在api端
 */
#[Process(name: 'socket-message-async-process')]
class SocketMessageAsyncProcess extends ConsumerProcess
{

    public string $queue = 'socket-message';

    public bool $enableCoroutine = true;

    public function isEnable($server): bool
    {
        return (bool)env('API_SERVER',false);
    }

}