<?php

/**
 * MarginAsyncJobProcess.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/7/7
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Process\AsyncConsumerProcess;

use Hyperf\AsyncQueue\Process\ConsumerProcess;
use Hyperf\Process\Annotation\Process;

#[Process(name:'margin-async-job-process')]
class MarginAsyncJobProcess extends ConsumerProcess
{
    public string $queue = 'position_monitoring';

    public bool $enableCoroutine = true;

    public function isEnable($server): bool
    {
        return (bool)env('MARKET_DATA_SERVER',false);
    }
}