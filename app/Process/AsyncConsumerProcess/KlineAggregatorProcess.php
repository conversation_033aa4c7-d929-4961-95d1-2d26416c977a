<?php

/**
 * KlineAggregatorJob.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/25
 * Website:algoquant.org
 */

namespace App\Process\AsyncConsumerProcess;

use Hyperf\AsyncQueue\Process\ConsumerProcess;
use Hyperf\Process\Annotation\Process;

#[Process(name: 'kline-Aggregator-Consumer-Process')]
class KlineAggregatorProcess extends ConsumerProcess
{
    public bool $enableCoroutine = true;

    public string $queue = 'kline-aggregator';

    public function isEnable($server): bool
    {
        return true;
    }
}