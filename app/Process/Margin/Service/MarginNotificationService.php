<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆通知服务
 */

namespace App\Process\Margin\Service;

use App\Model\User\User;
use App\Model\Currency\Currency;
use App\Model\Trade\TradeMarginPosition;
use App\Job\Margin\NotificationJob;
use App\Job\Margin\RiskWarningJob;
use Hyperf\AsyncQueue\Driver\DriverFactory;
use Hyperf\AsyncQueue\Driver\DriverInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;
use Hyperf\Logger\LoggerFactory;
use Carbon\Carbon;

class MarginNotificationService
{
    #[Inject]
    protected DriverFactory $driverFactory;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;
    protected DriverInterface $driver;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('margin_notification');
        $this->driver = $this->driverFactory->get('default');
    }

    /**
     * 发送风险预警通知
     *
     * @param int $userId 用户ID
     * @param array $riskInfo 风险信息
     * @return bool
     */
    public function sendRiskWarning(int $userId, array $riskInfo): bool
    {
        try {
            // 检查预警冷却期，避免频繁发送
            $cooldownKey = "margin:risk_warning:{$userId}:{$riskInfo['position_id']}";
            if ($this->redis->exists($cooldownKey)) {
                $this->logger->debug('风险预警在冷却期内，跳过发送', [
                    'user_id' => $userId,
                    'position_id' => $riskInfo['position_id']
                ]);
                return false;
            }

            // 设置冷却期（5分钟）
            $this->redis->setex($cooldownKey, 300, 1);

            // 推送到队列
            $this->driver->push(new RiskWarningJob($userId, $riskInfo));

            $this->logger->info('风险预警通知已推送到队列', [
                'user_id' => $userId,
                'position_id' => $riskInfo['position_id'],
                'risk_level' => $riskInfo['risk_level'],
                'margin_ratio' => $riskInfo['margin_ratio']
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('发送风险预警失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送强平通知
     *
     * @param int $userId 用户ID
     * @param string $type 通知类型
     * @param array $data 通知数据
     * @return bool
     */
    public function sendLiquidationNotification(int $userId, string $type, array $data): bool
    {
        try {
            $notificationData = [
                'user_id' => $userId,
                'type' => $type,
                'data' => $data,
                'timestamp' => Carbon::now()->toDateTimeString()
            ];

            // 推送到队列
            $this->driver->push(new NotificationJob($notificationData));

            $this->logger->info('强平通知已推送到队列', [
                'user_id' => $userId,
                'type' => $type,
                'data' => $data
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('发送强平通知失败', [
                'user_id' => $userId,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 批量发送风险预警
     *
     * @param array $warningTasks 预警任务列表
     * @return array 发送结果统计
     */
    public function batchSendRiskWarnings(array $warningTasks): array
    {
        $successCount = 0;
        $skipCount = 0;
        $failureCount = 0;

        foreach ($warningTasks as $task) {
            $userId = $task['user_id'];
            $riskInfo = $task['risk_info'];

            $result = $this->sendRiskWarning($userId, $riskInfo);
            
            if ($result === true) {
                $successCount++;
            } elseif ($result === false) {
                // 检查是否是因为冷却期跳过
                $cooldownKey = "margin:risk_warning:{$userId}:{$riskInfo['position_id']}";
                if ($this->redis->exists($cooldownKey)) {
                    $skipCount++;
                } else {
                    $failureCount++;
                }
            }
        }

        $stats = [
            'total' => count($warningTasks),
            'success' => $successCount,
            'skipped' => $skipCount,
            'failed' => $failureCount
        ];

        $this->logger->info('批量风险预警发送完成', $stats);

        return $stats;
    }

    /**
     * 发送WebSocket通知
     *
     * @param int $userId 用户ID
     * @param string $channel 频道
     * @param array $message 消息内容
     * @return bool
     */
    public function sendWebSocketNotification(int $userId, string $channel, array $message): bool
    {
        try {
            // 构造WebSocket消息
            $wsMessage = [
                'channel' => $channel,
                'data' => $message,
                'timestamp' => Carbon::now()->toDateTimeString()
            ];

            // 发布到Redis频道（WebSocket服务监听）
            $channelName = "ws:user:{$userId}:{$channel}";
            $this->redis->publish($channelName, json_encode($wsMessage));

            $this->logger->debug('WebSocket通知已发送', [
                'user_id' => $userId,
                'channel' => $channel,
                'message' => $message
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('发送WebSocket通知失败', [
                'user_id' => $userId,
                'channel' => $channel,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送APP推送通知
     *
     * @param int $userId 用户ID
     * @param string $title 标题
     * @param string $content 内容
     * @param array $extra 额外数据
     * @return bool
     */
    public function sendAppPushNotification(int $userId, string $title, string $content, array $extra = []): bool
    {
        try {
            // 获取用户推送token
            $user = User::find($userId);
            if (!$user || !$user->push_token) {
                $this->logger->warning('用户推送token不存在', ['user_id' => $userId]);
                return false;
            }

            $pushData = [
                'token' => $user->push_token,
                'title' => $title,
                'content' => $content,
                'extra' => $extra
            ];

            // 推送到队列（由专门的推送服务处理）
            $this->driver->push(new NotificationJob([
                'type' => 'app_push',
                'data' => $pushData
            ]));

            $this->logger->info('APP推送通知已推送到队列', [
                'user_id' => $userId,
                'title' => $title
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('发送APP推送通知失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送短信通知
     *
     * @param int $userId 用户ID
     * @param string $template 短信模板
     * @param array $params 模板参数
     * @return bool
     */
    public function sendSmsNotification(int $userId, string $template, array $params = []): bool
    {
        try {
            // 获取用户手机号
            $user = User::find($userId);
            if (!$user || !$user->mobile) {
                $this->logger->warning('用户手机号不存在', ['user_id' => $userId]);
                return false;
            }

            $smsData = [
                'mobile' => $user->mobile,
                'template' => $template,
                'params' => $params
            ];

            // 推送到队列（由专门的短信服务处理）
            $this->driver->push(new NotificationJob([
                'type' => 'sms',
                'data' => $smsData
            ]));

            $this->logger->info('短信通知已推送到队列', [
                'user_id' => $userId,
                'template' => $template
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('发送短信通知失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送邮件通知
     *
     * @param int $userId 用户ID
     * @param string $subject 邮件主题
     * @param string $content 邮件内容
     * @param string $template 邮件模板
     * @return bool
     */
    public function sendEmailNotification(int $userId, string $subject, string $content, string $template = 'default'): bool
    {
        try {
            // 获取用户邮箱
            $user = User::find($userId);
            if (!$user || !$user->email) {
                $this->logger->warning('用户邮箱不存在', ['user_id' => $userId]);
                return false;
            }

            $emailData = [
                'email' => $user->email,
                'subject' => $subject,
                'content' => $content,
                'template' => $template
            ];

            // 推送到队列（由专门的邮件服务处理）
            $this->driver->push(new NotificationJob([
                'type' => 'email',
                'data' => $emailData
            ]));

            $this->logger->info('邮件通知已推送到队列', [
                'user_id' => $userId,
                'subject' => $subject
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('发送邮件通知失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 格式化风险预警消息
     *
     * @param array $riskInfo 风险信息
     * @return array 格式化后的消息
     */
    public function formatRiskWarningMessage(array $riskInfo): array
    {
        $riskLevel = $riskInfo['risk_level'] ?? 1;
        $marginRatio = $riskInfo['margin_ratio'] ?? 0;
        $currency = Currency::find($riskInfo['currency_id']);
        $currencySymbol = $currency ? $currency->symbol : 'Unknown';

        // 根据风险等级设置消息内容
        switch ($riskLevel) {
            case 2: // 预警
                $title = '保证金风险预警';
                $content = "您的{$currencySymbol}杠杆仓位保证金率已达到" . number_format($marginRatio * 100, 2) . "%，请及时补充保证金或减少仓位。";
                $level = 'warning';
                break;

            case 3: // 危险
                $title = '保证金风险警告';
                $content = "您的{$currencySymbol}杠杆仓位保证金率已达到" . number_format($marginRatio * 100, 2) . "%，仓位面临强平风险，请立即处理！";
                $level = 'danger';
                break;

            default:
                $title = '保证金通知';
                $content = "您的{$currencySymbol}杠杆仓位保证金率：" . number_format($marginRatio * 100, 2) . "%";
                $level = 'info';
                break;
        }

        return [
            'title' => $title,
            'content' => $content,
            'level' => $level,
            'currency_symbol' => $currencySymbol,
            'margin_ratio' => $marginRatio,
            'liquidation_price' => $riskInfo['liquidation_price'] ?? 0
        ];
    }

    /**
     * 格式化强平通知消息
     *
     * @param string $type 通知类型
     * @param array $data 通知数据
     * @return array 格式化后的消息
     */
    public function formatLiquidationMessage(string $type, array $data): array
    {
        $currency = Currency::find($data['currency_id'] ?? 0);
        $currencySymbol = $currency ? $currency->symbol : 'Unknown';

        switch ($type) {
            case 'liquidation_started':
                return [
                    'title' => '强平开始',
                    'content' => "您的{$currencySymbol}杠杆仓位已触发强平，系统正在执行强平操作。",
                    'level' => 'danger'
                ];

            case 'liquidation_partial':
                return [
                    'title' => '部分强平',
                    'content' => "您的{$currencySymbol}杠杆仓位已部分强平，成交数量：{$data['liquidated_quantity']}，剩余数量：{$data['remaining_quantity']}。",
                    'level' => 'warning'
                ];

            case 'liquidation_completed':
                return [
                    'title' => '强平完成',
                    'content' => "您的{$currencySymbol}杠杆仓位强平已完成，成交数量：{$data['liquidated_quantity']}，成交价格：{$data['liquidation_price']}。",
                    'level' => 'info'
                ];

            case 'liquidation_failed':
                return [
                    'title' => '强平失败',
                    'content' => "您的{$currencySymbol}杠杆仓位强平失败，请联系客服处理。原因：{$data['reason']}",
                    'level' => 'danger'
                ];

            default:
                return [
                    'title' => '强平通知',
                    'content' => "您的{$currencySymbol}杠杆仓位状态更新。",
                    'level' => 'info'
                ];
        }
    }
} 