<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 杠杆强平执行服务
 */

namespace App\Process\Margin\Service;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\Enums\Trade\Margin\MarginType;
use App\Model\Enums\Trade\Margin\PositionSide;
use App\Model\Enums\Trade\Margin\LiquidationStatus;
use App\Model\Enums\Trade\Margin\LiquidationType;
use App\Model\Enums\Trade\Margin\TriggerSource;
use App\Model\Enums\Trade\Margin\MarginPositionStatus;
use App\Model\Enums\Trade\Margin\MarginOrderStatus;
use App\Model\Enums\Trade\Margin\MarginBorrowStatus;
use App\Model\Enums\User\AccountType;
use App\Model\Trade\TradeMarginPosition;
use App\Model\Trade\TradeMarginOrder;
use App\Model\Margin\MarginLiquidationRecord;
use App\Model\User\UserMarginBorrow;
use App\Model\Match\MatchOrder;
use App\Enum\OrderStatus;
use App\Service\UserAccounts\UserAccountsAssetService;
use App\Service\MatchEngineOrderService;
use App\Process\Margin\Service\MarginNotificationService;
use App\Enum\MarketType;
use App\Model\Enums\User\FlowsType;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Psr\Log\LoggerInterface;
use Hyperf\Logger\LoggerFactory;
use Carbon\Carbon;

class MarginLiquidationService
{
    #[Inject]
    protected MarginNotificationService $notificationService;

    #[Inject]
    protected UserAccountsAssetService $assetService;

    #[Inject]
    protected MatchEngineOrderService $matchEngineService;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('margin_liquidation');
    }

    /**
     * 执行强平操作（从风险信息直接执行）
     *
     * @param array $riskInfo 风险信息
     * @return bool
     */
    public function executeLiquidation(array $riskInfo): bool
    {
        try {
            $position = TradeMarginPosition::find($riskInfo['position_id']);
            if (!$position) {
                $this->logger->error('强平失败：仓位不存在', ['position_id' => $riskInfo['position_id']]);
                return false;
            }

            $currentPrice = $riskInfo['current_price'];
            $result = $this->liquidatePosition($position, $currentPrice, $riskInfo);

            return $result['success'];

        } catch (\Exception $e) {
            $this->logger->error('执行强平异常', [
                'risk_info' => $riskInfo,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 执行强平操作
     *
     * @param TradeMarginPosition $position 需要强平的仓位
     * @param float $currentPrice 当前市场价格
     * @param array $riskInfo 风险信息
     * @return array 强平结果
     */
    public function liquidatePosition(TradeMarginPosition $position, float $currentPrice, array $riskInfo): array
    {
        $userId = $position->getUserId();
        $currencyId = $position->getCurrencyId();
        $positionId = $position->getId();

        $this->logger->info('开始执行强平', [
            'position_id' => $positionId,
            'user_id' => $userId,
            'current_price' => $currentPrice,
            'risk_info' => $riskInfo
        ]);

        try {
            return Db::transaction(function () use ($position, $currentPrice, $riskInfo) {
                // 1. 检查是否已经在强平中
                if ($this->isPositionLiquidating($position->getId())) {
                    throw new BusinessException(ResultCode::FAIL, '仓位已在强平处理中');
                }

                // 2. 创建强平记录
                $liquidationRecord = $this->createLiquidationRecord($position, $currentPrice, $riskInfo);

                // 3. 创建强平订单
                $liquidationOrder = $this->createLiquidationOrder($position, $currentPrice);

                // 4. 更新仓位状态为强平中
                $this->updatePositionLiquidationStatus($position, true);

                // 5. 发送强平开始通知
                $this->notificationService->sendLiquidationNotification(
                    $position->getUserId(),
                    'liquidation_started',
                    [
                        'position_id' => $position->getId(),
                        'currency_id' => $position->getCurrencyId(),
                        'liquidation_price' => $currentPrice,
                        'liquidation_order_id' => $liquidationOrder->getId(),
                    ]
                );

                $this->logger->info('强平启动成功', [
                    'position_id' => $position->getId(),
                    'liquidation_record_id' => $liquidationRecord->getId(),
                    'liquidation_order_id' => $liquidationOrder->getId()
                ]);

                return [
                    'success' => true,
                    'liquidation_record' => $liquidationRecord,
                    'liquidation_order' => $liquidationOrder,
                    'message' => '强平启动成功'
                ];
            });

        } catch (\Exception $e) {
            $this->logger->error('强平执行失败', [
                'position_id' => $positionId,
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 发送强平失败通知
            $this->notificationService->sendLiquidationNotification(
                $userId,
                'liquidation_failed',
                [
                    'position_id' => $positionId,
                    'error' => $e->getMessage()
                ]
            );

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => '强平执行失败'
            ];
        }
    }

    /**
     * 批量执行强平操作
     *
     * @param array $liquidationTasks 强平任务列表
     * @return array 批量强平结果
     */
    public function batchLiquidate(array $liquidationTasks): array
    {
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($liquidationTasks as $task) {
            $position = $task['position'];
            $currentPrice = $task['current_price'];
            $riskInfo = $task['risk_info'];

            $result = $this->liquidatePosition($position, $currentPrice, $riskInfo);
            $results[] = $result;

            if ($result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }

        $this->logger->info('批量强平执行完成', [
            'total' => count($liquidationTasks),
            'success' => $successCount,
            'failure' => $failureCount
        ]);

        return [
            'total' => count($liquidationTasks),
            'success_count' => $successCount,
            'failure_count' => $failureCount,
            'results' => $results
        ];
    }

    /**
     * 处理强平订单成交回调
     *
     * @param int $orderId 订单ID
     * @param float $filledQuantity 成交数量
     * @param float $filledPrice 成交价格
     * @return bool
     */
    public function handleLiquidationOrderFilled(int $orderId, float $filledQuantity, float $filledPrice): bool
    {
        try {
            return Db::transaction(function () use ($orderId, $filledQuantity, $filledPrice) {
                // 1. 获取强平订单信息
                $order = MatchOrder::find($orderId);
                if (!$order) {
                    throw new BusinessException(ResultCode::FAIL, '强平订单不存在');
                }

                // 2. 获取对应的强平记录
                $liquidationRecord = MarginLiquidationRecord::where('liquidation_order_id', $orderId)->first();
                if (!$liquidationRecord) {
                    throw new BusinessException(ResultCode::FAIL, '强平记录不存在');
                }

                // 3. 更新强平记录
                $this->updateLiquidationRecord($liquidationRecord, $filledQuantity, $filledPrice);

                // 4. 获取仓位信息
                $position = TradeMarginPosition::find($liquidationRecord->getPositionId());
                if (!$position) {
                    throw new BusinessException(ResultCode::FAIL, '仓位不存在');
                }

                // 5. 更新仓位
                $this->updatePositionAfterLiquidation($position, $filledQuantity);

                // 6. 计算强平收益和费用
                $liquidatedValue = $filledQuantity * $filledPrice;
                $liquidationFee = $this->calculateLiquidationFee($liquidatedValue);

                // 7. 扣除强平费用
                $this->deductLiquidationFee($position->getUserId(), $liquidationFee, $position->getCurrencyId());

                // 8. 检查是否完全强平
                if ($position->getQuantity() <= 0) {
                    // 完全强平，关闭仓位
                    $this->closePosition($position);

                    // 9. 处理强平后的资产清算和借贷偿还
                    $this->processLiquidationSettlement($position, $liquidatedValue, $liquidationFee);

                    $liquidationRecord->setStatus(LiquidationStatus::COMPLETED);
                    $liquidationRecord->setCompletedTime(Carbon::now());
                    $liquidationRecord->setLiquidationFee($liquidationFee);

                    // 发送强平完成通知
                    $this->notificationService->sendLiquidationNotification(
                        $position->getUserId(),
                        'liquidation_completed',
                        [
                            'position_id' => $position->getId(),
                            'liquidated_quantity' => $filledQuantity,
                            'liquidation_price' => $filledPrice,
                            'liquidation_fee' => $liquidationFee,
                        ]
                    );
                } else {
                    // 部分强平（按要求应该是全部强平，这里保留逻辑）
                    $liquidationRecord->setStatus(LiquidationStatus::EXECUTING);
                    $liquidationRecord->setLiquidationFee($liquidationFee);

                    // 发送部分强平通知
                    $this->notificationService->sendLiquidationNotification(
                        $position->getUserId(),
                        'liquidation_partial',
                        [
                            'position_id' => $position->getId(),
                            'liquidated_quantity' => $filledQuantity,
                            'remaining_quantity' => $position->getQuantity(),
                            'liquidation_price' => $filledPrice,
                            'liquidation_fee' => $liquidationFee,
                        ]
                    );
                }

                $liquidationRecord->save();

                $this->logger->info('强平订单成交处理完成', [
                    'order_id' => $orderId,
                    'position_id' => $position->getId(),
                    'filled_quantity' => $filledQuantity,
                    'filled_price' => $filledPrice,
                    'remaining_quantity' => $position->getQuantity()
                ]);

                return true;
            });

        } catch (\Exception $e) {
            $this->logger->error('强平订单成交处理失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理强平失败
     *
     * @param int $positionId 仓位ID
     * @param string $reason 失败原因
     * @return bool
     */
    public function handleLiquidationFailure(int $positionId, string $reason): bool
    {
        try {
            return Db::transaction(function () use ($positionId, $reason) {
                // 1. 获取仓位
                $position = TradeMarginPosition::find($positionId);
                if (!$position) {
                    return false;
                }

                // 2. 获取强平记录
                $liquidationRecord = MarginLiquidationRecord::where('position_id', $positionId)
                    ->where('status', LiquidationStatus::EXECUTING)
                    ->first();

                if ($liquidationRecord) {
                    // 3. 更新强平记录状态
                    $liquidationRecord->setStatus(LiquidationStatus::FAILED);
                    $liquidationRecord->remark = $reason;
                    $liquidationRecord->save();
                }

                // 4. 重置仓位强平状态
                $this->updatePositionLiquidationStatus($position, false);

                // 5. 发送强平失败通知
                $this->notificationService->sendLiquidationNotification(
                    $position->getUserId(),
                    'liquidation_failed',
                    [
                        'position_id' => $positionId,
                        'reason' => $reason
                    ]
                );

                $this->logger->warning('强平失败处理完成', [
                    'position_id' => $positionId,
                    'reason' => $reason
                ]);

                return true;
            });

        } catch (\Exception $e) {
            $this->logger->error('强平失败处理异常', [
                'position_id' => $positionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 检查仓位是否在强平中
     *
     * @param int $positionId
     * @return bool
     */
    private function isPositionLiquidating(int $positionId): bool
    {
        return MarginLiquidationRecord::where('position_id', $positionId)
            ->where('status', LiquidationStatus::EXECUTING)
            ->exists();
    }

    /**
     * 创建强平记录
     *
     * @param TradeMarginPosition $position
     * @param float $currentPrice
     * @param array $riskInfo
     * @return MarginLiquidationRecord
     */
    private function createLiquidationRecord(TradeMarginPosition $position, float $currentPrice, array $riskInfo): MarginLiquidationRecord
    {
        $record = new MarginLiquidationRecord();
        $record->setUserId($position->getUserId());
        $record->setPositionId($position->getId());
        $record->setCurrencyId($position->getCurrencyId());
        $record->setSide($position->getSide()->value);
        $record->setAccountType($position->getMarginType() === MarginType::CROSS ? AccountType::MARGIN->value : AccountType::ISOLATED->value);
        $record->setLiquidationType(LiquidationType::MARGIN_INSUFFICIENT->value);
        $record->setTriggerSource(TriggerSource::SYSTEM->value);
        $record->setOriginalQuantity($position->getQuantity());
        $record->setTriggerTime(Carbon::now());
        $record->setMarkPrice($currentPrice);
        $record->setLiquidationPrice($riskInfo['liquidation_price'] ?? $currentPrice);
        $record->setMarginRatio($riskInfo['margin_ratio'] ?? 0);
        $record->setStatus(LiquidationStatus::EXECUTING->value);
        $record->save();

        return $record;
    }

    /**
     * 创建强平订单
     *
     * @param TradeMarginPosition $position
     * @param float $currentPrice
     * @return MatchOrder
     */
    private function createLiquidationOrder(TradeMarginPosition $position, float $currentPrice): MatchOrder
    {
        $userId = $position->getUserId();
        $currencyId = $position->getCurrencyId();
        $quantity = $position->getQuantity();
        $accountType = $position->getMarginType() === MarginType::CROSS ? AccountType::MARGIN->value : AccountType::ISOLATED->value;

        // 1. 冻结资产
        $this->freezeAssetForLiquidation($userId, $currencyId, $quantity, $currentPrice, $position->getSide(), $accountType);

        // 2. 创建杠杆订单记录
        $marginOrder = new TradeMarginOrder();
        $marginOrder->setUserId($userId);
        $marginOrder->setCurrencyId($currencyId);
        $marginOrder->setPositionSide($position->getSide());
        $marginOrder->setMarginType($position->getMarginType());
        $marginOrder->setLeverage($position->getLeverage());
        $marginOrder->setReduceOnly(\App\Model\Enums\Trade\Margin\ReduceOnly::TRUE); // 强平订单只减仓
        $marginOrder->setStatus(MarginOrderStatus::PENDING);
        $marginOrder->save();

        // 3. 创建撮合引擎订单
        $matchOrder = new MatchOrder();
        $matchOrder->setUserId($position->getUserId());
        $matchOrder->setCurrencyId($position->getCurrencyId());
        $matchOrder->order_type = 1; // 1 = 市价单，使用属性访问
        $matchOrder->setSide($position->getSide() === PositionSide::LONG ? -1 : 1); // 1买-1卖
        $matchOrder->quantity = $position->getQuantity(); // 使用属性访问
        $matchOrder->price = $currentPrice; // 使用属性访问
        $matchOrder->setMarketType(MarketType::CRYPTO->value);
        $matchOrder->setStatus(OrderStatus::PENDING->value);
        // 移除不存在的setRelatedId方法调用
        $matchOrder->save();

        // 4. 更新杠杆订单的撮合订单ID
        $marginOrder->setMatchOrderId($matchOrder->getId());
        $marginOrder->save();

        // 5. 提交到撮合引擎
        $symbol = $this->getCurrencySymbol($position->getCurrencyId());
        $engineOrderData = [
            'symbol' => $symbol,
            'order_id' => $matchOrder->getOrderId(),
            'user_id' => $position->getUserId(),
            'side' => $position->getSide() === PositionSide::LONG ? 'sell' : 'buy',
            'type' => 'market',
            'quantity' => (string)$position->getQuantity(),
            'price' => (string)$currentPrice,
            'timestamp' => time(),
            'is_liquidation' => true,
        ];

        $messageId = $this->matchEngineService->addOrder(
            MarketType::CRYPTO->value,
            $symbol,
            $position->getUserId(),
            $engineOrderData
        );

        if (!$messageId) {
            throw new BusinessException(ResultCode::FAIL, '提交强平订单到撮合引擎失败');
        }

        $this->logger->info('强平订单创建成功', [
            'position_id' => $position->getId(),
            'margin_order_id' => $marginOrder->getId(),
            'match_order_id' => $matchOrder->getId(),
            'engine_message_id' => $messageId
        ]);

        return $matchOrder;
    }

    /**
     * 冻结强平订单所需资产
     *
     * @param int $userId 用户ID
     * @param int $currencyId 币种ID
     * @param float $quantity 仓位数量
     * @param float $currentPrice 当前价格
     * @param PositionSide $side 仓位方向
     * @param int $accountType 账户类型
     */
    private function freezeAssetForLiquidation(int $userId, int $currencyId, float $quantity, float $currentPrice, PositionSide $side, int $accountType): void
    {
        if ($side === PositionSide::LONG) {
            // 多头强平（卖出）：冻结基础币数量
            $this->assetService->freezeAsset(
                $userId,
                $accountType,
                $currencyId, // 基础币（如BTC）
                $quantity,   // 冻结数量
                FlowsType::MARGIN_TRADE->value,
                0 // 关联ID
            );

            $this->logger->info('强平冻结基础币资产', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'quantity' => $quantity,
                'side' => 'LONG'
            ]);

        } else {
            // 空头强平（买入）：冻结计价币（USDT）
            $quoteCurrencyId = 1; // USDT的ID，实际应该从配置获取
            $requiredQuoteAmount = $quantity * $currentPrice; // 买入所需的USDT

            $this->assetService->freezeAsset(
                $userId,
                $accountType,
                $quoteCurrencyId, // 计价币（USDT）
                $requiredQuoteAmount, // 冻结金额
                FlowsType::MARGIN_TRADE->value,
                0 // 关联ID
            );

            $this->logger->info('强平冻结计价币资产', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'quote_currency_id' => $quoteCurrencyId,
                'required_amount' => $requiredQuoteAmount,
                'side' => 'SHORT'
            ]);
        }
    }

    /**
     * 获取币种交易对符号
     *
     * @param int $currencyId
     * @return string
     */
    private function getCurrencySymbol(int $currencyId): string
    {
        // 这里应该从配置或数据库获取，暂时硬编码
        // 实际应该根据currency_id查询对应的交易对符号
        return 'BTCUSDT'; // 示例
    }

    /**
     * 更新仓位强平状态
     *
     * @param TradeMarginPosition $position
     * @param bool $isLiquidating
     */
    private function updatePositionLiquidationStatus(TradeMarginPosition $position, bool $isLiquidating): void
    {
        // 注意：TradeMarginPosition模型中没有is_liquidating字段
        // 这里可以通过状态来判断是否在强平中
        // 如果需要is_liquidating字段，需要在数据库表中添加该字段并更新模型
        if ($isLiquidating) {
            // 可以设置一个临时状态或者使用其他方式标记
            // $position->setStatus(MarginPositionStatus::LIQUIDATED);
        }
        $position->save();
    }

    /**
     * 更新强平记录
     *
     * @param MarginLiquidationRecord $record
     * @param float $filledQuantity
     * @param float $filledPrice
     */
    private function updateLiquidationRecord(MarginLiquidationRecord $record, float $filledQuantity, float $filledPrice): void
    {
        $currentLiquidated = $record->getLiquidatedQuantity() ?? 0;
        $record->setLiquidatedQuantity($currentLiquidated + $filledQuantity);
        $record->liquidation_price = $filledPrice; // 使用属性访问，因为没有对应的setter方法
        $record->setUpdatedAt(Carbon::now());
    }

    /**
     * 更新仓位（强平后）
     *
     * @param TradeMarginPosition $position
     * @param float $liquidatedQuantity
     */
    private function updatePositionAfterLiquidation(TradeMarginPosition $position, float $liquidatedQuantity): void
    {
        $remainingQuantity = $position->getQuantity() - $liquidatedQuantity;
        $position->quantity = max(0, $remainingQuantity); // 使用属性访问
        $position->save();
    }

    /**
     * 关闭仓位
     *
     * @param TradeMarginPosition $position
     */
    private function closePosition(TradeMarginPosition $position): void
    {
        $position->quantity = 0; // 使用属性访问
        $position->setStatus(MarginPositionStatus::LIQUIDATED); // 强制平仓
        $position->save();
    }

    /**
     * 计算强平费用
     *
     * @param float $liquidatedValue 强平价值
     * @return float
     */
    private function calculateLiquidationFee(float $liquidatedValue): float
    {
        // 强平费率 0.5%
        $liquidationFeeRate = 0.005;
        return $liquidatedValue * $liquidationFeeRate;
    }

    /**
     * 扣除强平费用
     *
     * @param int $userId 用户ID
     * @param float $liquidationFee 强平费用
     * @param int $currencyId 币种ID
     */
    private function deductLiquidationFee(int $userId, float $liquidationFee, int $currencyId): void
    {
        // 从用户杠杆账户扣除强平费用（使用USDT）
        $quoteCurrencyId = 1; // USDT的ID，实际应该从配置获取

        $this->assetService->deductAvailableAsset(
            $userId,
            AccountType::MARGIN->value,
            $quoteCurrencyId,
            $liquidationFee,
            FlowsType::MARGIN_TRADE->value, // 使用杠杆交易流水类型
            0 // 关联ID
        );

        $this->logger->info('扣除强平费用', [
            'user_id' => $userId,
            'currency_id' => $currencyId,
            'liquidation_fee' => $liquidationFee
        ]);
    }

    /**
     * 处理强平后的资产清算和借贷偿还
     *
     * @param TradeMarginPosition $position 仓位
     * @param float $liquidatedValue 强平价值
     * @param float $liquidationFee 强平费用
     */
    private function processLiquidationSettlement(TradeMarginPosition $position, float $liquidatedValue, float $liquidationFee): void
    {
        $userId = $position->getUserId();
        $currencyId = $position->getCurrencyId();
        $marginType = $position->getMarginType();

        // 1. 获取该用户该币种的借贷记录
        $accountType = $marginType === MarginType::CROSS ? AccountType::MARGIN : AccountType::ISOLATED;

        $borrowRecords = UserMarginBorrow::where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->where('account_type', $accountType->value)
            ->where('status', MarginBorrowStatus::ACTIVE)
            ->orderBy('borrow_time', 'asc') // 先进先出
            ->get();

        if ($borrowRecords->isEmpty()) {
            $this->logger->info('无需偿还借贷', [
                'user_id' => $userId,
                'currency_id' => $currencyId,
                'margin_type' => $marginType->value
            ]);
            return;
        }

        // 2. 计算可用于偿还的金额（强平收益 - 强平费用）
        $availableForRepayment = $liquidatedValue - $liquidationFee;

        if ($availableForRepayment <= 0) {
            $this->logger->warning('强平收益不足以支付费用', [
                'user_id' => $userId,
                'liquidated_value' => $liquidatedValue,
                'liquidation_fee' => $liquidationFee
            ]);
            return;
        }

        // 3. 逐笔偿还借贷
        $totalRepaid = 0;
        foreach ($borrowRecords as $borrowRecord) {
            if ($availableForRepayment <= 0) {
                break;
            }

            $outstandingAmount = $borrowRecord->getBorrowAmount() - $borrowRecord->getRepaidAmount();
            $repayAmount = min($availableForRepayment, $outstandingAmount);

            if ($repayAmount > 0) {
                // 更新借贷记录
                $borrowRecord->setRepaidAmount($borrowRecord->getRepaidAmount() + $repayAmount);
                $borrowRecord->setRepayTime(Carbon::now());

                if ($borrowRecord->getRepaidAmount() >= $borrowRecord->getBorrowAmount()) {
                    $borrowRecord->setStatus(MarginBorrowStatus::REPAID);
                }

                $borrowRecord->save();

                $availableForRepayment -= $repayAmount;
                $totalRepaid += $repayAmount;

                $this->logger->info('自动偿还借贷', [
                    'user_id' => $userId,
                    'borrow_id' => $borrowRecord->getId(),
                    'repay_amount' => $repayAmount,
                    'remaining_debt' => $borrowRecord->getBorrowAmount() - $borrowRecord->getRepaidAmount()
                ]);
            }
        }

        $this->logger->info('强平后借贷偿还完成', [
            'user_id' => $userId,
            'currency_id' => $currencyId,
            'total_repaid' => $totalRepaid,
            'remaining_settlement' => $availableForRepayment
        ]);
    }
} 