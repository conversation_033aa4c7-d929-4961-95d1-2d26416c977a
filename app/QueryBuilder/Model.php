<?php

declare(strict_types=1);

namespace App\QueryBuilder;

use App\QueryBuilder\Trait\TimeScope;

class Model extends \Hyperf\DbConnection\Model\Model
{
    use TimeScope;

    public static function new()
    {
        return new static();
    }

    public static function getTableName()
    {
        return with(new static)->getTable();
    }

    public static function getMorphClassName()
    {
        return with(new static)->getMorphClass();
    }

    public function except($keys)
    {
        $keys = is_array($keys) ? $keys : func_get_args();
        $attributes = array_diff(array_keys($this->attributes), $keys);
        return $this->only($attributes);
    }

    // protected function boot(): void
    // {
    //     parent::boot();
    // }
}
