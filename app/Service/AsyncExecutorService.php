<?php

/**
 * AsyncExecutorService.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/24
 * Website:algoquant.org
 */

namespace App\Service;

use App\Job\AsyncFunExecutorJob;
use Hyperf\AsyncQueue\Driver\DriverFactory;
use Hyperf\AsyncQueue\Driver\DriverInterface;
use Psr\Container\ContainerInterface;

class AsyncExecutorService
{
    private DriverInterface $driver;

    public function __construct(ContainerInterface $container)
    {
        $driverFactory = $container->get(DriverFactory::class);
        $this->driver = $driverFactory->get('async-func-executor');
    }

    public function execute(string $className, string $methodName, array $parameters = [], int $delay = 0): string
    {
        $jobId = uniqid('async_job_', true);
        $job = new AsyncFunExecutorJob($className, $methodName, $parameters, $jobId);
        
        $this->driver->push($job, $delay);
        
        return $jobId;
    }

    public function executeStatic(string $className, string $methodName, array $parameters = [], int $delay = 0): string
    {
        return $this->execute($className, $methodName, $parameters, $delay);
    }

    public function executeService(string $serviceName, string $methodName, array $parameters = [], int $delay = 0): string
    {
        $serviceClass = "App\\Service\\{$serviceName}";
        return $this->execute($serviceClass, $methodName, $parameters, $delay);
    }

    public function executeController(string $controllerName, string $methodName, array $parameters = [], int $delay = 0): string
    {
        $controllerClass = "App\\Http\\Admin\\Controller\\{$controllerName}";
        return $this->execute($controllerClass, $methodName, $parameters, $delay);
    }

    public function executeCommand(string $commandName, string $methodName = 'handle', array $parameters = [], int $delay = 0): string
    {
        $commandClass = "App\\Command\\{$commandName}";
        return $this->execute($commandClass, $methodName, $parameters, $delay);
    }
}
