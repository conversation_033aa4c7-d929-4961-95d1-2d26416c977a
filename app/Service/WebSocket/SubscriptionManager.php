<?php

/**
 * SubscriptionManager.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/24
 * Website:algoquant.org
 */

namespace App\Service\WebSocket;

use App\Exception\BusinessException;
use App\Http\Api\Service\User\UserService;
use App\Http\Common\ResultCode;
use App\Model\User\User;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use <PERSON><PERSON>bucci\JWT\Token\RegisteredClaims;
use Mine\Jwt\Factory;
use Mine\Jwt\JwtInterface;
use Psr\Container\ContainerInterface;

class SubscriptionManager
{
    #[Inject]
    private Redis $redis;

    #[Inject]
    private UserService $userService;

    #[Inject]
    private Factory $jwtFactory;

    // 连接数量缓存
    private int $cachedConnectionCount = 0;
    private int $lastCountTime = 0;
    private const COUNT_CACHE_TTL = 30; // 缓存30秒
    
    /**
     * 用户连接映射过期时间（秒）- 5分钟
     */
    private const USER_CONNECTION_TTL = 300;

    public function subscribeTicker(int $fd, int $marketType): bool
    {
        try {
            $key = "ws:ticker:{$marketType}";
            $result = $this->redis->sAdd($key, $fd);

            $this->updateConnectionState($fd, "ticker_subscribed_{$marketType}", 1);
            return $result !== false;
        } catch (\RedisException $e) {
            var_dump("Redis error in subscribeTicker: " . $e->getMessage());
            return false;
        } catch (\Throwable $e) {
            var_dump("Unexpected error in subscribeTicker: " . $e->getMessage());
            return false;
        }
    }

    public function unsubscribeTicker(int $fd, int $marketType): bool
    {
        $key = "ws:ticker:{$marketType}";
        $result = $this->redis->sRem($key, $fd);

        $this->updateConnectionState($fd, "ticker_subscribed_{$marketType}", null);

        return $result !== false;
    }

    public function subscribeDepth(int $fd, int $marketType, string $symbol): bool
    {
        $oldSymbol = $this->getConnectionState($fd, "depth_{$marketType}");
        if ($oldSymbol && $oldSymbol !== $symbol) {
            $this->unsubscribeDepth($fd, $marketType, $oldSymbol);
        }

        $key = "ws:depth:{$marketType}:{$symbol}";
        $result = $this->redis->sAdd($key, $fd);

        $this->updateConnectionState($fd, "depth_{$marketType}", $symbol);

        return $result !== false;
    }

    public function unsubscribeDepth(int $fd, int $marketType, string $symbol): bool
    {
        $key = "ws:depth:{$marketType}:{$symbol}";
        $result = $this->redis->sRem($key, $fd);

        $this->updateConnectionState($fd, "depth_{$marketType}", null);

        $this->cleanupEmptyGroup($key);

        return $result !== false;
    }

    public function subscribeTrade(int $fd, int $marketType, string $symbol): bool
    {
        $oldSymbol = $this->getConnectionState($fd, "trade_{$marketType}");
        if ($oldSymbol && $oldSymbol !== $symbol) {
            $this->unsubscribeTrade($fd, $marketType, $oldSymbol);
        }

        $key = "ws:trade:{$marketType}:{$symbol}";
        $result = $this->redis->sAdd($key, $fd);

        $this->updateConnectionState($fd, "trade_{$marketType}", $symbol);

        return $result !== false;
    }

    public function unsubscribeTrade(int $fd, int $marketType, string $symbol): bool
    {
        $key = "ws:trade:{$marketType}:{$symbol}";
        $result = $this->redis->sRem($key, $fd);
        
        $this->updateConnectionState($fd, "trade_{$marketType}", null);
        
        $this->cleanupEmptyGroup($key);
        
        return $result !== false;
    }

    public function getTickerSubscribers(int $marketType): array
    {
        $key = "ws:ticker:{$marketType}";
        return $this->redis->sMembers($key) ?: [];
    }

    public function getDepthSubscribers(int $marketType, string $symbol): array
    {
        $key = "ws:depth:{$marketType}:{$symbol}";
        return $this->redis->sMembers($key) ?: [];
    }

    public function getTradeSubscribers(int $marketType, string $symbol): array
    {
        $key = "ws:trade:{$marketType}:{$symbol}";
        return $this->redis->sMembers($key) ?: [];
    }

    public function cleanupConnection(int $fd): void
    {
        $connectionKey = "ws:connection:{$fd}";
        $connectionData = $this->redis->hGetAll($connectionKey);
        
        if (empty($connectionData)) {
            return;
        }

        foreach ($connectionData as $field => $value) {
            if (str_starts_with($field, 'ticker_subscribed_')) {
                $marketType = str_replace('ticker_subscribed_', '', $field);
                if ($value) {
                    $this->unsubscribeTicker($fd, (int)$marketType);
                }
            } elseif (str_starts_with($field, 'depth_')) {
                $marketType = str_replace('depth_', '', $field);
                if ($value) {
                    $this->unsubscribeDepth($fd, (int)$marketType, $value);
                }
            } elseif (str_starts_with($field, 'trade_')) {
                $marketType = str_replace('trade_', '', $field);
                if ($value) {
                    $this->unsubscribeTrade($fd, (int)$marketType, $value);
                }
            }
        }

        // 清理用户连接映射
        $this->removeUserConnection($fd);
        
        $this->redis->del($connectionKey);
    }

    private function updateConnectionState(int $fd, string $field, mixed $value): void
    {
        $connectionKey = "ws:connection:{$fd}";

        if ($value === null) {
            $this->redis->hDel($connectionKey, $field);
        } else {
            $this->redis->hSet($connectionKey, $field, $value);
        }

        $this->redis->hSet($connectionKey, 'last_active', time());
        $this->redis->expire($connectionKey, 3600);
    }

    private function getConnectionState(int $fd, string $field): mixed
    {
        $connectionKey = "ws:connection:{$fd}";
        return $this->redis->hGet($connectionKey, $field);
    }

    private function cleanupEmptyGroup(string $key): void
    {
        if ($this->redis->sCard($key) === 0) {
            $this->redis->del($key);
        }
    }

    public function updateConnectionActivity(int $fd): void
    {
        $connectionKey = "ws:connection:{$fd}";
        $heartbeatKey = "ws:heartbeat:{$fd}";

        // 更新连接状态，设置较长的过期时间用于数据保存
        $this->redis->hSet($connectionKey, 'last_active', time());
        $this->redis->expire($connectionKey, 3600);

        // 设置心跳标记，90秒后自动过期，用于检测非活跃连接（30秒心跳间隔 * 3）
        $this->redis->setex($heartbeatKey, 90, time());
        
        // 延长用户连接映射的过期时间
        $this->extendUserConnectionTTL($fd);
    }

    public function cleanupInactiveConnections(): int
    {
        // 使用Redis过期机制，通过检查心跳键是否存在来判断连接活跃状态
        // 实际的清理工作由Redis键过期事件触发或在连接操作时检查

        // 这里可以实现一个轻量级的检查，但主要清理工作应该在其他地方进行
        // 比如在订阅操作时检查连接是否仍然活跃
        return $this->cleanupExpiredConnections();
    }

    /**
     * 清理已过期的连接（心跳键不存在的连接）
     */
    private function cleanupExpiredConnections(): int
    {
        $cleaned = 0;
        $cursor = 0;
        $pattern = "ws:connection:*";

        try {
            // 使用SCAN代替KEYS，避免阻塞Redis
            do {
                $result = $this->redis->scan($cursor, $pattern, 50);

                if ($result !== false && is_array($result) && count($result) >= 2) {
                    [$cursor, $keys] = $result;

                    if (is_array($keys)) {
                        foreach ($keys as $connectionKey) {
                            $fd = str_replace('ws:connection:', '', $connectionKey);
                            if (is_numeric($fd)) {
                                $heartbeatKey = "ws:heartbeat:{$fd}";

                                // 检查心跳键是否存在，不存在说明连接已过期
                                if (!$this->redis->exists($heartbeatKey)) {
                                    $this->cleanupConnection((int)$fd);
                                    $cleaned++;
                                }
                            }
                        }
                    }
                } else {
                    break; // 如果结果格式不正确，停止扫描
                }
            } while ($cursor !== 0 && $cleaned < 100); // 限制单次清理数量
        } catch (\Throwable $e) {
            // 记录错误但不抛出异常，避免影响主流程
            error_log("Redis scan error in cleanupExpiredConnections: " . $e->getMessage());
        }

        return $cleaned;
    }

    /**
     * 检查连接是否仍然活跃（通过心跳键是否存在判断）
     */
    public function isConnectionActive(int $fd): bool
    {
        $heartbeatKey = "ws:heartbeat:{$fd}";
        return $this->redis->exists($heartbeatKey) > 0;
    }

    /**
     * 获取活跃连接数量（带缓存优化）
     */
    public function getActiveConnectionsCount(): int
    {
        $now = time();
        
        // 缓存未过期，直接返回
        if ($now - $this->lastCountTime < self::COUNT_CACHE_TTL) {
            return $this->cachedConnectionCount;
        }
        
        // 缓存过期，重新统计
        $this->cachedConnectionCount = $this->scanActiveConnections();
        $this->lastCountTime = $now;
        
        return $this->cachedConnectionCount;
    }
    
    /**
     * 强制刷新连接数量统计
     */
    public function refreshConnectionCount(): int
    {
        $this->cachedConnectionCount = $this->scanActiveConnections();
        $this->lastCountTime = time();
        
        return $this->cachedConnectionCount;
    }
    
    /**
     * 扫描活跃连接数量
     */
    private function scanActiveConnections(): int
    {
        $pattern = "ws:heartbeat:*";
        $count = 0;
        $cursor = 0;
        $maxScans = 150; // 限制最大扫描次数，避免超时
        $scanCount = 0;

        try {
            do {
                // 针对1万连接优化：调整COUNT为200，减少网络往返次数
                $result = $this->redis->scan($cursor, $pattern, 200);

                if ($result !== false && is_array($result) && count($result) >= 2) {
                    [$cursor, $keys] = $result;
                    if (is_array($keys)) {
                        $count += count($keys);
                    }
                } else {
                    break;
                }
                
                $scanCount++;
            } while ($cursor !== 0 && $scanCount < $maxScans);
        } catch (\Throwable $e) {
            error_log("Redis scan error in scanActiveConnections: " . $e->getMessage());
        }

        return $count;
    }

    /**
     * 获取连接统计信息
     */
    public function getConnectionStats(): array
    {
        $activeCount = $this->getActiveConnectionsCount();
        $totalConnectionKeys = $this->countConnectionKeys();
        
        return [
            'active_connections' => $activeCount,
            'total_connection_keys' => $totalConnectionKeys,
            'expired_connections' => $totalConnectionKeys - $activeCount,
            'cache_age' => time() - $this->lastCountTime,
            'cache_ttl_remaining' => max(0, self::COUNT_CACHE_TTL - (time() - $this->lastCountTime))
        ];
    }
    
    /**
     * 快速统计连接键数量（用于对比）
     */
    private function countConnectionKeys(): int
    {
        $pattern = "ws:connection:*";
        $count = 0;
        $cursor = 0;
        $maxScans = 100; // 更快的检查

        try {
            do {
                $result = $this->redis->scan($cursor, $pattern, 200);

                if ($result !== false && is_array($result) && count($result) >= 2) {
                    [$cursor, $keys] = $result;
                    if (is_array($keys)) {
                        $count += count($keys);
                    }
                } else {
                    break;
                }
            } while ($cursor !== 0 && --$maxScans > 0);
        } catch (\Throwable $e) {
            error_log("Redis scan error in countConnectionKeys: " . $e->getMessage());
        }

        return $count;
    }

    /**
     * 用户认证
     */
    public function authenticateUser(int $fd, array $request): array
    {
        try {
            // 验证必要参数
            $token = $request['token'] ?? '';
            $deviceInfo = $request['X-Device-Info'] ?? '';

            if (empty($token)) {
                return ['success' => false, 'message' => 'Token is required'];
            }

            if (empty($deviceInfo)) {
                return ['success' => false, 'message' => 'X-Device-Info is required'];
            }

            // 解析并验证token
            $jwt = $this->getJwt();
            $parsedToken = $jwt->parserAccessToken($this->extractToken($token));

            // 检查token有效性
            $expireAt = $parsedToken->claims()->get(RegisteredClaims::EXPIRATION_TIME)->getTimestamp();
            if ($expireAt < time()) {
                $jwt->addBlackList($parsedToken);
                return ['success' => false, 'message' => 'Token has expired'];
            }

            // 检查token是否在黑名单中
            if ($jwt->hasBlackList($parsedToken)) {
                return ['success' => false, 'message' => 'Token is invalid'];
            }

            $userId = (int) $parsedToken->claims()->get(RegisteredClaims::ID);

            // 检查 Redis 中的登录信息
            $tokenCache = $this->userService->getTokenCache($userId, $deviceInfo);
            if (!$tokenCache || $tokenCache !== $parsedToken->toString()) {
                $jwt->addBlackList($parsedToken);
                return ['success' => false, 'message' => 'Authentication failed'];
            }

            // 获取用户信息
            $user = $this->userService->getUserCache($userId);
            if (!$user) {
                $user = User::query()->find($userId);
                if (!$user) {
                    return ['success' => false, 'message' => 'User not found'];
                }
                $this->userService->setUserCache($user);
            }

            // 存储用户连接映射
            $this->storeUserConnection($userId, $fd);

            return [
                'success' => true,
                'message' => 'Authentication successful',
                'data' => [
                    'user_id' => $userId,
                    'username' => $user->username,
                    'email' => $user->email
                ]
            ];

        } catch (\Throwable $e) {
            return ['success' => false, 'message' => 'Authentication error: ' . $e->getMessage()];
        }
    }

    /**
     * 存储用户连接映射
     */
    private function storeUserConnection(int $userId, int $fd): void
    {
        // 用户ID到fd的映射
        $userToFdKey = "ws:user:{$userId}:fd";
        $this->redis->setex($userToFdKey, self::USER_CONNECTION_TTL, $fd);

        // fd到用户ID的映射
        $fdToUserKey = "ws:fd:{$fd}:user";
        $this->redis->setex($fdToUserKey, self::USER_CONNECTION_TTL, $userId);

        // 在连接状态中记录用户ID
        $this->updateConnectionState($fd, 'user_id', $userId);
    }

    /**
     * 移除用户连接映射
     */
    private function removeUserConnection(int $fd): void
    {
        // 获取用户ID
        $fdToUserKey = "ws:fd:{$fd}:user";
        $userId = $this->redis->get($fdToUserKey);

        if ($userId) {
            // 删除用户ID到fd的映射
            $userToFdKey = "ws:user:{$userId}:fd";
            $this->redis->del($userToFdKey);
        }

        // 删除fd到用户ID的映射
        $this->redis->del($fdToUserKey);
    }

    /**
     * 延长用户连接映射的过期时间
     */
    private function extendUserConnectionTTL(int $fd): void
    {
        $fdToUserKey = "ws:fd:{$fd}:user";
        $userId = $this->redis->get($fdToUserKey);

        if ($userId) {
            // 延长两个映射的过期时间
            $userToFdKey = "ws:user:{$userId}:fd";
            $this->redis->expire($userToFdKey, self::USER_CONNECTION_TTL);
            $this->redis->expire($fdToUserKey, self::USER_CONNECTION_TTL);
        }
    }

    /**
     * 根据用户ID获取fd
     */
    public function getUserFd(int $userId): ?int
    {
        $userToFdKey = "ws:user:{$userId}:fd";
        $fd = $this->redis->get($userToFdKey);
        return $fd ? (int)$fd : null;
    }

    /**
     * 根据fd获取用户ID
     */
    public function getUserByFd(int $fd): ?int
    {
        $fdToUserKey = "ws:fd:{$fd}:user";
        $userId = $this->redis->get($fdToUserKey);
        return $userId ? (int)$userId : null;
    }

    /**
     * 获取JWT实例
     */
    private function getJwt(): JwtInterface
    {
        return $this->jwtFactory->get('api');
    }

    /**
     * 提取token字符串
     */
    private function extractToken(string $token): string
    {
        if (str_starts_with($token, 'Bearer ')) {
            return substr($token, 7);
        }
        return $token;
    }
}
