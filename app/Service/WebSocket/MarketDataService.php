<?php

/**
 * MarketDataService.php
 * Author:chenmaq (<EMAIL>)
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025/6/24
 * Website:algoquant.org
 */

namespace App\Service\WebSocket;

use Hyperf\Di\Annotation\Inject;
use Hyperf\WebSocketServer\Sender;

class MarketDataService
{
    #[Inject]
    private SubscriptionManager $subscriptionManager;

    #[Inject]
    private Sender $sender;



    /**
     * 推送ticker消息到订阅者（消息已格式化）
     */
    public function pushTickerData(int $marketType, string $message): bool
    {
        $subscribers = $this->subscriptionManager->getTickerSubscribers($marketType);

        if (empty($subscribers) || empty($message)) {
            return true;
        }

        $this->pushToSubscribers($subscribers, $message);
        return true;
    }



    /**
     * 推送depth消息到订阅者（消息已格式化）
     */
    public function pushDepthData(int $marketType, string $symbol, string $message): bool
    {
        $subscribers = $this->subscriptionManager->getDepthSubscribers($marketType, $symbol);

        if (empty($subscribers) || empty($message)) {
            return true;
        }

        $this->pushToSubscribers($subscribers, $message);
        return true;
    }

    /**
     * 推送trade消息到订阅者（消息已格式化）
     */
    public function pushTradeData(int $marketType, string $symbol, string $message): bool
    {
        $subscribers = $this->subscriptionManager->getTradeSubscribers($marketType, $symbol);

        if (empty($subscribers) || empty($message)) {
            return true;
        }

        $this->pushToSubscribers($subscribers, $message);
        return true;
    }


    /**
     * 推送消息给具体用户
     * @param int $user_id 用户ID
     * @param string $message 消息内容
     * @return bool 是否推送成功
     */
    public function pushMessageToUser(int $user_id, string $message): bool
    {
        // 根据用户ID获取对应的fd
        $fd = $this->subscriptionManager->getUserFd($user_id);
        
        if ($fd === null) {
            // 用户未连接或连接已过期
            return false;
        }
        
        // 推送消息给用户
        $this->batchPushMessage([$fd], $message);
        return true;
    }



    /**
     * 推送消息到订阅者（纯推送，不做任何格式化）
     */
    private function pushToSubscribers(array $subscribers, string $message): void
    {
        if (empty($subscribers) || empty($message)) {
            return;
        }

        $this->batchPushMessage($subscribers, $message);
    }

    private function batchPushMessage(array $fds, string $message): void
    {
        $validFds = $this->validateFds($fds);
        if (empty($validFds)) {
            return;
        }

        go(function () use ($validFds, $message) {
            $chunks = array_chunk($validFds, 100);

            foreach ($chunks as $chunk) {
                foreach ($chunk as $fd) {
                    go(function () use ($fd, $message) {
                        try {
                            $this->sender->push($fd, $message, WEBSOCKET_OPCODE_BINARY);
                        } catch (\Throwable $e) {
                            $this->subscriptionManager->cleanupConnection($fd);
                        }
                    });
                }
                \Swoole\Coroutine::sleep(0.001);
            }
        });
    }



    private function validateFds(array $fds): array
    {
        $validFds = [];
        foreach ($fds as $fd) {
            $fdInt = (int)$fd;
            if ($fdInt > 0) {
                $validFds[] = $fdInt;
            }
        }
        return $validFds;
    }


}
