<?php

declare(strict_types=1);

/**
 * 系统配置 service
 */

namespace App\Service;


use App\Http\Api\Request\V2\AppInfoRequest;
use App\Http\Api\Request\V2\SystemConfigRequest;

class SystemConfigService
{
    /**
     * 获取系统配置
     * @param string $config_key 应用标识
     * @return array
     */
    public function configList($config_key)
    {
        $sysmInfo = \Plugin\West\SysSettings\Helper\Helper::getSysSettingType($config_key);
        $config = [];
        if(!empty($sysmInfo) && !empty($sysmInfo['info'])) {
            $config = $sysmInfo['info']->map(function ($value, $key) {
                //只要key和value的值
                $value = $value->only(['key', 'value','name']);
                return $value;
            });
        }
        return $config;
    }

    /**
     * 获取系统配置信息
     * @param string $code 配置标识
     * @return array
     */
    public function configInfo($code)
    {
        $sysmInfo = \Plugin\West\SysSettings\Helper\Helper::getSysSettingByTypeCode($code);
        $config = [];
        if($sysmInfo){
            $config = [
                'key'=> $sysmInfo['key'],
                'value'=> $sysmInfo['value'],
                'name'=> $sysmInfo['name'],
                'input_type'=> $sysmInfo['input_type'],
                'remark'=> $sysmInfo['remark'],
                'config_select_data'=> $sysmInfo['config_select_data'],
            ];
        }
        return $config;
    }
    

}
