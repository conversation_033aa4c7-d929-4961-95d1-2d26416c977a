<?php

declare(strict_types=1);

namespace App\Service;

use GuzzleHttp\Client;
use Hyperf\Cache\Annotation\Cacheable;
use Hyperf\Di\Annotation\Inject;
use App\Logger\LoggerFactory;
use Hyperf\Guzzle\ClientFactory;

/**
 * LibreTranslate 翻译服务
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/24
 */
class TranslationService
{
    #[Inject]
    public LoggerFactory $loggerFactory;

    #[Inject]
    public ClientFactory $clientFactory;
    
    private Client $client;
    private string $baseUrl;
    private ?string $apiKey;
    private bool $enabled;
    
    public function __construct()
    {
        $this->baseUrl = env('LIBRETRANSLATE_URL', 'http://192.168.31.88:8000');
        $this->apiKey = env('LIBRETRANSLATE_API_KEY');
        $this->enabled = env('TRANSLATION_ENABLED', true);
        
        $this->client = $this->clientFactory->create([
            'base_uri' => $this->baseUrl,
            'timeout' => 30,
            'connect_timeout' => 10,
            'verify' => false,
            'http_errors' => false,
            'allow_redirects' => true,
            'headers' => [
                'User-Agent' => 'curl/7.68.0',
                'Accept' => '*/*',
                'Connection' => 'keep-alive',
                'Content-Type' => 'application/x-www-form-urlencoded'
            ],
            'proxy' => false
        ]);
    }
    
    /**
     * 翻译文本
     * 
     * @param string $text 要翻译的文本
     * @param string $from 源语言代码 (默认: en)
     * @param string $to 目标语言代码 (默认: zh)
     * @return string 翻译后的文本
     */
    #[Cacheable(prefix: "translation", ttl: 3600, value: "#{text}_#{from}_#{to}")]
    public function translate(string $text, string $from = 'en', string $to = 'zh'): string
    {
        if (!$this->enabled || empty(trim($text))) {
            return $text;
        }
        
        try {
            $payload = [
                'q' => trim($text),
                'source' => $from,
                'target' => $to,
                'format' => 'text'
            ];
            
            // 如果设置了 API Key，添加到请求中
            if ($this->apiKey) {
                $payload['api_key'] = $this->apiKey;
            }
            
            $response = $this->client->post('/translate', [
                'json' => $payload
            ]);
            
            $result = json_decode($response->getBody()->getContents(), true);
            
            if (isset($result['translatedText'])) {
                $translatedText = trim($result['translatedText']);
                return $translatedText;
            }
            return $text;
            
        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("翻译失败: " . $e->getMessage(), [
                'text' => $text,
                'from' => $from,
                'to' => $to,
                'error' => $e->getMessage()
            ]);
            return $text;
        }
    }
    
    /**
     * 批量翻译文本
     * 
     * @param array $texts 要翻译的文本数组
     * @param string $from 源语言代码
     * @param string $to 目标语言代码
     * @return array 翻译结果数组
     */
    public function batchTranslate(array $texts, string $from = 'en', string $to = 'zh'): array
    {
        if (!$this->enabled) {
            return $texts;
        }
        
        $results = [];
        $batchSize = 10; // 每批处理10个文本
        $batches = array_chunk($texts, $batchSize, true);
        
        foreach ($batches as $batch) {
            foreach ($batch as $key => $text) {
                $results[$key] = $this->translate($text, $from, $to);
                
                // 添加小延迟避免API限制
                if (count($batch) > 1) {
                    usleep(200000); // 0.2秒
                }
            }
            
            // 批次间稍长延迟
            if (count($batches) > 1) {
                sleep(1);
            }
        }
        
        return $results;
    }
    
    /**
     * 检查翻译服务是否可用
     * 
     * @return bool
     */
    public function isAvailable(): bool
    {
        if (!$this->enabled) {
            return false;
        }
        
        try {
            $response = $this->client->get('/languages', [
                'timeout' => 5
            ]);

            $statusCode = $response->getStatusCode();
            $available = $statusCode === 200;

            if ($available) {
                $this->loggerFactory->get(self::class)->debug("翻译服务可用");
            } else {
                $responseBody = $response->getBody()->getContents();
                $this->loggerFactory->get(self::class)->error("翻译服务返回错误", [
                    'status_code' => $statusCode,
                    'response_body' => $responseBody,
                    'url' => $this->baseUrl . '/languages'
                ]);
            }

            return $available;

        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("翻译服务连接失败: " . $e->getMessage(), [
                'url' => $this->baseUrl . '/languages',
                'exception_class' => get_class($e)
            ]);
            return false;
        }
    }
    
    /**
     * 获取支持的语言列表
     * 
     * @return array
     */
    public function getSupportedLanguages(): array
    {
        try {
            $response = $this->client->get('/languages');
            $languages = json_decode($response->getBody()->getContents(), true) ?? [];
            
            $this->loggerFactory->get(self::class)->debug("获取支持语言列表成功", [
                'count' => count($languages)
            ]);
            
            return $languages;
            
        } catch (\Throwable $e) {
            $this->loggerFactory->get(self::class)->error("获取支持语言列表失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 启用翻译服务
     */
    public function enable(): void
    {
        $this->enabled = true;
        $this->loggerFactory->get(self::class)->info("翻译服务已启用");
    }
    
    /**
     * 禁用翻译服务
     */
    public function disable(): void
    {
        $this->enabled = false;
        $this->loggerFactory->get(self::class)->info("翻译服务已禁用");
    }
    
    /**
     * 检查翻译服务是否启用
     * 
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }
    
    /**
     * 翻译加密货币相关文本
     * 专门针对加密货币术语优化的翻译方法
     * 
     * @param string $text
     * @param string $from
     * @param string $to
     * @return string
     */
    public function translateCrypto(string $text, string $from = 'en', string $to = 'zh'): string
    {
        // 预处理：保护加密货币专业术语
        $protectedTerms = [
            'Bitcoin' => 'Bitcoin',
            'BTC' => 'BTC',
            'Ethereum' => 'Ethereum',
            'ETH' => 'ETH',
            'USDT' => 'USDT',
            'DeFi' => 'DeFi',
            'NFT' => 'NFT',
            'DAO' => 'DAO',
            'DEX' => 'DEX',
            'CEX' => 'CEX',
        ];
        
        $placeholders = [];
        
        // 替换专业术语为占位符
        foreach ($protectedTerms as $term => $replacement) {
            if (stripos($text, $term) !== false) {
                $placeholder = "CRYPTO_TERM_" . count($placeholders);
                $placeholders[$placeholder] = $replacement;
                $text = str_ireplace($term, $placeholder, $text);
            }
        }
        
        // 翻译处理后的文本
        $translatedText = $this->translate($text, $from, $to);
        
        // 恢复专业术语
        foreach ($placeholders as $placeholder => $originalTerm) {
            $translatedText = str_replace($placeholder, $originalTerm, $translatedText);
        }
        
        return $translatedText;
    }
}
