<?php
/*
/**
 * MatchEngineOrderService.php
 * Author    chenmaq (<EMAIL>)
 * Version   1.0
 * Date      2025/6/30
 * @link     bbbtrade.net
 * @document bbbtrade.net
 */

namespace App\Service;

use App\Enum\AsyncExecutorKey;
use App\Enum\MarketType;
use App\Enum\MatchEngine\ManagerStreamKey;
use App\Enum\MatchEngine\OrderEventMap;
use App\Enum\OrderStatus;
use App\Http\Api\Event\MatchEngineEvent\OrderCancelEvent;
use App\Http\Api\Event\MatchEngineEvent\OrderConfirmEvent;
use App\Http\Api\Event\MatchEngineEvent\OrderFilledEvent;
use App\Http\Api\Event\MatchEngineEvent\OrderModifyEvent;
use App\Http\Api\Event\MatchEngineEvent\OrderModifyFailedEvent;
use App\Http\Api\Event\MatchEngineEvent\OrderPartialFilledEvent;
use App\Http\Api\Event\MatchEngineEvent\OrderTradeEvent;
use App\Job\MatchOrder\MatchOrderFilled;
use App\Model\Match\MatchOrder;
use App\Model\Match\MatchTrade;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\EventDispatcher\EventDispatcherInterface;

class MatchEngineOrderService
{
    #[Inject]
    private Redis $redis;

    #[Inject]
    private EventDispatcherInterface $eventDispatcher;

    /**
     * 投递订单到撮合引擎
     * @param $marketType
     * @param $symbol
     * @param $userId
     * @param array $order
     * @return bool|string|\Redis
     */
    public function addOrder(int $marketType, string $symbol, mixed $userId, array $order): bool|string|\Redis
    {
        try {
            $orderStream = ManagerStreamKey::getOrderStreamByMarket(
                MarketType::getMarketString($marketType)
            );
            // 发送到 Redis Stream
            $messageId = $this->redis->xAdd(
                $orderStream->value,
                '*',
                [
                    'type' => 'order', // 新订单类型，会被转换为 process_order
                    'data' => json_encode($order),
                    'symbol' => $symbol,
                    'user_id' => $userId,
                    'timestamp' => time(),
                ]
            );
            if (!$messageId) {
                return false;
            }
            return $messageId;
        } catch (\Throwable $t) {
            return false;
        }
    }

    public function cancelOrder(int $marketType, string $symbol, mixed $orderId, mixed $userId): bool
    {
        try {
            $orderStream = ManagerStreamKey::getOrderStreamByMarket(
                MarketType::getMarketString($marketType)
            );
            if (!$orderStream) {
                return false;
            }
            // 构造撤单数据
            $cancelData = [
                'symbol' => $symbol,
                'order_id' => $orderId,
                'user_id' => $userId,
                'timestamp' => time(),
            ];

            // 发送到 Redis Stream
            return $this->redis->xAdd(
                $orderStream->value,
                '*',
                [
                    'type' => 'cancel_order',
                    'data' => json_encode($cancelData),
                    'symbol' => $symbol,
                    'user_id' => $userId,
                    'timestamp' => time(),
                ]
            );
        } catch (\Throwable) {
            return false;
        }
    }

    /**
     * 修改撮合引擎中未成交的订单
     * @param int $userId
     * @param int $marketType
     * @param string $symbol
     * @param mixed $orderId
     * @param float $price
     * @param float $quantity
     * @param string $time_in_force
     * @return bool|string|\Redis
     */
    public function modifyOrder(int $userId, int $marketType, string $symbol, mixed $orderId, float $price, float $quantity, string $time_in_force): bool|string|\Redis
    {
        try {
            $orderStream = ManagerStreamKey::getOrderStreamByMarket(
                MarketType::getMarketString($marketType)
            );
            if (!$orderStream) {
                return false;
            }
            // 构造撤单数据
            $modifyOrder = [
                'symbol' => $symbol,
                'order_id' => $orderId,
                'user_id' => $userId,
                'timestamp' => time(),
                'price' => $price,
                'quantity' => $quantity,
                'time_in_force' => $time_in_force,
            ];

            // 发送到 Redis Stream
            return $this->redis->xAdd(
                $orderStream->value,
                '*',
                [
                    'type' => 'modify_order',
                    'data' => json_encode($modifyOrder),
                    'symbol' => $symbol,
                    'user_id' => $userId,
                    'timestamp' => time(),
                ]
            );
        } catch (\Throwable) {
            return false;
        }
    }

    /**
     * 根据撮合订单类型触发事件
     * @param string $event_name
     * @param int $market_type
     * @param mixed $data
     * @return bool
     */
    public function dispatcher(string $event_name, int $market_type, array $data): bool
    {
        try {
            $dir = OrderEventMap::getDirection($market_type);
            $class = "\\App\\Http\\Api\\Event\\{$dir}\\{$event_name}";
            if (!class_exists($class)) {
                return false;
            }
            go(function()use($class,$data){
                $this->eventDispatcher->dispatch(new $class(...$data));
            });
            return true;
        } catch (\Throwable $t) {
            echo "撮合引擎订单事件分发异常：{$t->getMessage()}\n";
            return false;
        }
    }

    /**
     * 撮合引擎订单放置事件回调更新数据状态
     * @param int $order_id
     * @return void
     */
    public function confirmOrderEvent(int $order_id): void
    {
        $result = Db::transaction(function () use ($order_id) {
            /**
             * @var MatchOrder $order
             */
            $order = MatchOrder::query()->where(MatchOrder::FIELD_ORDER_ID, $order_id)->first();
            if (!$order) {
                return false;
            }
            if ($order->setStatus(OrderStatus::PENDING->value)->save()) {
                return $order->getMarketType();
            }
            return false;
        });
        if ($result) {
            $this->dispatcher("OrderConfirmEvent", $result, [$order_id]);
        }
    }

    /**
     * 订单取消更新订单表和发布事件
     * @param array $order
     * @return void
     */
    public function cancelOrderEvent(array $order): void
    {
        $result = Db::transaction(function () use ($order) {
            /**
             * @var MatchOrder $matchOrder
             */
            $matchOrder = MatchOrder::query()->where(MatchOrder::FIELD_ORDER_ID, $order['order_id'])->first();
            if ($matchOrder->status <= OrderStatus::PENDING->value) {
                $matchOrder->status = OrderStatus::CANCELED->value;
                $matchOrder->setreason($order['reason'] ?? '');
                if ($matchOrder->save()) {
                    return $matchOrder->getMarketType();
                }
                return false;
            }
            return false;
        });
        if ($result) {
            $this->dispatcher("OrderCancelEvent", $result, [$order['order_id']]);
        }
    }

    /**
     * 订单修改事件回调
     * @param array $order
     * @return void
     */
    public function orderModifyEvent(array $order): void
    {
        //首先修改撮合订单标的数量和价格 然后再触发事件
        // 触发订单修改事件，让监听器处理资金冻结
        $result = Db::transaction(function () use ($order) {
            /**
             * @var MatchOrder $matchOrder
             */
            $matchOrder = MatchOrder::query()->where(MatchOrder::FIELD_ORDER_ID, $order['order_id'])->first();
            $matchOrder->price = (float)$order['price'];
            $matchOrder->quantity = (float)$order['quantity'];
            $matchOrder->order_force = $order['time_in_force'] ?? 'gtc';
            if ($matchOrder->save()) {
                return $matchOrder->getMarketType();
            }
            return false;
        });
        if ($result) {
            $this->dispatcher("OrderModifyEvent", $result, [$order['order_id'], $order['price'], $order['quantity']]);
        }
    }

    public function orderFilledEvent(array $order): void
    {
        $result = Db::transaction(function () use ($order) {
            /**
             * @var MatchOrder $order
             */
            $matchOrder = MatchOrder::query()->where('order_id', $order['order_id'])->first();
            //完全成交事件可以立即执行数据更新

            $matchOrder->status = OrderStatus::FILLED->value;
            $matchOrder->fill_time = $order['timestamp'];
            $matchOrder->avg_price = $order['avg_price'];
            if ($matchOrder->save()) {
                return $matchOrder->getMarketType();
            }
            return false;
        });
        $this->dispatcher("OrderFilledEvent", $result, [$order['order_id']]);
    }

    public function orderPartialFilledEvent(array $order): void
    {
        $result = Db::transaction(function () use ($order) {
            /**
             * @var MatchOrder $matchOrder
             */
            $matchOrder = MatchOrder::query()
                ->where('order_id', $order['order_id'])
                ->first();
            $matchOrder->setStatus(OrderStatus::PARTIAL_FILLED->value);
            $matchOrder->setFillTime($order['timestamp']);
            if ($matchOrder->save()) {
                return $matchOrder->getMarketType();
            }
            return false;
        });
        if ($result) {
            $this->dispatcher("OrderPartialFilledEvent", $result, [$order['order_id']]);
        }
    }

    public function orderTradeEvent(array $order): void
    {
        $result = Db::transaction(function () use ($order) {
            //记录成交数据，更新主表的成交数量

            /**
             * @var MatchOrder $buy_order
             */
            $buy_order = MatchOrder::query()->where('order_id', $order['buy_order_id'])->first();
            /**
             * @var MatchOrder $sell_order
             */
            $sell_order = MatchOrder::query()->where('order_id', $order['sell_order_id'])->first();

            $trades = [
                'currency_id' => $order['currency_id'],
                'buy_order_id' => $order['buy_order_id'],
                'sell_order_id' => $order['sell_order_id'],
                'buy_user_id' => $buy_order->user_id ?? "0",
                'sell_user_id' => $sell_order->user_id ?? "0",
                'market_type' => $order['market_type'],
                'trade_id' => $order['trade_id'],
                'price' => $order['price'],
                'quantity' => $order['quantity'],
                'is_bot' => $order['is_bot'] ?? 0,
                'match_time' => $order['timestamp']
            ];
            if (MatchTrade::create($trades)->save()) {

                if ($buy_order) {
                    $buy_order->fill_quantity = bcadd(
                        strval($buy_order->fill_quantity),
                        strval($order['quantity']),
                        8
                    );
                    $buy_order->has_trade = 1;
                    $buy_order->save();
                }

                if ($sell_order) {
                    $sell_order->fill_quantity = bcadd(
                        strval($sell_order->fill_quantity),
                        strval($order['quantity']),
                        8
                    );
                    $sell_order->has_trade = 1;
                    $sell_order->save();
                }

                return [$buy_order ? $buy_order->getMarketType() : false, $sell_order ? $sell_order->getMarketType() : false];
            }
            return false;
        });
        if ($result) {
            foreach ($result as $market_type) {
                if ($market_type) {
                    $this->dispatcher("OrderTradeEvent", $market_type, [$order]);
                }
            }
        }
    }

    /**
     * 订单修改失败事件回调
     * @param array $order
     * @return
     */
    public function orderModifyFailedEvent(array $order)
    {
        // 直接触发订单修改失败事件，让监听器处理资金解冻和消息推送
        return $this->eventDispatcher->dispatch(new OrderModifyFailedEvent($order));
    }
}
