<?php

declare(strict_types=1);
/**
 * 策略平台API
 * 委托订单触发服务
 */

namespace App\Service;

use App\Http\Api\Service\V1\TradeSpotCommissionService;
use App\Model\Trade\Enums\CommissionStatus;
use App\Model\Trade\TradeSpotCommission;
use Carbon\Carbon;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Logger\LoggerFactory;
use Hyperf\Redis\Redis;
use Psr\Log\LoggerInterface;

class CommissionTriggerService
{
    #[Inject]
    protected TradeSpotCommissionService $commissionService;

    #[Inject]
    protected Redis $redis;

    protected LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('commission_trigger');
    }

    /**
     * 检查并触发委托订单
     */
    public function checkAndTriggerOrders(?int $currencyId = null): array
    {
        $results = [
            'checked' => 0,
            'triggered' => 0,
            'failed' => 0,
            'details' => []
        ];

        try {
            // 获取等待执行的委托订单
            $pendingOrders = $this->commissionService->getPendingOrders($currencyId);
            $results['checked'] = count($pendingOrders);

            if (empty($pendingOrders)) {
                return $results;
            }

            // 按币种分组处理
            $ordersByCurrency = [];
            foreach ($pendingOrders as $order) {
                $ordersByCurrency[$order['currency_id']][] = $order;
            }

            // 逐个币种处理
            foreach ($ordersByCurrency as $currencyId => $orders) {
                $currentPrice = $this->getCurrentPrice($currencyId);
                
                if (!$currentPrice) {
                    $this->logger->warning("无法获取币种 {$currencyId} 的当前价格");
                    continue;
                }

                // 检查每个委托订单
                foreach ($orders as $orderData) {
                    $result = $this->processSingleOrder($orderData, $currentPrice);
                    
                    if ($result['success']) {
                        $results['triggered']++;
                    } else {
                        $results['failed']++;
                    }
                    
                    $results['details'][] = $result;
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('检查委托订单时发生错误', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $results;
    }

    /**
     * 处理单个委托订单
     */
    protected function processSingleOrder(array $orderData, string $currentPrice): array
    {
        $commissionId = $orderData['id'];
        $triggerPrice = $orderData['trigger_price'];
        $triggerCondition = $orderData['trigger_condition'];
        
        try {
            // 检查触发条件
            $shouldTrigger = $this->checkTriggerCondition($triggerCondition, $currentPrice, $triggerPrice);
            
            if (!$shouldTrigger) {
                return [
                    'commission_id' => $commissionId,
                    'success' => false,
                    'reason' => '触发条件未满足',
                    'current_price' => $currentPrice,
                    'trigger_price' => $triggerPrice
                ];
            }

            // 尝试触发订单
            $triggered = $this->commissionService->triggerOrder($commissionId, $currentPrice);
            
            if ($triggered) {
                $this->logger->info("委托订单触发成功", [
                    'commission_id' => $commissionId,
                    'current_price' => $currentPrice,
                    'trigger_price' => $triggerPrice
                ]);
                
                return [
                    'commission_id' => $commissionId,
                    'success' => true,
                    'reason' => '触发成功',
                    'current_price' => $currentPrice,
                    'trigger_price' => $triggerPrice
                ];
            } else {
                return [
                    'commission_id' => $commissionId,
                    'success' => false,
                    'reason' => '触发失败',
                    'current_price' => $currentPrice,
                    'trigger_price' => $triggerPrice
                ];
            }

        } catch (\Exception $e) {
            $this->logger->error("处理委托订单时发生错误", [
                'commission_id' => $commissionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'commission_id' => $commissionId,
                'success' => false,
                'reason' => '处理异常：' . $e->getMessage(),
                'current_price' => $currentPrice,
                'trigger_price' => $triggerPrice
            ];
        }
    }

    /**
     * 检查触发条件
     */
    protected function checkTriggerCondition(int $triggerCondition, string $currentPrice, string $triggerPrice): bool
    {
        switch ($triggerCondition) {
            case 1: // 大于等于
                return bccomp($currentPrice, $triggerPrice, 8) >= 0;
            case 2: // 小于等于
                return bccomp($currentPrice, $triggerPrice, 8) <= 0;
            default:
                return false;
        }
    }

    /**
     * 获取币种当前价格
     */
    protected function getCurrentPrice(int $currencyId): ?string
    {
        try {
            // 从Redis获取当前价格
            // 这里需要根据实际的Redis键名格式调整
            $priceKey = "market:price:{$currencyId}";
            $priceData = $this->redis->get($priceKey);
            
            if ($priceData) {
                $priceInfo = json_decode($priceData, true);
                return $priceInfo['price'] ?? null;
            }

            // 如果没有缓存价格，可以尝试从其他数据源获取
            // 比如从ticker数据中获取
            $tickerKey = "market:ticker:{$currencyId}";
            $tickerData = $this->redis->get($tickerKey);
            
            if ($tickerData) {
                $tickerInfo = json_decode($tickerData, true);
                return $tickerInfo['last'] ?? $tickerInfo['close'] ?? null;
            }

            return null;

        } catch (\Exception $e) {
            $this->logger->error("获取币种价格失败", [
                'currency_id' => $currencyId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 批量触发指定币种的委托订单
     */
    public function triggerOrdersByCurrency(int $currencyId): array
    {
        return $this->checkAndTriggerOrders($currencyId);
    }

    /**
     * 清理过期的委托订单
     */
    public function cleanupExpiredOrders(int $expireDays = 30): int
    {
        try {
            $expireDate = Carbon::now()->subDays($expireDays);
            
            $count = TradeSpotCommission::query()
                ->whereIn('status', [
                    CommissionStatus::CANCELLED->value,
                    CommissionStatus::COMPLETED->value,
                    CommissionStatus::TRIGGER_FAILED->value
                ])
                ->where('created_at', '<', $expireDate)
                ->delete();

            $this->logger->info("清理过期委托订单", [
                'count' => $count,
                'expire_date' => $expireDate->toDateTimeString()
            ]);

            return $count;

        } catch (\Exception $e) {
            $this->logger->error("清理过期委托订单失败", [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 获取委托订单统计信息
     */
    public function getStatistics(): array
    {
        try {
            $stats = [];
            
            // 按状态统计
            $statusStats = TradeSpotCommission::query()
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->get()
                ->keyBy('status')
                ->toArray();

            $stats['by_status'] = $statusStats;

            // 按币种统计等待执行的订单
            $pendingByCurrency = TradeSpotCommission::query()
                ->selectRaw('currency_id, COUNT(*) as count')
                ->where('status', CommissionStatus::PENDING->value)
                ->groupBy('currency_id')
                ->get()
                ->keyBy('currency_id')
                ->toArray();

            $stats['pending_by_currency'] = $pendingByCurrency;

            // 今日统计
            $todayStats = TradeSpotCommission::query()
                ->whereDate('created_at', Carbon::now()->toDateString())
                ->selectRaw('
                    COUNT(*) as total_created,
                    SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as triggered_today,
                    SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as failed_today
                ', [CommissionStatus::TRIGGERED->value, CommissionStatus::TRIGGER_FAILED->value])
                ->first();

            $stats['today'] = $todayStats ? $todayStats->toArray() : [];

            return $stats;

        } catch (\Exception $e) {
            $this->logger->error("获取委托订单统计失败", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
} 