<?php

declare(strict_types=1);

namespace App\Repository\Message;

use App\Repository\IRepository;
use App\Model\Message\Message as Model;
use Hyperf\Database\Model\Builder;
use Hyperf\Collection\Arr;

class MessageRepository extends IRepository
{
    public function __construct(protected readonly Model $model) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
        if (Arr::has($params, 'category_id')) {
            if(is_array($params['category_id'])) {
                $query->whereIn('category_id', $params['category_id']);
            } else {
                $query->where('category_id', $params['category_id']);
            }
        }
        if (Arr::has($params,'user_ids')) {
            if(is_array($params['user_ids'])) {
                $query->whereIn('user_ids', $params['user_ids']);
            } else {
                $query->whereJsonContains('user_ids', $params['user_ids']);
            }
        } else {
            $query->where('user_ids', null);
        }
        if (Arr::has($params, 'created_by')) {
            if(is_array($params['created_by'])) {
                $query->whereIn('created_by', $params['created_by']);
            } else {
                $query->where('created_by', $params['created_by']);
            }
        }
        if (Arr::has($params, 'updated_by')) {
            if(is_array($params['updated_by'])) {
                $query->whereIn('updated_by', $params['updated_by']);
            } else {
                $query->where('updated_by', $params['updated_by']);
            }
        }
        if (Arr::has($params, 'created_at')) {
            if(is_array($params['created_at'])) {
                $query->whereBetween('created_at', $params['created_at']);
            } else {
                $query->where('created_at', $params['created_at']);
            }
        }
        return $query;
    }
}