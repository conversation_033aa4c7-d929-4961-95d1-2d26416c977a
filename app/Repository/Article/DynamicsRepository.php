<?php

declare(strict_types=1);

namespace App\Repository\Article;

use App\Repository\IRepository;
use App\Model\Article\Dynamics as Model;
use Hyperf\Database\Model\Builder;
use Hyperf\Collection\Arr;

class DynamicsRepository extends IRepository
{
    public function __construct(protected readonly Model $model) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
        $query->with(['user:id,display_name,avatar','hottopic']);
        if (Arr::has($params, 'pid_id')) {
            if(is_array($params['pid_id'])) {
                $query->whereIn('pid_id', $params['pid_id']);
            }else {
                $query->where('pid_id', $params['pid_id']);
            }
        }
        if (Arr::has($params, 'member_id')) {
            if(is_array($params['member_id'])) {
                $query->whereIn('member_id', $params['member_id']);
            } else {
                $query->where('member_id', $params['member_id']);
            }
        }
        if (Arr::has($params, 'type')) {
            $query->where('type', $params['type']);
        }
        if (Arr::has($params, 'created_by')) {
            if(is_array($params['created_by'])) {
                $query->whereIn('created_by', $params['created_by']);
            } else {
                $query->where('created_by', $params['created_by']);
            }
        }
        if (Arr::has($params, 'updated_by')) {
            if(is_array($params['updated_by'])) {
                $query->whereIn('updated_by', $params['updated_by']);
            } else {
                $query->where('updated_by', $params['updated_by']);
            }
        }
        if (Arr::has($params, 'created_at')) {
            if(is_array($params['created_at'])) {
                $query->whereBetween('created_at', $params['created_at']);
            } else {
                $query->where('created_at', $params['created_at']);
            }
        } 
        $query->orderBy('created_at','desc');
        return $query;
    }
}