<?php

declare(strict_types=1);

namespace App\Repository\Article;

use App\Repository\IRepository;
use App\Model\Article\Category as Model;
use Hyperf\Database\Model\Builder;
use Hyperf\Collection\Arr;

class CategoryRepository extends IRepository
{
    public function __construct(protected readonly Model $model) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
        if (Arr::has($params, 'name')) {
            $query->where('name', 'like', '%'.$params['name'].'%');
        }
        if (Arr::has($params, 'key')) {
            $query->where('key', $params['key']);
        }
        if (Arr::has($params, 'sort')) {
            if(is_array($params['sort'])) {
                $query->whereIn('sort', $params['sort']);
            } else {
                $query->where('sort', $params['sort']);
            }
        }
        if (Arr::has($params, 'created_by')) {
            if(is_array($params['created_by'])) {
                $query->whereIn('created_by', $params['created_by']);
            } else {
                $query->where('created_by', $params['created_by']);
            }
        }
        if (Arr::has($params, 'updated_by')) {
            if(is_array($params['updated_by'])) {
                $query->whereIn('updated_by', $params['updated_by']);
            } else {
                $query->where('updated_by', $params['updated_by']);
            }
        }
        if (Arr::has($params, 'created_at')) {
            if(is_array($params['created_at'])) {
                $query->whereBetween('created_at', $params['created_at']);
            } else {
                $query->where('created_at', $params['created_at']);
            }
        }
        return $query;
    }
}