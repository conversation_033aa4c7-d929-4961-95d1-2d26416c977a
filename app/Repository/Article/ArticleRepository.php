<?php

declare(strict_types=1);

namespace App\Repository\Article;

use App\Repository\IRepository;
use App\Model\Article\Article as Model;
use Hyperf\Database\Model\Builder;
use Hyperf\Collection\Arr;

class ArticleRepository extends IRepository
{
    public function __construct(protected readonly Model $model) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
        if (Arr::has($params, 'category_id')) {
            if(is_array($params['category_id'])) {
                $query->whereIn('category_id', $params['category_id']);
            } else {
                $query->where('category_id', $params['category_id']);
            }
        }
        if (Arr::has($params, 'title')) {
            $query->where('title', 'like', '%'.$params['title'].'%');
        }
        if (Arr::has($params, 'summary')) {
            $query->where('summary', 'like', '%'.$params['summary'].'%');
        }
        if (Arr::has($params, 'remark')) {
            $query->where('remark', 'like', '%'.$params['remark'].'%');
        }
        if (Arr::has($params, 'created_by')) {
            if(is_array($params['created_by'])) {
                $query->whereIn('created_by', $params['created_by']);
            } else {
                $query->where('created_by', $params['created_by']);
            }
        }
        if (Arr::has($params, 'updated_by')) {
            if(is_array($params['updated_by'])) {
                $query->whereIn('updated_by', $params['updated_by']);
            } else {
                $query->where('updated_by', $params['updated_by']);
            }
        }
        if (Arr::has($params, 'created_at')) {
            if(is_array($params['created_at'])) {
                $query->whereBetween('created_at', $params['created_at']);
            } else {
                $query->where('created_at', $params['created_at']);
            }
        }
        return $query;
    }
}