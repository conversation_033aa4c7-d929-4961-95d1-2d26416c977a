<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Repository\Article;

use App\Model\Article\DynamicsCurrency as Model;
use App\Repository\IRepository;
use Hyperf\Collection\Arr;
use Hyperf\Database\Model\Builder;
use Hyperf\DbConnection\Db;

class DynamicsCurrencyRepository extends IRepository
{
    public function __construct(protected readonly Model $model) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
        $query->with(['currency']);
        if (Arr::has($params, 'symbol_id')) {
            if (\is_array($params['symbol_id'])) {
                $query->whereIn('symbol_id', $params['symbol_id']);
            } else {
                $query->where('symbol_id', $params['symbol_id']);
            }
        }
        if (Arr::has($params, 'symbol')) {
            if (\is_array($params['symbol'])) {
                $query->whereIn('symbol', $params['symbol']);
            } else if($params['symbol']){
                // $query->where('symbol', $params['symbol']);
                $symbol = strtoupper($params['symbol']);
                $query->where(Db::raw('UPPER(symbol)'), 'like', "%{$symbol}%");
            }
        }
        if (Arr::has($params, 'created_by')) {
            if (\is_array($params['created_by'])) {
                $query->whereIn('created_by', $params['created_by']);
            } else {
                $query->where('created_by', $params['created_by']);
            }
        }
        if (Arr::has($params, 'updated_by')) {
            if (\is_array($params['updated_by'])) {
                $query->whereIn('updated_by', $params['updated_by']);
            } else {
                $query->where('updated_by', $params['updated_by']);
            }
        }
        if (Arr::has($params, 'created_at')) {
            if (\is_array($params['created_at'])) {
                $query->whereBetween('created_at', $params['created_at']);
            } else {
                $query->where('created_at', $params['created_at']);
            }
        }
        return $query;
    }
}