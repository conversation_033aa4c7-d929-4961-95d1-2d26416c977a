APP_NAME=AlgoQuant
APP_ENV=dev
APP_DEBUG=true

# timezone 时区
TIMEZONE=Asia/Shanghai

DB_DRIVER=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=algoquant
DB_USERNAME=root
DB_PASSWORD=admin123
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_PREFIX=

REDIS_HOST=127.0.0.1
REDIS_AUTH=
REDIS_PORT=6379
REDIS_DB=0

APP_URL=http://127.0.0.1:9501

# apielf/hyperf-encryption 扩展加密密钥，通过命令：php bin/hyperf gen:key 生成（生成前需要先发布扩展配置 php bin/hyperf.php vendor:publish apielf/hyperf-encryption）
AES_KEY=

# 前端应用市场token 注册登录 www.mineadmin.com 获取
MINE_ACCESS_TOKEN=c28356*******b048

# 管理端jwt密钥 jwt密钥修改，命令：php bin/hyperf.php jwt:secret
JWT_SECRET=

# 用户端jwt密钥 jwt密钥修改，命令：php bin/hyperf.php jwt:secret
JWT_API_SECRET=

# LibreTranslate 翻译服务配置
LIBRETRANSLATE_URL=http://**************:9000
LIBRETRANSLATE_API_KEY=
TRANSLATION_ENABLED=true

# 请求代理配置
REQUEST_PROXY=true
HTTP_PROXY=http://127.0.0.1:1087
HTTPS_PROXY=http://127.0.0.1:1087

# socket代理
SOCKET_PROXY_HOST=127.0.0.1
SOCKET_PROXY_PORT=1088

# 阿里云邮件推送
ALIYUN_ACCESS_KEY_ID=LTAI5tB6CmvhuU5kwKCuwhqV
ALIYUN_ACCESS_KEY_SECRET=******************************
# 必须是已验证的发信地址，完整邮箱地址
ALIYUN_MAIL_ACCOUNT=<EMAIL>
# 发信人
ALIYUN_MAIL_ALIAS=AlgoQuant
ALIYUN_REGION_ID=cn-hangzhou

# 短信通道模式 1 短信宝 2 阿里云国际 3 闪速码
SMS_CHANNEL=3

# 短信宝配置
SMS_BAO_ACCOUNT=
SMS_BAO_PASSWORD=
SMS_BAO_SIGN=

#阿里云短信配置
ALI_SMS_KEY=
ALI_SMS_SEC=
ALI_SMS_SIGN=

# 闪速码
SMS_SSY_APPID=
SMS_SSY_SECRET=
SMS_SSY_VERIFY=
SMS_SSY_SIGN=

# es配置
ES_HOST=**************
ES_PORT=9200
ES_USER=elastic
ES_PASSWORD=crypto123456
ES_SSL_VERIFY=false

# 系统启动类型配置 数据处理端占用cpu和内存性能不应该与api服务器一起启动
MARKET_DATA_SERVER=true
API_SERVER=true