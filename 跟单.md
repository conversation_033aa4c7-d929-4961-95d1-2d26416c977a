# CPX_EXCHANGE 跟单模块开发文档

## 1. 模块概述

跟单模块是 CPX_EXCHANGE 交易所的核心功能之一，允许用户申请成为交易专家（分为合约和现货两种类型），其他用户可以跟随专家的交易策略进行自动跟单。系统支持专家等级管理、徽章系统、数据统计分析和尊享模式等高级功能。

## 2. 核心功能架构

### 2.1 交易专家申请系统

- **合约交易专家申请**：需要划转最少 100USDT 到合约带单账户
- **现货交易专家申请**：基于现有现货账户进行带单
- **审核机制**：可通过系统设置配置是否需要审核
- **用户关联**：在 cpx_user 表中记录专家身份 ID

### 2.2 专家设置管理

- **合约专家设置**：总资产展示、评分排名、仓位保护、跟单参数等
- **现货专家设置**：资产展示、资金构成、交易对管理等
- **分润比例**：可自定义分润比例设置

### 2.3 等级与徽章系统

- **等级条件**：基于带单金额和跟单者数量/资金
- **自动升降级**：每周一 6 点定时判定
- **徽章管理**：合约和现货分别的徽章体系

### 2.4 数据统计分析

- **带单数据**：收益率、交易笔数、胜率等综合统计
- **分润数据**：累计分润、预计分润、历史分润
- **跟随者管理**：跟单者列表、资产筛选、批量操作

### 2.5 尊享模式

- **白名单邀请**：专属邀请链接、有效期管理
- **策略保护**：未结仓位隐私保护
- **个性化分润**：0%-99%自由设置分润比例
- **精细化管理**：跟单者个性化管理

## 3. 数据库设计

### 3.1 核心表结构

#### 3.1.1 交易专家申请表 (copy_trader_applications)

```sql
CREATE TABLE `copy_trader_applications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `trader_type` tinyint NOT NULL COMMENT '专家类型:1=合约,2=现货',
  `display_name` varchar(100) NOT NULL COMMENT '展示名称',
  `introduction` text COMMENT '个人介绍',
  `transfer_amount` decimal(20,8) DEFAULT NULL COMMENT '划转金额(合约专家)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=待审核,2=审核通过,3=审核拒绝',
  `audit_user_id` bigint unsigned DEFAULT NULL COMMENT '审核人ID',
  `audit_reason` varchar(500) DEFAULT NULL COMMENT '审核原因',
  `audit_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_trader_type` (`trader_type`),
  KEY `idx_status` (`status`)
) COMMENT='交易专家申请表';
```

#### 3.1.2 合约交易专家设置表 (copy_trader_contract_settings)

```sql
CREATE TABLE `copy_trader_contract_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `application_id` bigint unsigned NOT NULL COMMENT '申请记录ID',
  `show_total_assets` tinyint NOT NULL DEFAULT '0' COMMENT '展示总资产:0=否,1=是',
  `show_rating_rank` tinyint NOT NULL DEFAULT '1' COMMENT '展示评分排名:0=否,1=是',
  `introduction` text COMMENT '带单简介',
  `position_protection` tinyint NOT NULL DEFAULT '1' COMMENT '未结仓位保护:0=否,1=是',
  `min_copy_amount` decimal(20,8) NOT NULL DEFAULT '50.00000000' COMMENT '最小跟单金额',
  `recommended_params` json COMMENT '推荐参数配置',
  `copy_currencies` json COMMENT '带单币种配置',
  `profit_share_rate` decimal(5,2) NOT NULL DEFAULT '20.00' COMMENT '分润比例(%)',
  `vip_mode_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '尊享模式:0=关闭,1=开启',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否激活:0=否,1=是',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_application_id` (`application_id`)
) COMMENT='合约交易专家设置表';
```

#### 3.1.3 现货交易专家设置表 (copy_trader_spot_settings)

```sql
CREATE TABLE `copy_trader_spot_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `application_id` bigint unsigned NOT NULL COMMENT '申请记录ID',
  `enable_copy_trading` tinyint NOT NULL DEFAULT '1' COMMENT '开启现货带单:0=否,1=是',
  `show_total_assets` tinyint NOT NULL DEFAULT '0' COMMENT '展示总资产:0=否,1=是',
  `show_asset_composition` tinyint NOT NULL DEFAULT '0' COMMENT '展示资金构成:0=否,1=是',
  `introduction` text COMMENT '带单简介',
  `auto_new_pairs` tinyint NOT NULL DEFAULT '1' COMMENT '开启新交易对:0=否,1=是',
  `position_protection` tinyint NOT NULL DEFAULT '1' COMMENT '未结仓位保护:0=否,1=是',
  `recommended_params` json COMMENT '推荐参数配置',
  `copy_currencies` json COMMENT '带单币种配置',
  `profit_share_rate` decimal(5,2) NOT NULL DEFAULT '20.00' COMMENT '分润比例(%)',
  `vip_mode_enabled` tinyint NOT NULL DEFAULT '0' COMMENT '尊享模式:0=关闭,1=开启',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否激活:0=否,1=是',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_application_id` (`application_id`)
) COMMENT='现货交易专家设置表';
```

#### 3.1.4 交易专家等级表 (copy_trader_levels)

```sql
CREATE TABLE `copy_trader_levels` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trader_type` tinyint NOT NULL COMMENT '专家类型:1=合约,2=现货',
  `level` tinyint NOT NULL COMMENT '等级',
  `level_name` varchar(50) NOT NULL COMMENT '等级名称',
  `min_trading_amount` decimal(20,8) NOT NULL COMMENT '最小带单金额',
  `min_followers_amount` decimal(20,8) DEFAULT NULL COMMENT '最小跟单者总资金',
  `min_followers_count` int DEFAULT NULL COMMENT '最小跟单者人数',
  `max_followers` int NOT NULL COMMENT '最大可带单人数',
  `max_profit_share_rate` decimal(5,2) NOT NULL COMMENT '最大分润比例(%)',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否激活:0=否,1=是',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_trader_type_level` (`trader_type`, `level`)
) COMMENT='交易专家等级表';
```

#### 3.1.5 交易专家徽章表 (copy_trader_badges)

```sql
CREATE TABLE `copy_trader_badges` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trader_type` tinyint NOT NULL COMMENT '专家类型:1=合约,2=现货',
  `badge_type` varchar(50) NOT NULL COMMENT '徽章类型',
  `badge_name` varchar(100) NOT NULL COMMENT '徽章名称',
  `badge_icon` varchar(255) COMMENT '徽章图标',
  `description` text COMMENT '徽章描述',
  `conditions` json COMMENT '获取条件',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否激活:0=否,1=是',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_trader_type` (`trader_type`),
  KEY `idx_badge_type` (`badge_type`)
) COMMENT='交易专家徽章表';
```

### 3.2 带单订单记录表

#### 3.2.1 现货带单订单表 (copy_trader_spot_orders)

```sql
CREATE TABLE `copy_trader_spot_orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trader_user_id` bigint unsigned NOT NULL COMMENT '交易专家用户ID',
  `spot_order_id` bigint unsigned NOT NULL COMMENT '现货订单ID',
  `currency_id` bigint unsigned NOT NULL COMMENT '币种ID',
  `side` tinyint NOT NULL COMMENT '方向:1=买入,-1=卖出',
  `order_type` tinyint NOT NULL COMMENT '订单类型:1=市价,2=限价',
  `price` decimal(20,8) NOT NULL COMMENT '价格',
  `quantity` decimal(20,8) NOT NULL COMMENT '数量',
  `filled_quantity` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '成交数量',
  `avg_price` decimal(20,8) DEFAULT NULL COMMENT '平均成交价',
  `status` tinyint NOT NULL COMMENT '状态:1=待成交,2=部分成交,3=完全成交,4=已撤销',
  `is_copy_trading` tinyint NOT NULL DEFAULT '1' COMMENT '是否为带单订单:0=否,1=是',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_trader_user_id` (`trader_user_id`),
  KEY `idx_spot_order_id` (`spot_order_id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_status` (`status`)
) COMMENT='现货带单订单表';
```

#### 3.2.2 合约带单订单表 (copy_trader_contract_orders)

```sql
CREATE TABLE `copy_trader_contract_orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trader_user_id` bigint unsigned NOT NULL COMMENT '交易专家用户ID',
  `contract_order_id` bigint unsigned NOT NULL COMMENT '合约订单ID',
  `currency_id` bigint unsigned NOT NULL COMMENT '币种ID',
  `side` tinyint NOT NULL COMMENT '方向:1=买入,-1=卖出',
  `order_type` tinyint NOT NULL COMMENT '订单类型:1=市价,2=限价',
  `price` decimal(20,8) NOT NULL COMMENT '价格',
  `quantity` decimal(20,8) NOT NULL COMMENT '数量',
  `filled_quantity` decimal(20,8) NOT NULL DEFAULT '0.00000000' COMMENT '成交数量',
  `avg_price` decimal(20,8) DEFAULT NULL COMMENT '平均成交价',
  `margin` decimal(20,8) NOT NULL COMMENT '保证金',
  `leverage` int NOT NULL COMMENT '杠杆倍数',
  `status` tinyint NOT NULL COMMENT '状态:1=待成交,2=部分成交,3=完全成交,4=已撤销',
  `is_copy_trading` tinyint NOT NULL DEFAULT '1' COMMENT '是否为带单订单:0=否,1=是',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_trader_user_id` (`trader_user_id`),
  KEY `idx_contract_order_id` (`contract_order_id`),
  KEY `idx_currency_id` (`currency_id`),
  KEY `idx_status` (`status`)
) COMMENT='合约带单订单表';
```

### 3.3 跟单配置和记录表

#### 3.3.1 跟单配置表 (copy_trading_configs)

```sql
CREATE TABLE `copy_trading_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `trader_user_id` bigint unsigned NOT NULL COMMENT '交易专家用户ID',
  `trader_type` tinyint NOT NULL COMMENT '专家类型:1=合约,2=现货',
  `copy_mode` tinyint NOT NULL COMMENT '跟单模式:1=智能比例,2=多元探索',
  `investment_amount` decimal(20,8) NOT NULL COMMENT '投资金额(USDT)',
  `copy_type` tinyint DEFAULT NULL COMMENT '跟单类型:1=固定额度,2=跟单倍率(仅多元探索)',
  `fixed_amount` decimal(20,8) DEFAULT NULL COMMENT '固定额度(USDT)',
  `multiplier` decimal(10,4) DEFAULT NULL COMMENT '跟单倍率',
  `stop_loss_rate` decimal(5,2) DEFAULT NULL COMMENT '止损比例(%)',
  `take_profit_rate` decimal(5,2) DEFAULT NULL COMMENT '止盈比例(%)',
  `max_copy_amount` decimal(20,8) DEFAULT NULL COMMENT '最大跟单金额(USDT)',
  `slippage_rate` decimal(5,2) DEFAULT '0.50' COMMENT '滑点比例(%)',
  `copy_currencies` json COMMENT '跟单币种配置',
  `auto_new_pairs` tinyint NOT NULL DEFAULT '0' COMMENT '自动跟随新币对:0=否,1=是',
  `is_vip_mode` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式:0=否,1=是',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=正常,2=暂停,3=停止',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_follower_trader` (`follower_user_id`, `trader_user_id`, `trader_type`),
  KEY `idx_follower_user_id` (`follower_user_id`),
  KEY `idx_trader_user_id` (`trader_user_id`),
  KEY `idx_trader_type` (`trader_type`)
) COMMENT='跟单配置表';
```

### 3.4 分润比例修改记录表 (copy_trader_profit_rate_logs)

```sql
CREATE TABLE `copy_trader_profit_rate_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '交易专家用户ID',
  `trader_type` tinyint NOT NULL COMMENT '专家类型:1=合约,2=现货',
  `old_rate` decimal(5,2) NOT NULL COMMENT '原分润比例(%)',
  `new_rate` decimal(5,2) NOT NULL COMMENT '新分润比例(%)',
  `change_date` date NOT NULL COMMENT '修改日期',
  `is_vip_mode` tinyint NOT NULL DEFAULT '0' COMMENT '是否尊享模式:0=否,1=是',
  `target_user_id` bigint unsigned DEFAULT NULL COMMENT '目标用户ID(尊享模式个性化设置)',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_date` (`user_id`, `change_date`),
  KEY `idx_trader_type` (`trader_type`)
) COMMENT='分润比例修改记录表';
```

### 3.5 用户表扩展字段

需要在 cpx_user 表中添加以下字段：

```sql
ALTER TABLE `cpx_user`
ADD COLUMN `contract_trader_setting_id` bigint unsigned DEFAULT NULL COMMENT '合约交易专家设置表ID',
ADD COLUMN `spot_trader_setting_id` bigint unsigned DEFAULT NULL COMMENT '现货交易专家设置表ID';
```

## 4. 系统设置配置

### 4.1 审核配置

- `copy_trader_audit_required`: 交易专家申请是否需要审核 (true/false)

### 4.2 问题反馈类型配置

- `copy_trader_feedback_types`: 问题反馈类型选项 (JSON 数组)

### 4.3 身份撤销原因配置

- `copy_trader_revoke_reasons`: 身份撤销原因选项 (JSON 数组)

## 5. 推荐参数详细设计

### 5.1 推荐参数 JSON 结构

```json
{
  "fixed_amount": {
    "enabled": true,
    "amount": "100.00",
    "stop_loss_rate": "10.00",
    "take_profit_rate": "20.00",
    "max_copy_amount": "1000.00"
  },
  "multiplier": {
    "enabled": true,
    "multiplier": "0.1",
    "stop_loss_rate": "10.00",
    "take_profit_rate": "20.00",
    "max_copy_amount": "1000.00"
  }
}
```

### 5.2 推荐参数说明

- **固定额度**：跟单者每次跟单固定投入的保证金（USDT）
- **跟单倍率**：跟单者每次下单数量是交易专家带单订单数量的倍数
- **最大跟单**：最大跟单的保证金数量（USDT）
- **止损比例**：自动止损的亏损比例
- **止盈比例**：自动止盈的盈利比例

## 6. 跟单模式详细设计

### 6.1 智能比例跟单模式

#### 6.1.1 核心概念

智能比例跟单是根据交易专家使用的资金比例自动进行跟单成本计算的跟单模式。按照资金比例进行跟单，可以保证跟随者每一单的资金风险与交易专家保持一致。

#### 6.1.2 计算公式

```
专家资金比例 = 专家单笔保证金 / 专家带单账户总资产
跟单者保证金 = 跟单者投资金额 × 专家资金比例
```

#### 6.1.3 业务流程

1. 跟单者设置投资金额（最低 50 USDT）
2. 交易专家下单时，系统计算资金比例
3. 根据比例自动计算跟单者应投入的保证金
4. 执行跟单交易

#### 6.1.4 优势特点

- 一键跟单，操作简单
- 精确跟单，风险一致
- 了解专家信心程度
- 独立投资金额管理

#### 6.1.5 限制条件

- 最多跟随 50 名交易专家
- 默认跟随专家杠杆模式
- 减少投资额时最低预留 50 USDT

### 6.2 多元探索跟单模式

#### 6.2.1 核心概念

多元探索跟单是经典跟单模式，允许投资者用一笔资金同时复制多个精英交易者的策略，获得多元化投资机会。

#### 6.2.2 跟单类型

**固定额度跟单**

- 每笔跟单使用固定的保证金成本
- 不受交易专家下单金额影响
- 适合风险控制明确的用户

**倍率跟单**

- 每笔跟单数量是专家下单数量的固定倍数
- 跟单金额随专家订单规模变化
- 适合完全复制专家策略的用户

#### 6.2.3 业务流程

1. 设置总投资金额（≥50 USDT）
2. 选择跟单方式（固定额度/倍率）
3. 设置风险控制参数
4. 选择跟单币对
5. 开始跟单交易

#### 6.2.4 优势特点

- 多样化投资机会
- 资金利用率高
- 共用资金池
- 灵活参数设置

#### 6.2.5 注意事项

- 可跟随无限多个交易专家
- 共用同一资金池
- 最低预留 50 USDT 运行资金

## 7. 业务逻辑设计

### 7.1 交易专家申请流程

1. 用户提交申请（选择类型、填写信息）
2. 合约专家需要划转资金到 COPY 账户类型（AccountType::COPY）
3. 系统检查是否需要审核
4. 如需审核，等待管理员审核；否则自动通过
5. 审核通过后创建专家设置记录
6. 更新用户表中的专家设置 ID

### 5.2 专家展示条件判断

```php
// 合约交易专家展示条件
function isContractTraderVisible($userId) {
    // 1. 开启合约带单（成为专家后即开启）
    // 2. 至少设置1个带单币对
    // 3. 至少存在1笔已平仓带单订单
    // 4. 合约带单账户总资产 ≥ 100 USDT
}

// 现货交易专家展示条件
function isSpotTraderVisible($userId) {
    // 1. 开启现货带单
    // 2. 至少设置1个带单币对
    // 3. 至少存在1笔已平仓带单订单
    // 4. 现货总资产 ≥ 100 USDT
}
```

### 7.3 等级升降级逻辑

#### 7.3.1 升级条件

交易专家等级升级需要同时满足以下两个条件：

- **条件一**：带单金额达到等级要求
- **条件二**：跟单者总资金达标 **或者** 跟单人数达标（满足其一即可）

#### 7.3.2 降级条件

当以下情况发生时进行降级：

- **条件一不满足**：带单金额低于当前等级要求
- **条件二不满足**：跟单者总资金和跟单人数都不达标

#### 7.3.3 等级判定逻辑

```php
// 每周一6点执行的定时任务
function updateTraderLevels() {
    // 1. 获取所有交易专家
    $traders = getAllTraders();

    foreach ($traders as $trader) {
        // 2. 计算带单金额和跟单者数据
        $tradingAmount = calculateTradingAmount($trader);
        $followersAmount = calculateFollowersAmount($trader);
        $followersCount = calculateFollowersCount($trader);

        // 3. 根据等级条件判断升降级
        $currentLevel = $trader->level;
        $targetLevel = determineTargetLevel(
            $tradingAmount,
            $followersAmount,
            $followersCount,
            $trader->trader_type
        );

        // 4. 更新专家等级信息
        if ($targetLevel !== $currentLevel) {
            updateTraderLevel($trader, $targetLevel);
            sendLevelChangeNotification($trader, $currentLevel, $targetLevel);
        }
    }
}

function determineTargetLevel($tradingAmount, $followersAmount, $followersCount, $traderType) {
    $levels = getTraderLevels($traderType);

    foreach ($levels as $level) {
        // 条件一：带单金额检查
        $condition1 = $tradingAmount >= $level->min_trading_amount;

        // 条件二：跟单者资金或人数检查（满足其一即可）
        $condition2 = ($followersAmount >= $level->min_followers_amount) ||
                     ($followersCount >= $level->min_followers_count);

        // 同时满足条件一和条件二才能达到该等级
        if ($condition1 && $condition2) {
            $targetLevel = $level->level;
        }
    }

    return $targetLevel ?? 1; // 默认等级1
}
```

## 6. API 接口设计

### 6.1 交易专家申请相关

- `POST /api/copy-trader/apply` - 申请成为交易专家
- `GET /api/copy-trader/application/{id}` - 获取申请详情
- `PUT /api/copy-trader/application/{id}` - 修改申请信息

### 6.2 专家设置管理

- `GET /api/copy-trader/settings` - 获取专家设置
- `PUT /api/copy-trader/settings` - 更新专家设置
- `POST /api/copy-trader/feedback` - 提交问题反馈
- `POST /api/copy-trader/revoke` - 申请身份撤销

### 6.3 数据统计接口

- `GET /api/copy-trader/stats/trading` - 获取带单数据统计
- `GET /api/copy-trader/stats/profit-share` - 获取分润数据统计
- `GET /api/copy-trader/followers` - 获取跟随者列表

### 6.4 跟单配置接口

- `POST /api/copy-trading/config` - 创建跟单配置
- `GET /api/copy-trading/config/{traderId}` - 获取跟单配置
- `PUT /api/copy-trading/config/{traderId}` - 更新跟单配置
- `DELETE /api/copy-trading/config/{traderId}` - 停止跟单
- `POST /api/copy-trading/pause/{traderId}` - 暂停跟单
- `POST /api/copy-trading/resume/{traderId}` - 恢复跟单

### 6.5 尊享模式接口

- `POST /api/copy-trader/vip/invite-link` - 创建邀请链接
- `GET /api/copy-trader/vip/invite-links` - 获取邀请链接列表
- `PUT /api/copy-trader/vip/invite-link/{id}` - 更新邀请链接
- `DELETE /api/copy-trader/vip/invite-link/{id}` - 删除邀请链接
- `PUT /api/copy-trader/vip/profit-share-rate` - 调整分润比例
- `GET /api/copy-trader/vip/whitelist` - 获取白名单用户
- `POST /api/copy-trader/vip/whitelist/{userId}` - 添加白名单用户
- `DELETE /api/copy-trader/vip/whitelist/{userId}` - 移除白名单用户

### 6.6 分润比例管理接口

- `GET /api/copy-trader/profit-rate/logs` - 获取分润比例修改记录
- `GET /api/copy-trader/profit-rate/daily-count` - 获取今日修改次数
- `PUT /api/copy-trader/profit-rate/batch` - 批量调整分润比例

## 7. 前端页面结构

### 7.1 交易专家申请页面

- 选择专家类型（合约/现货）
- 填写展示名称和个人介绍
- 合约专家资金划转界面
- 申请提交和状态查看

### 7.2 专家设置管理页面

- 基础设置（资产展示、排名展示等）
- 跟单参数设置
- 币种管理
- 分润比例设置

### 7.3 数据统计页面

- 带单数据统计图表
- 分润数据统计
- 跟随者管理界面

### 7.4 尊享模式管理页面

- 邀请链接管理
- 白名单用户管理
- 分润比例调整

## 8. 开发注意事项

### 8.1 数据一致性

- 资金划转操作需要事务保证
- 专家设置修改需要实时生效
- 等级升降级需要批量处理

### 8.2 性能优化

- 统计数据使用缓存机制
- 大量数据查询使用分页
- 定时任务避免高峰期执行

### 8.3 安全考虑

- 资金操作需要多重验证
- 敏感信息加密存储
- API 接口权限控制

### 8.4 扩展性设计

- 支持多种跟单策略
- 预留自定义字段
- 模块化设计便于维护

## 9. 详细数据库表设计补充

### 9.1 交易专家用户关联表 (copy_trader_user_badges)

```sql
CREATE TABLE `copy_trader_user_badges` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `badge_id` bigint unsigned NOT NULL COMMENT '徽章ID',
  `obtained_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否激活:0=否,1=是',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_badge` (`user_id`, `badge_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_badge_id` (`badge_id`)
) COMMENT='交易专家用户徽章关联表';
```

### 9.2 交易专家关注表 (copy_trader_follows)

```sql
CREATE TABLE `copy_trader_follows` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '关注者用户ID',
  `trader_user_id` bigint unsigned NOT NULL COMMENT '交易专家用户ID',
  `trader_type` tinyint NOT NULL COMMENT '专家类型:1=合约,2=现货',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_follower_trader` (`follower_user_id`, `trader_user_id`, `trader_type`),
  KEY `idx_follower_user_id` (`follower_user_id`),
  KEY `idx_trader_user_id` (`trader_user_id`)
) COMMENT='交易专家关注表';
```

### 9.3 问题反馈表 (copy_trader_feedbacks)

```sql
CREATE TABLE `copy_trader_feedbacks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `trader_type` tinyint NOT NULL COMMENT '专家类型:1=合约,2=现货',
  `feedback_type` varchar(50) NOT NULL COMMENT '问题类型',
  `description` text NOT NULL COMMENT '问题描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=待处理,2=处理中,3=已解决,4=已关闭',
  `admin_reply` text COMMENT '管理员回复',
  `admin_user_id` bigint unsigned DEFAULT NULL COMMENT '处理管理员ID',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_trader_type` (`trader_type`)
) COMMENT='交易专家问题反馈表';
```

### 9.4 身份撤销申请表 (copy_trader_revocations)

```sql
CREATE TABLE `copy_trader_revocations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `trader_type` tinyint NOT NULL COMMENT '专家类型:1=合约,2=现货',
  `revoke_reason` varchar(50) NOT NULL COMMENT '撤销原因',
  `description` text COMMENT '撤销说明',
  `refund_account_type` tinyint DEFAULT NULL COMMENT '资金退回账户:1=现货,2=合约(仅合约专家)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:1=待审核,2=审核通过,3=审核拒绝',
  `admin_user_id` bigint unsigned DEFAULT NULL COMMENT '审核管理员ID',
  `audit_reason` varchar(500) DEFAULT NULL COMMENT '审核原因',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_trader_type` (`trader_type`)
) COMMENT='交易专家身份撤销申请表';
```

### 9.5 尊享模式邀请链接表 (copy_trader_vip_invites)

```sql
CREATE TABLE `copy_trader_vip_invites` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '交易专家用户ID',
  `trader_type` tinyint NOT NULL COMMENT '专家类型:1=合约,2=现货',
  `invite_code` varchar(32) NOT NULL COMMENT '邀请码',
  `title` varchar(100) DEFAULT NULL COMMENT '链接标题',
  `max_invites` int NOT NULL DEFAULT '0' COMMENT '最大邀请人数(0=无限制)',
  `current_invites` int NOT NULL DEFAULT '0' COMMENT '当前邀请人数',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否激活:0=否,1=是',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_trader_type` (`trader_type`)
) COMMENT='尊享模式邀请链接表';
```

### 9.6 尊享模式白名单表 (copy_trader_vip_whitelist)

```sql
CREATE TABLE `copy_trader_vip_whitelist` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trader_user_id` bigint unsigned NOT NULL COMMENT '交易专家用户ID',
  `follower_user_id` bigint unsigned NOT NULL COMMENT '跟单者用户ID',
  `trader_type` tinyint NOT NULL COMMENT '专家类型:1=合约,2=现货',
  `invite_id` bigint unsigned DEFAULT NULL COMMENT '邀请链接ID',
  `custom_profit_share_rate` decimal(5,2) DEFAULT NULL COMMENT '自定义分润比例(%)',
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否激活:0=否,1=是',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trader_follower` (`trader_user_id`, `follower_user_id`, `trader_type`),
  KEY `idx_trader_user_id` (`trader_user_id`),
  KEY `idx_follower_user_id` (`follower_user_id`),
  KEY `idx_invite_id` (`invite_id`)
) COMMENT='尊享模式白名单表';
```

## 10. 枚举类设计

### 10.1 交易专家类型枚举

```php
<?php
namespace App\Model\Enums\CopyTrader;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum TraderType: int
{
    use EnumConstantsTrait;

    #[Message('copy_trader.trader_type.contract')]
    case CONTRACT = 1; // 合约交易专家

    #[Message('copy_trader.trader_type.spot')]
    case SPOT = 2; // 现货交易专家
}
```

### 10.2 申请状态枚举

```php
<?php
namespace App\Model\Enums\CopyTrader;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ApplicationStatus: int
{
    use EnumConstantsTrait;

    #[Message('copy_trader.application_status.pending')]
    case PENDING = 1; // 待审核

    #[Message('copy_trader.application_status.approved')]
    case APPROVED = 2; // 审核通过

    #[Message('copy_trader.application_status.rejected')]
    case REJECTED = 3; // 审核拒绝
}
```

### 10.3 跟单模式枚举

```php
<?php
namespace App\Model\Enums\CopyTrader;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum CopyMode: int
{
    use EnumConstantsTrait;

    #[Message('copy_trader.copy_mode.smart_ratio')]
    case SMART_RATIO = 1; // 智能比例

    #[Message('copy_trader.copy_mode.multi_explore')]
    case MULTI_EXPLORE = 2; // 多元探索
}
```

### 10.4 跟单类型枚举

```php
<?php
namespace App\Model\Enums\CopyTrader;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum CopyType: int
{
    use EnumConstantsTrait;

    #[Message('copy_trader.copy_type.fixed_amount')]
    case FIXED_AMOUNT = 1; // 固定额度

    #[Message('copy_trader.copy_type.multiplier')]
    case MULTIPLIER = 2; // 跟单倍率
}
```

### 10.5 分润比例修改限制枚举

```php
<?php
namespace App\Model\Enums\CopyTrader;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum ProfitRateChangeLimit: int
{
    use EnumConstantsTrait;

    #[Message('copy_trader.profit_rate_limit.daily_max')]
    case DAILY_MAX_CHANGES = 3; // 每日最大修改次数

    #[Message('copy_trader.profit_rate_limit.min_rate')]
    case MIN_RATE = 0; // 最小分润比例

    #[Message('copy_trader.profit_rate_limit.max_rate')]
    case MAX_RATE = 99; // 最大分润比例
}
```

## 11. 核心业务服务类设计

### 11.1 交易专家申请服务

```php
<?php
namespace App\Http\Api\Service\CopyTrader;

class CopyTraderApplicationService
{
    /**
     * 申请成为交易专家
     */
    public function apply(array $data): array
    {
        // 1. 验证用户是否已经是该类型专家
        // 2. 验证申请数据
        // 3. 合约专家处理资金划转
        // 4. 创建申请记录
        // 5. 检查是否需要审核
        // 6. 自动审核或等待人工审核
    }

    /**
     * 审核申请
     */
    public function audit(int $applicationId, bool $approved, string $reason = ''): bool
    {
        // 1. 更新申请状态
        // 2. 审核通过则创建专家设置
        // 3. 更新用户表专家ID
        // 4. 发送通知
    }
}
```

### 11.2 交易专家设置服务

```php
<?php
namespace App\Http\Api\Service\CopyTrader;

class CopyTraderSettingsService
{
    /**
     * 获取专家设置
     */
    public function getSettings(int $userId, int $traderType): array
    {
        // 根据类型获取对应设置
    }

    /**
     * 更新专家设置
     */
    public function updateSettings(int $userId, int $traderType, array $data): bool
    {
        // 1. 验证设置数据
        // 2. 更新设置
        // 3. 触发相关事件
    }

    /**
     * 检查专家展示条件
     */
    public function checkVisibilityConditions(int $userId, int $traderType): bool
    {
        // 实现展示条件判断逻辑
    }
}
```

## 12. 开发任务分解

### 12.1 第一阶段：基础架构搭建（预计 5 天）

#### 任务 1.1：数据库设计与迁移文件创建（1 天）

- [ ] 创建所有数据库迁移文件
- [ ] 设计表结构和索引
- [ ] 创建外键关联
- [ ] 添加 cpx_user 表扩展字段

#### 任务 1.2：模型类创建（1 天）

- [ ] 创建所有模型类（CopyTraderApplication、CopyTraderContractSettings 等）
- [ ] 定义模型关联关系
- [ ] 添加字段常量定义
- [ ] 配置类型转换

#### 任务 1.3：枚举类创建（0.5 天）

- [ ] 创建 TraderType 枚举
- [ ] 创建 ApplicationStatus 枚举
- [ ] 创建 RecommendedParamType 枚举
- [ ] 创建其他相关枚举

#### 任务 1.4：基础服务类框架（1.5 天）

- [ ] 创建 CopyTraderApplicationService
- [ ] 创建 CopyTraderSettingsService
- [ ] 创建 CopyTraderStatsService
- [ ] 创建 CopyTraderVipService

#### 任务 1.5：系统设置配置（1 天）

- [ ] 配置审核开关设置
- [ ] 配置问题反馈类型
- [ ] 配置身份撤销原因
- [ ] 创建系统设置管理接口

### 12.2 第二阶段：交易专家申请功能（预计 4 天）

#### 任务 2.1：申请接口开发（1.5 天）

- [ ] 创建申请控制器和请求验证
- [ ] 实现申请业务逻辑
- [ ] 处理合约专家资金划转
- [ ] 添加申请状态查询接口

#### 任务 2.2：审核功能开发（1 天）

- [ ] 创建管理端审核接口
- [ ] 实现自动审核逻辑
- [ ] 添加审核通知功能
- [ ] 创建审核记录查询

#### 任务 2.3：专家设置初始化（1 天）

- [ ] 实现审核通过后设置创建
- [ ] 更新用户表专家 ID 字段
- [ ] 设置默认配置参数
- [ ] 添加设置验证逻辑

#### 任务 2.4：申请流程测试（0.5 天）

- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 边界条件测试
- [ ] 性能测试

### 12.3 第三阶段：专家设置管理功能（预计 6 天）

#### 任务 3.1：合约专家设置（2 天）

- [ ] 总资产展示设置
- [ ] 评分排名展示设置
- [ ] 未结仓位保护设置
- [ ] 最小跟单金额设置
- [ ] 推荐参数配置

#### 任务 3.2：现货专家设置（2 天）

- [ ] 现货带单开关
- [ ] 资产和资金构成展示
- [ ] 新交易对自动开启
- [ ] 币种管理功能
- [ ] 分润比例设置

#### 任务 3.3：设置管理接口（1.5 天）

- [ ] 获取设置接口
- [ ] 更新设置接口
- [ ] 设置验证逻辑
- [ ] 设置变更通知

#### 任务 3.4：问题反馈和身份撤销（0.5 天）

- [ ] 问题反馈提交接口
- [ ] 身份撤销申请接口
- [ ] 管理端处理接口
- [ ] 状态查询接口

### 12.4 第四阶段：等级与徽章系统（预计 4 天）

#### 任务 4.1：等级系统开发（2 天）

- [ ] 等级配置管理
- [ ] 等级计算逻辑
- [ ] 定时升降级任务
- [ ] 等级变更通知

#### 任务 4.2：徽章系统开发（1.5 天）

- [ ] 徽章配置管理
- [ ] 徽章获取条件判断
- [ ] 徽章授予逻辑
- [ ] 徽章展示接口

#### 任务 4.3：关注功能开发（0.5 天）

- [ ] 关注/取消关注接口
- [ ] 关注列表查询
- [ ] 关注状态管理
- [ ] 关注通知功能

### 12.5 第五阶段：数据统计功能（预计 5 天）

#### 任务 5.1：带单数据统计（2 天）

- [ ] 综合统计计算
- [ ] 当前带单列表
- [ ] 历史带单查询
- [ ] 数据缓存优化

#### 任务 5.2：分润数据统计（2 天）

- [ ] 分润统计计算
- [ ] 历史分润查询
- [ ] 预计分润计算
- [ ] 分润报表生成

#### 任务 5.3：跟随者管理（1 天）

- [ ] 跟随者列表查询
- [ ] 跟随者筛选功能
- [ ] 批量移除功能
- [ ] 跟随者数据统计

### 12.6 第六阶段：跟单功能开发（预计 7 天）

#### 任务 6.1：跟单配置管理（2.5 天）

- [ ] 跟单配置创建和验证
- [ ] 智能比例跟单逻辑
- [ ] 多元探索跟单逻辑
- [ ] 跟单参数设置和验证
- [ ] 跟单状态管理

#### 任务 6.2：跟单执行引擎（3 天）

- [ ] 监听交易专家订单
- [ ] 智能比例计算逻辑
- [ ] 固定额度跟单逻辑
- [ ] 倍率跟单逻辑
- [ ] 风控参数执行（止盈止损）
- [ ] 跟单订单生成和执行

#### 任务 6.3：跟单数据统计（1 天）

- [ ] 跟单收益计算
- [ ] 跟单成功率统计
- [ ] 跟单历史记录
- [ ] 跟单者数据分析

#### 任务 6.4：跟单管理接口（0.5 天）

- [ ] 跟单配置 CRUD 接口
- [ ] 跟单暂停/恢复接口
- [ ] 跟单数据查询接口
- [ ] 跟单状态监控

### 12.7 第七阶段：尊享模式功能（预计 6 天）

#### 任务 7.1：邀请链接管理（2 天）

- [ ] 创建邀请链接
- [ ] 邀请链接列表
- [ ] 链接有效期管理
- [ ] 邀请统计功能

#### 任务 7.2：白名单管理（2 天）

- [ ] 白名单用户管理
- [ ] 邀请码验证
- [ ] 白名单状态管理
- [ ] 白名单查询接口

#### 任务 7.3：个性化分润（1.5 天）

- [ ] 自定义分润比例设置
- [ ] 分润比例变更限制
- [ ] 分润变更通知
- [ ] 分润历史记录

#### 任务 7.4：尊享模式开关（0.5 天）

- [ ] 尊享模式开启/关闭
- [ ] 模式切换影响处理
- [ ] 状态同步更新
- [ ] 模式变更通知

### 12.8 第八阶段：前端页面开发（预计 8 天）

#### 任务 8.1：申请页面（2 天）

- [ ] 专家类型选择
- [ ] 申请表单设计
- [ ] 资金划转界面
- [ ] 申请状态查看

#### 任务 8.2：设置管理页面（2.5 天）

- [ ] 基础设置界面
- [ ] 推荐参数设置
- [ ] 币种管理界面
- [ ] 分润比例设置

#### 任务 8.3：跟单页面（2 天）

- [ ] 跟单模式选择界面
- [ ] 智能比例跟单设置
- [ ] 多元探索跟单设置
- [ ] 跟单状态管理界面

#### 任务 8.4：数据统计页面（1 天）

- [ ] 统计图表展示
- [ ] 数据筛选功能
- [ ] 报表导出功能
- [ ] 实时数据更新

#### 任务 8.5：尊享模式页面（0.5 天）

- [ ] 邀请链接管理
- [ ] 白名单管理
- [ ] 分润设置界面
- [ ] 模式切换界面

### 12.9 第九阶段：测试与优化（预计 3 天）

#### 任务 9.1：功能测试（1.5 天）

- [ ] 完整流程测试
- [ ] 边界条件测试
- [ ] 异常情况测试
- [ ] 用户体验测试

#### 任务 9.2：性能优化（1 天）

- [ ] 数据库查询优化
- [ ] 缓存策略优化
- [ ] 接口响应优化
- [ ] 前端性能优化

#### 任务 9.3：安全测试（0.5 天）

- [ ] 权限验证测试
- [ ] 数据安全测试
- [ ] 接口安全测试
- [ ] 资金安全测试

## 13. 风险评估与注意事项

### 13.1 技术风险

- **数据一致性**：多表关联操作需要事务保证
- **性能问题**：大量统计查询可能影响性能
- **缓存同步**：实时数据更新与缓存同步

### 13.2 业务风险

- **资金安全**：合约专家资金划转安全
- **分润计算**：复杂的分润逻辑准确性
- **等级升降**：自动升降级逻辑正确性

### 13.3 用户体验风险

- **操作复杂度**：功能丰富但操作要简单
- **响应速度**：统计查询响应时间
- **数据准确性**：实时数据展示准确性

## 14. 部署与上线计划

### 14.1 开发环境部署

- [ ] 数据库迁移执行
- [ ] 基础数据初始化
- [ ] 系统设置配置
- [ ] 功能模块测试

### 14.2 测试环境部署

- [ ] 完整功能测试
- [ ] 性能压力测试
- [ ] 安全渗透测试
- [ ] 用户验收测试

### 14.3 生产环境上线

- [ ] 数据库备份
- [ ] 灰度发布
- [ ] 监控告警配置
- [ ] 回滚方案准备

## 15. 后续扩展规划

### 15.1 功能扩展

- 多币种分润支持
- 高级跟单策略
- 社交功能增强
- 移动端适配

### 15.2 性能优化

- 分布式缓存
- 读写分离
- 数据分片
- CDN 加速

### 15.3 运营支持

- 数据分析平台
- 运营工具开发
- 客服系统集成
- 营销活动支持

## 16. 需求澄清后的重要更新

### 16.1 账户类型澄清

- **合约带单账户**：使用现有的 `AccountType::COPY` 枚举值
- **资金划转**：合约专家申请时需要划转最少 100USDT 到 COPY 账户类型

### 16.2 推荐参数详细设计

- **固定额度**：跟单者每次跟单固定投入的保证金（USDT）
- **跟单倍率**：跟单者每次下单数量是交易专家带单订单数量的倍数
- **最大跟单**：最大跟单的保证金数量（USDT）
- **JSON 结构**：支持两种类型的推荐参数配置

### 16.3 跟单模式设计

#### 智能比例跟单

- 根据专家资金比例自动计算跟单金额
- 最多跟随 50 名交易专家
- 一键跟单，操作简单

#### 多元探索跟单

- 支持固定额度和跟单倍率两种类型
- 可跟随无限多个交易专家
- 共用资金池，资金利用率高

### 16.4 带单订单记录

- **新增表**：`copy_trader_spot_orders` 和 `copy_trader_contract_orders`
- **关联设计**：与现有订单表关联，记录带单订单详细信息
- **标识字段**：`is_copy_trading` 字段标识是否为带单订单

### 16.5 等级升降级逻辑

- **升级条件**：条件一（带单金额）AND 条件二（跟单者资金 OR 跟单人数）
- **降级条件**：条件一不满足 OR 条件二完全不满足
- **定时任务**：每周一 6 点执行等级判定

### 16.6 分润比例管理

- **修改限制**：每日最多修改 3 次
- **记录表**：`copy_trader_profit_rate_logs` 记录所有修改历史
- **尊享模式**：支持 0%-99%自由设置分润比例

### 16.7 开发任务调整

- **总计 9 个阶段**：新增跟单功能开发阶段
- **预计开发时间**：48 天（原 41 天增加到 48 天）
- **核心新增**：跟单执行引擎、智能比例计算、多元探索逻辑

这些更新确保了跟单模块的功能完整性和技术可行性，为后续开发提供了清晰的指导。
