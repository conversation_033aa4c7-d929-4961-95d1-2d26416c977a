<?php

declare(strict_types=1);

return [
    'enums' => [
        'commission_order_type' => [
            '1' => '止盈止损',
            '2' => '计划委托',
            '3' => '追踪委托',
        ],
        'commission_status' => [
            '0' => '等待执行',
            '1' => '已触发',
            '2' => '已完成',
            '-1' => '已取消',
            '-2' => '触发失败',
        ],
        'trigger_condition' => [
            '1' => '大于等于',
            '2' => '小于等于',
        ],
        'trigger_type' => [
            '1' => '限价单',
            '2' => '市价单',
        ],
    ],
    'margin' => [
        'margin_type' => [
            '1' => '逐仓',
            '2' => '全仓',
        ],
        'position_side' => [
            '1' => '做多',
            '2' => '做空',
        ],
        'margin_order_status' => [
            '0' => '已取消',
            '1' => '待成交',
            '2' => '部分成交',
            '3' => '完全成交',
        ],
        'margin_position_status' => [
            '1' => '持仓中',
            '2' => '已平仓',
            '3' => '强制平仓',
        ],
        'margin_borrow_status' => [
            '1' => '借贷中',
            '2' => '已还清',
            '3' => '逾期',
        ],
        'margin_interest_status' => [
            '1' => '待扣除',
            '2' => '已扣除',
            '3' => '扣除失败',
            '4' => '已豁免',
        ],
        'liquidation_type' => [
            '1' => '保证金不足',
            '2' => 'ADL自动减仓',
            '3' => '到期强平',
        ],
        'liquidation_status' => [
            '1' => '待执行',
            '2' => '执行中',
            '3' => '已完成',
            '4' => '执行失败',
            '5' => '已取消',
        ],
        'reduce_only' => [
            '0' => '否',
            '1' => '是',
        ],
        'trigger_source' => [
            '1' => '系统自动',
            '2' => '人工干预',
        ],
    ],
    'messages' => [
        'commission_order_created' => '委托订单创建成功',
        'commission_order_updated' => '委托订单修改成功',
        'commission_order_cancelled' => '委托订单取消成功',
        'commission_order_triggered' => '委托订单已触发',
        'commission_order_expired' => '委托订单已过期',
        'commission_order_not_found' => '委托订单不存在或已处理',
        'commission_order_no_update_fields' => '没有需要更新的字段',
        'commission_batch_cancelled' => '批量取消完成',
        'spot_order_with_stop_profit_loss' => '现货订单创建成功，止盈止损委托已设置',
        'spot_order_created' => '现货订单创建成功',
        'take_profit_order_created' => '止盈委托订单已创建',
        'stop_loss_order_created' => '止损委托订单已创建',
    ],
]; 