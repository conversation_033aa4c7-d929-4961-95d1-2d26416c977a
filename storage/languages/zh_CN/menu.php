<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq

 */
return [
    'parent_id' => '父级ID',
    'name' => '名称',
    'component' => '组件',
    'redirect' => '重定向',
    'path' => '路径',
    'type' => '类型',
    'status' => '状态',
    'sort' => '排序',
    'remark' => '备注',
    'meta' => [
        'title' => '标题',
        'i18n' => '国际化',
        'badge' => '徽章',
        'icon' => '图标',
        'affix' => '是否固定',
        'hidden' => '是否隐藏',
        'type' => '类型',
        'cache' => '是否缓存',
        'link' => '链接',
    ],
];
