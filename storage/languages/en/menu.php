<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq
 
 */
return [
    'parent_id' => 'parent ID',
    'name' => 'name',
    'component' => 'component',
    'path' => 'path',
    'redirect' => 'redirect',
    'type' => 'type',
    'status' => 'status',
    'sort' => 'sort',
    'remark' => 'remark',
    'meta' => [
        'title' => 'title',
        'i18n' => 'internationalization',
        'badge' => 'badge',
        'icon' => 'icon',
        'affix' => 'is affixed',
        'hidden' => 'is hidden',
        'type' => 'type',
        'cache' => 'is cached',
        'link' => 'link',
    ],
];
