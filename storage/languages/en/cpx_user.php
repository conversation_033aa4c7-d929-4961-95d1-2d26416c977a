<?php

declare(strict_types=1);

return [
    'enums' => [
        'register_type' => [
            '1' => 'Email Registration',
            '2' => 'Phone Registration',
            '3' => 'Apple Login Registration',
            '4' => 'Google Login Registration',
        ],
        'status' => [
            '1' => 'Normal',
            '2' => 'Disabled',
            '3' => 'Deleted',
        ],
        'kyc_status' => [
            '0' => 'Unsubmitted',
            '1' => 'Pending',
            '2' => 'Approved',
            '3' => 'Rejected',
        ],
        'login_result' => [
            '1' => 'Login Success',
            '2' => 'Login Failed',
        ],
        'device_status' => [
            '1' => 'Normal',
            '2' => 'Disabled',
        ],
        'vip_level_status' => [
            '1' => 'Enabled',
            '2' => 'Disabled',
        ],
        'password_type' => [
            '1' => 'Login Password',
            '2' => 'Fund Password',
        ],
        'trade_type' => [
            '1' => 'Spot',
            '2' => 'Contract',
            '3' => 'Spot Leverage',
        ],
        'commission_status' => [
            '0' => 'Pending',
            '1' => 'Settled',
            '2' => 'Cancelled',
        ],
        'flows_type' => [
            '1' => 'Recharge',
            '2' => 'Withdraw',
            '3' => 'Transfer',
            '4' => 'Spot Trade',
            '5' => 'Spot Trade Fee',
            '6' => 'Spot Trade Rebate',
            '30' => 'Futures Trade',
            '31' => 'Futures Trade Fee',
            '32' => 'Futures Trade Rebate',
            '50' => 'Margin Trade',
            '51' => 'Margin Trade Fee',
            '52' => 'Margin Borrow',
            '53' => 'Margin Repay',
            '54' => 'Margin Interest',
            '70' => 'Chain Trade',
            '100' => 'Copy Trade',
        ],
    ],
];
