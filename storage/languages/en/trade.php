<?php

declare(strict_types=1);

return [
    'enums' => [
        'commission_order_type' => [
            '1' => 'Stop Profit/Loss',
            '2' => 'Plan Order',
            '3' => 'Trailing Order',
        ],
        'commission_status' => [
            '0' => 'Pending',
            '1' => 'Triggered',
            '2' => 'Completed',
            '-1' => 'Cancelled',
            '-2' => 'Trigger Failed',
        ],
        'trigger_condition' => [
            '1' => 'Greater Than or Equal',
            '2' => 'Less Than or Equal',
        ],
        'trigger_type' => [
            '1' => 'Limit Order',
            '2' => 'Market Order',
        ],
    ],
    'margin' => [
        'margin_type' => [
            '1' => 'Isolated',
            '2' => 'Cross',
        ],
        'position_side' => [
            '1' => 'Long',
            '2' => 'Short',
        ],
        'margin_order_status' => [
            '0' => 'Cancelled',
            '1' => 'Pending',
            '2' => 'Partial Filled',
            '3' => 'Filled',
        ],
        'margin_position_status' => [
            '1' => 'Holding',
            '2' => 'Closed',
            '3' => 'Liquidated',
        ],
        'margin_borrow_status' => [
            '1' => 'Active',
            '2' => 'Repaid',
            '3' => 'Overdue',
        ],
        'margin_interest_status' => [
            '1' => 'Pending',
            '2' => 'Deducted',
            '3' => 'Failed',
            '4' => 'Waived',
        ],
        'liquidation_type' => [
            '1' => 'Margin Insufficient',
            '2' => 'ADL Reduction',
            '3' => 'Expiry Liquidation',
        ],
        'liquidation_status' => [
            '1' => 'Pending',
            '2' => 'Executing',
            '3' => 'Completed',
            '4' => 'Failed',
            '5' => 'Cancelled',
        ],
        'reduce_only' => [
            '0' => 'No',
            '1' => 'Yes',
        ],
        'trigger_source' => [
            '1' => 'System',
            '2' => 'Manual',
        ],
    ],
    'messages' => [
        'commission_order_created' => 'Commission order created successfully',
        'commission_order_updated' => 'Commission order updated successfully',
        'commission_order_cancelled' => 'Commission order cancelled successfully',
        'commission_order_triggered' => 'Commission order triggered',
        'commission_order_expired' => 'Commission order expired',
        'commission_order_not_found' => 'Commission order not found or processed',
        'commission_order_no_update_fields' => 'No fields to update',
        'commission_batch_cancelled' => 'Batch cancel completed',
        'spot_order_with_stop_profit_loss' => 'Spot order created successfully, stop profit/loss commission set',
        'spot_order_created' => 'Spot order created successfully',
        'take_profit_order_created' => 'Take profit commission order created',
        'stop_loss_order_created' => 'Stop loss commission order created',
    ],
]; 