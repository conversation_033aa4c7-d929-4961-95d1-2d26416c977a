<?php

declare(strict_types=1);

return [
    // 仅适用于开发环境下，每次启动服务时触发备份
    // 是否启用自动备份
    'enabled' => env('DB_BACKUP_ENABLED', false),

    // 备份时间间隔(小时)
    'interval' => env('DB_BACKUP_INTERVAL', 1),

    // 备份文件保存路径
    'backup_path' => env('DB_BACKUP_PATH', BASE_PATH . '/databases/mysql_backup'),

    // 不需要备份的表
    'exclude_tables' => [
        'cpx_user_login_log',
        'user_operation_log',
        'user_login_log',
    ],

    // 最大保留备份文件数
    'max_files' => env('DB_BACKUP_MAX_FILES', 7),
];
