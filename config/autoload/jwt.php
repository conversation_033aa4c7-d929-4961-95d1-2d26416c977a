<?php

declare(strict_types=1);
/**
 * This file is part of AlgoQuant.
 *
 * @link     https://www.algoquant.pro
 * @document https://doc.algoquant.pro
 * @contact  @chenmaq

 */

use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac\Sha256;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key\InMemory;
use <PERSON><PERSON>bucci\JWT\Token\RegisteredClaims;
use Mine\Jwt\Jwt;

if (!env("JWT_SECRET")) {
    dump("JWT_SECRET is not set. Please run `php bin/hyperf.php jwt:secret` to generate a new key.");
}

if (!env("JWT_API_SECRET")) {
    dump("JWT_API_SECRET is not set. Please run `php bin/hyperf.php jwt:secret` to generate a new key.");
}

return [
    'default' => [
        // jwt 配置 https://lcobucci-jwt.readthedocs.io/en/latest/
        'driver' => Jwt::class,
        // jwt 签名key
        'key' => env("JWT_SECRET") ? InMemory::base64Encoded(env("JWT_SECRET")) : InMemory::base64Encoded(base64_encode(random_bytes(64))),
        // jwt 签名算法 可选 https://lcobucci-jwt.readthedocs.io/en/latest/supported-algorithms/
        'alg' => new Sha256(),
        // token过期时间，单位为秒
        'ttl' => (int) env('JWT_TTL', 3600),
        // 刷新token过期时间，单位为秒
        'refresh_ttl' => (int) env('JWT_REFRESH_TTL', 7200),
        // 黑名单模式
        'blacklist' => [
            // 是否开启黑名单
            'enable' => true,
            // 黑名单缓存前缀
            'prefix' => 'jwt_blacklist',
            // 黑名单缓存驱动
            'connection' => 'default',
            // 黑名单缓存时间 该时间一定要设置比token过期时间要大一点，最好设置跟过期时间一样
            'ttl' => (int) env('JWT_BLACKLIST_TTL', 7201),
        ],
        'claims' => [
            // 默认的jwt claims
            RegisteredClaims::ISSUER => (string) env('APP_NAME'),
        ],
    ],
    // 在你想要使用不同的场景时，可以在这里添加配置.可以填一个。其他会使用默认配置
    'api' => [
        // jwt 配置 https://lcobucci-jwt.readthedocs.io/en/latest/
        'driver' => Jwt::class,
        // jwt 签名key
        'key' => env("JWT_API_SECRET") ? InMemory::base64Encoded(env("JWT_API_SECRET")) : InMemory::base64Encoded(base64_encode(random_bytes(64))),
        // jwt 签名算法 可选 https://lcobucci-jwt.readthedocs.io/en/latest/supported-algorithms/
        'alg' => new Sha256(),
        // token过期时间，单位为秒
        'ttl' => (int) env('JWT_API_TTL', 60 * 60 * 24 * 7),
        // 刷新token过期时间，单位为秒
        'refresh_ttl' => (int) env('JWT_API_REFRESH_TTL', 60 * 60 * 24 * 14),
        // 黑名单模式
        'blacklist' => [
            // 是否开启黑名单
            'enable' => true,
            // 黑名单缓存前缀
            'prefix' => 'jwt_blacklist',
            // 黑名单缓存驱动
            'connection' => 'default',
            // 黑名单缓存时间 该时间一定要设置比token过期时间要大一点，最好设置跟过期时间一样
            'ttl' => (int) env('JWT_API_BLACKLIST_TTL', 60 * 60 * 24 * 7 + 1),
        ],
        'claims' => [
            // 默认的jwt claims
            RegisteredClaims::ISSUER => (string) env('APP_NAME'),
        ],
    ],
];
