<?php

declare(strict_types=1);
/**
 * This file is part of apielf/hyperf-encryption.
 *
 * @link     https://github.com/apielf/hyperf-encryption
 * @license  https://github.com/apielf/hyperf-encryption/blob/master/LICENSE
 */
return [
    'default' => 'aes',

    'driver' => [
        'aes' => [
            'class' => \ApiElf\HyperfEncryption\Driver\AesDriver::class,
            'options' => [
                'key' => env('AES_KEY', ''),
                'cipher' => env('AES_CIPHER', 'AES-128-CBC'),
            ],
        ],
    ],
];
