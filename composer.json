{"name": "algoquant/base-dev-server", "type": "project", "keywords": ["algoquant", "base-dev-server"], "homepage": "https://www.algoquant.pro/", "description": "", "license": "Apache-2.0", "require": {"php": ">=8.1", "ext-bcmath": "*", "ext-fileinfo": "*", "ext-json": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-redis": "*", "ext-swoole": ">=5.0", "alibabacloud/dm-20151123": "^1.2", "alibabacloud/dysmsapi-20180501": "^1.0", "apielf/hyperf-encryption": "^3.0", "apielf/hyperf-query-builder": "^6.3", "firebase/php-jwt": "^6.11", "friendsofhyperf/tinker": "^3.1", "hyperf/async-queue": "3.1.*", "hyperf/cache": "3.1.*", "hyperf/carbon": "^3.1", "hyperf/command": "^3.1", "hyperf/config": "3.1.*", "hyperf/constants": "3.1.*", "hyperf/context": "3.1.*", "hyperf/contract": "3.1.*", "hyperf/coroutine": "3.1.*", "hyperf/crontab": "^3.1", "hyperf/database": "3.1.*", "hyperf/database-pgsql": "*", "hyperf/db-connection": "3.1.*", "hyperf/di": "3.1.*", "hyperf/dispatcher": "3.1.*", "hyperf/elasticsearch": "^3.1", "hyperf/engine": "^2.0", "hyperf/event": "3.1.*", "hyperf/exception-handler": "3.1.*", "hyperf/framework": "3.1.*", "hyperf/guzzle": "^3.1", "hyperf/helper": "~3.1", "hyperf/http-server": "3.1.*", "hyperf/logger": "3.1.*", "hyperf/model-cache": "3.1.*", "hyperf/paginator": "~3.1", "hyperf/polyfill-coroutine": "3.1.*", "hyperf/pool": "3.1.*", "hyperf/process": "3.1.*", "hyperf/redis": "3.1.*", "hyperf/resource": "^3.1", "hyperf/server": "3.1.*", "hyperf/snowflake": "^3.1", "hyperf/task": "^3.1", "hyperf/utils": "3.1.*", "hyperf/validation": "~3.1", "hyperf/view-engine": "^3.1", "hyperf/websocket-server": "^3.1", "mineadmin/access": "~3.0", "mineadmin/app-store": "~3.0", "mineadmin/auth-jwt": "~3.0", "mineadmin/core": "~3.0", "mineadmin/jwt": "~3.0", "mineadmin/support": "~3.0", "mineadmin/swagger": "~3.0", "mineadmin/upload": "~3.0", "overtrue/easy-sms": "^3.1", "pragmarx/google2fa": "^8.0", "rybakit/msgpack": "^0.9.1", "web-auth/webauthn-lib": "^5.2", "ext-curl": "*"}, "require-dev": {"doctrine/dbal": "^3.6", "fakerphp/faker": "^1.23", "hyperf/devtool": "3.1.*", "hyperf/ide-helper": "3.1.*", "hyperf/testing": "3.1.*", "hyperf/watcher": "3.1.*", "mockery/mockery": "^1.0", "qiutuleng/hyperf-dump-server": "^2.1", "swoole/ide-helper": "^6.0", "zircote/swagger-php": "4.10.6"}, "suggest": {"ext-openssl": "Required to use HTTPS.", "ext-json": "Required to use JSON.", "ext-pdo": "Required to use MySQL Client.", "ext-pdo_mysql": "Required to use MySQL Client.", "ext-redis": "Required to use Redis Client."}, "autoload": {"psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"HyperfTests\\": "./tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-autoload-dump": ["rm -rf runtime/container"], "start": ["Composer\\Config::disableProcessTimeout", "php bin/hyperf.php start"], "dev": ["Composer\\Config::disableProcessTimeout", "php bin/hyperf.php server:watch"]}}