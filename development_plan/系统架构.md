# 加密数字货币交易所系统架构设计文档

## 模块细分实施步骤与配合设计

为快速启动初期 MVP 产品，现将架构简化为以下模块：

* 核心统一服务（基于 Hyperf）：集成 API 网关 + WebSocket 行情推送 + 用户系统 + 代理系统等
* 做市机器人服务（独立运行进程）
* 撮合交易服务（C++ 实现，作为 PHP 扩展）
* 外部行情接入服务（可以内嵌或独立部署）
* CMS 内容管理系统模块
* 实时资讯互动模块
* 跟单交易模块
* 策略交易模块（网格、马丁）

---

### 1. 核心统一服务（Hyperf）

**职责：** 提供所有用户接口、WebSocket 推送、身份认证、撮合请求转发、行情订阅、代理管理。

**实施步骤：**

1. 建立 Hyperf 主服务项目结构。
2. 开发模块：用户登录注册、资产账户、下单、撤单、订单查询。
3. 集成 WebSocket 推送（Hyperf 自带组件）。
4. 开发代理商 / KOL 模块基础功能（邀请绑定、返佣记录）。
5. 接入做市机器人控制 API（如控制开关、配置）。
6. 对接撮合交易模块（使用 C++ 扩展或调用 FFI）。
7. WebSocket 行情：订阅成交、K线、盘口数据，使用 Redis 订阅推送。

**内部功能模块清单：**

* 用户系统：注册、登录、KYC、登录日志、会话管理
* 资金系统：充值、提现、划转、冻结、流水记录
* 订单系统：现货下单、撤单、订单查询、撮合指令中转
* 行情模块：K线生成、盘口构造、Redis 推送
* 代理系统：邀请关系绑定、多级返佣逻辑、佣金结算
* 通用模块：权限认证、配置中心、系统日志、参数校验

**配合模块：** 做市机器人、撮合引擎、外部行情服务

---

### 2. 撮合交易服务（C++，作为 PHP 扩展）

**职责：** 实时撮合所有市场交易订单，计算成交并返回撮合结果。

**实施步骤：**

1. 撰写撮合引擎逻辑，支持限价单、撮合优先级、撮合后输出成交。
2. 使用共享内存或队列方式输出成交数据。
3. 封装 PHP 扩展，暴露挂单、撤单、读取行情等方法。
4. 被 Hyperf 主服务直接调用。

**内部功能模块清单：**

* 市场管理模块：交易对注册、精度限制、手续费模型
* 撮合引擎核心模块：订单簿、价格优先撮合逻辑、成交事件处理
* 接口封装模块：PHP 扩展入口、参数解析、结果回传
* 性能优化模块：内存池、锁机制、异步写出成交数据

**配合模块：** Hyperf 主服务、行情推送、做市机器人

---

### 3. 做市机器人模块（独立服务）

**职责：** 自动模拟挂单，维持市场深度和成交活跃。

**实施步骤：**

1. 作为守护进程单独运行，语言可用 PHP/Go/Python。
2. 从外部行情服务或交易所内部行情订阅数据。
3. 生成挂单计划并调用 Hyperf 主服务下单接口。
4. 支持通过 Hyperf 提供的接口来启停控制与配置策略。

**内部功能模块清单：**

* 策略管理模块：随机挂单、趋势挂单、价差套利策略
* 控盘配置模块：盘口深度、价格控制区间、买卖频率
* 行情分析模块：实时订阅行情、生成下单意图
* 任务调度模块：定时执行策略、失败重试机制
* 控制接口模块：启动/停止/配置参数的 HTTP 控制接口

**配合模块：** Hyperf 主服务（API）、行情模块

---

### 4. 外部行情服务（可内嵌）

**职责：** 拉取币安、OKX 等交易对实时价格，作为市场指数参考价。

**实施步骤：**

1. 使用 Hyperf 协程客户端或独立进程拉取 Binance 等 websocket 数据。
2. 将行情数据写入 Redis 并供撮合与机器人使用。
3. 作为行情参考价用于控盘、做市算法、价格校准。

**内部功能模块清单：**

* 行情源适配器：Binance、OKX、Coinbase、Forex 等适配层
* 数据聚合模块：指数计算、平均价加权、数据清洗
* 行情缓存模块：内存缓存、Redis 发布通道、时序同步
* 容错与监控模块：自动重连、超时检测、异常报警

**配合模块：** 做市机器人、撮合引擎、行情推送

---

### 5. CMS 内容管理模块

**职责：** 提供公告、帮助中心、政策更新、市场分析等文章管理能力。

**实施步骤：**

1. 后台管理界面开发：支持文章发布、编辑、删除、分类、置顶。
2. 前端接口展示公告内容及栏目页。
3. 用户端支持展示系统公告、热门内容、帮助文档。

**内部功能模块清单：**

* 内容分类模块：公告、新闻、帮助中心、法律协议等
* 内容管理模块：富文本发布、草稿、审核、历史记录
* 前端展示模块：公告列表、详情页、置顶逻辑
* 权限控制模块：管理员发布权限、审计日志

**配合模块：** 用户系统、后台系统、通知推送

---

### 6. 实时资讯互动模块（社区）

**职责：** 用户可发布图文/文字内容，其他用户可点赞、评论、关注，形成社区氛围。

**实施步骤：**

1. 用户动态发布接口与前端页面（支持图文）
2. 评论系统、点赞系统、举报机制开发
3. 用户关注/粉丝体系设计，实现内容订阅流
4. 后台监控与内容审查接口构建（敏感词、人工介入）

**内部功能模块清单：**

* 动态内容模块：内容发布、编辑、图片上传、话题标签
* 评论互动模块：评论、楼层回复、@功能
* 点赞与收藏模块：记录点赞、取消、统计热度
* 用户关系模块：关注、粉丝、内容订阅流
* 内容审核模块：自动检测敏感内容、违规记录上报

**配合模块：** 用户系统、消息推送、管理后台

---

### 7. 跟单交易模块

**职责：** 支持用户跟随某个主账户的交易行为进行自动下单。

**实施步骤：**

1. 主账户交易记录实时广播。
2. 跟单者根据策略自动复制主账户操作。
3. 提供跟随关系管理、收益统计、风险控制等功能。

**内部功能模块清单：**

* 主账户记录模块：记录并广播交易指令
* 跟单逻辑模块：按比例或镜像方式同步下单
* 风控模块：限额、停止跟单条件、延迟保护
* 收益分析模块：主账户历史收益统计、排行榜
* 用户管理模块：绑定/解绑跟随关系、可用余额检测

**配合模块：** 用户系统、订单系统、资产系统

---

### 8. 策略交易模块（网格、马丁）

**职责：** 用户可配置自动交易策略，如网格、马丁等，系统自动执行下单逻辑。

**实施步骤：**

1. 提供策略创建与参数配置界面。
2. 后台服务定时检查并执行策略逻辑。
3. 支持暂停、恢复、终止策略执行。

**内部功能模块清单：**

* 策略配置模块：网格区间、马丁倍数、下单数量等
* 执行调度模块：周期检查、条件触发、下单接口调用
* 状态管理模块：运行中/暂停/终止状态标记与恢复点
* 用户可视化模块：图表展示、收益追踪、策略日志
* 风险控制模块：止损、止盈、最大亏损终止策略

**配合模块：** 用户系统、订单系统、行情模块、通知推送

---

### 配合说明

* 所有模块通过 Redis 消息（订阅/发布）或 HTTP 协议联动。
* 初期部署使用单体 Hyperf 服务减少开发复杂度。
* 后期如需拆分，可将 WebSocket 推送、代理系统、后台系统等进一步服务化。

