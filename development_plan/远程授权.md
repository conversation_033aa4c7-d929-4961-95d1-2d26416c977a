     ┌──────────────────────────────┐
     │       授权服务器（管理后台）  │
     │  - 授权生成、指纹登记         │
     │  - 客户状态动态变更           │
     └──────────┬───────────────────┘
                │ HTTPS + ECC 签名
                ▼
     ┌──────────────────────────────┐
     │  客户部署系统                 │
     │ ┌─────────────┐              │
     │ │ C++ 授权扩展 │              │
     │ └─────────────┘              │
     │ - 启动采集机器指纹            │
     │ - 加密后发送到服务端         │
     │ - 接收返回授权令牌 + 签名     │
     │ - 校验失败则中止系统运行     │
     └──────────────────────────────┘

## fork swoole 源码，修改暴露给php端使用的类名，修改mainserver 的启动流程
## 将撮合引成交引擎的逻辑在swoole 源码中实现，保留相关类给到php
