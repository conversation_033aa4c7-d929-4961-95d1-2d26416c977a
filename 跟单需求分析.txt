跟单需求分析：
1、申请成为交易专家时分为合约交易专家和现货交易专家。
2、申请成为交易专家时需要可设置展示名称（cpx_user 表中的 display_name），个人介绍。申请合约交易专家时还需要划转资金（最少100USDT）到合约带单账户。
3、交易专家申请是否需要审核可通过系统设置进行配置（系统设置表为：system_setting_config ）获取系统设置的方法：\Plugin\West\SysSettings\Helper\Helper: :getSysSettingByTypeCode('key');
4、在cpx_user表中增加两个字段：合约交易专家设置表ID，现货交易专家设置表ID。（申请成功（如果需要审核则审核通过后）后写入）
5、合约交易专家个人设置信息：
5.1、总资产（开启后您的合约总资产将公开展示）
5.2、展示专家评分及排名（开启此功能可公开您的评分和排名。关闭后，您的评分和排名将被隐藏，并从按评分排序的排名中移除。）
5.3、合约带单简介（申请时填写的个人介绍）
5.4、未结仓位保护（开启后，只有跟单者可以查看您的未结仓位。关闭后，您的未结仓位信息将对非跟单者滞后1小时公开。）
5.5、最小跟单金额（跟单者至少需要转入您设置的最小跟单金额才能跟单您的交易）（默认 50USDT）
5.6、推荐参数（推荐跟单者跟单时设置的参数，确认推荐后跟单者设置跟单参数时，将在页面显示推荐的各项参数）（推荐参数分为固定额度、跟单倍率两种。固定额度参数：固定额度（USDT）、止损比例、止盈比例、最大跟单；跟单倍率参数：跟单倍率、止损比例、止盈比例、最大跟单；）
5.7、带单合约（设置带单的交易对（币种））（默认平台支持的所有币种）
5.8、合约分润比例
6、现货交易专家个人设置信息：
6.1、现货带单（开启现货带单）
6.2、总资产（开启后您的现货总资产将公开展示）
6.3、资金构成（开启后您的现货账户当前资金构成比例将公开显示）
6.4、现货带单简介（申请时填写的个人介绍）
6.5、开启新交易对（开启后，新上线的交易对将自动开启带单模式）
6.6、未结仓位保护（开启后，只有跟单者可以查看您的未结仓位。关闭后，您的未结仓位信息将对非跟单者滞后1小时公开。）
6.7、推荐参数（推荐跟单者跟单时设置的参数，确认推荐后跟单者设置跟单参数时，将在页面显示推荐的各项参数）（推荐参数分为固定额度、跟单倍率两种。固定额度参数：固定额度（USDT）、止损比例、止盈比例、最大跟单；跟单倍率参数：跟单倍率、止损比例、止盈比例、最大跟单；）
6.8、现货币对（设置带单的交易对（币种））（默认平台支持的所有币种）
6.9、现货分润比例
7、合约交易专家问题反馈、身份撤销
7.1、问题反馈：提交数据：问题类型（可选类型在系统设置中配置）、问题描述
7.2、身份撤销：提交数据：撤销原因（可选类型在系统设置中配置）、撤销说明、合约带单资金退回账户选择（现货账户、合约账户）
8、现货交易专家问题反馈、身份撤销
8.1、问题反馈：提交数据：问题类型（可选类型在系统设置中配置）、问题描述
8.2、身份撤销：提交数据：撤销原因（可选类型在系统设置中配置）、撤销说明
9、交易专家列表展示状态条件
9.1、合约交易专家：开启合约带单（因为有专门的合约带单账户，所以成为成为合约交易专家后即为开启状态）、至少设置1个带单币对、至少存在1笔已平仓带单订单、合约带单账户总资产 ≧ 100 USDT
9.2、开启现货带单、至少设置1个带单币对、至少存在1笔已平仓带单订单、现货总资产 ≧ 100 USDT
10、交易专家等级功能（不同等级可带单人数不同，最大分润比例不同）
10.1、合约交易专家等级条件（满足条件一和条件二即可升级）：
条件一：合约带单金额（USDT）
条件二：跟单者总跟单资金（USDT）或者跟单交易人数（人）
10.2、现货交易专家等级条件（满足条件一和条件二即可升级）：
条件一：现货带单金额（USDT）
条件二：跟单者总跟单资金（USDT）或者跟单交易人数（人）
10.3、每周一6点定时判定，对交易员等级进行等级升降
11、交易专家徽章（合约交易专家徽章、现货交易专家徽章分开）
11.1、徽章类型
11.2、徽章（徽章图标、徽章名称、描述、获取条件）
12、关注交易专家（合约交易专家、现货交易专家分开）
13、数据统计、展示
13.1、合约交易专家：
(1)、带单数据：
(1.1)、综合统计（可选周期：7日、30日、90日、180日、全部）：收益率、交易笔数、累计跟单人数、总收益、胜率
(1.2)、当前带单列表（仓位、持仓均价、当前价、数量、订单入场价、止盈价/止损价、保证金、未实现盈亏、订单编号、操作）（支持币种筛选）
(1.3)、历史带单（仓位、订单入场价、平仓均价、数量、已实现盈亏、持仓均价、净利润、订单编号）
(2)、分润数据：
(2.1)、综合统计：累计已分润、预计待分润、昨日分润、当前分润比例
(2.2)、累计已分润（分润币种（暂时只支持USDT）、分润数量）
(2.3)、预计待分润（用户、待分润数量）
(2.4)、历史分润
(2.4.1)、按时间（时间（按天）、分润数量）
(2.4.2)、按跟随者（可选周期：7日、30日、90日、180日、全部）（跟随者、分润数量）
(3)、我的跟随者（筛选条件：资产（所有跟单者、资产为0、资产小于50USDT），尊享模式跟单者（是、否））：跟随者、跟单账户资金、累计跟单笔数、跟单收益、跟单净利润、分润比例、操作（移除）；批量移除
13.2、现货交易专家：
(1)、带单数据：
(1.1)、综合统计（可选周期：7日、30日、90日、180日、全部）：收益率、交易笔数、累计跟单人数、总收益、胜率
(1.2)、当前带单列表（仓位、持仓均价、当前价、数量、订单入场价、止盈价/止损价、保证金、未实现盈亏、订单编号、操作）（支持币种筛选）
(1.3)、历史带单（仓位、订单入场价、平仓均价、数量、已实现盈亏、持仓均价、净利润、订单编号）
(2)、分润数据：
(2.1)、综合统计：累计已分润、预计待分润、昨日分润、当前分润比例
(2.2)、累计已分润（分润币种（暂时只支持USDT）、分润数量）
(2.3)、预计待分润（用户、待分润数量）
(2.4)、历史分润
(2.4.1)、按时间（时间（按天）、分润数量）
(2.4.2)、按跟随者（可选周期：7日、30日、90日、180日、全部）（跟随者、分润数量）
(3)、我的跟随者（筛选条件：资产（所有跟单者、资产为0、资产小于50USDT），尊享模式跟单者（是、否））：跟随者、跟单账户资金、累计跟单笔数、跟单收益、跟单净利润、分润比例、操作（移除）；批量移除
14、跟单尊享模式：
一、核心概念
跟单尊享模式是一个私享模式，为交易专家提供隐私的带单环境，支持白名单邀请、策略保护、自由设置分润比例等功能。
二、主要功能模块
1. 白名单邀请功能
功能描述：交易专家可创建专属邀请链接，指定用户跟单
核心特性：
自定义链接标题（可选）
设置链接有效期
设置最大邀请人数
生成邀请链接或分享链接
支持多个白名单链接管理不同跟单者群体
2. 未结仓位保护功能
功能描述：保护交易专家的交易策略隐私
核心特性：
仅与尊享模式下的跟单者共享未结仓位
对普通平台用户隐藏仓位信息
全方位保护带单策略
3. 分润比例设置功能
功能描述：交易专家可自由设置分润比例
核心特性：
支持0% ~ 99%之间自由设置分润比例
每日可修改分润比例次数限制为3次
调整后新订单采用新比例，历史订单保持原比例
4. 跟单者个性化管理功能
功能描述：交易专家可管理跟单者
核心特性：
监控跟单者交易动态
支持添加和移除跟单者
查看跟单者信息（头像/昵称、跟单时间、状态、贡献分润）
实现精细化运营管理
三、模式特点
开启尊享模式的影响：
首页顶级交易专家、全部交易专家、排行榜将不展示排名
原有已跟单用户不受影响，分润及已跟单用户的分润也不受任何影响
用户仅可通过专属白名单链接进行跟单
其他普通平台用户无法直接进行跟单
支持针对白名单邀请的跟单者调整分润比例
关闭尊享模式的变化：
原有已跟单用户不受影响，所有公开用户可跟单
所有白名单链接均会失效，但保留已记录的白名单信息
分润比例恢复至根据等级对应的分润比例
四、跟单者视角
加入尊享模式的特点：
可跟随开启了白名单邀请的交易专家订单
可查看此类交易专家的资产和订单详情
交易专家有权调整分润比例（0% ~ 99%）
分润比例调整时会发送邮件通知

需求问题澄清及补充：
需求澄清：
合约专家申请时划转的100USDT是否需要创建专门的账户类型？
在 user_accounts_assets 表字段 account_type 对应的枚举类 \App\Model\Enums\User\AccountType 中定义了 COPY 即为 跟单钱包。

推荐参数补充说明
固定额度：跟单者每次跟单固定投入的保证金（USDT）
跟单倍率：跟单者每次下单数量是交易专家带单订单数量的倍数
其中，最大跟单为最大跟单的保证金数量（USDT）
推荐参数分为两种类型，用户跟单时需要在固定额度和跟单倍率中选择一种类型，然后在风险控制中默认使用对应类型的推荐参数。
另外这里还需要补充一下用户跟单时有两种模式可选：
第一种：智能比例
1、什么是智能比例跟单？
智能比例跟单是一种根据交易专家使用的资金比例自动进行跟单成本计算的新跟单模式。
按照资金比例进行跟单，可以保证跟随者每一单的资金风险与交易专家保持一致。根据计算出的比率，跟随者可以推算出交易专家进行每一笔交易时的风险控制和信心程度。举例说明：若交易专家的带单账户中有 1,000 USDT，其中一笔带单投入 100 USDT 的保证金，那么该笔订单的资金比例即为 10%。此时若跟随者跟随该交易专家的可用余额为 100 USDT，那么该笔跟单仅会投入 10 USDT 的保证金。
与原有的的复制模式不同，智能复制模式只需两个个步骤即可跟随交易大师开始赚钱。在选中一个满意的交易专家后，只需输入投资金额（最低 50 USDT），然后点击“立即跟单”按钮即可完成整个跟单设置。 这一模式的推出，进一步降低了合约跟单交易的门槛，对于对一些跟单参数完全不了解的小白用户来说，也可以真正实现一键跟单，赚取收益。
2、智能比例跟单的优势是什么？
a. 仅需输入投资金额即可一键跟单，无需再去设置其他复杂的参数，真正实现便捷高效地跟单；
b. 使用相同资金比例可确保精确跟单，如果交易专家在一笔交易中投入了10%的资金，跟随者也会投入相同比例的资金，无需再花费时间和精力设置每笔跟单的金额或数量；
c. 使用相同资金比例跟单，可以帮助跟随者了解交易专家每一笔订单的信心程度。例如：假设交易专家某笔订单使用资金比例为1%，可以推断交易专家对该笔订单收益的信心程度不是特别高，该情况下跟随者也以1%的资金占比进行跟单，该笔跟单就不会有太大风险；同样假设交易专家某笔订单使用资金比例为50%，可以推断交易专家对该笔订单收益的信心程度比较高，跟随者以同样比例跟单，就不会错失赚取更高收益的机会。
d. 智能比例跟单投资额的追加和减少都是针对单个跟随交易专家的，跟随者可自主控制针对每个交易专家的投资金额。
3、如何进行智能比例跟单？
a. 在心仪的交易专家主页点击“跟单”后，跳转进入跟单设置页；
b. 选择跟单模式：智能比例跟单；
c. 必填项：输入投资金额（USDT）：该投资金额仅针对当下正在进行跟随设置的交易专家，可从合约账户转入或者直接购买；
d. 设置风险项：止盈比例、止损比例、每个币对的最大跟随金额（USDT）、滑点比例；
e. 点击“下一步”，并确认跟单。
4、使用智能比例跟单的注意事项
a. 使用智能比例跟单只能跟随至多50名交易专家，要跟随更多交易专家，请使用多元探索跟单；
b. 智能比例跟单仅需要设置跟当前交易专家的投资金额即可，其他项为选填项；
c. 当前智能比例跟单默认跟随交易专家杠杆模式、全仓/逐仓保证金模式；
d. 智能比例跟单的投资额的追加和减少都是针对单个跟随交易专家的，且减少投资额时，最低需要预留 50 USD T 以保持跟单的正常运行。
第二种：多元探索（这种模式有固定额度和跟单倍率两种类型）
1、什么是多元探索跟单？
多元探索跟单是即 Bitget 原有的经典跟单模式，它允许投资者用一笔资金同时复制多个精英交易者的策略，以获得多元化的投资机会。在该模式下，无需每跟随一个交易专家都事先投入一笔资金。使用同一个资金池子，可以同时跟随无限多个交易专家。
在多元探索跟单模式下跟随交易专家时，跟随者可以选择按照固定额度跟单或者按照倍率跟单：
按照固定额度跟单，即每笔跟单都使用固定的保证金成本，无论交易专家的的每笔带单出多少金额；按照倍率跟单，即每笔跟单下单数量是交易专家下单数量的固定倍数。
2、多元探索跟单的优势是什么？
a. 提供多样化投资机会，在不同交易专家的交易风格中分散风险；
b. 有助于捕捉每个市场机会，不错过任何潜在的突破性交易机会；
c. 资金利用率极高，即使跟随多个交易专家，也共用一个资金池。
3、如何进行多元探索跟单？
a. 在心仪的交易专家主页点击“跟单”后，跳转进入跟单设置页；
b. 选择跟单模式：多元探索跟单；
c. 必填项：输入投资金额（USDT）：指定一个适用于跟单的投资金额，该金额将作为所有交易专家的共用资金池，投资金额可从合约账户转入或者直接购买，总投资额必须≥50 USDT；
d. 必填项：选择跟单方式：固定额度 & 倍率；
固定额度：代表每次跟单的保证金成本
倍率：代表每次跟单与交易专家成倍数关系
e. 选择币对：添加想要跟随的合约交易币对；
f. 设置风险项：止盈比例、止损比例、每个币对的最大跟随金额（USDT）、滑点比例；
g. 自动跟随：确认是否自动跟随新上的合约币对；
h. 点击“下一步”，并确认跟单。
* 普通 / 高级设置：默认普通设置，若需更详细的设置，请选择高级设置
（详细请看 Bitget 跟单：跟随者合约跟单操作指南）
4、使用多元探索跟单的注意事项
a. 选择后，系统将自动保存“下次使用此模式作为默认模式”；
b. 同一个交易专家，同一跟单用户只能选择一种跟单模式（多元探索跟单或智能比例跟单），支持在跟单过程中切换跟单模式；
c. 切换跟单模式时，需要平仓完跟随该交易专家的所有订单后才可以切换；
d. 多元探索跟单模式下，跟随所有交易专家共用同一个资金池，需要保证这个资金池下最低预留50 USDT 以保持跟单的正常运行。

带单订单问题（跟单考虑现货和合约）
建议增加与trade_spot_order(现货订单)、trade_perpetual_order(合约订单)关联的表，用于记录带单订单的详细信息。

等级升降级的具体条件
条件二中有两个子条件，满足其一即可，降级时条件二中的两个子条件都不满足时表示不满足条件二，因为条件一和条件二需要同时满足才能达到对应等级所以当条件二不满足时应该降级。

尊享模式的分润比例调整限制
建议通过一张表记录下修改记录，修改次数的限制通过查询修改记录来判定。